#!/usr/bin/env python3
"""
测试 OPT-T3.2 增强轮动阶段识别
验证模糊逻辑轮动阶段识别功能
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版板块轮动分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_sector_rotation_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_sector_rotation_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedSectorRotationAnalyzer = analyzer_module.EnhancedSectorRotationAnalyzer
RotationStageDetails = analyzer_module.RotationStageDetails


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health):
        self.internal_health = internal_health


def test_fuzzy_logic_basic():
    """测试基础模糊逻辑功能"""
    print("=== 测试基础模糊逻辑功能 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试不同的RII和速度组合
    test_scenarios = [
        {
            'name': '低RII低速度_稳定期',
            'rii': 0.15,
            'velocity': 0.1,
            'avg_health': 75,
            'expected_stage': '稳定期'
        },
        {
            'name': '中RII中速度_启动期',
            'rii': 0.45,
            'velocity': 0.35,
            'avg_health': 60,
            'expected_stage': '启动期'
        },
        {
            'name': '高RII高速度_加速期',
            'rii': 0.75,
            'velocity': 0.65,
            'avg_health': 65,
            'expected_stage': '加速期'
        },
        {
            'name': '高RII高速度低健康_混乱期',
            'rii': 0.85,
            'velocity': 0.8,
            'avg_health': 35,
            'expected_stage': '混乱期'
        },
        {
            'name': '中RII低速度_收敛期',
            'rii': 0.5,
            'velocity': 0.15,
            'avg_health': 55,
            'expected_stage': '收敛期'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"--- {scenario['name']} ---")
        print(f"输入: RII={scenario['rii']:.2f}, 速度={scenario['velocity']:.2f}, 健康度={scenario['avg_health']}")
        
        # 创建广度指标
        breadth_metrics = {
            f'Sector{i}': MockBreadthMetrics(scenario['avg_health'] + np.random.normal(0, 5))
            for i in range(5)
        }
        
        # 执行阶段识别
        result = analyzer.identify_rotation_stage_enhanced(
            rii=scenario['rii'],
            velocity=scenario['velocity'],
            breadth_metrics=breadth_metrics
        )
        
        print(f"识别结果: {result.stage}")
        print(f"置信度: {result.confidence:.3f}")
        print(f"预期阶段: {scenario['expected_stage']}")
        print(f"匹配: {'✅' if result.stage == scenario['expected_stage'] else '❌'}")
        
        print(f"阶段概率:")
        for stage, prob in result.stage_probabilities.items():
            print(f"  {stage}: {prob:.3f}")
        
        print(f"模糊分数 (前5个):")
        fuzzy_items = list(result.fuzzy_scores.items())[:5]
        for key, score in fuzzy_items:
            print(f"  {key}: {score:.3f}")
        
        print(f"转换指标:")
        for key, value in result.transition_indicators.items():
            print(f"  {key}: {value:.3f}")
        
        print(f"决策因素:")
        for factor in result.decision_factors[:3]:  # 显示前3个
            print(f"  - {factor}")
        
        print()
    
    return True


def test_stage_transition_detection():
    """测试阶段转换检测"""
    print("\n=== 测试阶段转换检测 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试边界情况（容易发生转换的情况）
    transition_scenarios = [
        {
            'name': '稳定期向启动期转换',
            'rii': 0.35,  # 在稳定期和启动期之间
            'velocity': 0.25,
            'avg_health': 65
        },
        {
            'name': '启动期向加速期转换',
            'rii': 0.6,
            'velocity': 0.55,  # 在启动期和加速期之间
            'avg_health': 60
        },
        {
            'name': '加速期向混乱期转换',
            'rii': 0.8,
            'velocity': 0.75,
            'avg_health': 45  # 健康度下降
        },
        {
            'name': '混乱期向收敛期转换',
            'rii': 0.4,  # RII开始下降
            'velocity': 0.3,
            'avg_health': 40
        }
    ]
    
    for scenario in transition_scenarios:
        print(f"--- {scenario['name']} ---")
        
        # 创建广度指标（增加一些变异性）
        breadth_metrics = {
            f'Sector{i}': MockBreadthMetrics(
                scenario['avg_health'] + np.random.normal(0, 10)
            ) for i in range(6)
        }
        
        result = analyzer.identify_rotation_stage_enhanced(
            rii=scenario['rii'],
            velocity=scenario['velocity'],
            breadth_metrics=breadth_metrics
        )
        
        print(f"主要阶段: {result.stage}")
        print(f"置信度: {result.confidence:.3f}")
        
        # 分析转换风险
        transition_risk = result.transition_indicators['transition_risk']
        stability_score = result.transition_indicators['stability_score']
        
        print(f"转换风险: {transition_risk:.3f}")
        print(f"稳定性评分: {stability_score:.3f}")
        
        if transition_risk > 0.6:
            print("🔄 高转换风险 - 阶段可能即将改变")
        elif transition_risk > 0.4:
            print("⚠️  中等转换风险 - 需要密切关注")
        else:
            print("✅ 低转换风险 - 阶段相对稳定")
        
        # 显示概率分布
        print(f"概率分布:")
        sorted_probs = sorted(result.stage_probabilities.items(), 
                            key=lambda x: x[1], reverse=True)
        for stage, prob in sorted_probs:
            print(f"  {stage}: {prob:.3f}")
        
        print()
    
    return True


def test_fuzzy_membership_calculation():
    """测试模糊隶属度计算"""
    print("\n=== 测试模糊隶属度计算 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试极端值
    extreme_cases = [
        {'rii': 0.0, 'velocity': 0.0, 'health': 20, 'desc': '极低值'},
        {'rii': 1.0, 'velocity': 1.0, 'health': 90, 'desc': '极高值'},
        {'rii': 0.5, 'velocity': 0.5, 'health': 50, 'desc': '中等值'},
        {'rii': 0.2, 'velocity': 0.8, 'health': 70, 'desc': '不匹配组合1'},
        {'rii': 0.9, 'velocity': 0.1, 'health': 30, 'desc': '不匹配组合2'}
    ]
    
    for case in extreme_cases:
        print(f"--- {case['desc']} ---")
        print(f"输入: RII={case['rii']}, 速度={case['velocity']}, 健康度={case['health']}")
        
        # 创建简单的广度指标
        breadth_metrics = {
            'TestSector': MockBreadthMetrics(case['health'])
        }
        
        # 计算模糊隶属度
        fuzzy_scores = analyzer._calculate_fuzzy_membership(
            case['rii'], case['velocity'], breadth_metrics
        )
        
        print(f"RII隶属度:")
        print(f"  低: {fuzzy_scores['rii_low']:.3f}")
        print(f"  中: {fuzzy_scores['rii_medium']:.3f}")
        print(f"  高: {fuzzy_scores['rii_high']:.3f}")
        
        print(f"速度隶属度:")
        print(f"  低: {fuzzy_scores['velocity_low']:.3f}")
        print(f"  中: {fuzzy_scores['velocity_medium']:.3f}")
        print(f"  高: {fuzzy_scores['velocity_high']:.3f}")
        
        print(f"健康度隶属度:")
        print(f"  低: {fuzzy_scores['health_low']:.3f}")
        print(f"  中: {fuzzy_scores['health_medium']:.3f}")
        print(f"  高: {fuzzy_scores['health_high']:.3f}")
        
        # 验证隶属度总和
        rii_sum = fuzzy_scores['rii_low'] + fuzzy_scores['rii_medium'] + fuzzy_scores['rii_high']
        vel_sum = fuzzy_scores['velocity_low'] + fuzzy_scores['velocity_medium'] + fuzzy_scores['velocity_high']
        health_sum = fuzzy_scores['health_low'] + fuzzy_scores['health_medium'] + fuzzy_scores['health_high']
        
        print(f"隶属度总和: RII={rii_sum:.3f}, 速度={vel_sum:.3f}, 健康度={health_sum:.3f}")
        
        # 检查是否合理
        if abs(rii_sum - 1.0) > 0.1 or abs(vel_sum - 1.0) > 0.1 or abs(health_sum - 1.0) > 0.1:
            print("⚠️  隶属度总和异常")
        else:
            print("✅ 隶属度计算正常")
        
        print()
    
    return True


def test_confidence_calculation():
    """测试置信度计算"""
    print("\n=== 测试置信度计算 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 创建不同置信度场景
    confidence_scenarios = [
        {
            'name': '高置信度场景',
            'stage_probs': {'稳定期': 0.8, '启动期': 0.1, '加速期': 0.05, '混乱期': 0.03, '收敛期': 0.02},
            'fuzzy_scores': {'rii_low': 0.9, 'rii_medium': 0.1, 'rii_high': 0.0, 
                           'velocity_low': 0.85, 'velocity_medium': 0.15, 'velocity_high': 0.0,
                           'health_low': 0.0, 'health_medium': 0.2, 'health_high': 0.8}
        },
        {
            'name': '低置信度场景',
            'stage_probs': {'稳定期': 0.25, '启动期': 0.22, '加速期': 0.20, '混乱期': 0.18, '收敛期': 0.15},
            'fuzzy_scores': {'rii_low': 0.4, 'rii_medium': 0.3, 'rii_high': 0.3, 
                           'velocity_low': 0.35, 'velocity_medium': 0.35, 'velocity_high': 0.3,
                           'health_low': 0.3, 'health_medium': 0.4, 'health_high': 0.3}
        },
        {
            'name': '中等置信度场景',
            'stage_probs': {'稳定期': 0.5, '启动期': 0.2, '加速期': 0.15, '混乱期': 0.1, '收敛期': 0.05},
            'fuzzy_scores': {'rii_low': 0.6, 'rii_medium': 0.3, 'rii_high': 0.1, 
                           'velocity_low': 0.7, 'velocity_medium': 0.2, 'velocity_high': 0.1,
                           'health_low': 0.1, 'health_medium': 0.3, 'health_high': 0.6}
        }
    ]
    
    for scenario in confidence_scenarios:
        print(f"--- {scenario['name']} ---")
        
        confidence = analyzer._calculate_stage_confidence(
            scenario['stage_probs'], scenario['fuzzy_scores']
        )
        
        print(f"计算置信度: {confidence:.3f}")
        
        # 分析置信度组成
        max_prob = max(scenario['stage_probs'].values())
        fuzzy_std = np.std(list(scenario['fuzzy_scores'].values()))
        fuzzy_consistency = 1 - fuzzy_std
        
        print(f"最高阶段概率: {max_prob:.3f}")
        print(f"模糊分数标准差: {fuzzy_std:.3f}")
        print(f"模糊一致性: {fuzzy_consistency:.3f}")
        
        # 验证置信度合理性
        if scenario['name'] == '高置信度场景' and confidence < 0.7:
            print("⚠️  高置信度场景的置信度偏低")
        elif scenario['name'] == '低置信度场景' and confidence > 0.5:
            print("⚠️  低置信度场景的置信度偏高")
        else:
            print("✅ 置信度计算合理")
        
        print()
    
    return True


def test_decision_factors_generation():
    """测试决策因素生成"""
    print("\n=== 测试决策因素生成 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 创建测试数据
    test_data = {
        'fuzzy_scores': {
            'rii_low': 0.1, 'rii_medium': 0.2, 'rii_high': 0.7,
            'velocity_low': 0.0, 'velocity_medium': 0.3, 'velocity_high': 0.7,
            'health_low': 0.8, 'health_medium': 0.2, 'health_high': 0.0
        },
        'stage_probabilities': {
            '稳定期': 0.1, '启动期': 0.1, '加速期': 0.2, '混乱期': 0.5, '收敛期': 0.1
        },
        'transition_indicators': {
            'entropy': 0.6,
            'probability_gap': 0.3,
            'transition_risk': 0.8,
            'stability_score': 0.2
        }
    }
    
    factors = analyzer._generate_decision_factors(
        test_data['fuzzy_scores'],
        test_data['stage_probabilities'],
        test_data['transition_indicators']
    )
    
    print(f"生成的决策因素 ({len(factors)}个):")
    for i, factor in enumerate(factors, 1):
        print(f"{i}. {factor}")
    
    # 验证因素的合理性
    expected_factors = [
        "轮动强度指数高",
        "排名变化速度快",
        "平均健康度低",
        "阶段转换风险高"
    ]
    
    print(f"\n因素验证:")
    for expected in expected_factors:
        found = any(expected in factor for factor in factors)
        print(f"  {expected}: {'✅' if found else '❌'}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T3.2 增强轮动阶段识别\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础模糊逻辑
        test_results.append(("基础模糊逻辑", test_fuzzy_logic_basic()))
        
        print("=" * 60)
        
        # 2. 测试阶段转换检测
        test_results.append(("阶段转换检测", test_stage_transition_detection()))
        
        print("=" * 60)
        
        # 3. 测试模糊隶属度计算
        test_results.append(("模糊隶属度计算", test_fuzzy_membership_calculation()))
        
        print("=" * 60)
        
        # 4. 测试置信度计算
        test_results.append(("置信度计算", test_confidence_calculation()))
        
        print("=" * 60)
        
        # 5. 测试决策因素生成
        test_results.append(("决策因素生成", test_decision_factors_generation()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T3.2 轮动阶段识别增强完成！")
            print("🚀 主要改进:")
            print("  - 模糊逻辑阶段识别")
            print("  - 阶段转换风险检测")
            print("  - 动态置信度评估")
            print("  - 详细决策因素分析")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
