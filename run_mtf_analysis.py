#!/usr/bin/env python3
"""
多时间框架综合分析系统 - 主运行脚本
"""

import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🚀 多时间框架板块轮动与MarketBreadth综合分析系统")
    print("   Multi-Timeframe Sector Rotation & Market Breadth Analysis")
    print("=" * 80)
    print(f"📅 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def run_single_sector_analysis(sector_name: str):
    """运行单个板块的多时间框架分析"""
    print(f"📊 分析板块: {sector_name}")
    print("-" * 60)
    
    try:
        from multi_timeframe_analyzer import analyze_sector_mtf
        
        # 执行多时间框架分析
        analysis = analyze_sector_mtf(sector_name, timeframes=['5m', '1h', '1d'])
        
        # 显示核心结果
        print(f"🎯 综合分析结果:")
        print(f"   统一决策: {analysis.unified_decision}")
        print(f"   一致性得分: {analysis.consensus_score:.1%}")
        print(f"   信号可靠性: {analysis.signal_reliability:.1%}")
        print(f"   市场状态: {analysis.market_regime}")
        print(f"   风险等级: {analysis.overall_risk_level}")
        
        print(f"\n📈 各时间框架信号:")
        for tf, signal in analysis.timeframe_signals.items():
            print(f"   {tf:>3}: {signal.signal_type:>4} (强度:{signal.strength:.2f}, 置信度:{signal.confidence:.2f})")
            print(f"        RSI:{signal.rsi:>5.1f}, 健康度:{signal.internal_health:>5.1f}")
        
        print(f"\n⚖️ 动态权重:")
        for tf, weight in analysis.dynamic_weights.items():
            print(f"   {tf:>3}: {weight:>6.1%}")
        print(f"   原因: {analysis.weight_reasoning}")
        
        if analysis.conflicting_signals:
            print(f"\n⚠️ 冲突信号: {', '.join(analysis.conflicting_signals)}")
            print(f"   解决方案: {analysis.conflict_resolution}")
        
        print(f"\n💡 操作建议:")
        print(f"   入场策略: {analysis.entry_strategy}")
        print(f"   出场策略: {analysis.exit_strategy}")
        print(f"   仓位建议: {analysis.position_sizing}")
        
        if analysis.risk_factors:
            print(f"\n🚨 风险提示:")
            for risk in analysis.risk_factors:
                print(f"   - {risk}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def run_multiple_sectors_analysis():
    """运行多个板块的批量分析"""
    print("📊 批量板块分析")
    print("=" * 60)
    
    # 主要板块列表
    sectors = [
        "Technology",
        "Financials", 
        "Health Care",
        "Consumer Discretionary",
        "Communication Services",
        "Industrials",
        "Consumer Staples",
        "Energy",
        "Utilities",
        "Real Estate",
        "Materials"
    ]
    
    results = []
    
    for i, sector in enumerate(sectors, 1):
        print(f"\n{i:2d}. 分析 {sector}")
        print("-" * 40)
        
        try:
            from multi_timeframe_analyzer import analyze_sector_mtf
            
            analysis = analyze_sector_mtf(sector)
            
            # 简化显示
            print(f"    决策: {analysis.unified_decision:>12}")
            print(f"    可靠性: {analysis.signal_reliability:>8.1%}")
            print(f"    状态: {analysis.market_regime:>12}")
            print(f"    风险: {analysis.overall_risk_level:>8}")
            
            results.append({
                'sector': sector,
                'decision': analysis.unified_decision,
                'reliability': analysis.signal_reliability,
                'regime': analysis.market_regime,
                'risk': analysis.overall_risk_level,
                'entry': analysis.entry_strategy
            })
            
        except Exception as e:
            print(f"    ❌ 失败: {str(e)[:50]}...")
            continue
    
    # 汇总结果
    if results:
        print(f"\n📋 分析汇总 ({len(results)}/{len(sectors)} 成功)")
        print("=" * 80)
        
        # 按决策分类
        buy_sectors = [r for r in results if '买' in r['decision']]
        sell_sectors = [r for r in results if '卖' in r['decision']]
        hold_sectors = [r for r in results if '持有' in r['decision'] or '观望' in r['decision']]
        
        if buy_sectors:
            print(f"\n🟢 买入建议 ({len(buy_sectors)}个):")
            for r in sorted(buy_sectors, key=lambda x: x['reliability'], reverse=True):
                print(f"   {r['sector']:25} | {r['decision']:12} | 可靠性:{r['reliability']:.1%} | {r['entry']}")
        
        if sell_sectors:
            print(f"\n🔴 卖出建议 ({len(sell_sectors)}个):")
            for r in sorted(sell_sectors, key=lambda x: x['reliability'], reverse=True):
                print(f"   {r['sector']:25} | {r['decision']:12} | 可靠性:{r['reliability']:.1%} | {r['entry']}")
        
        if hold_sectors:
            print(f"\n🟡 持有/观望 ({len(hold_sectors)}个):")
            for r in sorted(hold_sectors, key=lambda x: x['reliability'], reverse=True):
                print(f"   {r['sector']:25} | {r['decision']:12} | 可靠性:{r['reliability']:.1%}")
        
        # 风险统计
        risk_stats = {}
        for r in results:
            risk = r['risk']
            risk_stats[risk] = risk_stats.get(risk, 0) + 1
        
        print(f"\n📊 风险分布:")
        for risk, count in sorted(risk_stats.items()):
            print(f"   {risk:8}: {count:2d}个板块")
    
    return len(results)

def run_interactive_mode():
    """交互模式"""
    print("🎮 交互分析模式")
    print("=" * 60)
    print("输入板块名称进行分析，输入 'quit' 退出")
    print("常用板块: Technology, Financials, Health Care, Energy")
    print()
    
    while True:
        try:
            sector = input("请输入板块名称: ").strip()
            
            if sector.lower() in ['quit', 'exit', 'q']:
                print("👋 退出交互模式")
                break
            
            if not sector:
                continue
            
            print()
            success = run_single_sector_analysis(sector)
            print()
            
            if success:
                # 询问是否继续
                continue_choice = input("继续分析其他板块? (y/n): ").strip().lower()
                if continue_choice in ['n', 'no']:
                    break
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查系统状态
    print("🔧 系统检查...")
    try:
        from multi_timeframe_analyzer import MultiTimeframeAnalyzer
        print("✅ 多时间框架分析引擎: 正常")
        
        # 检查数据库连接
        import sys
        sys.path.insert(0, 'config')
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        conn.close()
        print("✅ 数据库连接: 正常")
        
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
        print("请确保数据库正常运行且配置正确")
        return
    
    print()
    
    # 显示菜单
    while True:
        print("📋 选择运行模式:")
        print("   1. 单个板块分析")
        print("   2. 批量板块分析")
        print("   3. 交互分析模式")
        print("   4. 退出程序")
        print()
        
        try:
            choice = input("请选择 (1-4): ").strip()
            
            if choice == '1':
                print()
                sector = input("请输入板块名称 (如: Technology): ").strip()
                if sector:
                    print()
                    run_single_sector_analysis(sector)
                    print()
                    
            elif choice == '2':
                print()
                count = run_multiple_sectors_analysis()
                print(f"\n✅ 批量分析完成，成功分析 {count} 个板块")
                print()
                
            elif choice == '3':
                print()
                run_interactive_mode()
                print()
                
            elif choice == '4':
                print("👋 感谢使用多时间框架综合分析系统!")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
                print()
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 运行错误: {e}")
            print()

if __name__ == "__main__":
    main()
