#!/usr/bin/env python3
"""
测试 OPT-T5.1 创建配置管理器
验证增强版配置管理器的功能
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入配置管理器
config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_config_manager.py')
spec = importlib.util.spec_from_file_location("enhanced_config_manager", config_path)
config_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_module)

EnhancedConfigManager = config_module.EnhancedConfigManager
ConfigVersion = config_module.ConfigVersion
MarketRegimeConfig = config_module.MarketRegimeConfig


def test_config_manager_initialization():
    """测试配置管理器初始化"""
    print("=== 测试配置管理器初始化 ===\n")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = EnhancedConfigManager(
            config_dir=temp_dir,
            enable_versioning=True,
            enable_auto_backup=True
        )
        
        print(f"📊 初始化结果:")
        summary = config_manager.get_config_summary()
        print(f"配置组数量: {summary['total_config_groups']}")
        print(f"当前市场状态: {summary['current_market_regime']}")
        print(f"配置组: {summary['config_groups']}")
        print(f"市场状态: {summary['market_regimes']}")
        print(f"验证规则数: {summary['validation_rules_count']}")
        
        # 验证默认配置
        algorithm_version = config_manager.get_config('algorithm.version')
        print(f"\n算法版本: {algorithm_version}")
        
        min_stocks = config_manager.get_config('algorithm.breadth_calculation.min_stocks_threshold')
        print(f"最小股票阈值: {min_stocks}")
        
        max_position = config_manager.get_config('risk_management.position_limits.max_position')
        print(f"最大仓位: {max_position}%")
        
        # 验证必需配置存在
        required_configs = [
            'algorithm.coherence.direction_threshold',
            'risk_management.decision_thresholds.strong_buy',
            'timeframes.daily.weight',
            'performance.parallel_processing.enable_multiprocessing'
        ]
        
        missing_configs = []
        for config_path in required_configs:
            value = config_manager.get_config(config_path)
            if value is None:
                missing_configs.append(config_path)
        
        if not missing_configs:
            print("✅ 所有必需配置都已正确初始化")
        else:
            print(f"❌ 缺少配置: {missing_configs}")
        
        return len(missing_configs) == 0


def test_parameter_validation():
    """测试参数验证功能"""
    print("\n=== 测试参数验证功能 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = EnhancedConfigManager(config_dir=temp_dir)
        
        # 测试有效参数
        valid_tests = [
            ('algorithm.breadth_calculation.min_stocks_threshold', 15),
            ('algorithm.breadth_calculation.data_quality_threshold', 0.8),
            ('risk_management.position_limits.max_position', 85),
            ('risk_management.position_limits.max_sector_weight', 0.3)
        ]
        
        print("📊 有效参数测试:")
        valid_count = 0
        for path, value in valid_tests:
            result = config_manager.set_config(path, value, f"测试设置 {path}")
            status = "✅" if result else "❌"
            print(f"  {path} = {value}: {status}")
            if result:
                valid_count += 1
        
        # 测试无效参数
        invalid_tests = [
            ('algorithm.breadth_calculation.min_stocks_threshold', 100),  # 超出最大值
            ('algorithm.breadth_calculation.data_quality_threshold', 1.5),  # 超出范围
            ('risk_management.position_limits.max_position', 30),  # 低于最小值
            ('risk_management.position_limits.max_sector_weight', 1.2)  # 超出范围
        ]
        
        print(f"\n📊 无效参数测试:")
        invalid_count = 0
        for path, value in invalid_tests:
            result = config_manager.set_config(path, value, f"测试无效设置 {path}")
            status = "❌" if not result else "⚠️"  # 应该失败
            print(f"  {path} = {value}: {status}")
            if not result:
                invalid_count += 1
        
        print(f"\n📈 验证结果:")
        print(f"有效参数通过: {valid_count}/{len(valid_tests)}")
        print(f"无效参数拒绝: {invalid_count}/{len(invalid_tests)}")
        
        return valid_count == len(valid_tests) and invalid_count == len(invalid_tests)


def test_market_regime_switching():
    """测试市场状态切换"""
    print("\n=== 测试市场状态切换 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = EnhancedConfigManager(config_dir=temp_dir)
        
        # 获取初始状态
        initial_regime = config_manager.current_market_regime
        print(f"初始市场状态: {initial_regime}")
        
        # 获取初始配置值
        initial_max_position = config_manager.get_config('risk_management.position_limits.max_position')
        print(f"初始最大仓位: {initial_max_position}%")
        
        # 测试切换到不同市场状态
        test_regimes = ['trending_stable', 'high_rotation', 'short_term_stress', 'regime_transition']
        
        switch_results = []
        for regime in test_regimes:
            print(f"\n--- 切换到 {regime} ---")
            
            success = config_manager.switch_market_regime(regime)
            switch_results.append(success)
            
            if success:
                current_regime = config_manager.current_market_regime
                current_max_position = config_manager.get_config('risk_management.position_limits.max_position')
                
                print(f"当前状态: {current_regime}")
                print(f"调整后最大仓位: {current_max_position}%")
                
                # 验证状态确实改变了
                if current_regime == regime:
                    print("✅ 状态切换成功")
                else:
                    print("❌ 状态切换失败")
            else:
                print("❌ 状态切换失败")
        
        # 测试切换到无效状态
        print(f"\n--- 测试无效状态切换 ---")
        invalid_switch = config_manager.switch_market_regime('invalid_regime')
        print(f"无效状态切换结果: {'❌ 正确拒绝' if not invalid_switch else '⚠️ 意外成功'}")
        
        # 切换回正常状态
        config_manager.switch_market_regime('normal_market')
        final_regime = config_manager.current_market_regime
        print(f"\n最终状态: {final_regime}")
        
        success_count = sum(switch_results)
        print(f"\n📈 切换结果: {success_count}/{len(test_regimes)} 成功")
        
        return success_count == len(test_regimes) and not invalid_switch


def test_version_control():
    """测试版本控制功能"""
    print("\n=== 测试版本控制功能 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = EnhancedConfigManager(
            config_dir=temp_dir,
            enable_versioning=True
        )
        
        # 获取初始版本数
        initial_versions = len(config_manager.get_version_history())
        print(f"初始版本数: {initial_versions}")
        
        # 进行一些配置更改
        changes = [
            ('algorithm.breadth_calculation.min_stocks_threshold', 12, "调整最小股票阈值"),
            ('risk_management.position_limits.max_position', 88, "调整最大仓位"),
            ('algorithm.coherence.direction_threshold', 0.002, "调整方向阈值")
        ]
        
        print(f"\n📝 执行配置更改:")
        for path, value, description in changes:
            success = config_manager.set_config(path, value, description, "test_user")
            print(f"  {description}: {'✅' if success else '❌'}")
        
        # 检查版本历史
        final_versions = config_manager.get_version_history()
        version_count = len(final_versions)
        print(f"\n📊 版本控制结果:")
        print(f"最终版本数: {version_count}")
        print(f"新增版本数: {version_count - initial_versions}")
        
        # 显示最近的版本
        if final_versions:
            print(f"\n📋 最近版本:")
            for version in final_versions[-3:]:  # 显示最近3个版本
                print(f"  {version.version}: {version.description} ({version.author})")
        
        # 测试市场状态切换的版本记录
        config_manager.switch_market_regime('high_rotation')
        regime_versions = config_manager.get_version_history()
        
        print(f"\n🔄 市场状态切换后版本数: {len(regime_versions)}")
        
        expected_new_versions = len(changes) + 1  # 配置更改 + 市场状态切换
        actual_new_versions = len(regime_versions) - initial_versions
        
        return actual_new_versions >= expected_new_versions


def test_config_persistence():
    """测试配置持久化"""
    print("\n=== 测试配置持久化 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 第一个配置管理器实例
        config_manager1 = EnhancedConfigManager(config_dir=temp_dir)
        
        # 设置一些配置
        test_configs = {
            'algorithm.breadth_calculation.min_stocks_threshold': 20,
            'algorithm.coherence.direction_threshold': 0.005,  # 使用不会被市场状态调整的配置
            'custom_test_value': 'test_persistence'
        }
        
        print(f"📝 设置测试配置:")
        for path, value in test_configs.items():
            config_manager1.set_config(path, value, "持久化测试")
            print(f"  {path} = {value}")
        
        # 切换市场状态
        config_manager1.switch_market_regime('trending_stable')
        print(f"切换市场状态到: trending_stable")
        
        # 创建第二个配置管理器实例（模拟重启）
        config_manager2 = EnhancedConfigManager(config_dir=temp_dir)
        
        print(f"\n📊 验证配置持久化:")
        persistence_success = True
        
        for path, expected_value in test_configs.items():
            actual_value = config_manager2.get_config(path)
            match = actual_value == expected_value
            status = "✅" if match else "❌"
            print(f"  {path}: 期望={expected_value}, 实际={actual_value} {status}")
            
            if not match:
                persistence_success = False
        
        # 验证市场状态持久化
        persisted_regime = config_manager2.current_market_regime
        regime_match = persisted_regime == 'trending_stable'
        print(f"  市场状态: 期望=trending_stable, 实际={persisted_regime} {'✅' if regime_match else '❌'}")
        
        if not regime_match:
            persistence_success = False
        
        # 验证版本历史持久化
        versions1 = config_manager1.get_version_history()
        versions2 = config_manager2.get_version_history()
        version_match = len(versions1) == len(versions2)
        print(f"  版本历史: 实例1={len(versions1)}个, 实例2={len(versions2)}个 {'✅' if version_match else '❌'}")
        
        if not version_match:
            persistence_success = False
        
        return persistence_success


def test_config_validation():
    """测试配置验证功能"""
    print("\n=== 测试配置验证功能 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = EnhancedConfigManager(config_dir=temp_dir)
        
        # 执行全面配置验证
        validation_results = config_manager.validate_all_configs()
        
        print(f"📊 配置验证结果:")
        print(f"错误数: {len(validation_results['errors'])}")
        print(f"警告数: {len(validation_results['warnings'])}")
        print(f"信息数: {len(validation_results['info'])}")
        
        if validation_results['errors']:
            print(f"\n❌ 错误:")
            for error in validation_results['errors']:
                print(f"  - {error}")
        
        if validation_results['warnings']:
            print(f"\n⚠️ 警告:")
            for warning in validation_results['warnings']:
                print(f"  - {warning}")
        
        # 故意破坏配置来测试验证
        print(f"\n🔧 测试配置验证检测:")
        
        # 设置无效的权重总和
        config_manager.configs['algorithm']['signal_strength']['components']['rii_strength']['weight'] = 0.8
        config_manager.configs['algorithm']['signal_strength']['components']['health_strength']['weight'] = 0.8
        
        validation_results_broken = config_manager.validate_all_configs()
        
        has_weight_warning = any('权重总和' in warning for warning in validation_results_broken['warnings'])
        print(f"权重总和验证: {'✅ 检测到异常' if has_weight_warning else '❌ 未检测到异常'}")
        
        # 验证是否检测到了问题
        total_issues = len(validation_results['errors']) + len(validation_results['warnings'])
        return total_issues == 0 or has_weight_warning  # 初始配置应该无问题，或者能检测到我们故意制造的问题


def test_export_import():
    """测试配置导出导入"""
    print("\n=== 测试配置导出导入 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建源配置管理器
        source_config = EnhancedConfigManager(config_dir=temp_dir)
        
        # 设置一些自定义配置
        source_config.set_config('test.export_value', 'exported_successfully')
        source_config.set_config('algorithm.breadth_calculation.min_stocks_threshold', 25)
        source_config.switch_market_regime('high_rotation')
        
        # 导出配置
        export_file = os.path.join(temp_dir, 'test_export.json')
        export_success = source_config.export_config(export_file, include_versions=True)
        print(f"配置导出: {'✅' if export_success else '❌'}")
        
        # 创建目标配置管理器
        target_dir = os.path.join(temp_dir, 'target')
        os.makedirs(target_dir, exist_ok=True)
        target_config = EnhancedConfigManager(config_dir=target_dir)
        
        # 导入配置
        import_success = target_config.import_config(export_file, merge_mode=True)
        print(f"配置导入: {'✅' if import_success else '❌'}")
        
        if import_success:
            # 验证导入的配置
            imported_value = target_config.get_config('test.export_value')
            imported_threshold = target_config.get_config('algorithm.breadth_calculation.min_stocks_threshold')
            imported_regime = target_config.current_market_regime
            
            print(f"\n📊 导入验证:")
            print(f"  自定义值: {imported_value} {'✅' if imported_value == 'exported_successfully' else '❌'}")
            print(f"  阈值: {imported_threshold} {'✅' if imported_threshold == 25 else '❌'}")
            print(f"  市场状态: {imported_regime} {'✅' if imported_regime == 'high_rotation' else '❌'}")
            
            return (export_success and import_success and 
                   imported_value == 'exported_successfully' and
                   imported_threshold == 25 and
                   imported_regime == 'high_rotation')
        
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T5.1 增强版配置管理器\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试初始化
        test_results.append(("配置管理器初始化", test_config_manager_initialization()))
        
        print("=" * 60)
        
        # 2. 测试参数验证
        test_results.append(("参数验证功能", test_parameter_validation()))
        
        print("=" * 60)
        
        # 3. 测试市场状态切换
        test_results.append(("市场状态切换", test_market_regime_switching()))
        
        print("=" * 60)
        
        # 4. 测试版本控制
        test_results.append(("版本控制功能", test_version_control()))
        
        print("=" * 60)
        
        # 5. 测试配置持久化
        test_results.append(("配置持久化", test_config_persistence()))
        
        print("=" * 60)
        
        # 6. 测试配置验证
        test_results.append(("配置验证功能", test_config_validation()))
        
        print("=" * 60)
        
        # 7. 测试导出导入
        test_results.append(("配置导出导入", test_export_import()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T5.1 配置管理器创建完成！")
            print("🚀 主要功能:")
            print("  - 集中化参数管理")
            print("  - 参数版本控制")
            print("  - 市场适应性调整")
            print("  - 配置验证机制")
            print("  - 配置持久化")
            print("  - 导出导入功能")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
