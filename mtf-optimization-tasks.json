{"project": "多时间框架板块轮动系统优化", "version": "4.2", "created_date": "2025-08-01", "total_tasks": 14, "phases": {"phase_1": {"name": "修复核心Bug", "priority": "高", "description": "修复现有算法的关键缺陷", "tasks": [{"id": "T001", "name": "创建SystemConfig配置管理类", "description": "实现集中化的系统配置管理，包含所有参数和阈值", "file_path": "core/system_config.py", "dependencies": [], "estimated_hours": 4, "status": "未完成", "priority": "高", "details": {"requirements": ["创建SystemConfig类", "定义PARAMS配置字典", "实现get_param()方法", "包含动量一致性、背离检测、风险管理参数"], "acceptance_criteria": ["所有硬编码参数移至配置文件", "支持嵌套参数访问", "参数验证机制"]}}, {"id": "T002", "name": "创建DecisionTracker决策追踪器", "description": "记录完整的决策过程，提供决策路径追踪", "file_path": "core/decision_tracker.py", "dependencies": ["T001"], "estimated_hours": 6, "status": "未完成", "priority": "高", "details": {"requirements": ["创建DecisionTracker类", "实现start_decision()方法", "实现add_step()和add_confidence_factor()方法", "实现get_decision_summary()方法"], "acceptance_criteria": ["能记录完整决策路径", "提供LLM友好的决策摘要", "支持置信度因素追踪"]}}, {"id": "T003", "name": "修复动量一致性计算算法", "description": "修复_calculate_enhanced_momentum_coherence()中均值接近0时的计算错误", "file_path": "core/hist_data.py", "dependencies": ["T001", "T002"], "estimated_hours": 8, "status": "未完成", "priority": "高", "details": {"requirements": ["创建ImprovedMomentumCoherence类", "使用四分位距法处理小均值情况", "实现7种细分的一致性类型", "增加动态权重计算", "集成DecisionTracker记录过程"], "acceptance_criteria": ["修复均值接近0的bug", "提供更详细的诊断信息", "一致性分类准确率>90%"]}}]}, "phase_2": {"name": "增强背离检测", "priority": "高", "description": "实现多维度背离检测机制", "tasks": [{"id": "T004", "name": "创建EnhancedDivergenceDetector类", "description": "实现专门的多维度背离检测模块", "file_path": "core/enhanced_divergence_detector.py", "dependencies": ["T001", "T002"], "estimated_hours": 10, "status": "未完成", "priority": "高", "details": {"requirements": ["创建EnhancedDivergenceDetector类", "实现多维度背离检测算法", "增加置信度评估机制", "实现风险分级功能", "集成到现有背离检测流程"], "acceptance_criteria": ["支持9个维度的背离检测", "提供置信度评分", "风险分级准确"]}}, {"id": "T005", "name": "优化analyze_sector_breadth()函数", "description": "在板块广度分析中集成新的背离检测功能", "file_path": "core/hist_data.py", "dependencies": ["T003", "T004"], "estimated_hours": 6, "status": "未完成", "priority": "中", "details": {"requirements": ["调用新的背离检测模块", "根据一致性类型调整健康度评分", "增加数据验证逻辑", "优化返回结果结构"], "acceptance_criteria": ["背离检测准确率提升30%", "健康度评分更准确", "数据验证覆盖率100%"]}}]}, "phase_3": {"name": "优化决策透明度", "priority": "中", "description": "提升决策过程的可解释性", "tasks": [{"id": "T006", "name": "优化_calculate_unified_rii()函数", "description": "分解RII计算步骤，增加中间变量说明", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T001", "T002"], "estimated_hours": 4, "status": "未完成", "priority": "中", "details": {"requirements": ["分解计算步骤", "增加中间变量说明", "返回各分量贡献度", "记录计算过程到DecisionTracker"], "acceptance_criteria": ["计算过程完全透明", "各分量贡献度清晰", "便于LLM理解"]}}, {"id": "T007", "name": "优化_identify_rotation_stage()函数", "description": "使用模糊逻辑改进轮动阶段判定", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T001", "T002"], "estimated_hours": 6, "status": "未完成", "priority": "中", "details": {"requirements": ["使用模糊逻辑改进判定", "增加阶段转换的过渡处理", "记录判定依据", "提供阶段概率分布"], "acceptance_criteria": ["阶段判定准确率提升20%", "过渡处理平滑", "判定依据清晰"]}}, {"id": "T008", "name": "优化_calculate_optimal_weights()函数", "description": "根据一致性类型调整权重分配逻辑", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T003"], "estimated_hours": 5, "status": "未完成", "priority": "中", "details": {"requirements": ["根据一致性类型调整权重上限", "增加最小分散度要求", "记录权重分配理由", "实现动态权重调整"], "acceptance_criteria": ["权重分配更合理", "风险分散度提升", "分配理由清晰"]}}, {"id": "T009", "name": "优化_calculate_signal_strength()函数", "description": "考虑更多维度的信号强度计算", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T001", "T002"], "estimated_hours": 4, "status": "未完成", "priority": "中", "details": {"requirements": ["考虑更多维度", "增加非线性映射", "返回各分量贡献", "集成决策追踪"], "acceptance_criteria": ["信号强度更准确", "各维度贡献清晰", "非线性映射合理"]}}]}, "phase_4": {"name": "完善LLM交互", "priority": "中", "description": "优化报告生成和LLM交互体验", "tasks": [{"id": "T010", "name": "优化_generate_unified_decision()函数", "description": "记录每个决策步骤，增加决策置信度细分", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T002", "T006", "T007", "T008"], "estimated_hours": 6, "status": "未完成", "priority": "中", "details": {"requirements": ["记录每个决策步骤", "增加决策置信度细分", "提供备选方案", "生成决策摘要"], "acceptance_criteria": ["决策过程完全透明", "置信度细分准确", "备选方案合理"]}}, {"id": "T011", "name": "优化_generate_avoid_list()函数", "description": "精细化避免板块的逻辑和建议", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T004", "T005"], "estimated_hours": 4, "status": "未完成", "priority": "低", "details": {"requirements": ["增加避免原因的权重", "分级避免建议", "提供改善条件", "集成背离检测结果"], "acceptance_criteria": ["避免建议更精准", "分级建议合理", "改善条件明确"]}}, {"id": "T012", "name": "优化generate_comprehensive_report()函数", "description": "增加决策路径章节和风险场景分析", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T002", "T010"], "estimated_hours": 8, "status": "未完成", "priority": "中", "details": {"requirements": ["增加决策路径章节", "添加风险场景分析", "提供交互式问答预设", "优化LLM上下文"], "acceptance_criteria": ["报告可解释性提升100%", "风险场景覆盖全面", "LLM交互体验优秀"]}}, {"id": "T013", "name": "优化_generate_executive_summary()函数", "description": "增加关键假设说明和置信度区间", "file_path": "core/multi_timeframe_analyzer.py", "dependencies": ["T002", "T010"], "estimated_hours": 4, "status": "未完成", "priority": "低", "details": {"requirements": ["增加关键假设说明", "添加置信度区间", "提供快速决策清单", "优化摘要结构"], "acceptance_criteria": ["摘要信息更丰富", "关键假设明确", "决策清单实用"]}}, {"id": "T014", "name": "优化calculate_market_breadth_summary()函数", "description": "增加板块分类统计和市场结构诊断", "file_path": "core/hist_data.py", "dependencies": ["T003", "T005"], "estimated_hours": 5, "status": "未完成", "priority": "低", "details": {"requirements": ["增加板块分类统计（强势、中性、弱势）", "添加市场结构诊断信息", "计算更多统计指标", "优化汇总逻辑"], "acceptance_criteria": ["板块分类准确", "市场诊断全面", "统计指标丰富"]}}]}}, "status_summary": {"未完成": 14, "进行中": 0, "已完成": 0}, "estimated_total_hours": 80, "success_metrics": {"signal_accuracy_improvement": "30-50%", "risk_identification_improvement": "60-80%", "decision_explainability_improvement": "100%+", "system_stability_improvement": "40-60%"}, "notes": ["建议按阶段顺序执行，确保依赖关系正确", "每个任务完成后需要进行单元测试", "Phase 1是基础，必须优先完成", "所有修改需要保持向后兼容性"]}