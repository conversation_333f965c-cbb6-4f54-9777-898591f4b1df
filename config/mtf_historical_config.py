#!/usr/bin/env python3
"""
多时间框架历史数据计算配置
针对不同时间框架的历史数据计算策略和参数
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any

# 多时间框架历史数据配置
MTF_HISTORICAL_CONFIG = {
    '5m': {
        'interval': '5min',
        'display_name': '5分钟',
        'history_strategy': 'recent_intensive',  # 最近密集型
        'max_history_days': 400,     # 增加到400天确保52周数据充足
        'min_data_points': 20280,    # 52周 × 5天 × 6.5小时 × 12个5分钟
        'batch_size': 10,            # 减小批次处理，避免内存问题
        'supports_ma': True,
        'supports_52w': True,        # 按领导要求：支持52周计算
        'supports_20d': True,        # 支持20日指标
        'supports_5d': True,         # 5日高低点
        'supports_1d': True,         # 1日高低点
        'ma_periods': [5, 10, 20, 50],  # 短期MA周期
        'priority': 3,               # 优先级（1最高）
        'update_frequency': 'hourly', # 每小时更新
        'storage_retention_days': 400  # 保留400天数据
    },
    
    '15m': {
        'interval': '15min',
        'display_name': '15分钟',
        'history_strategy': 'recent_intensive',
        'max_history_days': 400,     # 增加到400天确保52周数据
        'min_data_points': 6760,     # 52周 × 5天 × 6.5小时 × 4个15分钟
        'batch_size': 20,            # 减小批次处理
        'supports_ma': True,
        'supports_52w': True,        # 按领导要求：支持52周计算
        'supports_20d': True,
        'ma_periods': [5, 10, 20, 50, 200],
        'priority': 2,
        'update_frequency': 'hourly',
        'storage_retention_days': 400
    },
    
    '1h': {
        'interval': '1h',
        'display_name': '1小时',
        'history_strategy': 'balanced',  # 平衡型
        'max_history_days': 400,     # 增加到400天确保52周数据
        'min_data_points': 1690,     # 52周 × 5天 × 6.5小时
        'batch_size': 30,            # 适当减小批次
        'supports_ma': True,
        'supports_52w': True,        # 按领导要求：支持52周计算
        'supports_20d': True,
        'ma_periods': [5, 10, 20, 50, 200],
        'priority': 2,
        'update_frequency': 'daily',
        'storage_retention_days': 400
    },
    
    '1d': {
        'interval': '1d',
        'display_name': '日线',
        'history_strategy': 'comprehensive', # 全面型
        'max_history_days': 1000,    # 最多1000天历史
        'min_data_points': 252,      # 至少1年数据
        'batch_size': 100,
        'supports_ma': True,
        'supports_52w': True,        # 支持52周指标
        'supports_20d': True,
        'ma_periods': [5, 10, 20, 50, 200],
        'priority': 1,               # 最高优先级
        'update_frequency': 'daily',
        'storage_retention_days': 1825  # 保留5年
    },
    
    '1w': {
        'interval': '1w',
        'display_name': '周线',
        'history_strategy': 'comprehensive',
        'max_history_days': 2000,    # 最多2000天历史
        'min_data_points': 104,      # 至少2年数据
        'batch_size': 100,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'ma_periods': [5, 10, 20, 50, 200],
        'priority': 1,
        'update_frequency': 'weekly',
        'storage_retention_days': 3650  # 保留10年
    },
    
    '1M': {
        'interval': '1M',            # 注意：月度数据通过日线聚合生成
        'display_name': '月线',
        'history_strategy': 'comprehensive',
        'max_history_days': 3650,    # 最多10年历史
        'min_data_points': 24,       # 至少2年数据
        'batch_size': 100,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'ma_periods': [5, 10, 20, 50, 200],
        'priority': 1,
        'update_frequency': 'monthly',
        'storage_retention_days': 7300,  # 保留20年
        'data_source': 'aggregated_from_daily',  # 标明数据来源
        'aggregation_method': 'monthly_resample'  # 聚合方法
    }
}

# 历史数据计算策略
CALCULATION_STRATEGIES = {
    'recent_intensive': {
        'description': '最近密集型 - 重点计算最近数据',
        'history_weight': 0.3,      # 历史数据权重
        'recent_weight': 0.7,       # 最近数据权重
        'parallel_workers': 5,      # 并行工作数
        'memory_limit_mb': 1024,    # 内存限制
        'batch_processing': True,   # 批量处理
        'incremental_update': True  # 增量更新
    },
    
    'balanced': {
        'description': '平衡型 - 历史和最近数据并重',
        'history_weight': 0.5,
        'recent_weight': 0.5,
        'parallel_workers': 8,
        'memory_limit_mb': 2048,
        'batch_processing': True,
        'incremental_update': True
    },
    
    'comprehensive': {
        'description': '全面型 - 完整历史数据计算',
        'history_weight': 0.8,
        'recent_weight': 0.2,
        'parallel_workers': 10,
        'memory_limit_mb': 4096,
        'batch_processing': True,
        'incremental_update': True
    }
}

# 数据质量配置
DATA_QUALITY_CONFIG = {
    'min_trading_days_per_month': 15,  # 每月最少交易天数
    'max_missing_data_ratio': 0.1,    # 最大缺失数据比例
    'outlier_detection': True,         # 异常值检测
    'data_validation': True,           # 数据验证
    'auto_cleanup': True,              # 自动清理
    'backup_before_update': True       # 更新前备份
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'use_connection_pool': True,       # 使用连接池
    'connection_pool_size': 20,        # 连接池大小
    'query_timeout': 300,              # 查询超时（秒）
    'batch_insert_size': 1000,         # 批量插入大小
    'enable_compression': True,        # 启用压缩
    'use_prepared_statements': True,   # 使用预编译语句
    'parallel_symbol_processing': True, # 并行处理股票
    'memory_efficient_mode': True      # 内存高效模式
}

def get_mtf_config(timeframe: str) -> Dict[str, Any]:
    """获取指定时间框架的配置"""
    if timeframe not in MTF_HISTORICAL_CONFIG:
        raise ValueError(f"不支持的时间框架: {timeframe}")
    return MTF_HISTORICAL_CONFIG[timeframe].copy()

def get_calculation_strategy(strategy_name: str) -> Dict[str, Any]:
    """获取计算策略配置"""
    if strategy_name not in CALCULATION_STRATEGIES:
        raise ValueError(f"不支持的计算策略: {strategy_name}")
    return CALCULATION_STRATEGIES[strategy_name].copy()

def get_supported_timeframes() -> List[str]:
    """获取所有支持的时间框架"""
    return list(MTF_HISTORICAL_CONFIG.keys())

def get_timeframes_by_priority() -> List[str]:
    """按优先级排序获取时间框架"""
    timeframes = [(tf, config['priority']) for tf, config in MTF_HISTORICAL_CONFIG.items()]
    timeframes.sort(key=lambda x: x[1])  # 按优先级排序
    return [tf for tf, _ in timeframes]

def calculate_optimal_history_period(timeframe: str, target_months: int = 3) -> Dict[str, Any]:
    """
    计算最优历史数据周期
    
    Args:
        timeframe: 时间框架
        target_months: 目标月数
    
    Returns:
        包含计算结果的字典
    """
    config = get_mtf_config(timeframe)
    strategy = get_calculation_strategy(config['history_strategy'])
    
    # 基础计算
    max_days = min(config['max_history_days'], target_months * 30)
    min_points = config['min_data_points']
    
    # 根据时间框架调整
    if timeframe == '5m':
        # 5分钟：每天288个数据点
        required_days = max(7, min_points // 288)
    elif timeframe == '15m':
        # 15分钟：每天96个数据点
        required_days = max(14, min_points // 96)
    elif timeframe == '1h':
        # 1小时：每天24个数据点
        required_days = max(30, min_points // 24)
    elif timeframe == '1d':
        # 日线：每天1个数据点
        required_days = max(252, min_points)
    elif timeframe == '1w':
        # 周线：每周1个数据点
        required_days = max(104 * 7, min_points * 7)
    elif timeframe == '1M':
        # 月线：每月1个数据点
        required_days = max(24 * 30, min_points * 30)
    else:
        required_days = max_days
    
    # 最终天数
    optimal_days = min(max_days, max(required_days, target_months * 30))
    
    # 计算预期数据点数
    if timeframe == '5m':
        expected_points = optimal_days * 288
    elif timeframe == '15m':
        expected_points = optimal_days * 96
    elif timeframe == '1h':
        expected_points = optimal_days * 24
    elif timeframe == '1d':
        expected_points = optimal_days
    elif timeframe == '1w':
        expected_points = optimal_days // 7
    elif timeframe == '1M':
        expected_points = optimal_days // 30
    else:
        expected_points = optimal_days
    
    return {
        'timeframe': timeframe,
        'optimal_days': optimal_days,
        'expected_points': expected_points,
        'start_date': (datetime.now() - timedelta(days=optimal_days)).strftime('%Y-%m-%d'),
        'end_date': datetime.now().strftime('%Y-%m-%d'),
        'strategy': config['history_strategy'],
        'batch_size': config['batch_size'],
        'priority': config['priority']
    }

def get_incremental_update_config(timeframe: str) -> Dict[str, Any]:
    """获取增量更新配置"""
    config = get_mtf_config(timeframe)
    
    # 根据更新频率确定增量更新策略
    frequency_map = {
        'hourly': {'lookback_hours': 2, 'update_threshold': 3600},
        'daily': {'lookback_hours': 25, 'update_threshold': 86400},
        'weekly': {'lookback_hours': 168, 'update_threshold': 604800},
        'monthly': {'lookback_hours': 744, 'update_threshold': 2592000}
    }
    
    frequency_config = frequency_map.get(config['update_frequency'], frequency_map['daily'])
    
    return {
        'timeframe': timeframe,
        'update_frequency': config['update_frequency'],
        'lookback_hours': frequency_config['lookback_hours'],
        'update_threshold': frequency_config['update_threshold'],
        'batch_size': config['batch_size'],
        'parallel_workers': CALCULATION_STRATEGIES[config['history_strategy']]['parallel_workers']
    }

# 预设的历史数据计算方案
PRESET_CALCULATION_PLANS = {
    'quick_start': {
        'name': '快速启动',
        'description': '快速计算最近3个月数据，适合初次使用',
        'timeframes': ['1d', '1h'],
        'history_months': 3,
        'parallel_processing': True,
        'estimated_time_minutes': 30
    },
    
    'standard': {
        'name': '标准配置',
        'description': '计算所有时间框架的标准历史数据',
        'timeframes': ['5m', '15m', '1h', '1d', '1w', '1M'],
        'history_months': 6,
        'parallel_processing': True,
        'estimated_time_minutes': 120
    },
    
    'comprehensive': {
        'name': '全面配置',
        'description': '计算完整历史数据，适合生产环境',
        'timeframes': ['5m', '15m', '1h', '1d', '1w', '1M'],
        'history_months': 12,
        'parallel_processing': True,
        'estimated_time_minutes': 300
    },
    
    'intraday_focus': {
        'name': '日内重点',
        'description': '重点计算日内时间框架',
        'timeframes': ['5m', '15m', '1h'],
        'history_months': 3,
        'parallel_processing': True,
        'estimated_time_minutes': 60
    },
    
    'long_term_focus': {
        'name': '长期重点',
        'description': '重点计算长期时间框架',
        'timeframes': ['1d', '1w', '1M'],
        'history_months': 24,
        'parallel_processing': True,
        'estimated_time_minutes': 90
    }
}

def get_preset_plan(plan_name: str) -> Dict[str, Any]:
    """获取预设计算方案"""
    if plan_name not in PRESET_CALCULATION_PLANS:
        raise ValueError(f"不支持的计算方案: {plan_name}")
    return PRESET_CALCULATION_PLANS[plan_name].copy()

def list_preset_plans() -> List[Dict[str, Any]]:
    """列出所有预设方案"""
    return [
        {
            'name': name,
            'config': config
        }
        for name, config in PRESET_CALCULATION_PLANS.items()
    ]
