#!/usr/bin/env python3
"""
时间框架配置文件
统一管理所有时间框架的参数
"""

from datetime import timedelta

# 时间框架配置
TIMEFRAME_CONFIG = {
    '5m': {
        'interval': '5min',  # 修正：utils.py中使用'5min'
        'display_name': '5分钟',
        'history_days': 7,  # 获取7天的5分钟数据
        'min_data_points': 2,  # 至少需要2个数据点才能计算涨跌
        'supports_volume': True,
        'supports_52w_highs': False,  # 5分钟数据不支持52周新高新低
        'supports_ma': False,  # 5分钟数据不计算长期移动平均
        'batch_size': 100,  # 每批处理的股票数量
    },
    '15m': {
        'interval': '15min',  # 修正：utils.py中使用'15min'
        'display_name': '15分钟',
        'history_days': 14,  # 获取14天的15分钟数据
        'min_data_points': 2,
        'supports_volume': True,
        'supports_52w_highs': False,
        'supports_ma': False,
        'batch_size': 100,
    },
    '1h': {
        'interval': '1h',
        'display_name': '1小时',
        'history_days': 30,  # 获取30天的小时数据
        'min_data_points': 2,
        'supports_volume': True,
        'supports_52w_highs': False,
        'supports_ma': True,  # 小时数据可以计算短期移动平均
        'batch_size': 200,
    },
    '1d': {
        'interval': '1d',
        'display_name': '日度',
        'history_days': 365,  # 获取1年的日度数据
        'min_data_points': 2,
        'supports_volume': True,
        'supports_52w_highs': True,  # 日度数据支持52周新高新低
        'supports_ma': True,  # 日度数据支持所有移动平均
        'batch_size': 500,
    },
    '1w': {
        'interval': '1w',
        'display_name': '周度',
        'history_weeks': 12,  # 获取12周的周度数据
        'min_data_points': 2,
        'supports_volume': True,
        'supports_52w_highs': True,  # 从日度数据获取
        'supports_ma': True,  # 从日度数据获取
        'batch_size': 1000,
    },
    '1M': {
        'interval': '1M',
        'display_name': '月度',
        'history_months': 24,  # 获取24个月的月度数据
        'min_data_points': 2,
        'supports_volume': True,
        'supports_52w_highs': True,  # 从日度数据获取
        'supports_ma': True,  # 从日度数据获取
        'batch_size': 2000,
    }
}

# 数据库字段映射
COLUMN_MAPPING = {
    'high': 'h',
    'low': 'l', 
    'close': 'c',
    'volume': 'v'
}

# 反向映射（API返回的列名 -> 标准列名）
API_COLUMN_MAPPING = {
    'high': 'close',  # API返回的实际列名
    'low': 'low',
    'close': 'close', 
    'volume': 'volume'
}

def get_timeframe_config(timeframe: str) -> dict:
    """获取指定时间框架的配置"""
    if timeframe not in TIMEFRAME_CONFIG:
        raise ValueError(f"Unsupported timeframe: {timeframe}")
    return TIMEFRAME_CONFIG[timeframe].copy()

def get_supported_timeframes() -> list:
    """获取所有支持的时间框架"""
    return list(TIMEFRAME_CONFIG.keys())

def get_history_period(timeframe: str) -> dict:
    """获取历史数据获取周期"""
    config = get_timeframe_config(timeframe)
    
    if 'history_days' in config:
        return {
            'type': 'days',
            'value': config['history_days']
        }
    elif 'history_weeks' in config:
        return {
            'type': 'weeks', 
            'value': config['history_weeks']
        }
    elif 'history_months' in config:
        return {
            'type': 'months',
            'value': config['history_months']
        }
    else:
        return {
            'type': 'days',
            'value': 30  # 默认30天
        }

def should_calculate_field(timeframe: str, field: str) -> bool:
    """判断指定时间框架是否应该计算某个字段"""
    config = get_timeframe_config(timeframe)
    
    field_support_map = {
        'total_volume': config.get('supports_volume', True),
        'new_highs_52w': config.get('supports_52w_highs', False),
        'new_lows_52w': config.get('supports_52w_highs', False),
        'above_ma50': config.get('supports_ma', False),
        'above_ma200': config.get('supports_ma', False),
        'avg_rsi': True,  # RSI总是计算
    }
    
    return field_support_map.get(field, True)

def get_default_value(timeframe: str, field: str):
    """获取字段的默认值"""
    if not should_calculate_field(timeframe, field):
        if field in ['new_highs_52w', 'new_lows_52w', 'above_ma50', 'above_ma200']:
            return 0
        elif field == 'total_volume':
            return None
        elif field == 'avg_rsi':
            return 50.0
    
    return None  # 表示应该计算实际值
