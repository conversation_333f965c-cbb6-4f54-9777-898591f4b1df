#!/usr/bin/env python3
"""
价格数据采集器配置文件
包含所有价格获取、处理的配置参数和策略设置
"""

import os
import multiprocessing
from datetime import time as dt_time
from typing import Dict, Any

class PriceDataCollectorConfig:
    """价格数据采集器配置类"""
    
    # ============ 基础配置 ============
    QUEUE_SIZE = 400  # 维护400个数据点
    REDIS_TTL = 86400 * 30  # 30天TTL
    UPDATE_INTERVAL = 300  # 5分钟更新间隔（秒）

    # ============ 运行模式配置 ============
    class RunModeConfig:
        """运行模式配置"""
        # 运行模式选择
        # 'realtime' - 实时数据获取模式（默认）
        # 'historical_only' - 仅一次性更新历史数据
        # 'mixed' - 混合模式（先更新历史数据，然后启动实时更新）
        # 'disabled' - 禁用模式（仅初始化，不获取数据）
        RUN_MODE = 'realtime'

        # 是否在启动时强制更新历史数据（无论时间检查结果）
        FORCE_HISTORICAL_UPDATE_ON_STARTUP = False

        # 是否启用实时数据更新线程
        ENABLE_REALTIME_UPDATE = True

        # 是否在历史数据更新完成后自动退出（仅在historical_only模式下有效）
        EXIT_AFTER_HISTORICAL_UPDATE = True

        # 历史数据更新完成后的等待时间（秒），然后退出
        WAIT_BEFORE_EXIT = 5
    
    # ============ Redis配置 ============
    REDIS_CONFIG = {
        'host': '************',
        'port': 6379,
        'db': 15,  # 使用15库专门存储数据队列
        'decode_responses': True,
        'socket_timeout': 5,
        'socket_connect_timeout': 5
    }
    
    # ============ 美股交易时间配置 ============
    MARKET_OPEN_TIME = dt_time(9, 30)   # 9:30 AM ET
    MARKET_CLOSE_TIME = dt_time(16, 5)  # 4:05 PM ET (延后5分钟确保收盘数据不被跳过)
    HISTORICAL_UPDATE_TIME = dt_time(6, 30)  # UTC 6:30（历史数据更新时间）
    
    # ============ 线程和进程配置 ============
    class ThreadingConfig:
        """线程配置"""
        # 历史数据获取线程数
        HISTORICAL_DATA_THREADS = 20
        
        # 单个任务超时时间（秒）
        TASK_TIMEOUT = 30
        
        # 线程池关闭超时时间（秒）
        SHUTDOWN_TIMEOUT = 60
    
    class MultiprocessingConfig:
        """多进程配置"""
        # 最大进程数（不超过CPU核心数，且不超过8）
        MAX_PROCESSES = min(8, multiprocessing.cpu_count())
        
        # 启用多进程的最小股票数量阈值
        MIN_STOCKS_FOR_MULTIPROCESSING = 1000
        
        # 每个进程处理的最小批次大小
        MIN_BATCH_SIZE = 50
        
        # 进程间通信超时时间（秒）
        PROCESS_TIMEOUT = 120
        
        @classmethod
        def calculate_batch_size(cls, total_items: int, num_processes: int = None) -> int:
            """
            计算批次大小
            
            :param total_items: 总项目数
            :param num_processes: 进程数，None使用默认值
            :return: 批次大小
            """
            if num_processes is None:
                num_processes = cls.MAX_PROCESSES
            
            # 确保每个进程至少处理MIN_BATCH_SIZE个项目
            batch_size = max(cls.MIN_BATCH_SIZE, total_items // num_processes)
            return batch_size
        
        @classmethod
        def should_use_multiprocessing(cls, total_items: int) -> bool:
            """
            判断是否应该使用多进程
            
            :param total_items: 总项目数
            :return: 是否使用多进程
            """
            return total_items >= cls.MIN_STOCKS_FOR_MULTIPROCESSING
    
    # ============ 数据获取配置 ============
    class DataAcquisitionConfig:
        """数据获取配置"""
        # 历史数据起始日期
        HISTORICAL_START_DATE = '2024-01-01'
        
        # 获取历史数据的天数（回退方案）
        HISTORICAL_DAYS_FALLBACK = 280
        
        # 实时数据获取的最近天数
        REALTIME_RECENT_DAYS = 2
        
        # REST API重试次数
        REST_API_RETRY_COUNT = 3
        
        # REST API重试间隔（秒）
        REST_API_RETRY_INTERVAL = 1
        
        # 单次REST API请求超时（秒）
        REST_API_TIMEOUT = 10
    
    # ============ 数据处理策略配置 ============
    class DataProcessingConfig:
        """数据处理策略配置"""
        # 是否启用数据验证
        ENABLE_DATA_VALIDATION = True
        
        # 是否启用股票过滤（过滤不在ticker_to_secid_lookup中的股票）
        ENABLE_STOCK_FILTERING = True
        
        # 是否为冷门股票创建无成交数据
        ENABLE_NO_TRADING_DATA_CREATION = True
        
        # 是否在UTC 6:30后使用历史数据覆盖实时数据
        ENABLE_HISTORICAL_DATA_OVERRIDE = True
        
        # 进度条显示配置
        PROGRESS_BAR_CONFIG = {
            'desc_style': '🔄',  # 进度条描述符号
            'unit_scale': True,  # 是否自动缩放单位
            'miniters': 1,       # 最小更新间隔
            'disable': False     # 是否禁用进度条
        }
    
    # ============ 日志配置 ============
    class LoggingConfig:
        """日志配置"""
        # 日志级别
        LOG_LEVEL = 'INFO'
        
        # 日志文件名
        LOG_FILE = 'price_data_collector.log'
        
        # 是否记录详细的股票处理信息
        LOG_DETAILED_STOCK_INFO = False
        
        # 是否记录关键股票的详细信息
        KEY_STOCKS_FOR_DETAILED_LOG = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'ASLE']
        
        # 日志格式
        LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # ============ 信号和通知配置 ============
    class SignalConfig:
        """信号和通知配置"""
        # 信号TTL（秒）
        SIGNAL_TTL = 3600  # 1小时
        
        # 信号样例股票数量
        SIGNAL_SAMPLE_STOCKS_COUNT = 10
        
        # 更新信号发送的最小股票数量
        MIN_STOCKS_FOR_SIGNAL = 1
    
    # ============ 性能优化配置 ============
    class PerformanceConfig:
        """性能优化配置"""
        # Redis Pipeline批次大小
        REDIS_PIPELINE_BATCH_SIZE = 1000
        
        # 内存使用监控阈值（MB）
        MEMORY_THRESHOLD_MB = 1024
        
        # 是否启用内存监控
        ENABLE_MEMORY_MONITORING = True
        
        # 垃圾回收间隔（处理股票数）
        GC_INTERVAL = 500

# ============ 便捷访问方法 ============
def get_redis_config() -> Dict[str, Any]:
    """获取Redis配置"""
    return PriceDataCollectorConfig.REDIS_CONFIG.copy()

def get_threading_config() -> 'PriceDataCollectorConfig.ThreadingConfig':
    """获取线程配置"""
    return PriceDataCollectorConfig.ThreadingConfig

def get_multiprocessing_config() -> 'PriceDataCollectorConfig.MultiprocessingConfig':
    """获取多进程配置"""
    return PriceDataCollectorConfig.MultiprocessingConfig

def get_data_acquisition_config() -> 'PriceDataCollectorConfig.DataAcquisitionConfig':
    """获取数据获取配置"""
    return PriceDataCollectorConfig.DataAcquisitionConfig

def get_data_processing_config() -> 'PriceDataCollectorConfig.DataProcessingConfig':
    """获取数据处理配置"""
    return PriceDataCollectorConfig.DataProcessingConfig

def get_logging_config() -> 'PriceDataCollectorConfig.LoggingConfig':
    """获取日志配置"""
    return PriceDataCollectorConfig.LoggingConfig

def get_signal_config() -> 'PriceDataCollectorConfig.SignalConfig':
    """获取信号配置"""
    return PriceDataCollectorConfig.SignalConfig

def get_performance_config() -> 'PriceDataCollectorConfig.PerformanceConfig':
    """获取性能配置"""
    return PriceDataCollectorConfig.PerformanceConfig

def get_run_mode_config() -> 'PriceDataCollectorConfig.RunModeConfig':
    """获取运行模式配置"""
    return PriceDataCollectorConfig.RunModeConfig

# ============ 环境变量覆盖 ============
def load_config_from_env():
    """从环境变量加载配置（可选）"""
    # Redis配置覆盖
    if os.getenv('REDIS_HOST'):
        PriceDataCollectorConfig.REDIS_CONFIG['host'] = os.getenv('REDIS_HOST')
    if os.getenv('REDIS_PORT'):
        PriceDataCollectorConfig.REDIS_CONFIG['port'] = int(os.getenv('REDIS_PORT'))
    if os.getenv('REDIS_DB'):
        PriceDataCollectorConfig.REDIS_CONFIG['db'] = int(os.getenv('REDIS_DB'))
    
    # 线程数配置覆盖
    if os.getenv('HISTORICAL_DATA_THREADS'):
        PriceDataCollectorConfig.ThreadingConfig.HISTORICAL_DATA_THREADS = int(os.getenv('HISTORICAL_DATA_THREADS'))
    
    # 进程数配置覆盖
    if os.getenv('MAX_PROCESSES'):
        PriceDataCollectorConfig.MultiprocessingConfig.MAX_PROCESSES = min(
            int(os.getenv('MAX_PROCESSES')), 
            multiprocessing.cpu_count()
        )

# 自动加载环境变量配置
load_config_from_env()
