#!/usr/bin/env python3
"""
板块轮动调度器配置示例文件
展示如何自定义各种配置参数

使用方法：
1. 复制此文件为 sector_settings_custom.py
2. 修改其中的配置参数
3. 在 sector_rotation_scheduler.py 中导入自定义配置
"""

# ============ 配置示例 ============

# 示例1：修改历史数据天数和CPU使用
def get_custom_config_example1():
    """获取自定义配置示例1：更长的历史数据和更多CPU"""
    from sector_settings import SectorRotationConfig
    
    # 修改数据配置
    SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 60  # 使用60天历史数据
    SectorRotationConfig.DataConfig.DATA_SUFFICIENCY_RATIO = 0.3  # 降低数据充足性要求
    
    # 修改计算配置
    SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = 8  # 使用8个CPU核心
    SectorRotationConfig.CalculationConfig.MAX_CPU_USAGE_RATIO = 0.9  # 提高CPU使用率
    
    return SectorRotationConfig

# 示例2：修改信号配置和MTF设置
def get_custom_config_example2():
    """获取自定义配置示例2：自定义信号队列和MTF设置"""
    from sector_settings import SectorRotationConfig
    
    # 修改信号配置
    SectorRotationConfig.SignalConfig.INPUT_SIGNAL_QUEUE = "custom_market_signals"
    SectorRotationConfig.SignalConfig.OUTPUT_SIGNAL_QUEUE = "custom_rotation_signals"
    SectorRotationConfig.SignalConfig.SIGNAL_VALIDITY_SECONDS = 600  # 10分钟有效期
    
    # 修改MTF配置
    SectorRotationConfig.MTFConfig.MTF_ANALYSIS_DAYS = 45  # 使用45天MTF数据
    SectorRotationConfig.MTFConfig.MTF_USE_REAL_DATA_FIRST = False  # 优先使用模拟数据
    
    return SectorRotationConfig

# 示例3：修改权重和计算参数
def get_custom_config_example3():
    """获取自定义配置示例3：自定义权重和计算参数"""
    from sector_settings import SectorRotationConfig
    
    # 修改动量计算周期
    SectorRotationConfig.CalculationConfig.MOMENTUM_PERIODS = {
        'short': 3,   # 3日动量
        'medium': 7,  # 7日动量
        'long': 15    # 15日动量
    }
    
    # 修改综合得分权重
    SectorRotationConfig.CalculationConfig.COMPOSITE_SCORE_WEIGHTS = {
        'momentum_20d': 0.5,           # 提高动量权重
        'relative_strength': 0.3,     # 保持相对强度权重
        'rotation_intensity_index': 0.2 # 降低轮动强度权重
    }
    
    # 修改轮动强度指数权重
    SectorRotationConfig.CalculationConfig.RII_WEIGHTS = {
        'price_dispersion': 0.5,    # 提高价格离散度权重
        'velocity': 0.3,            # 降低轮动速度权重
        'volume_concentration': 0.2  # 保持成交量集中度权重
    }
    
    return SectorRotationConfig

# 示例4：启用文件日志和性能监控
def get_custom_config_example4():
    """获取自定义配置示例4：启用文件日志和性能监控"""
    from sector_settings import SectorRotationConfig
    
    # 修改日志配置
    SectorRotationConfig.LoggingConfig.ENABLE_FILE_LOGGING = True
    SectorRotationConfig.LoggingConfig.LOG_FILE_PATH = "logs/sector_rotation_custom.log"
    SectorRotationConfig.LoggingConfig.LOG_LEVEL = "DEBUG"  # 更详细的日志
    
    # 修改性能配置
    SectorRotationConfig.PerformanceConfig.ENABLE_MEMORY_MONITORING = True
    SectorRotationConfig.PerformanceConfig.MEMORY_THRESHOLD_MB = 2048  # 2GB内存阈值
    SectorRotationConfig.PerformanceConfig.GC_INTERVAL = 50  # 更频繁的垃圾回收
    
    return SectorRotationConfig

# 示例5：生产环境配置
def get_production_config():
    """获取生产环境配置：稳定性优先"""
    from sector_settings import SectorRotationConfig
    
    # 数据配置：更保守的设置
    SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 45
    SectorRotationConfig.DataConfig.DATA_SUFFICIENCY_RATIO = 0.7  # 更高的数据要求
    SectorRotationConfig.DataConfig.SINGLE_SECTOR_DATA_RATIO = 0.5
    
    # 计算配置：稳定性优先
    SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = 4
    SectorRotationConfig.CalculationConfig.MAX_CPU_USAGE_RATIO = 0.6  # 保守的CPU使用
    
    # 性能配置：启用监控
    SectorRotationConfig.PerformanceConfig.ENABLE_MEMORY_MONITORING = True
    SectorRotationConfig.PerformanceConfig.FALLBACK_TO_SINGLE_PROCESS = True
    
    # 日志配置：启用文件日志
    SectorRotationConfig.LoggingConfig.ENABLE_FILE_LOGGING = True
    SectorRotationConfig.LoggingConfig.LOG_LEVEL = "INFO"
    
    # MTF配置：保守设置
    SectorRotationConfig.MTFConfig.MTF_USE_REAL_DATA_FIRST = True
    SectorRotationConfig.MTFConfig.MTF_SAVE_TO_DATABASE = True
    
    return SectorRotationConfig

# 示例6：开发环境配置
def get_development_config():
    """获取开发环境配置：调试友好"""
    from sector_settings import SectorRotationConfig
    
    # 数据配置：快速测试
    SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 7  # 只用7天数据
    SectorRotationConfig.DataConfig.DATA_SUFFICIENCY_RATIO = 0.2  # 低数据要求
    
    # 计算配置：单进程调试
    SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = 1
    SectorRotationConfig.PerformanceConfig.ENABLE_MULTIPROCESSING = False
    
    # 日志配置：详细日志
    SectorRotationConfig.LoggingConfig.LOG_LEVEL = "DEBUG"
    SectorRotationConfig.LoggingConfig.ENABLE_FILE_LOGGING = True
    SectorRotationConfig.LoggingConfig.LOG_FILE_PATH = "logs/sector_rotation_dev.log"
    
    # 运行模式：显示更多排名
    SectorRotationConfig.RunModeConfig.STANDALONE_TOP_SECTORS_COUNT = 10
    
    # MTF配置：快速测试
    SectorRotationConfig.MTFConfig.MTF_ANALYSIS_DAYS = 7
    SectorRotationConfig.MTFConfig.MTF_USE_REAL_DATA_FIRST = False  # 使用模拟数据更快
    
    return SectorRotationConfig

# ============ 环境变量配置示例 ============

def setup_environment_variables():
    """设置环境变量的示例"""
    import os
    
    # 设置环境变量来覆盖配置
    os.environ['SECTOR_HISTORICAL_DAYS'] = '45'
    os.environ['SECTOR_CPU_COUNTS'] = '6'
    os.environ['SECTOR_LOG_LEVEL'] = 'DEBUG'
    os.environ['SECTOR_ENABLE_MTF'] = 'true'
    os.environ['SECTOR_INPUT_QUEUE'] = 'custom_breadth_signals'
    os.environ['SECTOR_OUTPUT_QUEUE'] = 'custom_rotation_signals'

# ============ 使用说明 ============

if __name__ == "__main__":
    print("板块轮动调度器配置示例")
    print("=" * 50)
    print("1. 复制此文件为 sector_settings_custom.py")
    print("2. 选择一个配置示例函数")
    print("3. 在主程序中调用配置函数")
    print("4. 或者设置环境变量来覆盖配置")
    print()
    print("可用的配置示例：")
    print("- get_custom_config_example1(): 更长历史数据和更多CPU")
    print("- get_custom_config_example2(): 自定义信号队列和MTF设置")
    print("- get_custom_config_example3(): 自定义权重和计算参数")
    print("- get_custom_config_example4(): 启用文件日志和性能监控")
    print("- get_production_config(): 生产环境配置")
    print("- get_development_config(): 开发环境配置")
    print()
    print("环境变量示例：")
    print("- SECTOR_HISTORICAL_DAYS: 历史数据天数")
    print("- SECTOR_CPU_COUNTS: CPU核心数")
    print("- SECTOR_LOG_LEVEL: 日志级别")
    print("- SECTOR_ENABLE_MTF: 是否启用MTF分析")
    print("- SECTOR_INPUT_QUEUE: 输入信号队列名")
    print("- SECTOR_OUTPUT_QUEUE: 输出信号队列名")
