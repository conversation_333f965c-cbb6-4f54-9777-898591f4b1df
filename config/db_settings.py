#!/usr/bin/env python3
"""
数据库配置文件
集中管理所有数据库连接配置，替代环境变量方式
"""

# ============ MySQL数据库配置 ============

# 默认数据库配置 (trend_asset_screener)
DEFAULT_DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'zhuxun',
    'password': '123456',
    'database': 'trend_asset_screener',
    'charset': 'utf8mb4'
}

# ALGO数据库配置 (市值数据等)
ALGO_DB_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'algoa01',
    'password': 'Xjmn6D54',
    'database': 'ALGO_A',
    'charset': 'utf8mb4'
}

# Security Master数据库配置 (vhcid映射等)
SECURITY_DB_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'algoa01',
    'password': 'Xjmn6D54',
    'database': 'security_master',
    'charset': 'utf8mb4'
}

# 价格数据过滤数据库配置
PRICE_FILTER_DB_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'test01',
    'password': 'test01',
    'database': 'test01',
    'charset': 'utf8mb4'
}

# ============ ClickHouse数据库配置 ============

CLICKHOUSE_CONFIG = {
    'host': '************',
    'port': 9000,
    'user': 'default',
    'password': 'Dx784vs7',
    'database': 'test'
}

# ============ Redis配置 ============

REDIS_CONFIG = {
    'host': '************',
    'port': 6379,
    'db': 15,
    'decode_responses': True
}

# ============ 配置获取函数 ============

def get_default_db_config():
    """获取默认数据库配置"""
    return DEFAULT_DB_CONFIG.copy()

def get_algo_db_config():
    """获取ALGO数据库配置"""
    return ALGO_DB_CONFIG.copy()

def get_security_db_config():
    """获取Security Master数据库配置"""
    return SECURITY_DB_CONFIG.copy()

def get_price_filter_db_config():
    """获取价格数据过滤数据库配置"""
    return PRICE_FILTER_DB_CONFIG.copy()

def get_clickhouse_config():
    """获取ClickHouse配置"""
    return CLICKHOUSE_CONFIG.copy()

def get_redis_config():
    """获取Redis配置"""
    return REDIS_CONFIG.copy()

# ============ 兼容性函数 ============

def get_db_config_by_name(db_name: str):
    """
    根据数据库名称获取配置

    :param db_name: 数据库名称 ('default', 'algo', 'security', 'price_filter')
    :return: 数据库配置字典
    """
    config_map = {
        'default': get_default_db_config,
        'algo': get_algo_db_config,
        'security': get_security_db_config,
        'price_filter': get_price_filter_db_config
    }

    if db_name not in config_map:
        raise ValueError(f"未知的数据库名称: {db_name}")

    return config_map[db_name]()

# ============ 环境变量兼容模式 ============

def load_from_env_if_available():
    """
    如果环境变量存在，则覆盖配置（向后兼容）
    """
    import os

    # 覆盖默认数据库配置
    if os.getenv('DEFAULT_DB_HOST'):
        DEFAULT_DB_CONFIG['host'] = os.getenv('DEFAULT_DB_HOST')
    if os.getenv('DEFAULT_DB_PORT'):
        DEFAULT_DB_CONFIG['port'] = int(os.getenv('DEFAULT_DB_PORT'))
    if os.getenv('DEFAULT_DB_USER'):
        DEFAULT_DB_CONFIG['user'] = os.getenv('DEFAULT_DB_USER')
    if os.getenv('DEFAULT_DB_PASSWORD'):
        DEFAULT_DB_CONFIG['password'] = os.getenv('DEFAULT_DB_PASSWORD')
    if os.getenv('DEFAULT_DB_NAME'):
        DEFAULT_DB_CONFIG['database'] = os.getenv('DEFAULT_DB_NAME')

    # 覆盖ALGO数据库配置
    if os.getenv('ALGO_DB_HOST'):
        ALGO_DB_CONFIG['host'] = os.getenv('ALGO_DB_HOST')
    if os.getenv('ALGO_DB_PORT'):
        ALGO_DB_CONFIG['port'] = int(os.getenv('ALGO_DB_PORT'))
    if os.getenv('ALGO_DB_USER'):
        ALGO_DB_CONFIG['user'] = os.getenv('ALGO_DB_USER')
    if os.getenv('ALGO_DB_PASSWORD'):
        ALGO_DB_CONFIG['password'] = os.getenv('ALGO_DB_PASSWORD')
    if os.getenv('ALGO_DB_NAME'):
        ALGO_DB_CONFIG['database'] = os.getenv('ALGO_DB_NAME')

    # 覆盖Security数据库配置
    if os.getenv('SECURITY_DB_HOST'):
        SECURITY_DB_CONFIG['host'] = os.getenv('SECURITY_DB_HOST')
    if os.getenv('SECURITY_DB_PORT'):
        SECURITY_DB_CONFIG['port'] = int(os.getenv('SECURITY_DB_PORT'))
    if os.getenv('SECURITY_DB_USER'):
        SECURITY_DB_CONFIG['user'] = os.getenv('SECURITY_DB_USER')
    if os.getenv('SECURITY_DB_PASSWORD'):
        SECURITY_DB_CONFIG['password'] = os.getenv('SECURITY_DB_PASSWORD')
    if os.getenv('SECURITY_DB_NAME'):
        SECURITY_DB_CONFIG['database'] = os.getenv('SECURITY_DB_NAME')

    # 覆盖Redis配置
    if os.getenv('REDIS_HOST'):
        REDIS_CONFIG['host'] = os.getenv('REDIS_HOST')
    if os.getenv('REDIS_PORT'):
        REDIS_CONFIG['port'] = int(os.getenv('REDIS_PORT'))
    if os.getenv('REDIS_DB'):
        REDIS_CONFIG['db'] = int(os.getenv('REDIS_DB'))

# ============ 市宽指标专用配置 ============

# 市宽指标历史数据存储配置（通常与默认数据库相同）
BREADTH_HISTORY_DB_CONFIG = DEFAULT_DB_CONFIG.copy()

# 市宽指标实时数据Redis配置（通常与默认Redis相同，但可以单独配置）
BREADTH_REDIS_CONFIG = REDIS_CONFIG.copy()
BREADTH_REDIS_CONFIG.update({
    'socket_timeout': 5,
    'socket_connect_timeout': 5
})

# ============ 扩展配置获取函数 ============

def get_breadth_history_db_config():
    """获取市宽指标历史数据数据库配置"""
    return BREADTH_HISTORY_DB_CONFIG.copy()

def get_breadth_redis_config():
    """获取市宽指标Redis配置（包含超时设置）"""
    return BREADTH_REDIS_CONFIG.copy()

# ============ 统一配置获取函数 ============

def get_all_db_configs():
    """获取所有数据库配置的字典"""
    return {
        'default': get_default_db_config(),
        'algo': get_algo_db_config(),
        'security': get_security_db_config(),
        'price_filter': get_price_filter_db_config(),
        'breadth_history': get_breadth_history_db_config(),
        'redis': get_redis_config(),
        'breadth_redis': get_breadth_redis_config(),
        'clickhouse': get_clickhouse_config()
    }

def print_all_configs():
    """打印所有配置（用于调试）"""
    configs = get_all_db_configs()
    for name, config in configs.items():
        print(f"{name}: {config}")

# 自动加载环境变量（如果存在）
load_from_env_if_available()