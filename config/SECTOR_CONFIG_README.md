# 板块轮动调度器配置系统

## 概述

板块轮动调度器现在支持完全可配置的参数系统，所有硬编码的参数都已提取到配置文件中，支持灵活的自定义和环境变量覆盖。

## 配置文件结构

### 主配置文件：`sector_settings.py`

包含以下配置类：

- **DataConfig**: 数据获取相关配置
- **CalculationConfig**: 计算参数配置
- **SignalConfig**: 信号队列配置
- **LoggingConfig**: 日志配置
- **MTFConfig**: MTF分析配置
- **PerformanceConfig**: 性能优化配置
- **RunModeConfig**: 运行模式配置

## 主要配置参数

### 📊 数据配置 (DataConfig)

```python
HISTORICAL_DAYS = 30                    # 历史数据天数
DATA_SUFFICIENCY_RATIO = 0.5           # 数据充足性阈值
SINGLE_SECTOR_DATA_RATIO = 0.3         # 单板块数据阈值
SECTOR_INDICES_TABLE = "sector_indices_gics"        # 板块指数表名
ROTATION_METRICS_TABLE = "sector_rotation_metrics_gics"  # 轮动指标表名
MIN_DATA_POINTS_FOR_CALCULATION = 3    # 最小数据点要求
```

### 🧮 计算配置 (CalculationConfig)

```python
DEFAULT_CPU_COUNTS = 4                  # 默认CPU核心数
MAX_CPU_USAGE_RATIO = 0.8              # 最大CPU使用率

# 动量计算周期
MOMENTUM_PERIODS = {
    'short': 5,   # 5日动量
    'medium': 10, # 10日动量
    'long': 20    # 20日动量
}

# 综合得分权重
COMPOSITE_SCORE_WEIGHTS = {
    'momentum_20d': 0.4,           # 20日动量权重
    'relative_strength': 0.3,     # 相对强度权重
    'rotation_intensity_index': 0.3 # 轮动强度指数权重
}
```

### 📡 信号配置 (SignalConfig)

```python
INPUT_SIGNAL_QUEUE = "market_breadth_signals"   # 输入信号队列
OUTPUT_SIGNAL_QUEUE = "sector_rotation_signals" # 输出信号队列
SIGNAL_VALIDITY_SECONDS = 300          # 信号有效期（秒）
SIGNAL_LISTEN_TIMEOUT = 5              # 信号监听超时
REDIS_RECONNECT_WAIT = 10              # Redis重连等待时间
```

### 📝 日志配置 (LoggingConfig)

```python
LOG_LEVEL = "INFO"                     # 日志级别
LOGGER_NAME = 'sector_rotation_scheduler'  # 日志记录器名称
ENABLE_FILE_LOGGING = False            # 是否启用文件日志
LOG_FILE_PATH = "logs/sector_rotation.log"  # 日志文件路径
```

### 🔍 MTF配置 (MTFConfig)

```python
ENABLE_MTF_ANALYSIS = True             # 是否启用MTF分析
MTF_ANALYSIS_DAYS = 30                 # MTF分析数据天数
MTF_USE_REAL_DATA_FIRST = True         # 是否优先使用真实数据
MTF_SAVE_TO_DATABASE = True            # MTF结果保存到数据库
```

## 使用方法

### 1. 基本使用

直接运行脚本，使用默认配置：

```bash
# 查看当前配置
python core/sector_rotation_scheduler.py --config

# 独立运行模式
python core/sector_rotation_scheduler.py --standalone

# 单次计算模式
python core/sector_rotation_scheduler.py --once

# 监听模式（默认）
python core/sector_rotation_scheduler.py
```

### 2. 环境变量覆盖

通过环境变量覆盖配置：

```bash
# Windows
set SECTOR_HISTORICAL_DAYS=60
set SECTOR_CPU_COUNTS=8
set SECTOR_LOG_LEVEL=DEBUG
python core/sector_rotation_scheduler.py --standalone

# Linux/Mac
export SECTOR_HISTORICAL_DAYS=60
export SECTOR_CPU_COUNTS=8
export SECTOR_LOG_LEVEL=DEBUG
python core/sector_rotation_scheduler.py --standalone
```

支持的环境变量：
- `SECTOR_HISTORICAL_DAYS`: 历史数据天数
- `SECTOR_CPU_COUNTS`: CPU核心数
- `SECTOR_INPUT_QUEUE`: 输入信号队列名
- `SECTOR_OUTPUT_QUEUE`: 输出信号队列名
- `SECTOR_LOG_LEVEL`: 日志级别
- `SECTOR_ENABLE_MTF`: 是否启用MTF分析

### 3. 自定义配置

创建自定义配置文件：

```python
# config/sector_settings_custom.py
from sector_settings import SectorRotationConfig

# 修改配置
SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 60
SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = 8
SectorRotationConfig.LoggingConfig.ENABLE_FILE_LOGGING = True

# 在主程序中导入自定义配置
```

## 配置示例

### 生产环境配置

```python
def get_production_config():
    from sector_settings import SectorRotationConfig
    
    # 稳定性优先
    SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 45
    SectorRotationConfig.DataConfig.DATA_SUFFICIENCY_RATIO = 0.7
    SectorRotationConfig.CalculationConfig.MAX_CPU_USAGE_RATIO = 0.6
    SectorRotationConfig.LoggingConfig.ENABLE_FILE_LOGGING = True
    SectorRotationConfig.PerformanceConfig.ENABLE_MEMORY_MONITORING = True
    
    return SectorRotationConfig
```

### 开发环境配置

```python
def get_development_config():
    from sector_settings import SectorRotationConfig
    
    # 快速调试
    SectorRotationConfig.DataConfig.HISTORICAL_DAYS = 7
    SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = 1
    SectorRotationConfig.PerformanceConfig.ENABLE_MULTIPROCESSING = False
    SectorRotationConfig.LoggingConfig.LOG_LEVEL = "DEBUG"
    
    return SectorRotationConfig
```

## 配置验证

系统会自动验证配置的有效性：

- CPU数量不超过系统最大值
- 历史数据天数大于0
- 权重总和接近1.0
- 必要的目录存在

## 最佳实践

1. **生产环境**：使用保守的CPU使用率和内存监控
2. **开发环境**：使用较少的历史数据和详细日志
3. **测试环境**：禁用多进程以便调试
4. **高性能环境**：增加CPU核心数和历史数据天数

## 故障排除

### 常见问题

1. **配置验证失败**：检查参数范围和类型
2. **内存不足**：降低历史数据天数或CPU核心数
3. **日志文件创建失败**：检查目录权限
4. **MTF分析失败**：检查MTF相关配置

### 调试技巧

```bash
# 查看详细配置
python core/sector_rotation_scheduler.py --config

# 启用调试日志
set SECTOR_LOG_LEVEL=DEBUG
python core/sector_rotation_scheduler.py --standalone

# 单进程调试
set SECTOR_CPU_COUNTS=1
python core/sector_rotation_scheduler.py --standalone
```

## 更新日志

- **v1.0**: 初始配置系统
- **v1.1**: 添加环境变量支持
- **v1.2**: 添加配置验证和示例
