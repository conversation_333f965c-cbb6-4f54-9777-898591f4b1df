#!/usr/bin/env python3
"""
高性能多时间框架配置 - 针对64核心64G服务器优化
"""

import multiprocessing
from typing import Dict, Any, List

# 服务器硬件配置
SERVER_CONFIG = {
    'cpu_cores': 64,
    'memory_gb': 64,
    'max_parallel_processes': 48,  # 保留16核心给系统
    'max_parallel_threads': 32,    # 每个进程内的线程数
    'memory_per_process_mb': 1024, # 每个进程最大内存
    'database_connections': 200    # 数据库连接池大小
}

# 高性能时间框架配置
HIGH_PERFORMANCE_MTF_CONFIG = {
    '5m': {
        'interval': '5min',
        'display_name': '5分钟',
        'history_strategy': 'recent_intensive',
        'max_history_days': 400,
        'min_data_points': 20280,
        'batch_size': 200,           # 大幅增加批次大小
        'parallel_processes': 16,    # 多进程并行
        'threads_per_process': 8,    # 每进程线程数
        'memory_limit_mb': 2048,     # 增加内存限制
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 3,
        'update_frequency': 'hourly',
        'storage_retention_days': 400
    },
    
    '15m': {
        'interval': '15min',
        'display_name': '15分钟',
        'history_strategy': 'recent_intensive',
        'max_history_days': 400,
        'min_data_points': 6760,
        'batch_size': 300,           # 增加批次大小
        'parallel_processes': 12,    # 多进程并行
        'threads_per_process': 10,
        'memory_limit_mb': 2048,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 2,
        'update_frequency': 'hourly',
        'storage_retention_days': 400
    },
    
    '1h': {
        'interval': '1h',
        'display_name': '1小时',
        'history_strategy': 'balanced',
        'max_history_days': 400,
        'min_data_points': 1690,
        'batch_size': 500,           # 大批次处理
        'parallel_processes': 10,
        'threads_per_process': 12,
        'memory_limit_mb': 2048,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 2,
        'update_frequency': 'daily',
        'storage_retention_days': 400
    },
    
    '1d': {
        'interval': '1d',
        'display_name': '日线',
        'history_strategy': 'comprehensive',
        'max_history_days': 1000,
        'min_data_points': 252,
        'batch_size': 1000,          # 最大批次
        'parallel_processes': 8,
        'threads_per_process': 16,
        'memory_limit_mb': 4096,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 1,
        'update_frequency': 'daily',
        'storage_retention_days': 2000
    },
    
    '1w': {
        'interval': '1w',
        'display_name': '周线',
        'history_strategy': 'comprehensive',
        'max_history_days': 2000,
        'min_data_points': 104,
        'batch_size': 1000,
        'parallel_processes': 6,
        'threads_per_process': 16,
        'memory_limit_mb': 4096,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 1,
        'update_frequency': 'weekly',
        'storage_retention_days': 3650
    },
    
    '1M': {
        'interval': '1M',
        'display_name': '月线',
        'history_strategy': 'comprehensive',
        'max_history_days': 3650,
        'min_data_points': 24,
        'batch_size': 1000,
        'parallel_processes': 4,
        'threads_per_process': 16,
        'memory_limit_mb': 4096,
        'supports_ma': True,
        'supports_52w': True,
        'supports_20d': True,
        'priority': 1,
        'update_frequency': 'monthly',
        'storage_retention_days': 7300,
        'data_source': 'aggregated_from_daily',
        'aggregation_method': 'monthly_resample'
    }
}

# 高性能计算策略
HIGH_PERFORMANCE_STRATEGIES = {
    'recent_intensive': {
        'description': '最近密集型 - 高性能版',
        'history_weight': 0.3,
        'recent_weight': 0.7,
        'parallel_workers': 16,      # 大幅增加并行数
        'memory_limit_mb': 8192,     # 增加内存限制
        'batch_processing': True,
        'incremental_update': True,
        'use_multiprocessing': True, # 启用多进程
        'process_pool_size': 16
    },
    
    'balanced': {
        'description': '平衡型 - 高性能版',
        'history_weight': 0.5,
        'recent_weight': 0.5,
        'parallel_workers': 20,
        'memory_limit_mb': 12288,
        'batch_processing': True,
        'incremental_update': True,
        'use_multiprocessing': True,
        'process_pool_size': 20
    },
    
    'comprehensive': {
        'description': '全面型 - 高性能版',
        'history_weight': 0.8,
        'recent_weight': 0.2,
        'parallel_workers': 24,
        'memory_limit_mb': 16384,
        'batch_processing': True,
        'incremental_update': True,
        'use_multiprocessing': True,
        'process_pool_size': 24
    }
}

# 高性能数据库配置
HIGH_PERFORMANCE_DB_CONFIG = {
    'connection_pool_size': 200,        # 大幅增加连接池
    'query_timeout': 600,               # 增加超时时间
    'batch_insert_size': 5000,          # 大批量插入
    'enable_compression': True,
    'use_prepared_statements': True,
    'parallel_symbol_processing': True,
    'memory_efficient_mode': False,     # 关闭内存高效模式，使用高性能模式
    'enable_query_cache': True,         # 启用查询缓存
    'max_connections_per_process': 8,   # 每进程最大连接数
    'connection_timeout': 30,
    'read_timeout': 300,
    'write_timeout': 300
}

# 高性能预设方案
HIGH_PERFORMANCE_PLANS = {
    'ultra_fast': {
        'name': '极速计算',
        'description': '利用全部64核心进行极速计算',
        'timeframes': ['5m', '15m', '1h', '1d', '1w', '1M'],
        'history_months': 3,
        'parallel_processing': True,
        'use_multiprocessing': True,
        'max_processes': 48,
        'estimated_time_minutes': 15,  # 大幅缩短时间
        'memory_usage_gb': 32
    },
    
    'full_power': {
        'name': '全功率计算',
        'description': '计算全部时间框架12个月历史数据',
        'timeframes': ['5m', '15m', '1h', '1d', '1w', '1M'],
        'history_months': 12,
        'parallel_processing': True,
        'use_multiprocessing': True,
        'max_processes': 48,
        'estimated_time_minutes': 60,  # 大幅缩短时间
        'memory_usage_gb': 48
    },
    
    'maximum_performance': {
        'name': '最大性能',
        'description': '18个月完整历史数据，高性能多进程计算，60天存储',
        'timeframes': ['5m', '15m', '1h', '1d', '1w', '1M'],
        'history_months': 18,
        'parallel_processing': True,   # 恢复多进程
        'use_multiprocessing': True,   # 启用多进程
        'max_processes': 48,           # 48个进程
        'estimated_time_minutes': 100, # 高性能计算
        'memory_usage_gb': 50
    }
}

def get_high_performance_config(timeframe: str) -> Dict[str, Any]:
    """获取高性能时间框架配置"""
    return HIGH_PERFORMANCE_MTF_CONFIG.get(timeframe, {})

def get_optimal_process_count(timeframe: str, total_symbols: int) -> int:
    """根据时间框架和股票数量计算最优进程数"""
    config = get_high_performance_config(timeframe)
    max_processes = config.get('parallel_processes', 8)
    
    # 根据股票数量调整进程数
    if total_symbols < 1000:
        return min(max_processes, 8)
    elif total_symbols < 3000:
        return min(max_processes, 16)
    elif total_symbols < 6000:
        return min(max_processes, 24)
    else:
        return max_processes

def get_optimal_batch_size(timeframe: str, available_memory_gb: int) -> int:
    """根据可用内存计算最优批次大小"""
    config = get_high_performance_config(timeframe)
    base_batch_size = config.get('batch_size', 100)
    
    # 根据可用内存调整批次大小
    memory_factor = min(available_memory_gb / 16, 4.0)  # 最大4倍
    optimal_batch_size = int(base_batch_size * memory_factor)
    
    return optimal_batch_size

def estimate_computation_time(plan_name: str, total_symbols: int) -> Dict[str, Any]:
    """估算计算时间"""
    plan = HIGH_PERFORMANCE_PLANS.get(plan_name, {})
    base_time = plan.get('estimated_time_minutes', 60)
    
    # 根据股票数量调整时间估算
    if total_symbols <= 1000:
        time_factor = 0.2
    elif total_symbols <= 3000:
        time_factor = 0.5
    elif total_symbols <= 6000:
        time_factor = 1.0
    else:
        time_factor = 1.5
    
    estimated_time = int(base_time * time_factor)
    
    return {
        'plan_name': plan_name,
        'total_symbols': total_symbols,
        'estimated_minutes': estimated_time,
        'estimated_hours': estimated_time / 60,
        'timeframes': plan.get('timeframes', []),
        'history_months': plan.get('history_months', 3),
        'max_processes': plan.get('max_processes', 24),
        'memory_usage_gb': plan.get('memory_usage_gb', 16)
    }

def get_system_resource_status() -> Dict[str, Any]:
    """获取系统资源状态"""
    import psutil
    
    return {
        'cpu_count': multiprocessing.cpu_count(),
        'cpu_usage_percent': psutil.cpu_percent(interval=1),
        'memory_total_gb': psutil.virtual_memory().total / (1024**3),
        'memory_available_gb': psutil.virtual_memory().available / (1024**3),
        'memory_usage_percent': psutil.virtual_memory().percent,
        'recommended_max_processes': min(48, multiprocessing.cpu_count() - 4),
        'recommended_memory_per_process_mb': int((psutil.virtual_memory().available / (1024**2)) / 48)
    }

# 导出主要配置
__all__ = [
    'HIGH_PERFORMANCE_MTF_CONFIG',
    'HIGH_PERFORMANCE_STRATEGIES', 
    'HIGH_PERFORMANCE_DB_CONFIG',
    'HIGH_PERFORMANCE_PLANS',
    'get_high_performance_config',
    'get_optimal_process_count',
    'get_optimal_batch_size',
    'estimate_computation_time',
    'get_system_resource_status'
]
