#!/usr/bin/env python3
"""
市宽计算器运行模式配置示例
展示如何配置不同的运行模式
"""

# ============ 运行模式配置示例 ============

# 示例1: 实时数据计算模式（默认）
REALTIME_MODE_CONFIG = {
    'RUN_MODE': 'realtime',
    'HISTORICAL_DAYS': 90,
    'FORCE_HISTORICAL_CALCULATION': False,
    'ENABLE_REALTIME_CALCULATION': True,
    'EXIT_AFTER_HISTORICAL_CALCULATION': False,
    'ENABLE_SIGNAL_LISTENING': True
}

# 示例2: 仅计算历史数据
HISTORICAL_ONLY_MODE_CONFIG = {
    'RUN_MODE': 'historical_only',
    'HISTORICAL_DAYS': 30,
    'FORCE_HISTORICAL_CALCULATION': True,
    'ENABLE_REALTIME_CALCULATION': False,
    'EXIT_AFTER_HISTORICAL_CALCULATION': True,
    'WAIT_BEFORE_EXIT': 10,
    'ENABLE_SIGNAL_LISTENING': False
}

# 示例3: 混合模式（先历史后实时）
MIXED_MODE_CONFIG = {
    'RUN_MODE': 'mixed',
    'HISTORICAL_DAYS': 60,
    'FORCE_HISTORICAL_CALCULATION': True,
    'ENABLE_REALTIME_CALCULATION': True,
    'EXIT_AFTER_HISTORICAL_CALCULATION': False,
    'ENABLE_SIGNAL_LISTENING': True
}

# ============ 性能配置示例 ============

# 高性能配置（适合服务器环境）
HIGH_PERFORMANCE_CONFIG = {
    'ENABLE_BATCH_PRELOAD': True,
    'ENABLE_PARALLEL_PROCESSING': True,
    'PARALLEL_PROCESSING_THRESHOLD': 5,
    'CPU_USAGE_RATIO': 0.9,
    'BATCH_SIZE': 2000
}

# 低资源配置（适合开发环境）
LOW_RESOURCE_CONFIG = {
    'ENABLE_BATCH_PRELOAD': True,
    'ENABLE_PARALLEL_PROCESSING': False,
    'PARALLEL_PROCESSING_THRESHOLD': 20,
    'CPU_USAGE_RATIO': 0.5,
    'BATCH_SIZE': 500
}

# ============ 使用方法 ============

def apply_config_to_class(config_dict, config_class):
    """
    将配置字典应用到配置类
    
    :param config_dict: 配置字典
    :param config_class: 配置类
    """
    for key, value in config_dict.items():
        if hasattr(config_class, key):
            setattr(config_class, key, value)
        else:
            print(f"警告: 配置类中不存在属性 {key}")

def set_realtime_mode():
    """设置为实时模式"""
    from breadth_settings import BreadthCalculatorConfig
    apply_config_to_class(REALTIME_MODE_CONFIG, BreadthCalculatorConfig.RunModeConfig)
    print("✅ 已设置为实时数据计算模式")

def set_historical_only_mode(days=30):
    """设置为历史数据专用模式"""
    from breadth_settings import BreadthCalculatorConfig
    config = HISTORICAL_ONLY_MODE_CONFIG.copy()
    config['HISTORICAL_DAYS'] = days
    apply_config_to_class(config, BreadthCalculatorConfig.RunModeConfig)
    print(f"✅ 已设置为历史数据专用模式（{days}天）")

def set_mixed_mode(days=60):
    """设置为混合模式"""
    from breadth_settings import BreadthCalculatorConfig
    config = MIXED_MODE_CONFIG.copy()
    config['HISTORICAL_DAYS'] = days
    apply_config_to_class(config, BreadthCalculatorConfig.RunModeConfig)
    print(f"✅ 已设置为混合模式（{days}天历史数据）")

def set_high_performance():
    """设置为高性能模式"""
    from breadth_settings import BreadthCalculatorConfig
    apply_config_to_class(HIGH_PERFORMANCE_CONFIG, BreadthCalculatorConfig.PerformanceConfig)
    print("✅ 已设置为高性能模式")

def set_low_resource():
    """设置为低资源模式"""
    from breadth_settings import BreadthCalculatorConfig
    apply_config_to_class(LOW_RESOURCE_CONFIG, BreadthCalculatorConfig.PerformanceConfig)
    print("✅ 已设置为低资源模式")

# ============ 命令行使用示例 ============

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python breadth_mode_examples.py realtime           # 设置实时模式")
        print("  python breadth_mode_examples.py historical [days]  # 设置历史数据专用模式")
        print("  python breadth_mode_examples.py mixed [days]       # 设置混合模式")
        print("  python breadth_mode_examples.py high_perf          # 设置高性能模式")
        print("  python breadth_mode_examples.py low_resource       # 设置低资源模式")
        print("  python breadth_mode_examples.py show               # 显示当前配置")
        sys.exit(1)
    
    mode = sys.argv[1].lower()
    
    if mode == 'realtime':
        set_realtime_mode()
    elif mode == 'historical':
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 30
        set_historical_only_mode(days)
    elif mode == 'mixed':
        days = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        set_mixed_mode(days)
    elif mode == 'high_perf':
        set_high_performance()
    elif mode == 'low_resource':
        set_low_resource()
    elif mode == 'show':
        from breadth_settings import print_current_config
        print_current_config()
    else:
        print(f"未知模式: {mode}")
        sys.exit(1)

# ============ 配置说明 ============
"""
运行模式说明:

1. realtime (实时模式) - 默认模式
   - 可选择是否计算历史数据
   - 启动实时数据计算
   - 启用信号监听
   - 程序持续运行

2. historical_only (历史数据专用模式)
   - 仅计算指定天数的历史数据
   - 不启动实时计算
   - 计算完成后自动退出

3. mixed (混合模式)
   - 先强制计算历史数据
   - 然后启动实时计算和信号监听
   - 程序持续运行

配置参数说明:

运行模式配置:
- RUN_MODE: 运行模式选择
- HISTORICAL_DAYS: 历史数据计算天数
- FORCE_HISTORICAL_CALCULATION: 是否强制计算历史数据
- ENABLE_REALTIME_CALCULATION: 是否启用实时计算
- EXIT_AFTER_HISTORICAL_CALCULATION: 历史计算后是否退出
- ENABLE_SIGNAL_LISTENING: 是否启用信号监听

性能配置:
- ENABLE_BATCH_PRELOAD: 是否启用批量预加载
- ENABLE_PARALLEL_PROCESSING: 是否启用多进程并行
- PARALLEL_PROCESSING_THRESHOLD: 并行处理阈值（天数）
- CPU_USAGE_RATIO: CPU使用率
- BATCH_SIZE: 批量查询大小

使用示例:

1. 计算最近30天历史数据:
   修改 breadth_settings.py:
   RUN_MODE = 'historical_only'
   HISTORICAL_DAYS = 30

2. 先计算60天历史数据，然后启动实时监听:
   修改 breadth_settings.py:
   RUN_MODE = 'mixed'
   HISTORICAL_DAYS = 60
   FORCE_HISTORICAL_CALCULATION = True

3. 仅实时计算（不计算历史数据）:
   修改 breadth_settings.py:
   RUN_MODE = 'realtime'
   FORCE_HISTORICAL_CALCULATION = False
"""
