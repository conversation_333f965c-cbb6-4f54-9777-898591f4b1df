"""
市场广度计算器配置文件
"""
import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class CalculationMode(Enum):
    """计算模式枚举"""
    HISTORICAL = "historical"  # 仅历史数据
    REALTIME = "realtime"     # 仅实时数据
    BOTH = "both"             # 历史+实时


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int
    user: str
    password: str
    database: str
    
    @classmethod
    def from_env(cls, prefix: str):
        """从环境变量创建配置"""
        return cls(
            host=os.environ[f'{prefix}_DB_HOST'],
            port=int(os.environ[f'{prefix}_DB_PORT']),
            user=os.environ[f'{prefix}_DB_USER'],
            password=os.environ[f'{prefix}_DB_PASSWORD'],
            database=os.environ[f'{prefix}_DB_NAME']
        )


@dataclass
class HistoricalConfig:
    """历史数据计算配置"""
    days: int = 90                    # 计算天数
    data_range_days: int = 500        # 数据下载天数范围
    min_data_days: int = 252          # 最少数据天数要求
    download_threads: int = 10        # 下载线程数
    price_columns: List[str] = None   # 价格数据列
    
    def __post_init__(self):
        if self.price_columns is None:
            self.price_columns = ['h', 'l', 'c', 'v']


@dataclass
class RealtimeConfig:
    """实时数据计算配置"""
    update_interval: int = 300        # 更新间隔（秒）
    market_hours_only: bool = True    # 仅在交易时间运行
    market_start_hour: int = 9        # 市场开始时间
    market_end_hour: int = 16         # 市场结束时间


@dataclass
class ProcessingConfig:
    """处理配置"""
    cpu_cores: int = 20                # CPU核心数
    batch_size: int = 100             # 批处理大小
    enable_multiprocess: bool = True  # 启用多进程
    log_level: str = "INFO"           # 日志级别
    log_file: str = "market_breadth.log"  # 日志文件


@dataclass
class MarketBreadthConfig:
    """市场广度计算器主配置"""
    mode: CalculationMode
    
    # 数据库配置
    default_db: DatabaseConfig
    algo_db: DatabaseConfig
    security_db: DatabaseConfig
    
    # 功能配置
    historical: HistoricalConfig
    realtime: RealtimeConfig
    processing: ProcessingConfig
    
    # 市场配置
    markets: Optional[List[str]] = None  # 如果为None，从数据库获取
    
    @classmethod
    def create_default(cls, mode: CalculationMode = CalculationMode.BOTH):
        """创建默认配置"""
        return cls(
            mode=mode,
            default_db=DatabaseConfig.from_env('DEFAULT'),
            algo_db=DatabaseConfig.from_env('ALGO'),
            security_db=DatabaseConfig.from_env('SECURITY'),
            historical=HistoricalConfig(),
            realtime=RealtimeConfig(),
            processing=ProcessingConfig()
        )
    
    def get_markets(self) -> List[str]:
        """获取市场列表"""
        if self.markets:
            return self.markets
        
        # 从数据库获取
        import pymysql
        conn = pymysql.Connection(
            host=self.default_db.host,
            port=self.default_db.port,
            user=self.default_db.user,
            password=self.default_db.password,
            database=self.default_db.database,
        )
        
        sql = "SELECT DISTINCT market FROM market_config_gics"
        cursor = conn.cursor()
        cursor.execute(sql)
        markets = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return [m[0] for m in markets]


# 预定义配置
HISTORICAL_ONLY_CONFIG = MarketBreadthConfig.create_default(CalculationMode.HISTORICAL)
REALTIME_ONLY_CONFIG = MarketBreadthConfig.create_default(CalculationMode.REALTIME)
FULL_CONFIG = MarketBreadthConfig.create_default(CalculationMode.BOTH)

# 调整历史配置为更保守的设置
HISTORICAL_ONLY_CONFIG.historical.days = 90
HISTORICAL_ONLY_CONFIG.historical.data_range_days = 500
HISTORICAL_ONLY_CONFIG.processing.cpu_cores = 4
