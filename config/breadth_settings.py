#!/usr/bin/env python3
"""
市宽计算器配置文件
集中管理市宽计算器的运行模式和参数配置
"""

# ============ 基础配置 ============

class BreadthCalculatorConfig:
    """市宽计算器配置类"""
    
    # ============ 运行模式配置 ============
    class RunModeConfig:
        """运行模式配置"""
        # 运行模式选择
        # 'realtime' - 实时数据计算模式（默认）
        # 'historical_only' - 仅计算历史数据
        # 'mixed' - 混合模式（先计算历史数据，然后启动实时计算）
        RUN_MODE = 'historical_only'

        # 历史数据计算天数
        HISTORICAL_DAYS = 90

        # 是否在启动时强制计算历史数据（无论时间检查结果）
        FORCE_HISTORICAL_CALCULATION = True

        # 是否启用实时数据计算
        ENABLE_REALTIME_CALCULATION = False

        # 历史数据计算完成后是否自动退出（仅在historical_only模式下有效）
        EXIT_AFTER_HISTORICAL_CALCULATION = True

        # 历史数据计算完成后的等待时间（秒），然后退出
        WAIT_BEFORE_EXIT = 10

        # 是否启用信号监听模式（实时响应Redis信号）
        ENABLE_SIGNAL_LISTENING = False
    
    # ============ 性能优化配置 ============
    class PerformanceConfig:
        """性能优化配置"""
        # 是否启用批量数据预加载（提升历史数据计算性能）
        ENABLE_BATCH_PRELOAD = True
        
        # 是否启用多进程并行计算（历史数据计算）
        ENABLE_PARALLEL_PROCESSING = False

        # 多进程并行计算的阈值（天数）
        PARALLEL_PROCESSING_THRESHOLD = 200
        
        # CPU使用率（多进程时）
        CPU_USAGE_RATIO = 0.8
        
        # 批量查询的批次大小
        BATCH_SIZE = 1000
        
        # Redis连接超时设置
        REDIS_TIMEOUT = 5
        REDIS_CONNECT_TIMEOUT = 5
    
    # ============ 计算配置 ============
    class CalculationConfig:
        """计算相关配置"""
        # 是否启用增强版背离检测
        ENABLE_ENHANCED_DIVERGENCE = True
        
        # 是否启用改进的动量一致性算法
        ENABLE_IMPROVED_MOMENTUM_COHERENCE = True
        
        # 市场广度指标计算的最小股票数量
        MIN_STOCKS_FOR_CALCULATION = 10
        
        # 是否保存详细的计算日志
        ENABLE_DETAILED_LOGGING = True
        
        # 数据库存储表名
        DATABASE_TABLE = 'market_breadth_metrics_gics'
    
    # ============ 监控配置 ============
    class MonitoringConfig:
        """监控和日志配置"""
        # 日志级别
        LOG_LEVEL = 'WARNING'
        
        # 是否启用文件日志
        ENABLE_FILE_LOGGING = True
        
        # 日志文件路径
        LOG_FILE_PATH = 'logs/market_breadth_calculator.log'
        
        # 日志文件最大大小（MB）
        LOG_FILE_MAX_SIZE = 50
        
        # 日志文件备份数量
        LOG_FILE_BACKUP_COUNT = 5
        
        # 是否启用性能监控
        ENABLE_PERFORMANCE_MONITORING = True
        
        # 性能统计报告间隔（秒）
        PERFORMANCE_REPORT_INTERVAL = 300

# ============ 配置获取函数 ============

def get_run_mode_config() -> 'BreadthCalculatorConfig.RunModeConfig':
    """获取运行模式配置"""
    return BreadthCalculatorConfig.RunModeConfig

def get_performance_config() -> 'BreadthCalculatorConfig.PerformanceConfig':
    """获取性能配置"""
    return BreadthCalculatorConfig.PerformanceConfig

def get_calculation_config() -> 'BreadthCalculatorConfig.CalculationConfig':
    """获取计算配置"""
    return BreadthCalculatorConfig.CalculationConfig

def get_monitoring_config() -> 'BreadthCalculatorConfig.MonitoringConfig':
    """获取监控配置"""
    return BreadthCalculatorConfig.MonitoringConfig

def get_all_configs() -> dict:
    """获取所有配置"""
    return {
        'run_mode': get_run_mode_config(),
        'performance': get_performance_config(),
        'calculation': get_calculation_config(),
        'monitoring': get_monitoring_config()
    }

# ============ 配置验证函数 ============

def validate_config() -> bool:
    """验证配置的有效性"""
    try:
        run_mode_config = get_run_mode_config()
        performance_config = get_performance_config()
        
        # 验证运行模式
        valid_modes = ['realtime', 'historical_only', 'mixed']
        if run_mode_config.RUN_MODE not in valid_modes:
            print(f"错误: 无效的运行模式 {run_mode_config.RUN_MODE}")
            return False
        
        # 验证历史天数
        if run_mode_config.HISTORICAL_DAYS <= 0:
            print(f"错误: 历史天数必须大于0")
            return False
        
        # 验证CPU使用率
        if not (0 < performance_config.CPU_USAGE_RATIO <= 1):
            print(f"错误: CPU使用率必须在0-1之间")
            return False
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# ============ 环境变量兼容模式 ============

def load_from_env_if_available():
    """
    如果环境变量存在，则覆盖配置（向后兼容）
    """
    import os
    
    # 覆盖运行模式配置
    if os.getenv('BREADTH_RUN_MODE'):
        BreadthCalculatorConfig.RunModeConfig.RUN_MODE = os.getenv('BREADTH_RUN_MODE')
    
    if os.getenv('BREADTH_HISTORICAL_DAYS'):
        BreadthCalculatorConfig.RunModeConfig.HISTORICAL_DAYS = int(os.getenv('BREADTH_HISTORICAL_DAYS'))
    
    if os.getenv('BREADTH_ENABLE_REALTIME'):
        BreadthCalculatorConfig.RunModeConfig.ENABLE_REALTIME_CALCULATION = os.getenv('BREADTH_ENABLE_REALTIME').lower() == 'true'
    
    # 覆盖性能配置
    if os.getenv('BREADTH_ENABLE_PARALLEL'):
        BreadthCalculatorConfig.PerformanceConfig.ENABLE_PARALLEL_PROCESSING = os.getenv('BREADTH_ENABLE_PARALLEL').lower() == 'true'
    
    if os.getenv('BREADTH_CPU_RATIO'):
        BreadthCalculatorConfig.PerformanceConfig.CPU_USAGE_RATIO = float(os.getenv('BREADTH_CPU_RATIO'))

# 自动加载环境变量（如果存在）
load_from_env_if_available()

# ============ 配置显示函数 ============

def print_current_config():
    """打印当前配置"""
    run_mode = get_run_mode_config()
    performance = get_performance_config()
    calculation = get_calculation_config()
    monitoring = get_monitoring_config()
    
    print("=" * 60)
    print("市宽计算器当前配置")
    print("=" * 60)
    
    print("🔧 运行模式配置:")
    print(f"  运行模式: {run_mode.RUN_MODE}")
    print(f"  历史数据天数: {run_mode.HISTORICAL_DAYS}")
    print(f"  强制历史计算: {run_mode.FORCE_HISTORICAL_CALCULATION}")
    print(f"  启用实时计算: {run_mode.ENABLE_REALTIME_CALCULATION}")
    print(f"  历史计算后退出: {run_mode.EXIT_AFTER_HISTORICAL_CALCULATION}")
    print(f"  启用信号监听: {run_mode.ENABLE_SIGNAL_LISTENING}")
    
    print("\n⚡ 性能优化配置:")
    print(f"  批量预加载: {performance.ENABLE_BATCH_PRELOAD}")
    print(f"  多进程并行: {performance.ENABLE_PARALLEL_PROCESSING}")
    print(f"  并行阈值: {performance.PARALLEL_PROCESSING_THRESHOLD}天")
    print(f"  CPU使用率: {performance.CPU_USAGE_RATIO}")
    
    print("\n🧮 计算配置:")
    print(f"  增强背离检测: {calculation.ENABLE_ENHANCED_DIVERGENCE}")
    print(f"  改进动量一致性: {calculation.ENABLE_IMPROVED_MOMENTUM_COHERENCE}")
    print(f"  最小股票数量: {calculation.MIN_STOCKS_FOR_CALCULATION}")
    
    print("\n📊 监控配置:")
    print(f"  日志级别: {monitoring.LOG_LEVEL}")
    print(f"  文件日志: {monitoring.ENABLE_FILE_LOGGING}")
    print(f"  性能监控: {monitoring.ENABLE_PERFORMANCE_MONITORING}")
    
    print("=" * 60)

if __name__ == "__main__":
    # 验证并显示配置
    if validate_config():
        print_current_config()
    else:
        print("❌ 配置验证失败")
