#!/usr/bin/env python3
"""
板块轮动调度器配置文件
集中管理所有板块轮动相关的配置参数
"""

import os
import multiprocessing
from typing import Dict, Any

class SectorRotationConfig:
    """板块轮动配置类"""
    
    # ============ 数据获取配置 ============
    class DataConfig:
        """数据获取配置"""
        # 历史数据天数
        HISTORICAL_DAYS = 30
        
        # 数据充足性检查阈值
        DATA_SUFFICIENCY_RATIO = 0.5  # 数据少于期望的50%时触发实时计算
        SINGLE_SECTOR_DATA_RATIO = 0.3  # 单个板块数据少于期望的30%时警告
        
        # 数据库表名
        SECTOR_INDICES_TABLE = "sector_indices_gics"
        ROTATION_METRICS_TABLE = "sector_rotation_metrics_gics"
        
        # 最小数据点要求
        MIN_DATA_POINTS_FOR_CALCULATION = 3
        
    # ============ 计算配置 ============
    class CalculationConfig:
        """计算配置"""
        # 多进程配置
        DEFAULT_CPU_COUNTS = 4
        MAX_CPU_USAGE_RATIO = 0.8  # 最大CPU使用率
        
        # 动量计算周期
        MOMENTUM_PERIODS = {
            'short': 5,   # 5日动量
            'medium': 10, # 10日动量
            'long': 20    # 20日动量
        }
        
        # 技术指标计算周期
        SMA_PERIODS = {
            'short': 5,   # 5日均线
            'medium': 10, # 10日均线
            'long': 20    # 20日均线
        }
        
        # 轮动强度指数计算参数
        RII_WEIGHTS = {
            'price_dispersion': 0.4,    # 价格离散度权重
            'velocity': 0.4,            # 轮动速度权重
            'volume_concentration': 0.2  # 成交量集中度权重
        }
        
        # 综合得分权重
        COMPOSITE_SCORE_WEIGHTS = {
            'momentum_20d': 0.4,           # 20日动量权重
            'relative_strength': 0.3,     # 相对强度权重
            'rotation_intensity_index': 0.3 # 轮动强度指数权重
        }
        
        # 轮动速度计算参数
        ROTATION_VELOCITY_LOOKBACK = 3  # 轮动速度计算回看期
        
        # 轮动强度指数计算参数
        RII_PRICE_LOOKBACK = 6    # 价格离散度计算回看期
        RII_VOLUME_LOOKBACK = 5   # 成交量集中度计算回看期
        
    # ============ 信号配置 ============
    class SignalConfig:
        """信号配置"""
        # 信号队列名称
        INPUT_SIGNAL_QUEUE = "market_breadth_signals"
        OUTPUT_SIGNAL_QUEUE = "sector_rotation_signals"
        
        # 支持的信号类型
        SUPPORTED_SIGNAL_TYPES = {
            'market_breadth_calculated': '市场广度计算完成',
            'sector_data_updated': '板块数据更新',
            'manual_trigger': '手动触发'
        }
        
        # 信号有效期（秒）
        SIGNAL_VALIDITY_SECONDS = 300  # 5分钟
        
        # 信号监听超时（秒）
        SIGNAL_LISTEN_TIMEOUT = 5
        
        # Redis重连等待时间（秒）
        REDIS_RECONNECT_WAIT = 10
        
    # ============ 日志配置 ============
    class LoggingConfig:
        """日志配置"""
        # 日志级别
        LOG_LEVEL = "INFO"
        
        # 日志格式
        LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # 日志记录器名称
        LOGGER_NAME = 'sector_rotation_scheduler'
        
        # 是否启用文件日志
        ENABLE_FILE_LOGGING = False
        
        # 日志文件路径
        LOG_FILE_PATH = "logs/sector_rotation.log"
        
    # ============ MTF分析配置 ============
    class MTFConfig:
        """MTF分析配置"""
        # 是否启用MTF分析
        ENABLE_MTF_ANALYSIS = True
        
        # MTF分析数据天数
        MTF_ANALYSIS_DAYS = 30
        
        # 是否优先使用真实数据
        MTF_USE_REAL_DATA_FIRST = True
        
        # MTF结果保存到数据库
        MTF_SAVE_TO_DATABASE = True
        
    # ============ 性能优化配置 ============
    class PerformanceConfig:
        """性能优化配置"""
        # 是否启用多进程计算
        ENABLE_MULTIPROCESSING = True
        
        # 多进程失败时是否回退到单进程
        FALLBACK_TO_SINGLE_PROCESS = True
        
        # 内存监控阈值（MB）
        MEMORY_THRESHOLD_MB = 1024
        
        # 是否启用内存监控
        ENABLE_MEMORY_MONITORING = False
        
        # 垃圾回收间隔
        GC_INTERVAL = 100
        
    # ============ 运行模式配置 ============
    class RunModeConfig:
        """运行模式配置"""
        # 默认运行模式
        DEFAULT_MODE = "listen"  # listen, once, standalone
        
        # 独立模式下显示的排名数量
        STANDALONE_TOP_SECTORS_COUNT = 5
        
        # 是否在独立模式下执行MTF分析
        STANDALONE_ENABLE_MTF = True

# ============ 配置获取函数 ============

def get_data_config() -> 'SectorRotationConfig.DataConfig':
    """获取数据配置"""
    return SectorRotationConfig.DataConfig

def get_calculation_config() -> 'SectorRotationConfig.CalculationConfig':
    """获取计算配置"""
    return SectorRotationConfig.CalculationConfig

def get_signal_config() -> 'SectorRotationConfig.SignalConfig':
    """获取信号配置"""
    return SectorRotationConfig.SignalConfig

def get_logging_config() -> 'SectorRotationConfig.LoggingConfig':
    """获取日志配置"""
    return SectorRotationConfig.LoggingConfig

def get_mtf_config() -> 'SectorRotationConfig.MTFConfig':
    """获取MTF配置"""
    return SectorRotationConfig.MTFConfig

def get_performance_config() -> 'SectorRotationConfig.PerformanceConfig':
    """获取性能配置"""
    return SectorRotationConfig.PerformanceConfig

def get_run_mode_config() -> 'SectorRotationConfig.RunModeConfig':
    """获取运行模式配置"""
    return SectorRotationConfig.RunModeConfig

def get_all_configs() -> Dict[str, Any]:
    """获取所有配置"""
    return {
        'data': get_data_config(),
        'calculation': get_calculation_config(),
        'signal': get_signal_config(),
        'logging': get_logging_config(),
        'mtf': get_mtf_config(),
        'performance': get_performance_config(),
        'run_mode': get_run_mode_config()
    }

# ============ 环境变量覆盖 ============

def load_config_from_env():
    """从环境变量加载配置（可选覆盖）"""
    # 数据配置覆盖
    if os.getenv('SECTOR_HISTORICAL_DAYS'):
        SectorRotationConfig.DataConfig.HISTORICAL_DAYS = int(os.getenv('SECTOR_HISTORICAL_DAYS'))
    
    # 计算配置覆盖
    if os.getenv('SECTOR_CPU_COUNTS'):
        SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = int(os.getenv('SECTOR_CPU_COUNTS'))
    
    # 信号配置覆盖
    if os.getenv('SECTOR_INPUT_QUEUE'):
        SectorRotationConfig.SignalConfig.INPUT_SIGNAL_QUEUE = os.getenv('SECTOR_INPUT_QUEUE')
    if os.getenv('SECTOR_OUTPUT_QUEUE'):
        SectorRotationConfig.SignalConfig.OUTPUT_SIGNAL_QUEUE = os.getenv('SECTOR_OUTPUT_QUEUE')
    
    # 日志配置覆盖
    if os.getenv('SECTOR_LOG_LEVEL'):
        SectorRotationConfig.LoggingConfig.LOG_LEVEL = os.getenv('SECTOR_LOG_LEVEL')
    
    # MTF配置覆盖
    if os.getenv('SECTOR_ENABLE_MTF'):
        SectorRotationConfig.MTFConfig.ENABLE_MTF_ANALYSIS = os.getenv('SECTOR_ENABLE_MTF').lower() == 'true'

# ============ 配置验证 ============

def validate_config() -> bool:
    """验证配置的有效性"""
    try:
        # 验证CPU数量
        max_cpu = multiprocessing.cpu_count()
        if SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS > max_cpu:
            print(f"警告：配置的CPU数量({SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS})超过系统最大值({max_cpu})")
            SectorRotationConfig.CalculationConfig.DEFAULT_CPU_COUNTS = max_cpu
        
        # 验证历史数据天数
        if SectorRotationConfig.DataConfig.HISTORICAL_DAYS <= 0:
            print("错误：历史数据天数必须大于0")
            return False
        
        # 验证权重总和
        weights = SectorRotationConfig.CalculationConfig.COMPOSITE_SCORE_WEIGHTS
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:
            print(f"警告：综合得分权重总和({total_weight})不等于1.0")
        
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False

# ============ 配置显示 ============

def print_current_config():
    """打印当前配置（用于调试）"""
    print("=" * 60)
    print("🔧 板块轮动调度器配置")
    print("=" * 60)
    
    data_config = get_data_config()
    calc_config = get_calculation_config()
    signal_config = get_signal_config()
    log_config = get_logging_config()
    mtf_config = get_mtf_config()
    perf_config = get_performance_config()
    run_config = get_run_mode_config()
    
    print("\n📊 数据配置:")
    print(f"  历史数据天数: {data_config.HISTORICAL_DAYS}")
    print(f"  数据充足性阈值: {data_config.DATA_SUFFICIENCY_RATIO}")
    print(f"  最小数据点: {data_config.MIN_DATA_POINTS_FOR_CALCULATION}")
    
    print("\n🧮 计算配置:")
    print(f"  默认CPU数量: {calc_config.DEFAULT_CPU_COUNTS}")
    print(f"  动量周期: {calc_config.MOMENTUM_PERIODS}")
    print(f"  综合得分权重: {calc_config.COMPOSITE_SCORE_WEIGHTS}")
    
    print("\n📡 信号配置:")
    print(f"  输入队列: {signal_config.INPUT_SIGNAL_QUEUE}")
    print(f"  输出队列: {signal_config.OUTPUT_SIGNAL_QUEUE}")
    print(f"  信号有效期: {signal_config.SIGNAL_VALIDITY_SECONDS}秒")
    
    print("\n📝 日志配置:")
    print(f"  日志级别: {log_config.LOG_LEVEL}")
    print(f"  文件日志: {log_config.ENABLE_FILE_LOGGING}")
    
    print("\n🔍 MTF配置:")
    print(f"  启用MTF分析: {mtf_config.ENABLE_MTF_ANALYSIS}")
    print(f"  MTF数据天数: {mtf_config.MTF_ANALYSIS_DAYS}")
    
    print("\n⚡ 性能配置:")
    print(f"  启用多进程: {perf_config.ENABLE_MULTIPROCESSING}")
    print(f"  内存监控: {perf_config.ENABLE_MEMORY_MONITORING}")
    
    print("\n🚀 运行模式:")
    print(f"  默认模式: {run_config.DEFAULT_MODE}")
    print(f"  显示排名数: {run_config.STANDALONE_TOP_SECTORS_COUNT}")
    
    print("=" * 60)

# 自动加载环境变量（如果存在）
load_config_from_env()

if __name__ == "__main__":
    # 验证并显示配置
    if validate_config():
        print_current_config()
    else:
        print("❌ 配置验证失败")
