#!/usr/bin/env python3
"""
多时间框架配置检查脚本
检查和配置MTF分析设置
"""

import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def check_mtf_configuration():
    """检查MTF配置"""
    print("🔍 检查多时间框架（MTF）配置")
    print("=" * 50)
    
    try:
        from sector_settings import (
            get_mtf_config, get_run_mode_config, 
            get_data_config, print_current_config
        )
        
        # 获取配置
        mtf_config = get_mtf_config()
        run_config = get_run_mode_config()
        data_config = get_data_config()
        
        print("📊 MTF分析配置:")
        print(f"   启用MTF分析: {'✅ 是' if mtf_config.ENABLE_MTF_ANALYSIS else '❌ 否'}")
        print(f"   MTF数据天数: {mtf_config.MTF_ANALYSIS_DAYS} 天")
        print(f"   优先真实数据: {'✅ 是' if mtf_config.MTF_USE_REAL_DATA_FIRST else '❌ 否'}")
        print(f"   保存到数据库: {'✅ 是' if mtf_config.MTF_SAVE_TO_DATABASE else '❌ 否'}")
        
        print("\n🎯 运行模式配置:")
        print(f"   独立模式MTF: {'✅ 启用' if run_config.STANDALONE_ENABLE_MTF else '❌ 禁用'}")
        print(f"   显示排名数量: {run_config.STANDALONE_TOP_SECTORS_COUNT}")
        
        print("\n📈 数据配置:")
        print(f"   历史数据天数: {data_config.HISTORICAL_DAYS} 天")
        print(f"   最小数据点数: {data_config.MIN_DATA_POINTS}")
        
        # 检查MTF分析器是否可用
        print("\n🔧 MTF分析器状态:")
        try:
            from mtf_analyzer_service import MTFAnalyzerService
            mtf_analyzer = MTFAnalyzerService()
            print("   ✅ MTF分析器可用")
            
            # 测试MTF分析
            print("   🧪 测试MTF分析功能...")
            test_result = mtf_analyzer.run_mtf_analysis(days=7, use_real_data=False)
            if test_result and test_result.consensus_score > 0:
                print("   ✅ MTF分析功能正常")
                print(f"   📊 测试结果: 一致性得分 {test_result.consensus_score:.3f}")
            else:
                print("   ⚠️  MTF分析返回空结果")
                
        except Exception as e:
            print(f"   ❌ MTF分析器不可用: {e}")
        
        # 配置建议
        print("\n💡 配置建议:")
        if not mtf_config.ENABLE_MTF_ANALYSIS:
            print("   ⚠️  建议启用MTF分析以获得完整的多时间框架轮动指标")
        
        if not run_config.STANDALONE_ENABLE_MTF:
            print("   ⚠️  建议启用独立模式MTF分析")
        
        if mtf_config.MTF_ANALYSIS_DAYS < 30:
            print("   💡 建议MTF数据天数设置为30天以上以获得更准确的分析")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_sector_rotation_with_mtf():
    """测试带MTF的板块轮动计算"""
    print("\n🧪 测试板块轮动 + MTF分析")
    print("=" * 50)
    
    try:
        from sector_rotation_scheduler import SectorRotationScheduler
        
        # 创建调度器
        scheduler = SectorRotationScheduler()
        
        print("📊 调度器状态:")
        print(f"   MTF分析器: {'✅ 可用' if scheduler.mtf_analyzer else '❌ 不可用'}")
        print(f"   MTF配置启用: {'✅ 是' if scheduler.mtf_config.ENABLE_MTF_ANALYSIS else '❌ 否'}")
        
        if scheduler.mtf_analyzer and scheduler.mtf_config.ENABLE_MTF_ANALYSIS:
            print("\n🔄 执行完整的板块轮动 + MTF分析...")
            
            # 执行计算
            success = scheduler.calculate_rotation_metrics()
            
            if success:
                print("✅ 板块轮动计算成功")
                
                # 检查是否执行了MTF分析
                print("🔍 检查MTF分析结果...")
                
                # 这里可以检查数据库中的MTF结果
                try:
                    import pymysql
                    from db_settings import get_default_db_config
                    
                    config = get_default_db_config()
                    conn = pymysql.connect(**config)
                    cursor = conn.cursor()
                    
                    # 检查是否有MTF分析结果
                    cursor.execute("""
                    SELECT COUNT(*) FROM sector_rotation_metrics 
                    WHERE rotation_stage IS NOT NULL 
                    AND rotation_stage != 'unknown'
                    AND DATE(recorded_at) = CURDATE()
                    """)
                    
                    mtf_count = cursor.fetchone()[0]
                    
                    if mtf_count > 0:
                        print(f"✅ 发现 {mtf_count} 条包含MTF分析的轮动数据")
                        
                        # 获取轮动阶段信息
                        cursor.execute("""
                        SELECT DISTINCT rotation_stage, risk_level 
                        FROM sector_rotation_metrics 
                        WHERE DATE(recorded_at) = CURDATE()
                        AND rotation_stage IS NOT NULL
                        LIMIT 5
                        """)
                        
                        stages = cursor.fetchall()
                        if stages:
                            print("📊 MTF分析结果:")
                            for stage, risk in stages:
                                print(f"   轮动阶段: {stage}, 风险等级: {risk}")
                    else:
                        print("⚠️  未发现包含MTF分析的数据")
                    
                    conn.close()
                    
                except Exception as db_e:
                    print(f"⚠️  数据库检查失败: {db_e}")
                
                return True
            else:
                print("❌ 板块轮动计算失败")
                return False
        else:
            print("⚠️  MTF分析器不可用或未启用，只能执行基础轮动计算")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_mtf_timeframes():
    """显示MTF支持的时间框架"""
    print("\n📅 MTF支持的时间框架")
    print("=" * 50)
    
    try:
        from multi_timeframe_analyzer import SUPPORTED_TIMEFRAMES
        
        print("支持的时间框架:")
        for tf in SUPPORTED_TIMEFRAMES:
            print(f"   • {tf}")
        
        print(f"\n总计: {len(SUPPORTED_TIMEFRAMES)} 个时间框架")
        
        # 显示时间框架组合
        print("\n📊 推荐的时间框架组合:")
        combinations = [
            ("短期组合", ["5m", "15m", "1h"]),
            ("标准组合", ["15m", "1h", "1d"]),
            ("长期组合", ["1h", "1d", "1w"]),
            ("完整组合", ["5m", "15m", "1h", "1d", "1w", "1M"])
        ]
        
        for name, timeframes in combinations:
            print(f"   {name}: {', '.join(timeframes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取时间框架信息失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 多时间框架板块轮动配置检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查1: MTF配置
    config_ok = check_mtf_configuration()
    
    # 检查2: 支持的时间框架
    timeframes_ok = show_mtf_timeframes()
    
    # 检查3: 实际测试
    test_ok = test_sector_rotation_with_mtf()
    
    print("\n" + "=" * 60)
    print("📋 检查结果总结:")
    print(f"   MTF配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   时间框架支持: {'✅ 正常' if timeframes_ok else '❌ 异常'}")
    print(f"   功能测试: {'✅ 成功' if test_ok else '❌ 失败'}")
    
    if config_ok and timeframes_ok and test_ok:
        print("\n🎉 多时间框架板块轮动系统完全就绪!")
        print("\n💡 使用建议:")
        print("1. 运行: python run_sector_rotation_calculation.py")
        print("2. 启动Web界面: python web_interface.py")
        print("3. 选择完整组合进行多时间框架分析")
        print("4. 查看RII指数、轮动阶段等完整指标")
    else:
        print("\n⚠️  系统配置需要调整")
        print("\n🔧 解决建议:")
        if not config_ok:
            print("- 检查sector_settings.py中的MTF配置")
        if not test_ok:
            print("- 确保数据库连接正常")
            print("- 运行市场广度计算提供基础数据")
    
    return config_ok and timeframes_ok and test_ok

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
