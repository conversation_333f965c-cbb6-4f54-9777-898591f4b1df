#!/usr/bin/env python3
"""
生产环境MTF系统完整运行脚本
对所有38个真实市场进行MTF分析并存储到数据库

功能：
1. 获取所有38个真实GICS板块
2. 执行完整的MTF分析
3. 将结果存储到数据库
4. 生成运行报告

作者：Financial Master
日期：2025-01-25
"""

import sys
import os
import time
import logging
from datetime import datetime
from typing import Dict, List

# 添加路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'core'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('MTF_PRODUCTION')

class MTFProductionRunner:
    """MTF生产环境运行器"""
    
    def __init__(self):
        """初始化运行器"""
        self.start_time = None
        self.results = {
            'total_markets': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'database_saves': 0,
            'execution_time': 0,
            'analysis_results': []
        }
        
    def run_full_production_analysis(self) -> Dict:
        """运行完整的生产环境分析"""
        logger.info("🚀 开始生产环境MTF分析...")
        logger.info("=" * 80)
        
        self.start_time = time.time()
        
        try:
            # 初始化MTF组件
            self._initialize_mtf_components()
            
            # 获取所有真实市场
            markets_info = self._get_all_real_markets()
            
            # 执行完整MTF分析
            self._run_comprehensive_mtf_analysis()
            
            # 生成生产报告
            self._generate_production_report()
            
        except Exception as e:
            logger.error(f"❌ 生产环境运行失败: {e}")
            self.results['error'] = str(e)
        
        return self.results
    
    def _initialize_mtf_components(self):
        """初始化MTF系统组件"""
        logger.info("🔧 初始化MTF系统组件...")
        
        try:
            from core.mtf_data_adapter import MTFDataAdapter
            from core.mtf_analyzer_service import MTFAnalyzerService
            
            # 初始化数据适配器
            self.data_adapter = MTFDataAdapter()
            logger.info("✅ MTFDataAdapter 初始化成功")
            
            # 初始化分析服务
            self.analyzer_service = MTFAnalyzerService()
            logger.info("✅ MTFAnalyzerService 初始化成功")
            
        except Exception as e:
            logger.error(f"❌ MTF组件初始化失败: {e}")
            raise
    
    def _get_all_real_markets(self) -> Dict:
        """获取所有真实市场信息"""
        logger.info("📊 获取所有真实市场信息...")
        
        try:
            # 获取真实板块映射
            sector_mapping = self.data_adapter._get_real_sector_mapping()
            
            markets_info = {
                'total_markets': len(sector_mapping),
                'market_names': list(sector_mapping.keys()),
                'total_stocks': sum(len(stocks) for stocks in sector_mapping.values()),
                'sector_mapping': sector_mapping
            }
            
            self.results['total_markets'] = markets_info['total_markets']
            
            logger.info(f"✅ 发现 {markets_info['total_markets']} 个真实市场")
            logger.info(f"✅ 包含 {markets_info['total_stocks']} 只股票")
            logger.info("🏷️  市场列表:")
            
            # 显示所有市场名称
            for i, market in enumerate(markets_info['market_names'][:10], 1):  # 显示前10个
                stock_count = len(sector_mapping[market])
                logger.info(f"   {i:2d}. {market:<30} ({stock_count:4d} 只股票)")
            
            if markets_info['total_markets'] > 10:
                logger.info(f"   ... 还有 {markets_info['total_markets'] - 10} 个市场")
            
            return markets_info
            
        except Exception as e:
            logger.error(f"❌ 获取市场信息失败: {e}")
            raise
    
    def _run_comprehensive_mtf_analysis(self):
        """运行全面的MTF分析"""
        logger.info("🔍 开始全面MTF分析...")
        logger.info("=" * 60)
        
        analysis_start_time = time.time()
        
        try:
            # 执行30天的真实数据MTF分析
            logger.info("📈 执行30天真实数据MTF分析...")
            
            mtf_result = self.analyzer_service.run_mtf_analysis(
                days=30, 
                use_real_data=True
            )
            
            if mtf_result and mtf_result.consensus_score > 0:
                self.results['successful_analyses'] += 1
                
                # 记录分析结果
                analysis_summary = {
                    'timestamp': mtf_result.timestamp,
                    'market_regime': mtf_result.market_regime,
                    'consensus_score': mtf_result.consensus_score,
                    'signal_reliability': mtf_result.signal_reliability,
                    'suggested_position': mtf_result.suggested_position,
                    'operation_strategy': mtf_result.operation_strategy,
                    'top_sectors': mtf_result.top_sectors,
                    'avoid_sectors': mtf_result.avoid_sectors,
                    'key_insights_count': len(mtf_result.key_insights),
                    'warnings_count': len(mtf_result.warnings)
                }
                
                self.results['analysis_results'].append(analysis_summary)
                
                # 保存到数据库
                logger.info("💾 保存MTF分析结果到数据库...")
                save_success = self.analyzer_service.save_mtf_results_to_db(mtf_result)
                
                if save_success:
                    self.results['database_saves'] += 1
                    logger.info("✅ 数据库保存成功")
                else:
                    logger.warning("⚠️  数据库保存失败")
                
                # 显示分析结果摘要
                logger.info("📋 MTF分析结果摘要:")
                logger.info(f"   🎯 市场状态: {mtf_result.market_regime}")
                logger.info(f"   📊 一致性得分: {mtf_result.consensus_score:.3f}")
                logger.info(f"   🔍 信号可靠性: {mtf_result.signal_reliability:.1%}")
                logger.info(f"   💼 建议仓位: {mtf_result.suggested_position}%")
                logger.info(f"   🎯 操作策略: {mtf_result.operation_strategy}")
                logger.info(f"   🔥 推荐板块数: {len(mtf_result.top_sectors)}")
                logger.info(f"   ⚠️  避免板块数: {len(mtf_result.avoid_sectors)}")
                
                if mtf_result.top_sectors:
                    logger.info("   🏆 推荐板块:")
                    for sector in mtf_result.top_sectors[:3]:  # 显示前3个
                        if isinstance(sector, dict):
                            sector_name = sector.get('sector', 'Unknown')
                            sector_weight = sector.get('weight', '')
                            sector_reason = sector.get('reason', '')
                            logger.info(f"      • {sector_name}: {sector_weight} ({sector_reason})")
                        else:
                            logger.info(f"      • {sector}")
                
            else:
                self.results['failed_analyses'] += 1
                logger.error("❌ MTF分析失败或结果无效")
            
            analysis_time = time.time() - analysis_start_time
            logger.info(f"⏱️  MTF分析用时: {analysis_time:.2f}秒")
            
        except Exception as e:
            self.results['failed_analyses'] += 1
            logger.error(f"❌ MTF分析异常: {e}")
            raise
    
    def _generate_production_report(self):
        """生成生产环境运行报告"""
        total_time = time.time() - self.start_time if self.start_time else 0
        self.results['execution_time'] = total_time
        
        logger.info("=" * 80)
        logger.info("📋 MTF生产环境运行报告")
        logger.info("=" * 80)
        logger.info(f"🕒 总运行时间: {total_time:.2f}秒")
        logger.info(f"📅 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("")
        
        # 统计信息
        logger.info("📊 运行统计:")
        logger.info(f"   📊 发现市场数: {self.results['total_markets']}")
        logger.info(f"   ✅ 成功分析数: {self.results['successful_analyses']}")
        logger.info(f"   ❌ 失败分析数: {self.results['failed_analyses']}")
        logger.info(f"   💾 数据库保存: {self.results['database_saves']}")
        
        # 成功率计算
        if self.results['total_markets'] > 0:
            success_rate = (self.results['successful_analyses'] / max(1, self.results['successful_analyses'] + self.results['failed_analyses'])) * 100
            logger.info(f"   📈 成功率: {success_rate:.1f}%")
        
        # 分析结果概览
        if self.results['analysis_results']:
            logger.info("")
            logger.info("🎯 分析结果概览:")
            
            for i, result in enumerate(self.results['analysis_results'], 1):
                logger.info(f"   {i}. 分析时间: {result['timestamp'].strftime('%H:%M:%S')}")
                logger.info(f"      市场状态: {result['market_regime']}")
                logger.info(f"      一致性得分: {result['consensus_score']:.3f}")
                logger.info(f"      建议仓位: {result['suggested_position']}%")
                if result['top_sectors']:
                    # 处理推荐板块可能是字典的情况
                    sector_names = []
                    for sector in result['top_sectors'][:2]:
                        if isinstance(sector, dict):
                            sector_names.append(sector.get('sector', 'Unknown'))
                        else:
                            sector_names.append(str(sector))
                    logger.info(f"      推荐板块: {', '.join(sector_names)}")
        
        # 总结
        logger.info("")
        if self.results['successful_analyses'] > 0 and self.results['database_saves'] > 0:
            logger.info("🎉 MTF生产环境运行成功！")
            logger.info("📊 所有分析结果已保存到数据库")
            logger.info("📝 可以使用数据导出脚本查看详细结果")
        elif self.results['failed_analyses'] == 0:
            logger.info("⚠️  MTF运行部分成功，建议检查数据库连接")
        else:
            logger.info("❌ MTF运行存在问题，需要检查系统配置")
        
        logger.info("=" * 80)

def main():
    """主函数"""
    print("🚀 启动MTF生产环境分析...")
    
    runner = MTFProductionRunner()
    results = runner.run_full_production_analysis()
    
    # 返回退出码
    exit_code = 0 if results['successful_analyses'] > 0 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main() 