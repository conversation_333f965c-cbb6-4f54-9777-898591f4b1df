#!/usr/bin/env python3
"""
测试 OPT-T3.1 优化权重分配算法
验证增强版板块轮动分析器的权重分配功能
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版板块轮动分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_sector_rotation_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_sector_rotation_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedSectorRotationAnalyzer = analyzer_module.EnhancedSectorRotationAnalyzer
WeightAllocationDetails = analyzer_module.WeightAllocationDetails


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health, momentum_coherence=None):
        self.internal_health = internal_health
        self.momentum_coherence = momentum_coherence


def test_weight_allocation_basic():
    """测试基础权重分配功能"""
    print("=== 测试基础权重分配功能 ===\n")
    
    # 创建分析器
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 创建测试数据
    sectors = ['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer', 'Industrial']
    relative_strength = pd.Series([0.85, 0.72, 0.45, 0.28, 0.63, 0.55], index=sectors)
    
    # 创建广度指标
    breadth_metrics = {
        'Technology': MockBreadthMetrics(75.2, 0.68),
        'Healthcare': MockBreadthMetrics(68.5, 0.72),
        'Finance': MockBreadthMetrics(52.3, 0.45),
        'Energy': MockBreadthMetrics(35.8, 0.32),
        'Consumer': MockBreadthMetrics(61.7, 0.58),
        'Industrial': MockBreadthMetrics(58.9, 0.51)
    }
    
    # 一致性分析
    coherence_analysis = {
        'coherence_type': 'moderate_coherence',
        'overall_coherence': 0.55
    }
    
    print(f"📊 输入数据:")
    print(f"相对强度: {relative_strength.to_dict()}")
    print(f"一致性类型: {coherence_analysis['coherence_type']}")
    
    # 测试不同阶段和风险等级
    test_scenarios = [
        ('稳定期', 'medium'),
        ('混乱期', 'high'),
        ('加速期', 'low')
    ]
    
    for stage, risk_level in test_scenarios:
        print(f"\n--- {stage} + {risk_level}风险 ---")
        
        result = analyzer.calculate_optimal_weights_enhanced(
            relative_strength=relative_strength,
            stage=stage,
            breadth_metrics=breadth_metrics,
            risk_level=risk_level,
            coherence_analysis=coherence_analysis
        )
        
        print(f"分配理由: {result.allocation_reason}")
        print(f"权重分配:")
        for sector, weight in result.weights.items():
            if weight > 0.01:  # 只显示有意义的权重
                print(f"  {sector}: {weight:.1%}")
        
        print(f"分散度指标:")
        div_metrics = result.diversification_metrics
        print(f"  Herfindahl指数: {div_metrics['herfindahl_index']:.4f}")
        print(f"  有效持仓数: {div_metrics['effective_positions']:.1f}")
        print(f"  综合评分: {div_metrics['overall_score']:.3f}")
        
        print(f"风险约束:")
        constraints = result.risk_constraints
        print(f"  最大单一权重: {constraints['max_single_weight']:.1%}")
        print(f"  最大前三权重: {constraints['max_top3_weight']:.1%}")
        print(f"  最小分散数: {constraints['min_diversification']}")
    
    return True


def test_coherence_impact():
    """测试一致性对权重分配的影响"""
    print("\n=== 测试一致性对权重分配的影响 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 相同的相对强度数据
    sectors = ['Tech', 'Health', 'Finance', 'Energy']
    relative_strength = pd.Series([0.8, 0.6, 0.4, 0.2], index=sectors)
    
    # 不同一致性类型的测试
    coherence_scenarios = [
        {
            'type': 'strong_consensus',
            'metrics': {
                'Tech': MockBreadthMetrics(80, 0.85),
                'Health': MockBreadthMetrics(75, 0.82),
                'Finance': MockBreadthMetrics(70, 0.78),
                'Energy': MockBreadthMetrics(65, 0.75)
            }
        },
        {
            'type': 'high_dispersion',
            'metrics': {
                'Tech': MockBreadthMetrics(85, 0.25),
                'Health': MockBreadthMetrics(45, 0.30),
                'Finance': MockBreadthMetrics(65, 0.20),
                'Energy': MockBreadthMetrics(35, 0.28)
            }
        },
        {
            'type': 'moderate_coherence',
            'metrics': {
                'Tech': MockBreadthMetrics(75, 0.55),
                'Health': MockBreadthMetrics(60, 0.48),
                'Finance': MockBreadthMetrics(55, 0.52),
                'Energy': MockBreadthMetrics(40, 0.45)
            }
        }
    ]
    
    for scenario in coherence_scenarios:
        coherence_type = scenario['type']
        breadth_metrics = scenario['metrics']
        
        print(f"--- {coherence_type} ---")
        
        coherence_analysis = {'coherence_type': coherence_type}
        
        result = analyzer.calculate_optimal_weights_enhanced(
            relative_strength=relative_strength,
            stage='稳定期',
            breadth_metrics=breadth_metrics,
            risk_level='medium',
            coherence_analysis=coherence_analysis
        )
        
        print(f"分配策略: {result.allocation_reason}")
        print(f"权重分配:")
        for sector, weight in result.weights.items():
            if weight > 0.01:
                print(f"  {sector}: {weight:.1%}")
        
        print(f"一致性调整:")
        adj = result.coherence_adjustment
        print(f"  集中度因子: {adj['concentration_factor']:.2f}")
        print(f"  最大单一权重: {adj['max_single_weight']:.1%}")
        print(f"  分散化要求: {adj['diversification_requirement']}")
        
        print(f"备选方案数: {len(result.alternative_allocations)}")
        print()
    
    return True


def test_risk_constraints():
    """测试风险约束功能"""
    print("\n=== 测试风险约束功能 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 创建高风险场景数据
    sectors = ['Sector1', 'Sector2', 'Sector3', 'Sector4', 'Sector5']
    relative_strength = pd.Series([0.9, 0.8, 0.7, 0.3, 0.1], index=sectors)
    
    breadth_metrics = {
        'Sector1': MockBreadthMetrics(85, 0.7),
        'Sector2': MockBreadthMetrics(75, 0.6),
        'Sector3': MockBreadthMetrics(65, 0.5),
        'Sector4': MockBreadthMetrics(45, 0.4),
        'Sector5': MockBreadthMetrics(25, 0.3)
    }
    
    # 测试不同风险等级
    risk_levels = ['low', 'medium', 'high']
    
    for risk_level in risk_levels:
        print(f"--- {risk_level.upper()} 风险等级 ---")
        
        result = analyzer.calculate_optimal_weights_enhanced(
            relative_strength=relative_strength,
            stage='稳定期',
            breadth_metrics=breadth_metrics,
            risk_level=risk_level,
            coherence_analysis={'coherence_type': 'moderate_coherence'}
        )
        
        print(f"权重分配:")
        total_weight = 0
        for sector, weight in result.weights.items():
            if weight > 0.001:
                print(f"  {sector}: {weight:.1%}")
                total_weight += weight
        
        print(f"总权重: {total_weight:.1%}")
        print(f"现金比例: {1-total_weight:.1%}")
        
        constraints = result.risk_constraints
        print(f"约束条件:")
        print(f"  最大单一: {constraints['max_single_weight']:.1%}")
        print(f"  最大前三: {constraints['max_top3_weight']:.1%}")
        print(f"  现金储备: {constraints['cash_reserve']:.1%}")
        
        # 验证约束是否被遵守
        weights_series = result.weights[result.weights > 0]
        if len(weights_series) > 0:
            max_weight = weights_series.max()
            top3_weight = weights_series.nlargest(3).sum()
            
            print(f"实际情况:")
            print(f"  实际最大单一: {max_weight:.1%}")
            print(f"  实际前三权重: {top3_weight:.1%}")
            
            # 检查约束违反
            violations = []
            if max_weight > constraints['max_single_weight'] + 0.001:
                violations.append("单一权重超限")
            if top3_weight > constraints['max_top3_weight'] + 0.001:
                violations.append("前三权重超限")
            
            if violations:
                print(f"⚠️  约束违反: {', '.join(violations)}")
            else:
                print(f"✅ 约束遵守良好")
        
        print()
    
    return True


def test_alternative_allocations():
    """测试备选分配方案"""
    print("\n=== 测试备选分配方案 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    sectors = ['A', 'B', 'C', 'D', 'E', 'F']
    relative_strength = pd.Series([0.8, 0.7, 0.6, 0.5, 0.4, 0.3], index=sectors)
    
    breadth_metrics = {sector: MockBreadthMetrics(70 - i*5, 0.6 - i*0.05) 
                      for i, sector in enumerate(sectors)}
    
    result = analyzer.calculate_optimal_weights_enhanced(
        relative_strength=relative_strength,
        stage='稳定期',
        breadth_metrics=breadth_metrics,
        risk_level='medium',
        coherence_analysis={'coherence_type': 'moderate_coherence'}
    )
    
    print(f"主要分配方案:")
    for sector, weight in result.weights.items():
        if weight > 0.01:
            print(f"  {sector}: {weight:.1%}")
    
    print(f"\n备选分配方案:")
    for strategy, weights in result.alternative_allocations.items():
        print(f"--- {strategy.upper()} 策略 ---")
        for sector, weight in weights.items():
            if weight > 0.01:
                print(f"  {sector}: {weight:.1%}")
        
        # 计算策略特征
        non_zero = weights[weights > 0]
        if len(non_zero) > 0:
            max_weight = non_zero.max()
            num_positions = len(non_zero)
            print(f"  特征: {num_positions}个持仓, 最大权重{max_weight:.1%}")
        print()
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T3.1 优化权重分配算法\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础权重分配
        test_results.append(("基础权重分配", test_weight_allocation_basic()))
        
        print("=" * 60)
        
        # 2. 测试一致性影响
        test_results.append(("一致性影响", test_coherence_impact()))
        
        print("=" * 60)
        
        # 3. 测试风险约束
        test_results.append(("风险约束", test_risk_constraints()))
        
        print("=" * 60)
        
        # 4. 测试备选方案
        test_results.append(("备选分配方案", test_alternative_allocations()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T3.1 权重分配算法优化完成！")
            print("🚀 主要改进:")
            print("  - 一致性感知的权重分配")
            print("  - 动态风险约束调整")
            print("  - 多种备选分配策略")
            print("  - 详细的分散度分析")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
