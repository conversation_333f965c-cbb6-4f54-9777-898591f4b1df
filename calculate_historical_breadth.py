#!/usr/bin/env python3
"""
计算历史市场广度数据
使用真实股价数据计算过去30天的市场广度指标
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def calculate_historical_breadth_using_hist_data():
    """使用hist_data.py计算真实的90天历史数据"""
    print("🚀 使用hist_data.py计算90天历史市场广度数据")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    print("📊 将计算以下内容:")
    print("   ✅ 90个工作日的历史数据")
    print("   ✅ 所有配置的市场/板块")
    print("   ✅ 完整的市场广度指标")
    print("   ✅ 使用真实股价API数据")
    print("   ✅ 多进程并行计算")
    print()

    # 确认执行
    confirm = input("是否开始计算90天历史市场广度数据? (y/N): ").strip().lower()
    if confirm != 'y':
        print("👋 用户取消操作")
        return False

    try:
        from hist_data import main as hist_main

        print("\n🔄 开始计算历史市场广度数据...")
        print("⚠️  这将需要较长时间（可能20-40分钟），请耐心等待...")
        print("💡 系统将自动下载股价数据并计算市场广度指标")
        print()

        start_time = time.time()

        # 执行历史数据计算
        hist_main()

        duration = time.time() - start_time

        print(f"\n🎉 历史市场广度计算完成!")
        print(f"⏱️  总耗时: {duration:.1f} 秒")

        # 聚合月度数据
        print("\n📅 开始聚合月度数据...")
        monthly_success = aggregate_monthly_data_from_daily()
        if monthly_success:
            print("✅ 月度数据聚合完成")
        else:
            print("⚠️  月度数据聚合失败")

        # 验证计算结果
        verify_historical_data()

        return True

    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def aggregate_monthly_data_from_daily():
    """从日数据聚合月度数据"""
    print("📊 从日数据聚合月度市场广度数据...")

    try:
        import pymysql
        from db_settings import get_default_db_config

        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        # 获取需要聚合的月份
        cursor.execute("""
        SELECT DISTINCT YEAR(recorded_at) as year, MONTH(recorded_at) as month
        FROM market_breadth_metrics_gics
        WHERE timeframe = '1d'
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        ORDER BY year DESC, month DESC
        """)

        months_to_aggregate = cursor.fetchall()

        if not months_to_aggregate:
            print("⚠️  没有找到可聚合的日数据")
            conn.close()
            return False

        print(f"📅 找到 {len(months_to_aggregate)} 个月需要聚合")

        success_count = 0

        for year, month in months_to_aggregate:
            print(f"📊 聚合 {year}-{month:02d} 月度数据...")

            # 删除该月的旧月度数据
            cursor.execute("""
            DELETE FROM market_breadth_metrics_gics
            WHERE timeframe = '1M'
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            """, (year, month))

            # 聚合该月的日数据
            cursor.execute("""
            SELECT market,
                   AVG(total_stocks) as avg_total_stocks,
                   AVG(advances) as avg_advances,
                   AVG(declines) as avg_declines,
                   AVG(unchanged) as avg_unchanged,
                   AVG(new_highs_52w) as avg_new_highs,
                   AVG(new_lows_52w) as avg_new_lows,
                   AVG(avg_rsi) as avg_rsi,
                   AVG(internal_health) as avg_internal_health,
                   COUNT(*) as trading_days,
                   MAX(recorded_at) as latest_date
            FROM market_breadth_metrics_gics
            WHERE timeframe = '1d'
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            GROUP BY market
            HAVING COUNT(*) >= 5  -- 至少5个交易日
            """, (year, month))

            monthly_data = cursor.fetchall()

            if monthly_data:
                # 插入月度聚合数据
                insert_sql = """
                INSERT INTO market_breadth_metrics_gics (
                    market, timeframe, total_stocks, advances, declines, unchanged,
                    new_highs_52w, new_lows_52w, avg_rsi, internal_health, recorded_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                for row in monthly_data:
                    market, avg_total, avg_adv, avg_dec, avg_unch, avg_highs, avg_lows, avg_rsi, avg_health, trading_days, latest_date = row

                    # 使用该月最后一个交易日作为记录时间
                    record_time = latest_date.replace(hour=16, minute=0, second=0)

                    cursor.execute(insert_sql, (
                        market, '1M',
                        round(avg_total), round(avg_adv), round(avg_dec), round(avg_unch),
                        round(avg_highs), round(avg_lows), round(avg_rsi, 2), round(avg_health, 2),
                        record_time
                    ))

                conn.commit()
                print(f"✅ {year}-{month:02d} 聚合完成，{len(monthly_data)} 个市场")
                success_count += 1
            else:
                print(f"⚠️  {year}-{month:02d} 没有足够的日数据进行聚合")

        conn.close()

        print(f"\n📊 月度数据聚合总结:")
        print(f"   成功聚合: {success_count}/{len(months_to_aggregate)} 个月")

        return success_count > 0

    except Exception as e:
        print(f"❌ 月度数据聚合失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_historical_data():
    """验证历史数据计算结果"""
    print("\n🔍 验证历史数据计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查各时间框架的数据
        cursor.execute("""
        SELECT timeframe,
               COUNT(DISTINCT DATE(recorded_at)) as unique_dates,
               COUNT(DISTINCT market) as unique_markets,
               COUNT(*) as total_records,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 WHEN '15m' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        print("📊 历史数据统计:")
        print("-" * 80)
        print(f"{'时间框架':<8} {'天数':<6} {'市场数':<8} {'总记录':<8} {'最早':<12} {'最新':<12}")
        print("-" * 80)

        total_days = 0
        has_monthly = False
        for row in results:
            tf, days, markets, total, earliest, latest = row
            if tf == '1d':  # 重点关注日线数据
                total_days = days
            elif tf == '1M':  # 检查是否有月度数据
                has_monthly = True
            earliest_str = earliest.strftime('%m-%d') if earliest else 'N/A'
            latest_str = latest.strftime('%m-%d') if latest else 'N/A'
            status = ""
            if tf == '1M':
                status = " (聚合)" if has_monthly else ""
            print(f"{tf:<8} {days:<6} {markets:<8} {total:<8} {earliest_str:<12} {latest_str:<12}{status}")
        
        # 检查1d数据的详细情况
        if total_days > 1:
            print(f"\n📅 1d时间框架详细分析（最近10天）:")
            cursor.execute("""
            SELECT DATE(recorded_at) as date,
                   COUNT(DISTINCT market) as market_count,
                   AVG(total_stocks) as avg_stocks,
                   AVG(advances) as avg_advances,
                   AVG(declines) as avg_declines,
                   AVG(avg_rsi) as avg_rsi
            FROM market_breadth_metrics_gics 
            WHERE timeframe = '1d'
            GROUP BY DATE(recorded_at)
            ORDER BY date DESC
            LIMIT 10
            """)
            
            daily_results = cursor.fetchall()
            
            print("-" * 90)
            print(f"{'日期':<12} {'市场数':<8} {'平均股票':<10} {'平均涨':<8} {'平均跌':<8} {'平均RSI':<10}")
            print("-" * 90)
            
            for row in daily_results:
                date, market_count, avg_stocks, avg_advances, avg_declines, avg_rsi = row
                print(f"{date:<12} {market_count:<8} {avg_stocks:<10.0f} {avg_advances:<8.0f} {avg_declines:<8.0f} {avg_rsi:<10.1f}")
        
        conn.close()
        
        print(f"\n📈 数据质量评估:")
        if total_days >= 30:
            print("✅ 数据充足，可以进行完整的30天轮动分析")
        elif total_days >= 7:
            print("⚠️  数据较少，建议等待更多数据积累")
        else:
            print("❌ 数据不足，无法进行有效的轮动分析")
        
        return total_days >= 7
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def run_single_timeframe_calculation():
    """运行单个时间框架的计算（仅1d）"""
    print("🔄 运行单个时间框架计算（仅1d数据）...")
    
    try:
        from market_breadth_calculator import calculate_all_markets_breadth

        print("🔄 计算1d时间框架的市场广度数据...")

        # 直接调用独立函数计算1d时间框架
        result = calculate_all_markets_breadth('1d', num_processes=4, save_to_db=True)

        if result:
            print(f"✅ 1d时间框架计算完成，成功计算 {len(result)} 个市场")
            return True
        else:
            print("⚠️  1d时间框架计算返回空结果")
            return False
        
    except Exception as e:
        print(f"❌ 单时间框架计算失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 历史市场广度数据计算工具")
    print("使用真实股价数据计算历史市场广度指标")
    print()
    
    print("📋 计算选项:")
    print("1. 90天历史数据计算 (推荐) - 使用hist_data.py计算完整历史数据")
    print("2. 仅1d时间框架计算 - 只计算当前日线数据")
    print("3. 退出")
    print()
    print("💡 选项1说明:")
    print("   - 计算90个工作日的完整历史数据")
    print("   - 使用真实股价API获取数据")
    print("   - 自动聚合月度数据")
    print("   - 多进程并行计算，提高效率")
    print("   - 为30天轮动分析提供充足数据")

    while True:
        try:
            choice = input("\n请选择 (1-3): ").strip()

            if choice == '1':
                print("\n🔧 选择90天历史数据计算...")
                success = calculate_historical_breadth_using_hist_data()
                break

            elif choice == '2':
                print("\n🔧 选择仅1d时间框架计算...")
                success = run_single_timeframe_calculation()
                break

            elif choice == '3':
                print("👋 退出程序")
                return False

            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            return False
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    if success:
        print("\n🎉 历史数据计算成功!")
        print("\n💡 下一步操作:")
        print("1. 运行: python calculate_30day_rotation.py")
        print("2. 计算30天板块轮动指标")
        print("3. 启动Web界面查看完整轮动分析")
        print("4. 现在轮动指标应该都有真实意义了!")
    else:
        print("\n❌ 历史数据计算失败")
        print("💡 故障排除:")
        print("1. 检查网络连接和API配置")
        print("2. 确认数据库连接正常")
        print("3. 查看错误日志信息")
    
    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
