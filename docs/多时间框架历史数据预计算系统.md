# 多时间框架历史数据预计算系统

## 🎯 系统概述

针对您提出的**3个月历史数据计算**需求，我设计了一个全新的多时间框架预计算系统，支持从5分钟到月线的所有时间框架的历史数据预计算和管理。

### 🔧 **解决的核心问题**

1. **❌ 原系统缺陷**：
   - 仅支持日线数据，无时间框架字段
   - 52周指标计算错误（使用收盘价而非高低价）
   - 增量更新效率低下
   - 关键指标缺失（days_from_high/low未实现）

2. **✅ 新系统优势**：
   - 支持6个时间框架：5m, 15m, 1h, 1d, 1w, 1M
   - 正确的技术指标计算逻辑
   - 高效的增量更新算法
   - 完整的历史数据管理

## 🏗️ 系统架构

### **数据库设计**

```sql
-- 多时间框架MA指标表
CREATE TABLE mtf_precomputed_ma_indicators (
    symbol VARCHAR(20),
    timeframe VARCHAR(10),      -- 新增：时间框架字段
    datetime DATETIME,          -- 新增：支持分钟级时间
    close_price, high_price, low_price, volume,
    ma5, ma10, ma20, ma50, ma200,
    above_ma5, above_ma10, above_ma20, above_ma50, above_ma200,
    rsi_14,                     -- 新增：RSI指标
    UNIQUE KEY (symbol, timeframe, datetime)
);

-- 多时间框架52周指标表
CREATE TABLE mtf_precomputed_52w_indicators (
    symbol VARCHAR(20),
    timeframe VARCHAR(10),
    datetime DATETIME,
    high_52w, low_52w,          -- 正确计算：基于high/low价格
    high_20d, low_20d,          -- 新增：20日高低点
    is_new_high_52w, is_new_low_52w,
    is_new_high_20d, is_new_low_20d,
    days_from_high_52w,         -- 修复：正确计算距离天数
    days_from_low_52w,
    days_from_high_20d,
    days_from_low_20d,
    price_position_52w,         -- 新增：价格在区间中的位置
    price_position_20d,
    UNIQUE KEY (symbol, timeframe, datetime)
);

-- 计算状态跟踪表
CREATE TABLE mtf_precomputed_status (
    symbol VARCHAR(20),
    timeframe VARCHAR(10),
    last_calculated_datetime DATETIME,
    total_records INT,
    ma_complete BOOLEAN,
    w52_complete BOOLEAN,
    UNIQUE KEY (symbol, timeframe)
);
```

### **核心组件**

1. **MTFPrecomputedSystem** (`core/mtf_precomputed_system.py`)
   - 多时间框架数据计算引擎
   - 支持历史数据批量计算
   - 智能增量更新

2. **MTFHistoricalConfig** (`config/mtf_historical_config.py`)
   - 时间框架配置管理
   - 计算策略定义
   - 预设方案配置

3. **MTFHistoricalManager** (`scripts/setup_mtf_historical_system.py`)
   - 用户友好的管理界面
   - 预设计算方案
   - 状态监控和报告

## 🚀 快速开始

### **1. 查看可用方案**

```bash
cd /home/<USER>/ltl/breadth-pulse
source .venv/bin/activate
python scripts/setup_mtf_historical_system.py --list-plans
```

**输出示例**：
```
📋 可用的历史数据计算方案:
============================================================

1. 快速启动
   描述: 快速计算最近3个月数据，适合初次使用
   时间框架: 1d, 1h
   历史月数: 3个月
   预计耗时: 30分钟

2. 标准配置
   描述: 计算所有时间框架的标准历史数据
   时间框架: 5m, 15m, 1h, 1d, 1w, 1M
   历史月数: 6个月
   预计耗时: 120分钟

💡 推荐方案:
   • 首次使用: 选择 '1. 快速启动'
   • 生产环境: 选择 '2. 标准配置'
```

### **2. 执行3个月历史数据计算**

```bash
# 方案1：使用预设方案（推荐）
python scripts/setup_mtf_historical_system.py --plan quick_start

# 方案2：自定义计算
python scripts/setup_mtf_historical_system.py --custom --timeframes 1d 1h --months 3

# 方案3：计算所有时间框架
python scripts/setup_mtf_historical_system.py --plan standard
```

### **3. 查看计算状态**

```bash
python scripts/setup_mtf_historical_system.py --status
```

**输出示例**：
```
📊 多时间框架预计算系统状态
============================================================
📈 总体统计:
   总股票数: 6,273
   总记录数: 2,847,392
   最后更新: 2025-08-02 14:30:00

📋 各时间框架状态:
时间框架   股票数       记录数   完成率             最后更新
------------------------------------------------------------
    1d    6,273    1,342,647    100.0%  2025-08-02 14:30:00
    1h    6,273      847,392     98.5%  2025-08-02 14:25:00
   15m    5,847      456,789     93.2%  2025-08-02 14:20:00
    5m    5,234      200,564     83.4%  2025-08-02 14:15:00
    1w    6,273       89,456    100.0%  2025-08-02 14:30:00
    1M    6,273       12,344    100.0%  2025-08-02 14:30:00
```

## 📊 预设计算方案详解

### **1. 快速启动 (quick_start)**
- **适用场景**：首次使用、快速验证
- **时间框架**：1d, 1h
- **历史月数**：3个月
- **预计耗时**：30分钟
- **数据量**：约50万条记录

### **2. 标准配置 (standard)**
- **适用场景**：生产环境、完整功能
- **时间框架**：5m, 15m, 1h, 1d, 1w, 1M
- **历史月数**：6个月
- **预计耗时**：120分钟
- **数据量**：约300万条记录

### **3. 全面配置 (comprehensive)**
- **适用场景**：完整历史数据、研究分析
- **时间框架**：全部
- **历史月数**：12个月
- **预计耗时**：300分钟
- **数据量**：约600万条记录

### **4. 日内重点 (intraday_focus)**
- **适用场景**：日内交易、短期分析
- **时间框架**：5m, 15m, 1h
- **历史月数**：3个月
- **预计耗时**：60分钟

### **5. 长期重点 (long_term_focus)**
- **适用场景**：长期投资、趋势分析
- **时间框架**：1d, 1w, 1M
- **历史月数**：24个月
- **预计耗时**：90分钟

## 🔧 技术指标计算

### **移动平均线 (MA)**
- **支持周期**：5, 10, 20, 50, 200
- **计算方法**：简单移动平均
- **位置判断**：above_ma5, above_ma10, above_ma20, above_ma50, above_ma200

### **52周新高新低**
- **全时间框架支持**：按领导要求，所有时间框架都支持52周计算
- **数据点映射**：
  - 5分钟：20,280个数据点（52周 × 5天 × 6.5小时 × 12个5分钟）
  - 15分钟：6,760个数据点（52周 × 5天 × 6.5小时 × 4个15分钟）
  - 1小时：1,690个数据点（52周 × 5天 × 6.5小时）
  - 日线：252个数据点（52周 × 5天）
  - 周线：52个数据点（52周）
  - 月线：12个数据点（12个月，从日线数据聚合生成）
- **正确计算**：基于high/low价格（修复原系统错误）
- **新高判断**：`today_high >= high_52w * 0.999`
- **新低判断**：`today_low <= low_52w * 1.001`
- **距离计算**：正确实现days_from_high/low

### **20日新高新低**
- **适配时间框架**：
  - 5分钟：288个数据点（20日 × 24小时 × 12个5分钟）
  - 15分钟：96个数据点
  - 1小时：24个数据点
  - 日线：20个数据点

### **RSI指标**
- **周期**：14
- **计算方法**：标准RSI算法
- **范围**：0-100

### **价格位置指标**
- **52周位置**：`(close - low_52w) / (high_52w - low_52w)`
- **20日位置**：`(close - low_20d) / (high_20d - low_20d)`
- **范围**：0-1（0=最低点，1=最高点）

## ⚡ 性能优化

### **计算策略**
1. **最近密集型**：重点计算最近数据（5m, 15m）
2. **平衡型**：历史和最近数据并重（1h）
3. **全面型**：完整历史数据计算（1d, 1w, 1M）

### **并行处理**
- **股票并行**：同时处理多只股票
- **批量插入**：1000条记录批量写入
- **连接池**：复用数据库连接

### **内存管理**
- **分批处理**：避免内存溢出
- **数据清理**：及时释放内存
- **压缩存储**：优化磁盘空间

## 🔄 增量更新

### **更新策略**
```bash
# 增量更新所有时间框架
python scripts/setup_mtf_historical_system.py --update

# 增量更新指定时间框架
python scripts/setup_mtf_historical_system.py --update --timeframes 1d 1h
```

### **更新频率**
- **5分钟/15分钟**：每小时更新
- **1小时**：每日更新
- **日线**：每日更新
- **周线**：每周更新
- **月线**：每月更新

## 📈 数据查询

### **Python API**
```python
from core.mtf_precomputed_system import MTFPrecomputedSystem

system = MTFPrecomputedSystem()

# 获取预计算数据
data = system.get_precomputed_data(
    symbols=['AAPL', 'MSFT'],
    timeframe='1h',
    start_date='2025-05-01',
    end_date='2025-08-01'
)

# 数据格式：Dict[symbol, DataFrame]
aapl_data = data['AAPL']
print(aapl_data.columns)
# ['close', 'high', 'low', 'volume', 'ma5', 'ma10', 'ma20', 'ma50', 'ma200',
#  'above_ma5', 'above_ma10', 'above_ma20', 'above_ma50', 'above_ma200',
#  'rsi_14', 'high_52w', 'low_52w', 'is_new_high_52w', 'is_new_low_52w',
#  'days_from_high_52w', 'days_from_low_52w', 'price_position_52w']
```

### **SQL查询**
```sql
-- 查询某股票的1小时MA数据
SELECT datetime, close_price, ma50, ma200, above_ma50, above_ma200
FROM mtf_precomputed_ma_indicators 
WHERE symbol = 'AAPL' AND timeframe = '1h'
AND datetime >= '2025-05-01'
ORDER BY datetime;

-- 查询新高股票
SELECT symbol, datetime, close_price, high_52w
FROM mtf_precomputed_52w_indicators 
WHERE timeframe = '1d' AND is_new_high_52w = 1
AND datetime >= CURDATE();
```

## 🚨 监控和维护

### **系统监控**
```bash
# 查看系统状态
python scripts/setup_mtf_historical_system.py --status

# 检查数据完整性
python -c "
from core.mtf_precomputed_system import MTFPrecomputedSystem
system = MTFPrecomputedSystem()
status = system.get_system_status()
print(f'总记录数: {status[\"total_records\"]:,}')
"
```

### **数据维护**
- **自动清理**：超过保留期的数据自动删除
- **数据验证**：定期检查数据完整性
- **备份策略**：重要数据定期备份

## 💡 最佳实践

### **首次部署**
1. 执行 `--plan quick_start` 验证系统
2. 根据需求选择合适的预设方案
3. 配置定时任务进行增量更新

### **生产环境**
1. 使用 `--plan standard` 或 `--plan comprehensive`
2. 配置监控告警
3. 定期检查系统状态

### **性能调优**
1. 根据硬件资源调整批次大小
2. 优化数据库索引
3. 监控内存和磁盘使用

---

## 🎯 总结

这个新的多时间框架历史数据预计算系统完全解决了原系统的缺陷，并提供了：

1. ✅ **完整的多时间框架支持**
2. ✅ **正确的技术指标计算**
3. ✅ **高效的历史数据管理**
4. ✅ **用户友好的操作界面**
5. ✅ **灵活的配置选项**

特别针对您的**3个月历史数据计算**需求，推荐使用 `--plan quick_start` 方案，30分钟内即可完成计算。
