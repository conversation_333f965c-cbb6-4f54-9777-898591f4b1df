# 52周指标全时间框架支持说明

## 📋 需求背景

根据领导要求，所有时间框架（5分钟、15分钟、1小时、日线、周线、月线）都需要支持52周新高新低指标的计算。

## ✅ 技术实现确认

### **1. 数据点计算**

| 时间框架 | 52周对应数据点数 | 计算公式 | 存储需求 |
|---------|-----------------|----------|----------|
| 5分钟 | 20,280个 | 52周 × 5天 × 6.5小时 × 12个5分钟 | 约20万条/股票 |
| 15分钟 | 6,760个 | 52周 × 5天 × 6.5小时 × 4个15分钟 | 约7万条/股票 |
| 1小时 | 1,690个 | 52周 × 5天 × 6.5小时 | 约1.7万条/股票 |
| 日线 | 252个 | 52周 × 5天 | 约252条/股票 |
| 周线 | 52个 | 52周 | 约52条/股票 |
| 月线 | 12个 | 12个月 | 约12条/股票 |

### **2. 配置更新**

已更新 `config/mtf_historical_config.py`：

```python
# 5分钟时间框架
'5m': {
    'supports_52w': True,        # ✅ 启用52周计算
    'max_history_days': 400,     # 增加历史数据范围
    'min_data_points': 20280,    # 52周数据点要求
    'batch_size': 10,            # 减小批次避免内存问题
}

# 15分钟时间框架
'15m': {
    'supports_52w': True,        # ✅ 启用52周计算
    'max_history_days': 400,
    'min_data_points': 6760,
    'batch_size': 20,
}

# 1小时时间框架
'1h': {
    'supports_52w': True,        # ✅ 启用52周计算
    'max_history_days': 400,
    'min_data_points': 1690,
    'batch_size': 30,
}
```

### **3. 计算逻辑实现**

在 `core/mtf_precomputed_system.py` 中实现了动态窗口计算：

```python
# 根据时间框架计算52周对应的数据点数
window_52w_map = {
    '5m': 20280,    # 52周 × 5天 × 6.5小时 × 12个5分钟
    '15m': 6760,    # 52周 × 5天 × 6.5小时 × 4个15分钟
    '1h': 1690,     # 52周 × 5天 × 6.5小时
    '1d': 252,      # 52周 × 5天
    '1w': 52,       # 52周
    '1M': 12        # 12个月
}

window_52w = window_52w_map.get(timeframe, 252)

if len(df) >= window_52w:
    df['high_52w'] = df['high'].rolling(window=window_52w, min_periods=window_52w).max()
    df['low_52w'] = df['low'].rolling(window=window_52w, min_periods=window_52w).min()
    df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
    df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
```

## 📊 计算结果示例

### **5分钟时间框架的52周指标**

```python
# 查询5分钟的52周新高
SELECT symbol, datetime, close_price, high_price, high_52w, is_new_high_52w
FROM mtf_precomputed_52w_indicators 
WHERE timeframe = '5m' AND is_new_high_52w = 1
AND datetime >= '2025-08-01'
ORDER BY datetime DESC;

# 结果示例：
# AAPL, 2025-08-02 14:35:00, 185.50, 185.60, 185.60, 1
# MSFT, 2025-08-02 14:30:00, 420.25, 420.30, 420.30, 1
```

### **数据完整性验证**

```python
# 验证5分钟数据的52周计算
from core.mtf_precomputed_system import MTFPrecomputedSystem

system = MTFPrecomputedSystem()
data = system.get_precomputed_data(['AAPL'], '5m', '2025-07-01', '2025-08-01')

aapl_5m = data['AAPL']
print("5分钟52周指标列：")
print([col for col in aapl_5m.columns if '52w' in col])
# ['high_52w', 'low_52w', 'is_new_high_52w', 'is_new_low_52w', 
#  'days_from_high_52w', 'days_from_low_52w', 'price_position_52w']

print(f"数据点数量: {len(aapl_5m)}")
print(f"52周新高次数: {aapl_5m['is_new_high_52w'].sum()}")
print(f"52周新低次数: {aapl_5m['is_new_low_52w'].sum()}")
```

## ⚡ 性能影响评估

### **存储需求**

假设6000只股票：

| 时间框架 | 单股票记录数 | 总记录数 | 存储估算 |
|---------|-------------|----------|----------|
| 5分钟 | 20万条 | 12亿条 | ~120GB |
| 15分钟 | 7万条 | 4.2亿条 | ~42GB |
| 1小时 | 1.7万条 | 1亿条 | ~10GB |
| 日线 | 252条 | 150万条 | ~150MB |
| 周线 | 52条 | 31万条 | ~31MB |
| 月线 | 12条 | 7万条 | ~7MB |
| **总计** | - | **约17.5亿条** | **~172GB** |

### **计算时间估算**

| 时间框架 | 预计计算时间 | 内存需求 | 建议批次大小 |
|---------|-------------|----------|-------------|
| 5分钟 | 180-240分钟 | 8GB+ | 10只股票/批次 |
| 15分钟 | 90-120分钟 | 4GB+ | 20只股票/批次 |
| 1小时 | 45-60分钟 | 2GB+ | 30只股票/批次 |
| 日线 | 15-30分钟 | 1GB+ | 50只股票/批次 |
| 周线 | 10-20分钟 | 512MB+ | 100只股票/批次 |
| 月线 | 5-10分钟 | 256MB+ | 100只股票/批次 |

## 🚀 执行计划

### **阶段1：验证计算（建议先执行）**

```bash
# 测试少量股票的5分钟52周计算
python scripts/setup_mtf_historical_system.py --custom \
  --timeframes 5m --months 3 \
  --symbols AAPL MSFT GOOGL TSLA AMZN
```

### **阶段2：全量计算**

```bash
# 执行所有时间框架的52周计算
python scripts/setup_mtf_historical_system.py --plan comprehensive
```

### **阶段3：验证结果**

```bash
# 检查计算状态
python scripts/setup_mtf_historical_system.py --status

# 验证5分钟52周数据
python -c "
from core.mtf_precomputed_system import MTFPrecomputedSystem
system = MTFPrecomputedSystem()
status = system.get_system_status()
print('5分钟时间框架状态:')
print(status['timeframes']['5m'])
"
```

## 📋 质量保证

### **数据验证检查**

1. **数据完整性**：确保每个时间框架都有52周相关字段
2. **计算正确性**：验证52周最高最低价的计算逻辑
3. **新高新低判断**：确认is_new_high_52w和is_new_low_52w的准确性
4. **时间一致性**：验证不同时间框架的时间戳对齐

### **性能监控**

1. **内存使用**：监控计算过程中的内存消耗
2. **计算时间**：记录各时间框架的实际计算耗时
3. **存储空间**：监控数据库存储空间增长
4. **查询性能**：测试52周指标的查询响应时间

## 💡 使用建议

### **对于开发团队**

1. **分阶段执行**：建议先测试小批量数据，验证无误后再全量计算
2. **监控资源**：密切关注服务器内存、CPU和磁盘使用情况
3. **备份策略**：在大量计算前做好数据备份
4. **错误处理**：准备好处理计算过程中可能出现的异常情况

### **对于业务使用**

1. **5分钟52周新高**：可用于识别盘中突破关键阻力位的股票
2. **15分钟52周新低**：可用于识别短期超跌反弹机会
3. **1小时52周位置**：可用于判断股票在长期区间中的相对位置
4. **跨时间框架分析**：结合不同时间框架的52周指标进行综合分析

## ✅ 确认清单

- [x] 配置文件已更新，所有时间框架支持52周计算
- [x] 核心计算逻辑已实现动态窗口大小
- [x] 数据库表结构支持多时间框架52周指标
- [x] 文档已更新，明确说明全时间框架支持
- [x] 性能影响已评估，提供了优化建议
- [x] 执行计划已制定，支持分阶段实施

---

**总结**：按照领导要求，系统现在完全支持所有时间框架的52周新高新低计算。虽然5分钟和15分钟的52周指标在技术分析中使用较少，但系统已经具备了完整的计算能力和数据存储支持。
