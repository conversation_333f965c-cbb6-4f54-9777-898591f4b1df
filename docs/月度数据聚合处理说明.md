# 月度数据聚合处理说明

## 🎯 问题背景

您的观察完全正确！新的多时间框架预计算系统中，**月度数据无法直接下载**，因为：

1. **数据源限制**：`utils.py`中的`download_hist_price`函数不支持月度interval
2. **API限制**：底层数据API通常不提供月度K线数据
3. **技术现实**：月度数据通常需要从日线数据聚合生成

## 🔧 解决方案：日线数据聚合

### **新系统的月度数据处理流程**

```python
# 1. 检测月度时间框架
if timeframe == '1M':
    # 2. 获取日线数据
    daily_data = download_hist_price(
        symbols=batch_symbols,
        interval='1d',  # 使用日线数据
        start=start_date,
        end=end_date
    )
    
    # 3. 按月聚合
    monthly_data = daily_data.resample('M').agg({
        'high': 'max',      # 月最高价
        'low': 'min',       # 月最低价  
        'close': 'last',    # 月收盘价
        'volume': 'sum'     # 月成交量
    })
```

### **聚合逻辑详解**

| 字段 | 聚合方法 | 说明 |
|------|----------|------|
| **high** | `max()` | 该月所有交易日的最高价 |
| **low** | `min()` | 该月所有交易日的最低价 |
| **close** | `last()` | 该月最后一个交易日的收盘价 |
| **volume** | `sum()` | 该月所有交易日的成交量总和 |

### **技术指标计算**

月度技术指标基于聚合后的月度OHLCV数据计算：

```python
# 月度MA指标
monthly_df['ma5'] = monthly_df['close'].rolling(5).mean()    # 5个月MA
monthly_df['ma10'] = monthly_df['close'].rolling(10).mean()  # 10个月MA
monthly_df['ma20'] = monthly_df['close'].rolling(20).mean()  # 20个月MA

# 月度52周指标（实际是52个月 = 4.3年）
monthly_df['high_52w'] = monthly_df['high'].rolling(52).max()
monthly_df['low_52w'] = monthly_df['low'].rolling(52).min()

# 月度新高新低判断
monthly_df['is_new_high_52w'] = monthly_df['high'] >= monthly_df['high_52w'] * 0.999
monthly_df['is_new_low_52w'] = monthly_df['low'] <= monthly_df['low_52w'] * 1.001
```

## 📊 与现有系统的一致性

### **现有系统的月度处理**

现有系统已经在使用类似的聚合方法：

1. **monthly_aggregator.py**：专门的月度聚合器
2. **市场广度月度数据**：从日线市场广度数据聚合
3. **run_all_timeframes.py**：特殊处理月度计算

### **新系统的改进**

```python
# 新系统的月度数据处理
def _aggregate_monthly_data_from_daily(self, symbols, start_date, end_date):
    """从日线数据聚合月度数据"""
    
    # 1. 获取日线数据
    daily_data = download_hist_price(symbols, '1d', start_date, end_date)
    
    # 2. 按股票分别聚合
    monthly_data = {}
    for symbol, df in daily_data.items():
        # 3. 月度聚合
        monthly_df = df.resample('M').agg({
            'high': 'max',
            'low': 'min', 
            'close': 'last',
            'volume': 'sum'
        })
        monthly_data[symbol] = monthly_df
    
    return monthly_data
```

## 🎯 实际使用影响

### **对历史计算能力的影响**

您能计算的月度历史数据范围取决于：

1. **日线数据可用性**：月度数据 = 日线数据的月度聚合
2. **聚合质量**：每月至少需要15个交易日才能生成可靠的月度数据
3. **计算效率**：月度聚合比直接下载月度数据稍慢，但更准确

### **配置更新**

```python
'1M': {
    'interval': '1M',                    # 标识符
    'data_source': 'aggregated_from_daily',  # 数据来源
    'aggregation_method': 'monthly_resample', # 聚合方法
    'min_trading_days_per_month': 15,    # 每月最少交易日
    'supports_52w': True,                # 支持52月指标
}
```

## 💡 使用建议

### **1. 月度历史计算**

```bash
# 计算12个月的月度历史数据
python scripts/setup_mtf_historical_system.py --custom \
  --timeframes 1M --months 12

# 实际执行：
# 1. 下载12个月的日线数据
# 2. 按月聚合生成月度数据  
# 3. 计算月度技术指标
# 4. 存储到mtf_precomputed_*表
```

### **2. 数据质量保证**

```python
# 验证月度数据质量
def validate_monthly_data(symbol, monthly_df):
    """验证月度数据质量"""
    
    # 检查每月数据点数量
    for month_end, row in monthly_df.iterrows():
        # 确保该月有足够的交易日数据
        month_start = month_end.replace(day=1)
        daily_count = get_trading_days_count(symbol, month_start, month_end)
        
        if daily_count < 15:  # 少于15个交易日
            logger.warning(f"{symbol} {month_end.strftime('%Y-%m')} 只有{daily_count}个交易日")
```

### **3. 性能优化**

```python
# 批量聚合优化
def batch_aggregate_monthly_data(symbols, months):
    """批量聚合月度数据"""
    
    # 1. 一次性下载所有股票的日线数据
    all_daily_data = download_hist_price(symbols, '1d', start, end)
    
    # 2. 并行聚合
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for symbol, daily_df in all_daily_data.items():
            future = executor.submit(aggregate_single_symbol, symbol, daily_df)
            futures.append(future)
        
        # 3. 收集结果
        monthly_results = {}
        for future in as_completed(futures):
            symbol, monthly_df = future.result()
            monthly_results[symbol] = monthly_df
    
    return monthly_results
```

## 🔍 技术细节

### **时间对齐**

```python
# 月末对齐
monthly_df = daily_df.resample('M').agg({...})  # 自动对齐到月末

# 月初对齐（如果需要）
monthly_df = daily_df.resample('MS').agg({...})  # 对齐到月初
```

### **缺失数据处理**

```python
# 处理月度数据中的缺失值
monthly_df = monthly_df.dropna()  # 删除缺失月份

# 或者插值填充
monthly_df = monthly_df.fillna(method='ffill')  # 前向填充
```

### **数据验证**

```python
# 验证聚合结果
def validate_aggregation(daily_df, monthly_df):
    """验证聚合结果的正确性"""
    
    for month_end, monthly_row in monthly_df.iterrows():
        # 获取该月的日线数据
        month_start = month_end.replace(day=1)
        month_daily = daily_df[month_start:month_end]
        
        # 验证聚合结果
        assert monthly_row['high'] == month_daily['high'].max()
        assert monthly_row['low'] == month_daily['low'].min()
        assert monthly_row['close'] == month_daily['close'].iloc[-1]
        assert abs(monthly_row['volume'] - month_daily['volume'].sum()) < 1e-6
```

## ✅ 总结

您的观察非常准确！新系统确实需要通过日线数据聚合来生成月度数据，这是技术上的必然选择。我已经：

1. ✅ **修正了代码**：添加了月度数据聚合逻辑
2. ✅ **更新了配置**：明确标注月度数据来源
3. ✅ **完善了文档**：说明月度数据处理方式

这种方法虽然比直接下载月度数据稍慢，但能确保数据的准确性和一致性，与现有系统的处理方式保持一致。
