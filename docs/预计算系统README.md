# 预计算系统 - 快速上手指南

## 🎯 核心概念

预计算系统通过预先计算和存储关键技术指标，将市场广度分析的响应时间从**几分钟缩短到几秒钟**，特别适合高频时间框架（5分钟、15分钟、1小时）的实时分析。

### 预计算的指标
- **MA50/MA200**: 移动平均线及股价位置判断
- **52周新高新低**: 52周内新高新低统计
- **RSI**: 相对强弱指数（平均值）

## 🚀 快速开始（3步搞定）

### 第1步：初始化系统
```bash
# 首次运行，初始化预计算数据（约5-10分钟）
python scripts/setup_precomputed_system.py --init
```

### 第2步：配置定时任务
```bash
# 自动配置定时任务（Linux/Mac）
python scripts/setup_cron_jobs.py
# 选择 "4. 市宽计算配置" （推荐）或 "1. 基础配置"
```

### 第3步：验证运行
```bash
# 测试预计算系统功能
python scripts/setup_precomputed_system.py --test

# 测试多时间框架市宽计算（可选）
python scripts/run_all_timeframes.py
```

**完成！** 系统将自动每日更新数据并计算多时间框架市场广度。

## 🎯 市宽计算配置（推荐）

**市宽计算配置**包含完整的预计算系统 + 多时间框架市场广度计算：

### 自动计算的时间框架
- **5分钟**: 短期市场情绪
- **15分钟**: 日内趋势分析
- **1小时**: 中短期趋势
- **1天**: 日线趋势分析
- **1周**: 周线趋势分析
- **1月**: 月线长期趋势

### 计算流程
1. **预计算更新**: 更新MA指标和52周新高新低数据
2. **市宽计算**: 基于预计算数据，计算所有时间框架的市场广度
3. **数据存储**: 结果保存到数据库，供Web界面和API使用
4. **状态验证**: 自动检查计算结果的完整性

### 性能优势
- **预计算加速**: MA和52周指标预先计算，避免重复计算
- **并行处理**: 多进程并行计算不同市场
- **增量更新**: 只计算新增数据，节省计算资源

## ⏰ 运行时间安排

### 推荐配置方案

| 配置方案 | 更新时间 | 适用场景 | 优势 |
|---------|---------|---------|------|
| **基础配置** | 每日 5:30 AM | 常规使用 | 获取最新美股数据 |
| **下午配置** | 每日 14:30 PM | 白天工作 | 便于监控和处理 |
| **双重配置** | 5:30 AM + 14:30 PM | 高可靠性 | 双重保障，容错性强 |
| **市宽计算配置** | 预计算 + 市宽计算 | 完整分析 | 自动多时间框架分析 |
| **市宽计算配置** | 预计算 + 市宽计算 | 完整分析 | 自动多时间框架分析 |

**详细时间安排**：
| 任务类型 | 基础配置 | 下午配置 | 双重配置 |
|---------|---------|---------|---------|
| **数据更新** | 5:30 AM | 14:30 PM | 5:30 AM + 14:30 PM |
| **状态检查** | 6:00 AM | 15:00 PM | 6:00 AM + 15:00 PM |
| **系统重建** | 周日 2:00 AM | 周日 2:00 AM | 周日 2:00 AM |

### 高频配置（进阶版）
- **盘中更新**: 交易时间内每小时更新
- **实时监控**: 每4小时检查数据新鲜度
- **自动恢复**: 检测异常时自动修复

## 📊 性能对比

| 操作 | 实时计算 | 预计算系统 | 提升倍数 |
|------|----------|------------|----------|
| 5分钟广度分析 | 2-5分钟 | 3-5秒 | **30-100x** |
| 1小时广度分析 | 1-3分钟 | 2-4秒 | **20-50x** |
| 日线广度分析 | 30-60秒 | 1-2秒 | **15-30x** |
| 多时间框架分析 | 5-10分钟 | 10-15秒 | **20-40x** |

## 🛠️ 常用命令

### 日常维护
```bash
# 检查预计算系统状态
python scripts/setup_precomputed_system.py --status

# 手动更新预计算数据
python scripts/setup_precomputed_system.py --update

# 手动运行多时间框架市宽计算
python scripts/run_all_timeframes.py

# 系统监控
python scripts/monitor_precomputed.py

# 持续监控（每5分钟检查一次）
python scripts/monitor_precomputed.py --continuous --interval 300
```

### 市宽计算相关
```bash
# 运行所有时间框架的市宽计算
python scripts/run_all_timeframes.py

# 测试单个时间框架（如1小时）
python scripts/test_1h_breadth.py

# 查看市宽计算结果
python -c "
import sys, os
sys.path.insert(0, 'core')
from market_breadth_calculator import calculate_all_markets_breadth
results = calculate_all_markets_breadth('1d', save_to_db=False)
print(f'计算了 {len(results)} 个市场')
"
```

### 故障排除
```bash
# 完整重建（解决大部分问题）
python scripts/setup_precomputed_system.py --init

# 查看详细日志
tail -f logs/daily_update.log

# 监控系统健康度
python scripts/monitor_precomputed.py --json
```

## 📁 文件结构

```
breadth-pulse/
├── scripts/
│   ├── setup_precomputed_system.py    # 主要管理脚本
│   ├── setup_cron_jobs.py            # 定时任务配置
│   └── monitor_precomputed.py         # 系统监控
├── core/
│   ├── precomputed_indicators.py      # 预计算核心逻辑
│   └── precomputed_query.py          # 数据查询接口
├── logs/                              # 日志文件
│   ├── daily_update.log              # 日常更新日志
│   ├── status_check.log              # 状态检查日志
│   └── monitor.log                   # 监控日志
└── docs/
    ├── 预计算系统运行文档.md          # 详细文档
    └── 定时任务配置指南.md            # 配置指南
```

## 🔧 配置选项

### 数据库配置
预计算系统使用两个主要数据表：
- `precomputed_ma_indicators`: MA指标数据
- `precomputed_52w_indicators`: 52周新高新低数据

### 时间框架支持
| 时间框架 | 预计算支持 | 说明 |
|---------|------------|------|
| 5分钟 | ❌ | 使用实时计算 |
| 15分钟 | ❌ | 使用实时计算 |
| 1小时 | ✅ | 支持MA指标 |
| 日线 | ✅ | 完整支持 |
| 周线 | ✅ | 从日线数据聚合 |
| 月线 | ✅ | 从日线数据聚合 |

## 🚨 监控告警

### 自动监控指标
- **数据新鲜度**: 数据是否及时更新
- **覆盖率**: 股票数据覆盖范围
- **系统状态**: 整体运行状况
- **响应时间**: 查询性能监控

### 告警触发条件
- 数据超过6小时未更新
- 股票覆盖率低于1000只
- 系统状态异常
- 查询响应时间超过1秒

## 💡 最佳实践

### 1. 运行时间选择
- **最佳**: 美股收盘后30分钟（北京时间5:30）
- **避免**: 交易高峰期和系统维护时间
- **备用**: 如果主要时间失败，6小时内补充运行

### 2. 资源要求
- **内存**: 至少4GB可用内存
- **磁盘**: 预留10GB空间
- **网络**: 稳定连接用于获取股价数据

### 3. 故障恢复
```bash
# 自动恢复流程
1. 检测异常 → 2. 尝试增量更新 → 3. 完整重建 → 4. 人工干预
```

## 🔍 常见问题

### Q: 初始化失败怎么办？
**A**: 检查数据库连接、网络状态和磁盘空间，然后重新运行初始化。

### Q: 数据不新鲜怎么办？
**A**: 运行 `python scripts/setup_precomputed_system.py --update` 手动更新。

### Q: 查询速度慢怎么办？
**A**: 检查数据库索引，考虑增加内存或清理过期数据。

### Q: 如何在Windows上配置？
**A**: 使用任务计划程序，参考《定时任务配置指南.md》中的Windows部分。

## 📞 技术支持

### 故障排查顺序
1. 运行状态检查: `python scripts/setup_precomputed_system.py --status`
2. 查看系统日志: `tail -f logs/daily_update.log`
3. 检查数据库连接
4. 验证网络连接
5. 重新初始化: `python scripts/setup_precomputed_system.py --init`

### 日志文件位置
- 主要日志: `logs/daily_update.log`
- 状态日志: `logs/status_check.log`
- 监控日志: `logs/monitor.log`
- 错误日志: `logs/error.log`

## 🎉 成功指标

系统正常运行时，您应该看到：
- ✅ 状态检查显示 "ready"
- ✅ 数据新鲜度显示 "True"
- ✅ 股票覆盖率 > 2000只
- ✅ 查询响应时间 < 100ms
- ✅ 市场广度分析在5秒内完成

---

**快速链接**:
- [详细运行文档](预计算系统运行文档.md)
- [定时任务配置](../定时任务配置指南.md)
- [系统架构说明](../README.md)

**版本**: 1.0 | **更新**: 2025-07-31 | **兼容**: breadth-pulse v4.1+
