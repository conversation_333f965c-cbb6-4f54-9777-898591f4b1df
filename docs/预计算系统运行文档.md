# 预计算系统运行文档

## 📋 系统概述

预计算系统是多时间框架板块轮动与MarketBreadth系统的核心组件，通过预先计算和存储关键技术指标，大幅提升分析效率。

### 核心功能
- **MA指标预计算**: MA50/MA200移动平均线及股价位置判断
- **52周新高新低**: 52周内新高新低统计
- **增量更新**: 只计算新增数据，避免重复计算
- **多时间框架支持**: 支持5分钟到月线的各种时间框架

## 🚀 快速开始

### 1. 系统初始化（首次运行）

```bash
# 初始化预计算系统（约需5-10分钟）
python scripts/setup_precomputed_system.py --init
```

**初始化过程**：
- 创建预计算数据表
- 计算所有股票的历史MA指标（400天）
- 计算所有股票的52周新高新低（400天）
- 验证数据完整性

### 2. 检查系统状态

```bash
# 检查预计算数据状态
python scripts/setup_precomputed_system.py --status
```

**输出示例**：
```
📊 预计算数据状态:
   整体状态: ready
   MA数据覆盖: 3247 只股票
   MA最新日期: 2025-07-31
   52周数据覆盖: 3247 只股票
   52周最新日期: 2025-07-31
   MA数据新鲜: True
   52周数据新鲜: True
```

### 3. 测试系统

```bash
# 测试预计算系统功能
python scripts/setup_precomputed_system.py --test
```

## ⏰ 运行频率建议

### 推荐运行计划

| 时间框架 | 更新频率 | 运行时间 | 命令 |
|---------|---------|---------|------|
| **日常更新** | 每日1次 | 美股收盘后 | `python scripts/setup_precomputed_system.py --update` |
| **盘中更新** | 每小时1次 | 交易时间内 | `python scripts/setup_precomputed_system.py --update` |
| **周末维护** | 每周1次 | 周末 | `python scripts/setup_precomputed_system.py --init` |

### 具体时间安排

#### 1. 日常更新（推荐）

**⚠️ 重要：时区配置说明**
- 以下时间配置基于**服务器系统时区**
- 如果服务器使用UTC时间，请使用UTC配置
- 如果服务器使用北京时间，请使用北京时间配置

**方案A：美股收盘后更新**

*北京时间配置（Asia/Shanghai）：*
```bash
# 每日美股收盘后运行（北京时间早上5:30）
30 5 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update
```

*UTC时间配置（推荐）：*
```bash
# 每日美股收盘后运行（UTC时间21:30 = 北京时间5:30）
30 21 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update
```

**方案B：下午时段更新**

*北京时间配置（Asia/Shanghai）：*
```bash
# 每日下午2:30更新（北京时间14:30）
30 14 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update
```

*UTC时间配置（推荐）：*
```bash
# 每日下午2:30更新（UTC时间6:30 = 北京时间14:30）
30 6 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update
```

**方案选择建议**：
- **方案A（UTC 21:30）**: 适合获取最新美股收盘数据，数据最新鲜
- **方案B（UTC 6:30）**: 适合白天工作时间使用，便于监控和处理异常
- **双重保障**: 可以同时配置两个时间点，确保数据更新的可靠性

**双重更新配置（UTC时间）**：
```bash
# 主要更新：美股收盘后（UTC 21:30 = 北京时间5:30）
30 21 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update >> logs/morning_update.log 2>&1

# 备用更新：下午时段（UTC 6:30 = 北京时间14:30）
30 6 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 状态检查：早上更新后（UTC 22:00 = 北京时间6:00）
0 22 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --status >> logs/morning_status.log 2>&1

# 状态检查：下午更新后（UTC 7:00 = 北京时间15:00）
0 7 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1
```

#### 2. 盘中更新（可选）
```bash
# 交易时间内每小时更新（北京时间21:30-4:30）
30 21-23,0-4 * * 1-5 cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update
```

#### 3. 周末完整重建（可选）
```bash
# 每周日凌晨2点完整重建
0 2 * * 0 cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --init
```

## 🔧 运行命令详解

### 初始化命令
```bash
python scripts/setup_precomputed_system.py --init
```
- **用途**: 首次安装或完整重建
- **耗时**: 5-10分钟
- **频率**: 首次安装 + 每周1次（可选）

### 增量更新命令
```bash
python scripts/setup_precomputed_system.py --update
```
- **用途**: 日常数据更新
- **耗时**: 30秒-2分钟
- **频率**: 每日1次或每小时1次

### 状态检查命令
```bash
python scripts/setup_precomputed_system.py --status
```
- **用途**: 检查数据状态和新鲜度
- **耗时**: 5-10秒
- **频率**: 随时可用

### 功能测试命令
```bash
python scripts/setup_precomputed_system.py --test
```
- **用途**: 验证系统功能正常
- **耗时**: 10-30秒
- **频率**: 系统维护时

## 📊 性能优化

### 数据库优化
```sql
-- 为预计算表添加索引（已自动创建）
CREATE INDEX idx_symbol_date ON precomputed_ma_indicators (symbol, date);
CREATE INDEX idx_symbol_date ON precomputed_52w_indicators (symbol, date);
```

### 批处理优化
- **MA指标**: 每批处理200只股票
- **52周指标**: 每批处理100只股票
- **并发处理**: 使用ThreadPoolExecutor提升效率

## 🚨 故障排除

### 常见问题

#### 1. 初始化失败
```bash
❌ MA指标初始化失败
```
**解决方案**:
- 检查数据库连接
- 确认磁盘空间充足
- 检查网络连接（获取股价数据）

#### 2. 数据不新鲜
```bash
⚠️ 预计算数据过期
```
**解决方案**:
```bash
# 强制更新
python scripts/setup_precomputed_system.py --update
```

#### 3. 查询性能慢
**解决方案**:
- 检查数据库索引
- 考虑增加数据库内存
- 清理过期数据

### 日志查看
```bash
# 查看预计算日志
tail -f logs/precomputed_indicators.log
```

## 🔄 自动化部署

### 使用crontab（Linux/Mac）
```bash
# 编辑crontab
crontab -e

# 添加以下内容
# 每日美股收盘后更新
30 5 * * * cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --update >> logs/cron.log 2>&1

# 每周日完整重建
0 2 * * 0 cd /path/to/breadth-pulse && python scripts/setup_precomputed_system.py --init >> logs/cron.log 2>&1
```

### 使用Windows任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器：每日5:30
4. 设置操作：启动程序
   - 程序：`python`
   - 参数：`scripts/setup_precomputed_system.py --update`
   - 起始位置：`C:\path\to\breadth-pulse`

## 📈 监控指标

### 关键指标
- **数据覆盖率**: 应覆盖所有活跃股票
- **数据新鲜度**: 最新数据不超过1个交易日
- **更新成功率**: 应保持在95%以上
- **查询响应时间**: 应在100ms以内

### 监控脚本
```python
# 简单监控脚本
from core.precomputed_query import check_precomputed_data_status

status = check_precomputed_data_status()
if status['status'] != 'ready':
    print("⚠️ 预计算系统异常")
    # 发送告警通知
else:
    print("✅ 预计算系统正常")
```

## 🎯 最佳实践

### 1. 运行时间选择
- **最佳时间**: 美股收盘后30分钟（北京时间5:30）
- **避免时间**: 交易时间内的高峰期
- **备用时间**: 如果主要时间失败，可在6小时内补充运行

### 2. 资源管理
- **内存**: 建议至少4GB可用内存
- **磁盘**: 预留至少10GB空间用于数据存储
- **网络**: 稳定的网络连接用于获取股价数据

### 3. 数据备份
```bash
# 定期备份预计算数据
mysqldump -u username -p database_name precomputed_ma_indicators precomputed_52w_indicators > backup_$(date +%Y%m%d).sql
```

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 运行状态检查命令
2. 查看系统日志
3. 检查数据库连接
4. 验证网络连接
5. 重新初始化系统（最后手段）

---

**文档版本**: 1.0  
**最后更新**: 2025-07-31  
**适用版本**: breadth-pulse v4.1+
