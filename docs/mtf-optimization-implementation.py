"""
多时间框架板块轮动系统 - 优化实现
包含所有关键函数的改进版本
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from datetime import datetime
import json
from enum import Enum

# ========================================
# 1. 配置管理系统
# ========================================

class SystemConfig:
    """集中化的系统配置管理"""
    
    # 版本信息
    VERSION = "4.2"
    
    # 参数配置
    PARAMS = {
        # 数据验证
        'min_stocks_threshold': 10,
        'data_quality_threshold': 0.8,
        
        # 动量一致性
        'coherence': {
            'direction_threshold': 0.001,
            'cv_scale': 1.0,
            'weight_dynamic_threshold': 0.6
        },
        
        # 背离检测
        'divergence': {
            'price_threshold': 0.01,
            'ad_neutral': 1.0,
            'severity_levels': {
                'extreme': 0.05,
                'high': 0.03,
                'medium': 0.015,
                'low': 0.008
            }
        },
        
        # 风险管理
        'risk': {
            'max_position': 90,
            'min_position': 20,
            'max_sector_weight': 0.4,
            'min_sector_weight': 0.05
        }
    }
    
    @classmethod
    def get_param(cls, path: str, default=None):
        """获取嵌套参数"""
        keys = path.split('.')
        value = cls.PARAMS
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value


# ========================================
# 2. 决策追踪器
# ========================================

class DecisionTracker:
    """记录决策过程，提供完整的决策路径"""
    
    def __init__(self):
        self.decisions = []
        self.current_context = {}
        
    def start_decision(self, decision_type: str, inputs: Dict):
        """开始一个新的决策过程"""
        self.current_context = {
            'type': decision_type,
            'timestamp': datetime.now(),
            'inputs': inputs,
            'steps': [],
            'outputs': {},
            'confidence_factors': {}
        }
        
    def add_step(self, step_name: str, result: Any, 
                 reasoning: str, confidence: float = 1.0):
        """添加决策步骤"""
        step = {
            'name': step_name,
            'result': result,
            'reasoning': reasoning,
            'confidence': confidence,
            'timestamp': datetime.now()
        }
        self.current_context['steps'].append(step)
        
    def add_confidence_factor(self, factor: str, value: float, impact: str):
        """添加置信度影响因素"""
        self.current_context['confidence_factors'][factor] = {
            'value': value,
            'impact': impact
        }
        
    def complete_decision(self, final_output: Dict, overall_confidence: float):
        """完成决策记录"""
        self.current_context['outputs'] = final_output
        self.current_context['overall_confidence'] = overall_confidence
        self.current_context['duration'] = (
            datetime.now() - self.current_context['timestamp']
        ).total_seconds()
        
        self.decisions.append(self.current_context)
        return self.current_context
        
    def get_decision_summary(self) -> Dict:
        """获取决策摘要，供LLM理解"""
        if not self.current_context:
            return {}
            
        return {
            'decision_type': self.current_context['type'],
            'key_inputs': self._summarize_inputs(self.current_context['inputs']),
            'decision_path': [
                {
                    'step': s['name'],
                    'result': self._summarize_result(s['result']),
                    'reasoning': s['reasoning']
                }
                for s in self.current_context['steps'][-5:]  # 最近5步
            ],
            'confidence_breakdown': self.current_context['confidence_factors'],
            'final_confidence': self.current_context.get('overall_confidence', 0)
        }
        
    def _summarize_inputs(self, inputs: Dict) -> Dict:
        """简化输入数据用于展示"""
        summary = {}
        for key, value in inputs.items():
            if isinstance(value, (int, float, str, bool)):
                summary[key] = value
            elif isinstance(value, (list, dict)):
                summary[key] = f"{type(value).__name__}[{len(value)}]"
            else:
                summary[key] = str(type(value).__name__)
        return summary
        
    def _summarize_result(self, result: Any) -> Any:
        """简化结果用于展示"""
        if isinstance(result, (int, float, str, bool)):
            return result
        elif isinstance(result, dict):
            return {k: self._summarize_result(v) for k, v in list(result.items())[:3]}
        elif isinstance(result, list):
            return f"List[{len(result)}]"
        else:
            return str(type(result).__name__)


# ========================================
# 3. 改进的动量一致性计算
# ========================================

class ImprovedMomentumCoherence:
    """改进版动量一致性计算器"""
    
    def __init__(self, decision_tracker: Optional[DecisionTracker] = None):
        self.direction_threshold = SystemConfig.get_param('coherence.direction_threshold')
        self.cv_scale = SystemConfig.get_param('coherence.cv_scale')
        self.tracker = decision_tracker
        
    def calculate_coherence(self, individual_returns: List[float]) -> Tuple[float, Dict]:
        """
        计算板块动量一致性（改进版）
        返回: (overall_coherence, details)
        """
        if self.tracker:
            self.tracker.add_step(
                'coherence_calculation_start',
                {'return_count': len(individual_returns) if individual_returns else 0},
                '开始计算板块内部动量一致性'
            )
        
        if not individual_returns or len(individual_returns) < 2:
            result = {
                'overall_coherence': 0.5,
                'direction_coherence': 0.5,
                'magnitude_coherence': 0.5,
                'coherence_type': 'insufficient_data',
                'interpretation': '数据不足，无法评估一致性',
                'sample_size': len(individual_returns) if individual_returns else 0
            }
            return 0.5, result
        
        returns_array = np.array(individual_returns)
        
        # 1. 计算方向一致性
        positive_count = np.sum(returns_array > self.direction_threshold)
        negative_count = np.sum(returns_array < -self.direction_threshold)
        neutral_count = len(returns_array) - positive_count - negative_count
        total_count = len(returns_array)
        
        direction_coherence = max(positive_count, negative_count) / total_count
        dominant_direction = 'bullish' if positive_count > negative_count else 'bearish'
        
        # 2. 计算幅度一致性
        mean_return = np.mean(returns_array)
        std_return = np.std(returns_array)
        
        if abs(mean_return) < 0.0001:
            # 使用四分位距方法
            q75, q25 = np.percentile(returns_array, [75, 25])
            iqr = q75 - q25
            median_abs = np.median(np.abs(returns_array))
            
            if median_abs > 0:
                magnitude_coherence = 1 / (1 + iqr / median_abs)
            else:
                magnitude_coherence = 0.3  # 默认低一致性
                
            magnitude_method = 'iqr_based'
        else:
            # 使用改进的变异系数法
            cv = std_return / abs(mean_return)
            # 使用tanh函数获得更平滑的映射
            magnitude_coherence = 0.5 * (1 + np.tanh(self.cv_scale * (1 - cv)))
            magnitude_coherence = np.clip(magnitude_coherence, 0, 1)
            magnitude_method = 'cv_based'
        
        # 3. 计算综合一致性（动态权重）
        if direction_coherence > 0.8:
            weight_dir = 0.4
            weight_mag = 0.6
            weight_reason = '方向高度一致，重视幅度'
        elif direction_coherence < 0.6:
            weight_dir = 0.7
            weight_mag = 0.3
            weight_reason = '方向分歧，优先考虑方向'
        else:
            weight_dir = 0.5
            weight_mag = 0.5
            weight_reason = '均衡权重'
            
        overall_coherence = weight_dir * direction_coherence + weight_mag * magnitude_coherence
        
        # 4. 判断一致性类型和生成解释
        coherence_type, interpretation, action_suggestion = self._classify_coherence(
            direction_coherence, magnitude_coherence, mean_return,
            positive_count, negative_count, total_count
        )
        
        # 5. 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'coherence_analysis',
                {
                    'direction': direction_coherence,
                    'magnitude': magnitude_coherence,
                    'overall': overall_coherence
                },
                f'{weight_reason}，使用{magnitude_method}方法计算幅度一致性',
                confidence=0.9 if len(returns_array) > 20 else 0.7
            )
        
        details = {
            'overall_coherence': round(overall_coherence, 3),
            'direction_coherence': round(direction_coherence, 3),
            'magnitude_coherence': round(magnitude_coherence, 3),
            'coherence_type': coherence_type,
            'dominant_direction': dominant_direction,
            'interpretation': interpretation,
            'action_suggestion': action_suggestion,
            'statistics': {
                'positive_ratio': round(positive_count / total_count, 3),
                'negative_ratio': round(negative_count / total_count, 3),
                'neutral_ratio': round(neutral_count / total_count, 3),
                'mean_return': round(mean_return * 100, 2),  # 转换为百分比
                'std_return': round(std_return * 100, 2),
                'sample_size': total_count
            },
            'calculation_method': {
                'magnitude': magnitude_method,
                'weights': {'direction': weight_dir, 'magnitude': weight_mag},
                'weight_reason': weight_reason
            }
        }
        
        return overall_coherence, details
    
    def _classify_coherence(self, dir_coh: float, mag_coh: float, 
                          mean_return: float, pos_count: int, 
                          neg_count: int, total_count: int) -> Tuple[str, str, str]:
        """分类一致性类型并生成解释"""
        
        # 判断类型
        if dir_coh > 0.8 and mag_coh > 0.7:
            coh_type = "strong_consensus"
            interpretation = f"板块内{dir_coh*100:.0f}%个股同向，且涨跌幅度相近(一致性{mag_coh:.2f})"
            action = "适合板块整体配置，可以通过ETF或龙头股参与"
            
        elif dir_coh > 0.8 and mag_coh < 0.4:
            coh_type = "directional_divergence"
            interpretation = f"方向一致但强弱分化严重，{pos_count}只上涨但涨幅差异大"
            action = "不宜板块配置，需精选强势个股"
            
        elif dir_coh < 0.4:
            coh_type = "high_dispersion"
            interpretation = f"板块严重分化，仅{max(pos_count, neg_count)/total_count*100:.0f}%个股同向"
            action = "避免板块操作，个股机会需要深度研究"
            
        elif dir_coh < 0.6 and mag_coh > 0.6:
            coh_type = "balanced_market"
            interpretation = f"多空平衡({pos_count}涨{neg_count}跌)，个股表现温和"
            action = "板块整体机会不大，可寻找结构性机会"
            
        elif mean_return > 0.01 and dir_coh > 0.7:
            coh_type = "bullish_trend"
            interpretation = f"板块偏多趋势，{dir_coh*100:.0f}%个股上涨，平均涨幅{mean_return*100:.1f}%"
            action = "可以跟随趋势，但注意涨幅分化情况"
            
        elif mean_return < -0.01 and dir_coh > 0.7:
            coh_type = "bearish_trend"
            interpretation = f"板块偏空趋势，{dir_coh*100:.0f}%个股下跌，平均跌幅{abs(mean_return)*100:.1f}%"
            action = "谨慎观望，等待企稳信号"
            
        else:
            coh_type = "moderate_coherence"
            interpretation = "板块表现分化但不极端，存在个股机会"
            action = "适度参与，均衡配置"
        
        return coh_type, interpretation, action


# ========================================
# 4. 增强版背离检测
# ========================================

@dataclass
class DivergenceResult:
    """背离检测结果"""
    sector: str
    divergence_type: str
    base_severity: float
    enhanced_severity: float
    adjustments: Dict[str, float]
    confidence: float
    risk_level: str
    description: str
    recommendation: str
    supporting_evidence: List[str]
    decision_path: List[Dict]  # 新增：决策路径


class EnhancedDivergenceDetector:
    """增强版背离检测器"""
    
    def __init__(self, decision_tracker: Optional[DecisionTracker] = None):
        self.price_threshold = SystemConfig.get_param('divergence.price_threshold')
        self.ad_neutral = SystemConfig.get_param('divergence.ad_neutral')
        self.severity_levels = SystemConfig.get_param('divergence.severity_levels')
        self.tracker = decision_tracker
        
        # 调整系数
        self.adjustment_factors = {
            'volume_divergence': 1.2,
            'nh_nl_confirmation': 1.3,
            'ma_divergence': 1.15,
            'rsi_divergence': 1.1,
            'coherence_penalty': 0.8,
            'trend_against': 1.25
        }
        
    def detect_divergence(self, price_change: float, breadth_metrics: Dict) -> DivergenceResult:
        """综合背离检测"""
        decision_path = []
        
        # 1. 基础背离检测
        div_type, base_severity = self._detect_basic_divergence(price_change, breadth_metrics)
        decision_path.append({
            'step': 'basic_detection',
            'result': div_type,
            'severity': base_severity,
            'logic': f'价格变化{price_change:.1%}, AD比率{breadth_metrics.get("ad_ratio", 1):.2f}'
        })
        
        if div_type == 'none':
            return self._create_no_divergence_result(
                breadth_metrics.get('sector_name', 'Unknown'),
                decision_path
            )
        
        # 2. 多维度验证
        adjustments, evidences = self._calculate_adjustments(
            price_change, breadth_metrics, div_type
        )
        
        # 3. 计算增强严重度
        total_adjustment = np.prod(list(adjustments.values())) if adjustments else 1.0
        enhanced_severity = base_severity * total_adjustment
        
        decision_path.append({
            'step': 'enhancement',
            'adjustments': adjustments,
            'total_factor': total_adjustment,
            'enhanced_severity': enhanced_severity
        })
        
        # 4. 计算置信度
        confidence = self._calculate_confidence(adjustments, len(evidences))
        
        # 5. 确定风险等级
        risk_level = self._determine_risk_level(enhanced_severity, confidence)
        
        # 6. 生成描述和建议
        description = self._generate_description(
            div_type, price_change, breadth_metrics, adjustments
        )
        recommendation = self._generate_recommendation(
            div_type, risk_level, enhanced_severity, confidence
        )
        
        # 记录到决策追踪器
        if self.tracker:
            self.tracker.add_step(
                'divergence_detected',
                {
                    'type': div_type,
                    'severity': enhanced_severity,
                    'risk': risk_level
                },
                description,
                confidence
            )
        
        return DivergenceResult(
            sector=breadth_metrics.get('sector_name', 'Unknown'),
            divergence_type=div_type,
            base_severity=round(base_severity, 4),
            enhanced_severity=round(enhanced_severity, 4),
            adjustments=adjustments,
            confidence=round(confidence, 3),
            risk_level=risk_level,
            description=description,
            recommendation=recommendation,
            supporting_evidence=evidences,
            decision_path=decision_path
        )
    
    def _detect_basic_divergence(self, price_change: float, 
                                breadth_metrics: Dict) -> Tuple[str, float]:
        """检测基础背离类型和严重度"""
        ad_ratio = breadth_metrics.get('ad_ratio', 1.0)
        
        # 负背离：价格上涨但内部结构弱
        if price_change > self.price_threshold and ad_ratio < self.ad_neutral:
            severity = abs(price_change * (1 - ad_ratio))
            return 'negative', severity
            
        # 正背离：价格下跌但内部结构强  
        elif price_change < -self.price_threshold and ad_ratio > self.ad_neutral:
            severity = abs(price_change * ad_ratio)
            return 'positive', severity
            
        # 成交量背离
        elif self._check_volume_divergence(price_change, breadth_metrics):
            severity = abs(price_change * 0.5)
            return 'volume', severity
            
        return 'none', 0.0
    
    def _calculate_adjustments(self, price_change: float, breadth_metrics: Dict,
                             div_type: str) -> Tuple[Dict[str, float], List[str]]:
        """计算多维度调整系数"""
        adjustments = {}
        evidences = []
        
        # 1. 成交量背离确认
        volume_breadth = breadth_metrics.get('volume_breadth', 0)
        if volume_breadth * price_change < 0:
            adjustments['volume_divergence'] = self.adjustment_factors['volume_divergence']
            evidences.append(f"成交量流向与价格相反(资金流向={volume_breadth:.2f})")
        
        # 2. 新高新低确认
        nh_nl_ratio = breadth_metrics.get('nh_nl_ratio', 1.0)
        if div_type == 'negative' and nh_nl_ratio < 0.5:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新低数量是新高的{1/nh_nl_ratio:.1f}倍，确认弱势")
        elif div_type == 'positive' and nh_nl_ratio > 2.0:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新高数量是新低的{nh_nl_ratio:.1f}倍，暗示强势")
        
        # 3. 均线支撑
        ma_health = (breadth_metrics.get('ma50_breadth', 0.5) + 
                    breadth_metrics.get('ma200_breadth', 0.5)) / 2
        if div_type == 'negative' and ma_health < 0.4:
            adjustments['ma_divergence'] = self.adjustment_factors['ma_divergence']
            evidences.append(f"仅{ma_health*100:.0f}%个股在均线上方，技术面疲弱")
        
        # 4. 动量一致性
        coherence = breadth_metrics.get('momentum_coherence', 0.5)
        if coherence < 0.3:
            adjustments['coherence_penalty'] = self.adjustment_factors['coherence_penalty']
            evidences.append(f"板块内部高度分化(一致性仅{coherence:.2f})，背离信号可靠性降低")
        
        return adjustments, evidences
    
    def _check_volume_divergence(self, price_change: float, 
                               breadth_metrics: Dict) -> bool:
        """检查成交量背离"""
        volume_breadth = breadth_metrics.get('volume_breadth', 0)
        # 价格大涨但资金流出，或价格大跌但资金流入
        return abs(price_change) > 0.02 and volume_breadth * price_change < -0.01
    
    def _calculate_confidence(self, adjustments: Dict[str, float], 
                            evidence_count: int) -> float:
        """计算置信度"""
        base_confidence = 0.5
        
        # 证据数量加分
        evidence_bonus = min(0.3, evidence_count * 0.05)
        
        # 调整因子一致性
        if adjustments:
            confirming = sum(1 for v in adjustments.values() if v > 1.0)
            consistency_bonus = (confirming / len(adjustments)) * 0.2
        else:
            consistency_bonus = 0
        
        return min(0.95, base_confidence + evidence_bonus + consistency_bonus)
    
    def _determine_risk_level(self, severity: float, confidence: float) -> str:
        """确定风险等级"""
        risk_score = severity * confidence
        
        for level, threshold in self.severity_levels.items():
            if risk_score >= threshold:
                return level
        return 'minimal'
    
    def _generate_description(self, div_type: str, price_change: float,
                            breadth_metrics: Dict, adjustments: Dict) -> str:
        """生成描述"""
        sector = breadth_metrics.get('sector_name', 'Unknown')
        ad_ratio = breadth_metrics.get('ad_ratio', 1.0)
        
        if div_type == 'negative':
            desc = f"{sector}板块价格上涨{price_change:.1%}，但仅{ad_ratio*100:.0f}%个股上涨，内部结构疲弱"
        elif div_type == 'positive':
            desc = f"{sector}板块价格下跌{abs(price_change):.1%}，但有{ad_ratio*100:.0f}%个股在上涨，暗示支撑较强"
        else:
            desc = f"{sector}板块出现{div_type}背离"
        
        # 添加关键因素
        key_factors = [k.replace('_', ' ') for k, v in adjustments.items() if v > 1.1]
        if key_factors:
            desc += f"，{len(key_factors)}个因素确认背离信号"
            
        return desc
    
    def _generate_recommendation(self, div_type: str, risk_level: str,
                               severity: float, confidence: float) -> str:
        """生成建议"""
        recommendations = {
            ('negative', 'extreme'): "立即减仓50%以上，上涨无法持续",
            ('negative', 'high'): "逐步减仓30-50%，密切关注支撑位",
            ('negative', 'medium'): "适度减仓20-30%，可保留部分底仓",
            ('negative', 'low'): "暂时观望，不追高",
            
            ('positive', 'extreme'): "积极建仓，下跌接近尾声",
            ('positive', 'high'): "分批买入，把握反弹机会",
            ('positive', 'medium'): "小仓试探，等待确认",
            ('positive', 'low'): "继续观察，暂不行动",
            
            ('volume', 'high'): "警惕资金异动，控制仓位",
            ('volume', 'medium'): "关注量价配合，谨慎操作"
        }
        
        key = (div_type, risk_level)
        base_rec = recommendations.get(key, "根据具体情况操作")
        
        # 添加置信度说明
        if confidence > 0.8:
            base_rec += "（高置信度信号，建议重视）"
        elif confidence < 0.6:
            base_rec += "（信号可靠性一般，需要其他确认）"
            
        return base_rec
    
    def _create_no_divergence_result(self, sector: str, 
                                   decision_path: List[Dict]) -> DivergenceResult:
        """创建无背离结果"""
        return DivergenceResult(
            sector=sector,
            divergence_type='none',
            base_severity=0.0,
            enhanced_severity=0.0,
            adjustments={},
            confidence=0.0,
            risk_level='none',
            description=f"{sector}板块价格与内部结构一致，无背离",
            recommendation="正常操作，跟随趋势",
            supporting_evidence=[],
            decision_path=decision_path
        )


# ========================================
# 5. 增强的市场广度分析器
# ========================================

class EnhancedMarketBreadthAnalyzer:
    """增强版市场广度分析器"""
    
    def __init__(self):
        self.min_stocks_threshold = SystemConfig.get_param('min_stocks_threshold')
        self.coherence_calculator = ImprovedMomentumCoherence()
        self.divergence_detector = EnhancedDivergenceDetector()
        self.decision_tracker = DecisionTracker()
        
    def analyze_sector_breadth(self, sector_data: 'SectorBreadthData',
                             price_change: Optional[float] = None) -> 'BreadthMetrics':
        """分析单个板块的内部广度"""
        # 数据验证
        is_valid, errors = self._validate_sector_data(sector_data)
        if not is_valid:
            self.decision_tracker.add_step(
                'data_validation_failed',
                {'errors': errors},
                f'数据验证失败: {"; ".join(errors)}',
                confidence=0.0
            )
            # 返回默认值或抛出异常
        
        # 基础广度指标
        ad_ratio = self._calculate_ad_ratio(sector_data.advances, sector_data.declines)
        purity = self._calculate_purity(sector_data)
        volume_breadth = self._calculate_volume_breadth(sector_data)
        nh_nl_ratio = self._calculate_nh_nl_ratio(sector_data)
        
        # 均线广度
        ma50_breadth = sector_data.above_ma50 / sector_data.total_stocks if sector_data.total_stocks > 0 else 0.5
        ma200_breadth = sector_data.above_ma200 / sector_data.total_stocks if sector_data.total_stocks > 0 else 0.5
        
        # 参与率
        participation_rate = 1 - (sector_data.unchanged / sector_data.total_stocks) if sector_data.total_stocks > 0 else 0.5
        
        # 动量一致性（使用改进算法）
        momentum_coherence = None
        coherence_details = None
        if sector_data.individual_returns:
            momentum_coherence, coherence_details = self.coherence_calculator.calculate_coherence(
                sector_data.individual_returns
            )
        
        # 内部健康度评分（考虑一致性）
        internal_health = self._calculate_internal_health(
            ad_ratio, purity, volume_breadth, nh_nl_ratio, 
            ma50_breadth, ma200_breadth, participation_rate,
            momentum_coherence, coherence_details
        )
        
        # 背离检测（如果提供了价格变化）
        price_breadth_divergence = None
        if price_change is not None:
            breadth_dict = {
                'sector_name': sector_data.sector_name,
                'ad_ratio': ad_ratio,
                'volume_breadth': volume_breadth,
                'nh_nl_ratio': nh_nl_ratio,
                'ma50_breadth': ma50_breadth,
                'ma200_breadth': ma200_breadth,
                'avg_rsi': sector_data.avg_rsi,
                'momentum_coherence': momentum_coherence
            }
            divergence_result = self.divergence_detector.detect_divergence(
                price_change, breadth_dict
            )
            
            if divergence_result.divergence_type != 'none':
                price_breadth_divergence = {
                    'type': divergence_result.divergence_type,
                    'severity': divergence_result.enhanced_severity,
                    'confidence': divergence_result.confidence,
                    'risk_level': divergence_result.risk_level,
                    'description': divergence_result.description,
                    'recommendation': divergence_result.recommendation
                }
        
        # 构建结果
        return BreadthMetrics(
            sector_name=sector_data.sector_name,
            timestamp=sector_data.timestamp,
            ad_ratio=ad_ratio,
            purity=purity,
            volume_breadth=volume_breadth,
            nh_nl_ratio=nh_nl_ratio,
            ma50_breadth=ma50_breadth,
            ma200_breadth=ma200_breadth,
            internal_health=internal_health,
            participation_rate=participation_rate,
            momentum_coherence=momentum_coherence,
            coherence_details=coherence_details,
            price_breadth_divergence=price_breadth_divergence
        )
    
    def _validate_sector_data(self, data: 'SectorBreadthData') -> Tuple[bool, List[str]]:
        """验证数据完整性和合理性"""
        errors = []
        
        # 完整性检查
        if data.total_stocks < self.min_stocks_threshold:
            errors.append(f"股票数量过少: {data.total_stocks}")
        
        # 逻辑检查
        if data.advances + data.declines + data.unchanged != data.total_stocks:
            errors.append("涨跌停家数不匹配总数")
        
        # 范围检查
        if not 0 <= data.avg_rsi <= 100:
            errors.append(f"RSI超出范围: {data.avg_rsi}")
        
        if data.above_ma50 > data.total_stocks or data.above_ma200 > data.total_stocks:
            errors.append("均线统计数据异常")
        
        return len(errors) == 0, errors
    
    def _calculate_internal_health(self, ad_ratio: float, purity: float, 
                                 volume_breadth: float, nh_nl_ratio: float,
                                 ma50_breadth: float, ma200_breadth: float,
                                 participation_rate: float,
                                 momentum_coherence: Optional[float],
                                 coherence_details: Optional[Dict]) -> float:
        """计算内部健康度（改进版，考虑一致性）"""
        
        # 基础评分
        scores = {
            'ad_score': min(ad_ratio * 20, 100),
            'purity_score': purity * 100,
            'volume_score': (volume_breadth + 1) * 50,
            'nh_nl_score': min(nh_nl_ratio * 15, 100),
            'ma_score': (ma50_breadth * 0.6 + ma200_breadth * 0.4) * 100,
            'participation_score': participation_rate * 100
        }
        
        # 权重分配
        weights = {
            'ad_score': 0.20,
            'purity_score': 0.15,
            'volume_score': 0.15,
            'nh_nl_score': 0.10,
            'ma_score': 0.25,
            'participation_score': 0.15
        }
        
        # 基础健康度
        base_health = sum(scores[k] * weights[k] for k in scores)
        
        # 一致性调整
        if momentum_coherence is not None and coherence_details is not None:
            coherence_type = coherence_details.get('coherence_type')
            
            if coherence_type == 'strong_consensus':
                health_bonus = 5
                adjustment_reason = "强共识加分"
            elif coherence_type == 'high_dispersion':
                health_bonus = -10
                adjustment_reason = "高度分化减分"
            elif coherence_type == 'directional_divergence':
                health_bonus = -3
                adjustment_reason = "幅度分化轻微减分"
            elif momentum_coherence > 0.7:
                health_bonus = 3
                adjustment_reason = "高一致性加分"
            elif momentum_coherence < 0.3:
                health_bonus = -5
                adjustment_reason = "低一致性减分"
            else:
                health_bonus = 0
                adjustment_reason = "一致性正常"
                
            adjusted_health = base_health + health_bonus
            
            # 记录调整
            self.decision_tracker.add_step(
                'health_adjustment',
                {
                    'base_health': base_health,
                    'coherence_bonus': health_bonus,
                    'final_health': adjusted_health
                },
                adjustment_reason,
                confidence=0.85
            )
        else:
            adjusted_health = base_health
        
        return round(max(0, min(100, adjusted_health)), 2)
    
    def calculate_market_breadth_summary(self, 
                                       sector_breadth_list: List['BreadthMetrics']) -> Dict:
        """计算市场整体广度摘要（增强版）"""
        if not sector_breadth_list:
            return {}
        
        # 基础统计
        health_scores = [b.internal_health for b in sector_breadth_list]
        avg_internal_health = np.mean(health_scores)
        
        # 板块分类
        strong_sectors = [(b.sector_name, b.internal_health) 
                         for b in sector_breadth_list if b.internal_health > 70]
        neutral_sectors = [(b.sector_name, b.internal_health) 
                          for b in sector_breadth_list if 40 <= b.internal_health <= 70]
        weak_sectors = [(b.sector_name, b.internal_health) 
                       for b in sector_breadth_list if b.internal_health < 40]
        
        # 一致性分析
        coherence_scores = []
        coherence_types = {}
        for b in sector_breadth_list:
            if b.coherence_details:
                coherence_scores.append(b.coherence_details['overall_coherence'])
                coh_type = b.coherence_details['coherence_type']
                coherence_types[coh_type] = coherence_types.get(coh_type, 0) + 1
        
        avg_coherence = np.mean(coherence_scores) if coherence_scores else 0.5
        
        # 背离统计
        divergence_count = sum(1 for b in sector_breadth_list 
                             if b.price_breadth_divergence is not None)
        high_risk_divergences = [
            b.sector_name for b in sector_breadth_list
            if b.price_breadth_divergence and 
            b.price_breadth_divergence['risk_level'] in ['high', 'extreme']
        ]
        
        # 市场结构诊断
        market_diagnosis = self._diagnose_market_structure(
            avg_internal_health, len(strong_sectors), len(weak_sectors),
            avg_coherence, divergence_count, len(sector_breadth_list)
        )
        
        return {
            'market_internal_health': round(avg_internal_health, 2),
            'health_distribution': {
                'strong_sectors': strong_sectors,
                'neutral_sectors': neutral_sectors,
                'weak_sectors': weak_sectors
            },
            'sector_counts': {
                'strong': len(strong_sectors),
                'neutral': len(neutral_sectors),
                'weak': len(weak_sectors),
                'total': len(sector_breadth_list)
            },
            'coherence_analysis': {
                'average_coherence': round(avg_coherence, 3),
                'coherence_distribution': coherence_types,
                'interpretation': self._interpret_market_coherence(avg_coherence, coherence_types)
            },
            'divergence_analysis': {
                'total_divergences': divergence_count,
                'high_risk_sectors': high_risk_divergences,
                'divergence_rate': round(divergence_count / len(sector_breadth_list), 2)
            },
            'market_diagnosis': market_diagnosis,
            'breadth_dispersion': round(np.std(health_scores), 2),
            'market_participation': round(
                np.mean([b.participation_rate for b in sector_breadth_list]), 3
            )
        }
    
    def _diagnose_market_structure(self, avg_health: float, strong_count: int,
                                 weak_count: int, avg_coherence: float,
                                 divergence_count: int, total_sectors: int) -> Dict:
        """诊断市场结构"""
        diagnosis = {
            'overall_condition': '',
            'key_observations': [],
            'risk_factors': [],
            'opportunities': [],
            'recommended_stance': ''
        }
        
        # 整体状况判断
        if avg_health > 70 and strong_count > total_sectors * 0.6:
            diagnosis['overall_condition'] = '市场结构健康，多数板块表现强势'
            diagnosis['recommended_stance'] = '积极参与，可适度提高仓位'
        elif avg_health < 40 or weak_count > total_sectors * 0.5:
            diagnosis['overall_condition'] = '市场结构疲弱，风险较高'
            diagnosis['recommended_stance'] = '谨慎防守，降低仓位'
        else:
            diagnosis['overall_condition'] = '市场结构分化，存在结构性机会'
            diagnosis['recommended_stance'] = '精选个股，控制总仓位'
        
        # 关键观察
        if avg_coherence > 0.7:
            diagnosis['key_observations'].append('板块内部一致性高，适合板块操作')
        elif avg_coherence < 0.4:
            diagnosis['key_observations'].append('板块内部分化严重，需要精选个股')
        
        if divergence_count > total_sectors * 0.3:
            diagnosis['risk_factors'].append('多个板块出现背离，需要警惕')
        
        if strong_count > 0 and weak_count > 0:
            diagnosis['opportunities'].append('市场分化提供轮动机会')
        
        return diagnosis
    
    def _interpret_market_coherence(self, avg_coherence: float, 
                                  type_distribution: Dict) -> str:
        """解释市场整体一致性"""
        if avg_coherence > 0.7:
            return "市场整体一致性高，板块内部结构良好，适合指数和板块操作"
        elif avg_coherence > 0.5:
            high_dispersion = type_distribution.get('high_dispersion', 0)
            if high_dispersion > 2:
                return f"市场一致性适中，但有{high_dispersion}个板块严重分化，需要区别对待"
            return "市场一致性适中，部分板块存在分化，建议精选"
        else:
            return "市场一致性低，多数板块内部分化严重，不宜进行板块配置"
    
    # 其他辅助方法保持不变...
    def _calculate_ad_ratio(self, advances: int, declines: int) -> float:
        if declines == 0:
            return 10.0 if advances > 0 else 1.0
        return min(advances / declines, 10.0)
    
    def _calculate_purity(self, data):
        if data.total_stocks == 0:
            return 0.5
        advance_ratio = data.advances / data.total_stocks
        decline_ratio = data.declines / data.total_stocks
        purity = advance_ratio**2 + decline_ratio**2
        return max(0, min(1, purity))
    
    def _calculate_volume_breadth(self, data):
        total_volume = data.advancing_volume + data.declining_volume
        if total_volume == 0:
            return 0.0
        return (data.advancing_volume - data.declining_volume) / total_volume
    
    def _calculate_nh_nl_ratio(self, data):
        if data.new_lows_52w == 0:
            return 10.0 if data.new_highs_52w > 0 else 1.0
        return min(data.new_highs_52w / data.new_lows_52w, 10.0)


# ========================================
# 6. LLM交互增强输出生成器
# ========================================

class LLMInteractionEnhancer:
    """增强LLM交互的输出生成器"""
    
    @staticmethod
    def generate_llm_context(analysis: 'MultiTimeframeAnalysis', 
                           decision_tracker: DecisionTracker) -> Dict:
        """生成供LLM理解的完整上下文"""
        
        # 获取决策摘要
        decision_summary = decision_tracker.get_decision_summary()
        
        # 构建LLM上下文
        llm_context = {
            'analysis_metadata': {
                'timestamp': analysis.timestamp.isoformat(),
                'reliability': analysis.signal_reliability,
                'confidence_level': _interpret_confidence(analysis.signal_reliability),
                'data_quality': _assess_data_quality(analysis.timeframe_results)
            },
            
            'market_understanding': {
                'current_state': {
                    'regime': analysis.market_regime,
                    'regime_meaning': _explain_regime(analysis.market_regime),
                    'consensus': analysis.consensus_score,
                    'consensus_meaning': _explain_consensus(analysis.consensus_score)
                },
                'key_drivers': _identify_key_drivers(analysis),
                'market_psychology': _assess_market_psychology(analysis)
            },
            
            'decision_logic': {
                'primary_decision': analysis.unified_decision,
                'decision_path': decision_summary['decision_path'],
                'confidence_factors': decision_summary['confidence_breakdown'],
                'alternative_considered': _get_alternatives(analysis)
            },
            
            'risk_reward_profile': {
                'risk_assessment': analysis.risk_assessment,
                'potential_scenarios': _generate_scenarios(analysis),
                'risk_mitigation': _suggest_risk_mitigation(analysis)
            },
            
            'actionable_insights': {
                'immediate_actions': _extract_immediate_actions(analysis),
                'monitoring_triggers': _define_monitoring_triggers(analysis),
                'adjustment_conditions': _define_adjustment_conditions(analysis)
            },
            
            'interactive_elements': {
                'clarification_needed': _identify_clarifications(analysis),
                'customization_options': _provide_customization_options(analysis),
                'follow_up_questions': _generate_follow_up_questions(analysis)
            },
            
            'sector_deep_dive': _generate_sector_analysis(analysis),
            
            'execution_guidance': {
                'timing': _suggest_execution_timing(analysis),
                'method': _suggest_execution_method(analysis),
                'size_management': _suggest_position_sizing(analysis)
            }
        }
        
        return llm_context


# 辅助函数
def _interpret_confidence(reliability: float) -> str:
    if reliability > 0.8:
        return "高度可信，可以作为主要决策依据"
    elif reliability > 0.6:
        return "较为可信，建议结合其他信号确认"
    elif reliability > 0.4:
        return "可信度一般，需要额外验证"
    else:
        return "可信度较低，仅供参考"

def _assess_data_quality(timeframe_results: Dict) -> Dict:
    """评估数据质量"""
    quality_scores = []
    issues = []
    
    for tf, result in timeframe_results.items():
        # 检查噪声水平
        if result.noise_level > 0.6:
            issues.append(f"{tf.description}噪声过高")
        quality_scores.append(1 - result.noise_level)
    
    avg_quality = np.mean(quality_scores)
    
    return {
        'overall_score': round(avg_quality, 2),
        'assessment': '优秀' if avg_quality > 0.8 else '良好' if avg_quality > 0.6 else '一般',
        'issues': issues
    }

def _explain_regime(regime: str) -> str:
    """解释市场状态的含义"""
    explanations = {
        'trending_stable': "市场处于稳定上升趋势，各时间框架信号一致，适合趋势跟踪策略",
        'high_rotation': "板块轮动速度快，需要灵活调整持仓，不宜长期持有单一板块",
        'short_term_stress': "短期出现压力，但中长期趋势未破坏，可能是调整或洗盘",
        'regime_transition': "市场可能正在转换运行模式，需要密切观察确认新趋势",
        'divergent_market': "市场内部分歧严重，缺乏一致性，建议降低仓位等待明朗",
        'normal_market': "市场运行正常，无特殊信号，可按常规策略操作"
    }
    return explanations.get(regime, "市场状态不明确，建议谨慎观察")

def _explain_consensus(consensus: float) -> str:
    """解释一致性分数的含义"""
    if consensus > 0.8:
        return "各时间框架高度一致，信号非常可靠，可以提高操作确定性"
    elif consensus > 0.6:
        return "多数时间框架方向一致，信号较为可靠，但仍需注意分歧点"
    elif consensus > 0.4:
        return "时间框架存在一定分歧，需要重点关注主导时间框架"
    else:
        return "各时间框架分歧严重，建议等待信号收敛或降低操作频率"

def _identify_key_drivers(analysis) -> List[Dict]:
    """识别关键驱动因素"""
    drivers = []
    
    # 找出最强势的板块
    if analysis.unified_decision['top_sectors']:
        top_sector = analysis.unified_decision['top_sectors'][0]
        drivers.append({
            'factor': '板块领涨',
            'description': f"{top_sector['sector']}板块领涨，健康度{top_sector['health']}",
            'impact': '正面',
            'weight': 'high'
        })
    
    # 检查一致性
    if analysis.consensus_score > 0.7:
        drivers.append({
            'factor': '时间框架一致',
            'description': '多时间框架信号趋同，趋势确认度高',
            'impact': '正面',
            'weight': 'high'
        })
    
    return drivers

def _assess_market_psychology(analysis) -> Dict:
    """评估市场心理"""
    # 基于各种指标评估市场情绪
    avg_health = np.mean([
        r.market_breadth_summary['market_internal_health'] 
        for r in analysis.timeframe_results.values()
    ])
    
    if avg_health > 70:
        psychology = "乐观"
        description = "市场情绪积极，多数参与者看好后市"
    elif avg_health > 50:
        psychology = "中性偏乐观"
        description = "市场情绪平稳，存在分歧但整体正面"
    elif avg_health > 30:
        psychology = "谨慎"
        description = "市场情绪谨慎，参与者在观望"
    else:
        psychology = "悲观"
        description = "市场情绪低迷，避险情绪浓厚"
    
    return {
        'sentiment': psychology,
        'description': description,
        'confidence': 0.75,
        'indicators': {
            'market_health': avg_health,
            'participation': analysis.timeframe_results[list(analysis.timeframe_results.keys())[0]].market_breadth_summary.get('market_participation', 0.5)
        }
    }

def _get_alternatives(analysis) -> List[Dict]:
    """获取备选方案"""
    alternatives = []
    
    # 如果建议仓位较高，提供保守方案
    if analysis.unified_decision['suggested_position'] > 70:
        alternatives.append({
            'name': '保守方案',
            'position': analysis.unified_decision['suggested_position'] - 20,
            'rationale': '降低风险暴露，保留加仓空间',
            'condition': '风险偏好较低或账户波动限制'
        })
    
    # 如果有避免板块，提供分散方案
    if analysis.unified_decision.get('avoid_sectors'):
        alternatives.append({
            'name': '分散配置方案',
            'adjustment': '将避免板块的资金平均分配到推荐板块',
            'rationale': '完全规避风险板块，集中优势板块',
            'condition': '不接受任何背离风险'
        })
    
    return alternatives

def _generate_scenarios(analysis) -> List[Dict]:
    """生成可能的市场场景"""
    scenarios = []
    
    # 基础场景
    base_position = analysis.unified_decision['suggested_position']
    
    scenarios.append({
        'name': '基准场景',
        'probability': 0.6,
        'description': '市场按当前趋势运行',
        'impact': f'维持{base_position}%仓位合理',
        'action': '按计划执行'
    })
    
    # 风险场景
    if analysis.risk_assessment['overall_risk'] in ['high', 'medium']:
        scenarios.append({
            'name': '风险场景',
            'probability': 0.25,
            'description': '短期调整超预期',
            'impact': f'可能需要降至{base_position-20}%仓位',
            'action': '设置止损，准备减仓计划'
        })
    
    # 机会场景
    if analysis.consensus_score > 0.6:
        scenarios.append({
            'name': '加速场景',
            'probability': 0.15,
            'description': '趋势加速上行',
            'impact': f'可考虑加至{min(base_position+10, 90)}%仓位',
            'action': '准备加仓资金，等待突破确认'
        })
    
    return scenarios

def _suggest_risk_mitigation(analysis) -> List[Dict]:
    """建议风险缓解措施"""
    measures = []
    
    # 基于风险等级
    risk_level = analysis.risk_assessment['overall_risk']
    
    if risk_level == 'high':
        measures.extend([
            {
                'measure': '严格止损',
                'detail': '设置2-3%的严格止损',
                'priority': 'high'
            },
            {
                'measure': '分批建仓',
                'detail': '分3-4批建仓，控制节奏',
                'priority': 'high'
            }
        ])
    
    # 基于分歧
    if analysis.consensus_score < 0.5:
        measures.append({
            'measure': '缩短持仓周期',
            'detail': '以短线思维操作，快进快出',
            'priority': 'medium'
        })
    
    return measures

def _extract_immediate_actions(analysis) -> List[Dict]:
    """提取立即执行的操作"""
    actions = []
    
    # 检查是否有极端风险的避免板块
    if 'avoid_sectors' in analysis.unified_decision:
        for sector in analysis.unified_decision['avoid_sectors']:
            if sector.get('risk_level') == 'extreme':
                actions.append({
                    'action': f"清仓{sector['sector']}",
                    'urgency': 'immediate',
                    'reason': sector['recommendation']
                })
    
    # 检查是否需要调整仓位
    suggested_pos = analysis.unified_decision['suggested_position']
    actions.append({
        'action': f"调整总仓位至{suggested_pos}%",
        'urgency': 'today',
        'reason': analysis.unified_decision['position_recommendation']['reasoning']
    })
    
    return actions

def _define_monitoring_triggers(analysis) -> List[Dict]:
    """定义监控触发条件"""
    triggers = []
    
    # 一致性监控
    triggers.append({
        'indicator': '时间框架一致性',
        'current': analysis.consensus_score,
        'trigger': 0.3,
        'action': '重新评估所有持仓',
        'direction': 'below'
    })
    
    # RII监控
    max_rii = max(
        r.rotation_metrics.unified_rii 
        for r in analysis.timeframe_results.values()
    )
    triggers.append({
        'indicator': '轮动强度指数',
        'current': max_rii,
        'trigger': 0.8,
        'action': '降低仓位，提高现金比例',
        'direction': 'above'
    })
    
    return triggers

def _define_adjustment_conditions(analysis) -> List[Dict]:
    """定义调仓条件"""
    conditions = []
    
    # 加仓条件
    if analysis.unified_decision['suggested_position'] < 80:
        conditions.append({
            'condition': '主要推荐板块回调2-3%',
            'action': '加仓5-10%',
            'rationale': '逢低吸纳'
        })
    
    # 减仓条件
    conditions.append({
        'condition': '避免板块的避险指标改善',
        'action': '可以小幅试探',
        'rationale': '风险缓解'
    })
    
    return conditions

def _identify_clarifications(analysis) -> List[str]:
    """识别需要澄清的问题"""
    clarifications = []
    
    if analysis.unified_decision['suggested_position'] > 70:
        clarifications.append("您的风险承受能力如何？能接受多大的账户回撤？")
    
    if len(analysis.unified_decision.get('avoid_sectors', [])) > 2:
        clarifications.append("您当前是否持有建议避免的板块？持仓比例多少？")
    
    return clarifications

def _provide_customization_options(analysis) -> Dict:
    """提供定制化选项"""
    return {
        'risk_preference': {
            'current': 'moderate',
            'options': ['conservative', 'moderate', 'aggressive'],
            'impact': '可调整建议仓位±20%'
        },
        'time_horizon': {
            'current': 'medium_term',
            'options': ['day_trade', 'swing_trade', 'position_trade'],
            'impact': '影响执行时间框架选择'
        },
        'sector_preference': {
            'description': '可以调整个别板块权重±10%',
            'constraint': '需要说明调整理由'
        }
    }

def _generate_follow_up_questions(analysis) -> List[Dict]:
    """生成后续问题"""
    questions = []
    
    questions.append({
        'question': "需要我详细解释某个板块的分析逻辑吗？",
        'purpose': "深入理解",
        'expected_response': "板块名称"
    })
    
    questions.append({
        'question': "您有什么特殊的约束条件需要考虑吗？",
        'purpose': "个性化调整",
        'expected_response': "约束描述"
    })
    
    return questions

def _generate_sector_analysis(analysis) -> Dict:
    """生成板块深度分析"""
    sector_analysis = {}
    
    # 分析每个推荐板块
    for sector_info in analysis.unified_decision.get('top_sectors', [])[:3]:
        sector = sector_info['sector']
        
        # 查找该板块的详细指标
        sector_details = None
        for tf_result in analysis.timeframe_results.values():
            if sector in tf_result.breadth_metrics:
                sector_details = tf_result.breadth_metrics[sector]
                break
        
        if sector_details:
            coherence = sector_details.coherence_details or {}
            sector_analysis[sector] = {
                'recommendation_level': sector_info['recommendation'],
                'weight': sector_info['weight'],
                'health_score': sector_info['health'],
                'internal_structure': {
                    'coherence': coherence.get('overall_coherence', 'N/A'),
                    'coherence_type': coherence.get('coherence_type', 'unknown'),
                    'interpretation': coherence.get('interpretation', '无数据')
                },
                'key_strengths': _identify_sector_strengths(sector_details),
                'key_risks': _identify_sector_risks(sector_details),
                'action_guide': coherence.get('action_suggestion', '正常操作')
            }
    
    return sector_analysis

def _identify_sector_strengths(details) -> List[str]:
    """识别板块优势"""
    strengths = []
    
    if details.internal_health > 70:
        strengths.append(f"内部健康度高达{details.internal_health}")
    
    if details.ma50_breadth > 0.7:
        strengths.append(f"{details.ma50_breadth*100:.0f}%个股站上50日均线")
    
    if details.ad_ratio > 2:
        strengths.append(f"涨跌比达到{details.ad_ratio:.1f}，多方占优")
    
    return strengths

def _identify_sector_risks(details) -> List[str]:
    """识别板块风险"""
    risks = []
    
    if details.momentum_coherence and details.momentum_coherence < 0.3:
        risks.append("内部一致性低，个股分化严重")
    
    if details.price_breadth_divergence:
        div = details.price_breadth_divergence
        risks.append(f"{div['type']}背离，{div['description']}")
    
    return risks

def _suggest_execution_timing(analysis) -> Dict:
    """建议执行时机"""
    market_regime = analysis.market_regime
    
    timing_map = {
        'trending_stable': {
            'suggestion': '可以立即执行，趋势明确',
            'best_time': '开盘30分钟后',
            'avoid_time': '尾盘最后30分钟'
        },
        'high_rotation': {
            'suggestion': '分批执行，避免追高',
            'best_time': '板块回调时',
            'avoid_time': '开盘和尾盘'
        },
        'short_term_stress': {
            'suggestion': '等待企稳信号',
            'best_time': '确认支撑后',
            'avoid_time': '下跌过程中'
        }
    }
    
    return timing_map.get(market_regime, {
        'suggestion': '择机执行',
        'best_time': '根据盘面情况',
        'avoid_time': '情绪极端时'
    })

def _suggest_execution_method(analysis) -> Dict:
    """建议执行方法"""
    if analysis.consensus_score > 0.7:
        method = {
            'approach': '集中执行',
            'detail': '信号明确，可以一次性建立目标仓位',
            'risk_control': '设置整体止损'
        }
    else:
        method = {
            'approach': '分散执行',
            'detail': '信号有分歧，建议分2-3批建仓',
            'risk_control': '每批单独止损'
        }
    
    return method

def _suggest_position_sizing(analysis) -> Dict:
    """建议仓位管理"""
    base_position = analysis.unified_decision['suggested_position']
    
    return {
        'total_position': f"{base_position}%",
        'allocation_method': '按推荐权重分配',
        'reserve_cash': f"{100-base_position}%",
        'rebalance_frequency': '每周检查一次',
        'adjustment_threshold': '偏离目标权重超过5%时调整'
    }

# 数据类定义（简化版，实际使用时需要导入）
@dataclass
class SectorBreadthData:
    sector_name: str
    timestamp: datetime
    advances: int
    declines: int
    unchanged: int
    total_stocks: int
    advancing_volume: float
    declining_volume: float
    new_highs_52w: int
    new_lows_52w: int
    above_ma50: int
    above_ma200: int
    avg_rsi: float
    individual_returns: Optional[List[float]] = None

@dataclass 
class BreadthMetrics:
    sector_name: str
    timestamp: datetime
    ad_ratio: float
    purity: float
    volume_breadth: float
    nh_nl_ratio: float
    ma50_breadth: float
    ma200_breadth: float
    internal_health: float
    participation_rate: float
    momentum_coherence: Optional[float] = None
    coherence_details: Optional[Dict] = None
    price_breadth_divergence: Optional[Dict] = None

# 这些只是部分核心优化实现
# 完整实现需要整合到主系统中