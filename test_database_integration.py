#!/usr/bin/env python3
"""
测试数据库集成 - 验证改进的指标是否能正确从数据库获取
"""

import sys
import os
import pandas as pd
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List
import json

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.hist_data import calculate_breadth_metrics, get_stock_data_for_markets, get_markets_from_config
from core.improved_momentum_coherence import ImprovedMomentumCoherence
from core.market_breadth_analyzer import MarketBreadthAnalyzer, SectorBreadthData
from core.decision_tracker import DecisionTracker

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()


def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===\n")
    
    try:
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ 数据库连接成功")
        print(f"MySQL版本: {version[0]}")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'market_breadth_metrics_gics'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print(f"✅ 表 market_breadth_metrics_gics 存在")
            
            # 检查表结构
            cursor.execute("DESCRIBE market_breadth_metrics_gics")
            columns = cursor.fetchall()
            
            print(f"表结构 ({len(columns)} 列):")
            for col in columns:
                print(f"  - {col[0]}: {col[1]}")
                
            # 检查最新数据
            cursor.execute("""
                SELECT COUNT(*), MAX(recorded_at), MIN(recorded_at) 
                FROM market_breadth_metrics_gics
            """)
            count, max_date, min_date = cursor.fetchone()
            print(f"\n数据统计:")
            print(f"  总记录数: {count}")
            print(f"  最新日期: {max_date}")
            print(f"  最早日期: {min_date}")
            
        else:
            print(f"❌ 表 market_breadth_metrics_gics 不存在")
            
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_get_latest_breadth_metrics():
    """测试获取最新的广度指标"""
    print("\n=== 测试获取最新广度指标 ===\n")
    
    try:
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        
        # 获取最新的广度指标数据
        query = """
        SELECT 
            recorded_at, market, total_stocks, advances, declines,
            purity, internal_health, momentum_coherence,
            divergence_type, divergence_severity, divergence_confidence,
            coherence_details, enhanced_divergence_details
        FROM market_breadth_metrics_gics 
        WHERE recorded_at = (SELECT MAX(recorded_at) FROM market_breadth_metrics_gics)
        ORDER BY market
        """
        
        df = pd.read_sql(query, conn)
        
        if len(df) > 0:
            print(f"✅ 获取到 {len(df)} 个市场的最新数据")
            print(f"数据日期: {df['recorded_at'].iloc[0]}")
            
            print(f"\n各市场指标概览:")
            for _, row in df.iterrows():
                print(f"\n--- {row['market']} ---")
                print(f"股票总数: {row['total_stocks']}")
                print(f"涨跌家数: {row['advances']}/{row['declines']}")
                print(f"纯度指标: {row['purity']:.3f}")
                print(f"内部健康度: {row['internal_health']:.1f}")
                
                if pd.notna(row['momentum_coherence']):
                    print(f"动量一致性: {row['momentum_coherence']:.3f}")
                else:
                    print(f"动量一致性: N/A")
                
                print(f"背离类型: {row['divergence_type']}")
                if row['divergence_severity'] > 0:
                    print(f"背离严重度: {row['divergence_severity']:.4f}")
                    print(f"背离置信度: {row['divergence_confidence']:.3f}")
                
                # 解析一致性详情
                if pd.notna(row['coherence_details']):
                    try:
                        coherence_details = json.loads(row['coherence_details'])
                        print(f"一致性类型: {coherence_details.get('coherence_type', 'unknown')}")
                        print(f"方向一致性: {coherence_details.get('direction_coherence', 0):.3f}")
                        print(f"幅度一致性: {coherence_details.get('magnitude_coherence', 0):.3f}")
                    except:
                        print(f"一致性详情: 解析失败")
                
                # 解析背离详情
                if pd.notna(row['enhanced_divergence_details']):
                    try:
                        div_details = json.loads(row['enhanced_divergence_details'])
                        if div_details.get('supporting_evidence'):
                            print(f"背离证据: {len(div_details['supporting_evidence'])}条")
                    except:
                        print(f"背离详情: 解析失败")
        else:
            print(f"❌ 未找到数据")
            
        conn.close()
        return df
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return pd.DataFrame()


def test_calculate_single_market():
    """测试单个市场的实时计算"""
    print("\n=== 测试单个市场实时计算 ===\n")
    
    try:
        # 获取市场列表
        markets = get_markets_from_config()
        if not markets:
            print("❌ 无法获取市场列表")
            return
            
        test_market = markets[0]  # 使用第一个市场进行测试
        print(f"测试市场: {test_market}")
        
        # 获取股票数据
        print("正在获取股票数据...")
        stock_data = get_stock_data_for_markets([test_market], batch_size=500)
        
        if stock_data.empty:
            print("❌ 无法获取股票数据")
            return
            
        print(f"✅ 获取到 {len(stock_data)} 只股票的数据")
        
        # 计算广度指标
        print("正在计算广度指标...")
        current_time = datetime.now()
        result = calculate_breadth_metrics(current_time, test_market, stock_data)
        
        print(f"\n=== {test_market} 实时计算结果 ===")
        print(f"计算时间: {result['recorded_at']}")
        print(f"股票总数: {result['total_stocks']}")
        print(f"涨跌家数: {result['advances']}/{result['declines']}")
        print(f"纯度指标: {result['purity']:.3f}")
        print(f"内部健康度: {result['internal_health']:.1f}")
        
        if result['momentum_coherence'] is not None:
            print(f"动量一致性: {result['momentum_coherence']:.3f}")
        else:
            print(f"动量一致性: N/A")
            
        print(f"背离类型: {result['divergence_type']}")
        if result['divergence_severity'] > 0:
            print(f"背离严重度: {result['divergence_severity']:.4f}")
            print(f"背离置信度: {result['divergence_confidence']:.3f}")
            print(f"风险等级: {result['divergence_risk_level']}")
        
        # 显示详细的一致性信息
        if result['coherence_details']:
            details = result['coherence_details']
            print(f"\n一致性详情:")
            print(f"  类型: {details['coherence_type']}")
            print(f"  方向一致性: {details['direction_coherence']:.3f}")
            print(f"  幅度一致性: {details['magnitude_coherence']:.3f}")
        
        # 显示背离详情
        if result['enhanced_divergence_details']:
            div_details = result['enhanced_divergence_details']
            print(f"\n背离详情:")
            print(f"  建议: {div_details.get('recommendation', 'N/A')}")
            if div_details.get('supporting_evidence'):
                print(f"  支持证据 ({len(div_details['supporting_evidence'])}条):")
                for evidence in div_details['supporting_evidence'][:3]:  # 只显示前3条
                    print(f"    - {evidence}")
        
        return result
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_improved_coherence_integration():
    """测试改进的一致性算法集成"""
    print("\n=== 测试改进的一致性算法集成 ===\n")
    
    # 创建决策追踪器
    tracker = DecisionTracker()
    
    # 创建改进的一致性计算器
    coherence_calc = ImprovedMomentumCoherence(tracker)
    
    # 测试数据（模拟个股收益率）
    test_scenarios = {
        '高度一致': [0.02, 0.025, 0.018, 0.022, 0.019, 0.021, 0.020],
        '方向分化': [0.05, -0.03, 0.08, -0.02, 0.04, -0.01, 0.06],
        '均值接近0': [0.0001, -0.0002, 0.0003, -0.0001, 0.0002]
    }
    
    for scenario_name, returns in test_scenarios.items():
        print(f"--- {scenario_name} ---")
        
        overall_coherence, details = coherence_calc.calculate_coherence(returns)
        
        print(f"综合一致性: {details.overall_coherence:.3f}")
        print(f"一致性类型: {details.coherence_type}")
        print(f"计算方法: {details.calculation_method['magnitude']}")
        print(f"解释: {details.interpretation}")
        
        # 获取决策摘要
        summary = tracker.get_decision_summary()
        if summary:
            print(f"决策步骤数: {summary['step_count']}")
            print(f"最终置信度: {summary['final_confidence']:.2f}")
        
        print()


def test_market_breadth_analyzer_integration():
    """测试市场广度分析器集成"""
    print("\n=== 测试市场广度分析器集成 ===\n")
    
    # 创建决策追踪器
    tracker = DecisionTracker()
    
    # 创建市场广度分析器
    analyzer = MarketBreadthAnalyzer(tracker)
    
    # 创建测试数据
    sector_data = SectorBreadthData(
        sector_name="SP500",
        timestamp=datetime.now(),
        total_stocks=500,
        advances=320,
        declines=150,
        unchanged=30,
        advancing_volume=5000000000,
        declining_volume=2000000000,
        total_volume=7500000000,
        new_highs_52w=45,
        new_lows_52w=12,
        above_ma50=350,
        above_ma200=300,
        avg_rsi=58.5,
        individual_returns=[0.02, 0.01, 0.03, -0.01, 0.025, 0.015, 0.008, 0.012]
    )
    
    print(f"分析板块: {sector_data.sector_name}")
    print(f"股票数量: {sector_data.total_stocks}")
    print(f"涨跌家数: {sector_data.advances}/{sector_data.declines}")
    
    # 执行分析
    result = analyzer.analyze_sector_breadth(sector_data, price_change=0.015)
    
    print(f"\n分析结果:")
    print(f"涨跌比: {result.ad_ratio:.2f}")
    print(f"纯度: {result.purity:.3f}")
    print(f"内部健康度: {result.internal_health:.1f}")
    print(f"动量一致性: {result.momentum_coherence:.3f}")
    
    if result.coherence_details:
        print(f"一致性类型: {result.coherence_details.coherence_type}")
        print(f"解释: {result.coherence_details.interpretation}")
    
    if result.price_breadth_divergence:
        div = result.price_breadth_divergence
        print(f"背离检测: {div['type']} (严重度: {div['severity']:.4f})")
    else:
        print(f"背离检测: 无背离")
    
    # 获取决策摘要
    summary = tracker.get_decision_summary()
    if summary:
        print(f"\n决策过程:")
        print(f"总步骤数: {summary['step_count']}")
        print(f"最终置信度: {summary['final_confidence']:.2f}")
        
        print(f"关键步骤:")
        for step in summary['decision_path'][-3:]:  # 显示最后3步
            print(f"  - {step['step']}: {step['reasoning']}")


def main():
    """主测试函数"""
    print("开始测试数据库集成和改进指标\n")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("数据库连接失败，无法继续测试")
        return
    
    print("=" * 60)
    
    # 2. 测试获取最新数据
    latest_data = test_get_latest_breadth_metrics()
    
    print("=" * 60)
    
    # 3. 测试实时计算
    test_calculate_single_market()
    
    print("=" * 60)
    
    # 4. 测试改进的一致性算法
    test_improved_coherence_integration()
    
    print("=" * 60)
    
    # 5. 测试市场广度分析器
    test_market_breadth_analyzer_integration()
    
    print("=" * 60)
    print("✅ 所有数据库集成测试完成！")


if __name__ == "__main__":
    main()
