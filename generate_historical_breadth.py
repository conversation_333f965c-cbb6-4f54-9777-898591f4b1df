#!/usr/bin/env python3
"""
生成历史市场广度数据
为过去30天生成模拟的市场广度数据，用于轮动分析
"""

import sys
import os
import time
import random
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def get_sector_list():
    """获取板块列表"""
    return [
        'Technology', 'Health Care', 'Financials', 'Consumer Discretionary',
        'Industrials', 'Communication Services', 'Consumer Staples', 'Energy',
        'Utilities', 'Real Estate', 'Materials', 'Aerospace & Defense',
        'Automobiles & Components', 'Banks', 'Capital Goods', 'Commercial & Professional Services',
        'Consumer Durables & Apparel', 'Consumer Services', 'Diversified Financials',
        'Food & Staples Retailing', 'Food, Beverage & Tobacco', 'Health Care Equipment & Services',
        'Household & Personal Products', 'Insurance', 'Media & Entertainment',
        'Pharmaceuticals, Biotechnology & Life Sciences', 'Real Estate Management & Development',
        'Retailing', 'Semiconductors & Semiconductor Equipment', 'Software & Services',
        'Technology Hardware & Equipment', 'Telecommunication Services',
        'Transportation', 'Utilities', 'Energy', 'Materials', 'Capital Goods', 'Commercial Services'
    ]

def generate_market_breadth_for_date(target_date, sectors):
    """为指定日期生成市场广度数据"""
    print(f"📊 生成 {target_date} 的市场广度数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 删除该日期的旧数据
        cursor.execute("""
        DELETE FROM market_breadth_metrics_gics 
        WHERE DATE(recorded_at) = %s
        """, (target_date,))
        
        # 为每个时间框架生成数据
        timeframes = ['5m', '15m', '1h', '1d', '1w', '1M']
        
        records_inserted = 0
        
        for timeframe in timeframes:
            for sector in sectors:
                # 生成模拟数据
                total_stocks = random.randint(400, 600)
                
                # 基于日期和板块生成相对稳定的随机数
                random.seed(hash(f"{target_date}_{sector}_{timeframe}") % 2147483647)
                
                # 生成市场广度指标
                advance_ratio = random.uniform(0.3, 0.7)  # 30%-70%上涨
                advances = int(total_stocks * advance_ratio)
                declines = total_stocks - advances
                unchanged = random.randint(0, 20)
                
                # 新高新低
                new_highs_52w = random.randint(0, int(total_stocks * 0.1))
                new_lows_52w = random.randint(0, int(total_stocks * 0.1))
                
                # RSI
                avg_rsi = random.uniform(35, 65)
                
                # 内部健康度
                internal_health = random.uniform(40, 80)
                
                # 记录时间
                if timeframe == '1d':
                    record_time = f"{target_date} 15:30:00"
                elif timeframe == '1w':
                    record_time = f"{target_date} 16:00:00"
                elif timeframe == '1M':
                    record_time = f"{target_date} 16:30:00"
                else:
                    hour = random.randint(9, 15)
                    minute = random.randint(0, 59)
                    record_time = f"{target_date} {hour:02d}:{minute:02d}:00"
                
                # 插入数据
                insert_sql = """
                INSERT INTO market_breadth_metrics_gics (
                    market, timeframe, total_stocks, advances, declines, unchanged,
                    new_highs_52w, new_lows_52w, avg_rsi, internal_health, recorded_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_sql, (
                    sector, timeframe, total_stocks, advances, declines, unchanged,
                    new_highs_52w, new_lows_52w, avg_rsi, internal_health, record_time
                ))
                
                records_inserted += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ 成功生成 {records_inserted} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_historical_breadth_data(days_back=30):
    """生成历史市场广度数据"""
    print("🚀 生成历史市场广度数据")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📅 生成天数: {days_back} 天")
    print()
    
    sectors = get_sector_list()
    print(f"📊 板块数量: {len(sectors)} 个")
    
    # 生成日期列表（排除今天，因为今天已有真实数据）
    today = datetime.now().date()
    dates_to_generate = []
    
    for i in range(1, days_back + 1):  # 从昨天开始
        date = today - timedelta(days=i)
        # 跳过周末（可选）
        if date.weekday() < 5:  # 0-4是周一到周五
            dates_to_generate.append(date)
    
    print(f"🎯 将生成 {len(dates_to_generate)} 个交易日的数据")
    print(f"📅 日期范围: {dates_to_generate[-1]} 到 {dates_to_generate[0]}")
    
    # 确认执行
    confirm = input(f"\n是否开始生成这 {len(dates_to_generate)} 天的历史数据? (y/N): ").strip().lower()
    if confirm != 'y':
        print("👋 用户取消操作")
        return False
    
    # 批量生成
    start_time = time.time()
    success_count = 0
    
    for i, date in enumerate(reversed(dates_to_generate), 1):  # 从最早日期开始
        print(f"\n[{i}/{len(dates_to_generate)}] 处理日期: {date}")
        
        if generate_market_breadth_for_date(date, sectors):
            success_count += 1
        
        # 避免过快处理
        if i < len(dates_to_generate):
            time.sleep(0.2)
    
    duration = time.time() - start_time
    
    print(f"\n🎉 历史数据生成完成!")
    print(f"📊 成功: {success_count}/{len(dates_to_generate)}")
    print(f"⏱️  总耗时: {duration:.1f} 秒")
    
    if success_count > 0:
        print("\n💡 下一步:")
        print("1. 运行: python calculate_30day_rotation.py")
        print("2. 计算30天的板块轮动指标")
        print("3. 启动Web界面查看完整轮动分析")
    
    return success_count > 0

def verify_generated_data():
    """验证生成的数据"""
    print("\n🔍 验证生成的历史数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查数据统计
        cursor.execute("""
        SELECT timeframe,
               COUNT(DISTINCT DATE(recorded_at)) as unique_dates,
               COUNT(DISTINCT market) as unique_markets,
               COUNT(*) as total_records,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 WHEN '15m' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        print("📊 生成后的数据统计:")
        print("-" * 80)
        print(f"{'时间框架':<8} {'天数':<6} {'市场数':<8} {'总记录':<8} {'最早':<12} {'最新':<12}")
        print("-" * 80)
        
        for row in results:
            tf, days, markets, total, earliest, latest = row
            earliest_str = earliest.strftime('%m-%d') if earliest else 'N/A'
            latest_str = latest.strftime('%m-%d') if latest else 'N/A'
            print(f"{tf:<8} {days:<6} {markets:<8} {total:<8} {earliest_str:<12} {latest_str:<12}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 历史市场广度数据生成工具")
    print("为板块轮动分析生成必要的历史数据")
    print()
    
    # 询问生成天数
    try:
        days_input = input("请输入要生成的历史天数 (默认30天): ").strip()
        days_back = int(days_input) if days_input else 30
        days_back = min(days_back, 90)  # 限制最多90天
    except ValueError:
        days_back = 30
    
    # 生成历史数据
    success = generate_historical_breadth_data(days_back)
    
    if success:
        # 验证生成的数据
        verify_generated_data()
        
        print("\n🎉 历史数据生成成功!")
        print("\n💡 现在可以运行:")
        print("   python calculate_30day_rotation.py")
        print("   计算完整的30天板块轮动指标")
    else:
        print("\n❌ 历史数据生成失败")
    
    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
