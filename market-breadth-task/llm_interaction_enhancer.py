"""
LLM交互增强器 - 生成供LLM理解的完整上下文
基于优化文档要求，提供可解释的分析结果和交互式问答预设
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class MarketContext:
    """市场上下文信息"""
    market_name: str
    timestamp: datetime
    overall_sentiment: str
    key_metrics: Dict[str, float]
    risk_factors: List[str]
    opportunities: List[str]
    market_phase: str


@dataclass
class LLMContext:
    """LLM交互上下文"""
    executive_summary: str
    market_state_explanation: str
    decision_logic_explanation: str
    risk_reward_analysis: Dict
    actionable_recommendations: List[Dict]
    interactive_qa_presets: List[Dict]
    confidence_breakdown: Dict
    supporting_evidence: List[str]
    alternative_scenarios: List[Dict]


class LLMInteractionEnhancer:
    """LLM交互增强器 - 将技术分析结果转换为LLM友好的上下文"""
    
    def __init__(self):
        """初始化LLM交互增强器"""
        self.context_templates = self._load_context_templates()
        
    def enhance_breadth_analysis(self, breadth_metrics: Dict, 
                                market_context: Optional[MarketContext] = None) -> LLMContext:
        """
        增强市场广度分析，生成LLM友好的上下文
        
        Args:
            breadth_metrics: 市场广度指标字典
            market_context: 可选的市场上下文
            
        Returns:
            LLMContext: LLM交互上下文
        """
        # 1. 生成执行摘要
        executive_summary = self._generate_executive_summary(breadth_metrics, market_context)
        
        # 2. 市场状态解释
        market_state_explanation = self._explain_market_state(breadth_metrics)
        
        # 3. 决策逻辑说明
        decision_logic_explanation = self._explain_decision_logic(breadth_metrics)
        
        # 4. 风险收益分析
        risk_reward_analysis = self._analyze_risk_reward(breadth_metrics)
        
        # 5. 可操作建议
        actionable_recommendations = self._generate_actionable_recommendations(breadth_metrics)
        
        # 6. 交互式问答预设
        interactive_qa_presets = self._generate_qa_presets(breadth_metrics)
        
        # 7. 置信度分解
        confidence_breakdown = self._breakdown_confidence(breadth_metrics)
        
        # 8. 支持证据
        supporting_evidence = self._collect_supporting_evidence(breadth_metrics)
        
        # 9. 替代场景
        alternative_scenarios = self._generate_alternative_scenarios(breadth_metrics)
        
        return LLMContext(
            executive_summary=executive_summary,
            market_state_explanation=market_state_explanation,
            decision_logic_explanation=decision_logic_explanation,
            risk_reward_analysis=risk_reward_analysis,
            actionable_recommendations=actionable_recommendations,
            interactive_qa_presets=interactive_qa_presets,
            confidence_breakdown=confidence_breakdown,
            supporting_evidence=supporting_evidence,
            alternative_scenarios=alternative_scenarios
        )
    
    def _generate_executive_summary(self, metrics: Dict, 
                                  context: Optional[MarketContext] = None) -> str:
        """生成执行摘要"""
        market = metrics.get('market', 'Unknown')
        health = metrics.get('internal_health', 50)
        coherence = metrics.get('momentum_coherence', 0.5) or 0.5
        ad_ratio = metrics.get('ad_ratio', 1.0)
        participation = metrics.get('participation_rate', 0.5)
        
        # 健康度评级
        if health > 70:
            health_grade = "强势"
        elif health > 50:
            health_grade = "中性"
        else:
            health_grade = "疲弱"
        
        # 一致性评级
        if coherence > 0.7:
            coherence_grade = "高度一致"
        elif coherence > 0.4:
            coherence_grade = "适度分化"
        else:
            coherence_grade = "严重分化"
        
        # 背离状态
        divergence_status = "无背离"
        if metrics.get('divergence_type') and metrics.get('divergence_type') != 'none':
            div_type = metrics.get('divergence_type')
            if div_type == 'positive':
                divergence_status = "正背离（价跌内强）"
            elif div_type == 'negative':
                divergence_status = "负背离（价涨内弱）"
        
        summary = f"""
📊 {market}板块分析摘要：
• 内部健康度：{health:.1f}/100 ({health_grade})
• 动量一致性：{coherence:.2f} ({coherence_grade})
• 背离状态：{divergence_status}
• 涨跌比：{ad_ratio:.2f}
• 参与率：{participation:.1%}

💡 核心观点：{self._get_core_insight(metrics)}
        """.strip()
        
        return summary
    
    def _explain_market_state(self, metrics: Dict) -> str:
        """解释市场状态"""
        explanations = []
        
        # 健康度解释
        health = metrics.get('internal_health', 50)
        if health > 70:
            explanations.append(f"板块内部健康度{health:.1f}分，表明多数股票表现强势，技术面良好。")
        elif health > 50:
            explanations.append(f"板块内部健康度{health:.1f}分，表明板块整体表现中性，存在结构性机会。")
        else:
            explanations.append(f"板块内部健康度{health:.1f}分，表明板块内部疲弱，需要谨慎操作。")
        
        # 一致性解释
        coherence_details = metrics.get('coherence_details')
        if coherence_details and isinstance(coherence_details, dict):
            coherence_type = coherence_details.get('coherence_type', 'unknown')
            interpretation = coherence_details.get('interpretation', '无详细解释')
            explanations.append(f"动量一致性分析显示：{interpretation}")
        
        # 背离解释
        if metrics.get('divergence_type') and metrics.get('divergence_type') != 'none':
            div_type = metrics.get('divergence_type')
            div_severity = metrics.get('divergence_severity', 0)
            explanations.append(f"检测到{div_type}背离，严重度{div_severity:.3f}")
        
        return " ".join(explanations)
    
    def _explain_decision_logic(self, metrics: Dict) -> str:
        """解释决策逻辑"""
        logic_steps = []
        
        # 步骤1：基础指标分析
        ad_ratio = metrics.get('ad_ratio', 1.0)
        purity = metrics.get('purity', 0.5)
        participation = metrics.get('participation_rate', 0.5)
        logic_steps.append(f"1. 基础指标：涨跌比{ad_ratio:.2f}，纯度{purity:.3f}，参与率{participation:.1%}")
        
        # 步骤2：技术指标分析
        ma50_breadth = metrics.get('ma50_breadth', 0.5)
        ma200_breadth = metrics.get('ma200_breadth', 0.5)
        logic_steps.append(f"2. 技术面：{ma50_breadth:.1%}股票在50日均线上，{ma200_breadth:.1%}在200日均线上")
        
        # 步骤3：一致性分析
        coherence_details = metrics.get('coherence_details')
        if coherence_details and isinstance(coherence_details, dict):
            coherence_type = coherence_details.get('coherence_type', 'unknown')
            action_suggestion = coherence_details.get('action_suggestion', '无建议')
            logic_steps.append(f"3. 一致性：{coherence_type}，{action_suggestion}")
        
        # 步骤4：综合评分
        health = metrics.get('internal_health', 50)
        logic_steps.append(f"4. 综合评分：内部健康度{health:.1f}分，反映板块整体状况")
        
        return "\n".join(logic_steps)
    
    def _analyze_risk_reward(self, metrics: Dict) -> Dict:
        """分析风险收益"""
        analysis = {
            "risk_level": "中等",
            "reward_potential": "中等",
            "risk_factors": [],
            "reward_drivers": [],
            "risk_score": 0.5,
            "reward_score": 0.5
        }
        
        health = metrics.get('internal_health', 50)
        coherence = metrics.get('momentum_coherence', 0.5) or 0.5
        divergence_type = metrics.get('divergence_type', 'none')
        
        # 风险评估
        risk_factors = []
        risk_score = 0.5
        
        if health < 40:
            risk_factors.append("内部健康度低，技术面疲弱")
            risk_score += 0.2
        
        if coherence < 0.3:
            risk_factors.append("动量一致性低，板块分化严重")
            risk_score += 0.15
        
        if divergence_type == 'negative':
            risk_factors.append("负背离信号，价格与内部结构不匹配")
            risk_score += 0.2
        
        # 收益潜力评估
        reward_drivers = []
        reward_score = 0.5
        
        if health > 70:
            reward_drivers.append("内部健康度高，技术面强势")
            reward_score += 0.2
        
        if coherence > 0.7:
            reward_drivers.append("动量一致性高，板块整体向好")
            reward_score += 0.15
        
        if divergence_type == 'positive':
            reward_drivers.append("正背离信号，内部结构强于价格表现")
            reward_score += 0.2
        
        # 风险等级
        if risk_score > 0.7:
            analysis["risk_level"] = "高"
        elif risk_score < 0.4:
            analysis["risk_level"] = "低"
        
        # 收益潜力等级
        if reward_score > 0.7:
            analysis["reward_potential"] = "高"
        elif reward_score < 0.4:
            analysis["reward_potential"] = "低"
        
        analysis.update({
            "risk_factors": risk_factors,
            "reward_drivers": reward_drivers,
            "risk_score": min(1.0, risk_score),
            "reward_score": min(1.0, reward_score)
        })
        
        return analysis
    
    def _generate_actionable_recommendations(self, metrics: Dict) -> List[Dict]:
        """生成可操作建议"""
        recommendations = []
        
        health = metrics.get('internal_health', 50)
        coherence_details = metrics.get('coherence_details', {})
        divergence_type = metrics.get('divergence_type', 'none')
        divergence_confidence = metrics.get('divergence_confidence', 0.5)
        
        # 基于健康度的建议
        if health > 70:
            recommendations.append({
                "action": "积极配置",
                "reasoning": f"内部健康度{health:.1f}分，板块表现强势",
                "timeframe": "中短期",
                "confidence": 0.8
            })
        elif health < 40:
            recommendations.append({
                "action": "谨慎观望",
                "reasoning": f"内部健康度{health:.1f}分，板块表现疲弱",
                "timeframe": "短期",
                "confidence": 0.7
            })
        
        # 基于一致性的建议
        if isinstance(coherence_details, dict):
            coherence_type = coherence_details.get('coherence_type', 'unknown')
            if coherence_type == "strong_consensus":
                recommendations.append({
                    "action": "板块配置",
                    "reasoning": "强共识状态，适合通过ETF或指数进行板块配置",
                    "timeframe": "中期",
                    "confidence": 0.85
                })
            elif coherence_type == "high_dispersion":
                recommendations.append({
                    "action": "精选个股",
                    "reasoning": "板块分化严重，需要精选强势个股",
                    "timeframe": "短期",
                    "confidence": 0.75
                })
        
        # 基于背离的建议
        if divergence_type == 'positive':
            recommendations.append({
                "action": "逢低布局",
                "reasoning": "正背离信号，内部结构强于价格表现，可考虑逢低布局",
                "timeframe": "中期",
                "confidence": divergence_confidence
            })
        elif divergence_type == 'negative':
            recommendations.append({
                "action": "减仓观望",
                "reasoning": "负背离信号，价格强于内部结构，建议减仓观望",
                "timeframe": "短期",
                "confidence": divergence_confidence
            })
        
        return recommendations

    def _generate_qa_presets(self, metrics: Dict) -> List[Dict]:
        """生成交互式问答预设"""
        market = metrics.get('market', 'Unknown')

        qa_presets = [
            {
                "question": f"{market}板块现在适合投资吗？",
                "answer_template": "基于当前分析，{recommendation}。主要原因是{reasoning}。建议{timeframe}关注。",
                "context_keys": ["recommendations", "risk_reward_analysis"]
            },
            {
                "question": "这个板块的主要风险是什么？",
                "answer_template": "主要风险包括：{risk_factors}。风险等级为{risk_level}。",
                "context_keys": ["risk_reward_analysis"]
            },
            {
                "question": "为什么动量一致性这么重要？",
                "answer_template": "动量一致性反映板块内部股票的表现是否同步。当前{coherence_explanation}，这意味着{implication}。",
                "context_keys": ["coherence_details"]
            },
            {
                "question": "检测到背离意味着什么？",
                "answer_template": "背离表示价格走势与内部技术指标不一致。{divergence_explanation}，通常预示着{prediction}。",
                "context_keys": ["price_breadth_divergence"]
            }
        ]

        return qa_presets

    def _breakdown_confidence(self, metrics: Dict) -> Dict:
        """分解置信度"""
        breakdown = {
            "overall_confidence": 0.5,
            "data_quality": 0.9,
            "algorithm_reliability": 0.85,
            "market_conditions": 0.6,
            "factors": []
        }

        # 数据质量评估
        total_stocks = metrics.get('total_stocks', 0)
        if total_stocks > 100:
            breakdown["data_quality"] = 0.95
            breakdown["factors"].append("样本量充足，数据质量高")
        elif total_stocks > 50:
            breakdown["data_quality"] = 0.85
            breakdown["factors"].append("样本量适中，数据质量良好")
        else:
            breakdown["data_quality"] = 0.7
            breakdown["factors"].append("样本量较小，数据质量一般")

        # 算法可靠性
        coherence_details = metrics.get('coherence_details', {})
        if isinstance(coherence_details, dict):
            calc_method = coherence_details.get('calculation_method', 'unknown')
            if calc_method == 'cv_based':
                breakdown["algorithm_reliability"] = 0.9
                breakdown["factors"].append("使用标准变异系数法，算法可靠")
            elif calc_method == 'iqr_based':
                breakdown["algorithm_reliability"] = 0.85
                breakdown["factors"].append("使用四分位距法，算法稳定")

        # 市场条件评估
        health = metrics.get('internal_health', 50)
        if health > 70:
            breakdown["market_conditions"] = 0.8
            breakdown["factors"].append("市场条件良好，分析结果可信度高")
        elif health < 40:
            breakdown["market_conditions"] = 0.4
            breakdown["factors"].append("市场条件疲弱，分析结果需谨慎解读")

        # 计算总体置信度
        breakdown["overall_confidence"] = (
            breakdown["data_quality"] * 0.3 +
            breakdown["algorithm_reliability"] * 0.4 +
            breakdown["market_conditions"] * 0.3
        )

        return breakdown

    def _collect_supporting_evidence(self, metrics: Dict) -> List[str]:
        """收集支持证据"""
        evidence = []

        # 基础指标证据
        ad_ratio = metrics.get('ad_ratio', 1.0)
        purity = metrics.get('purity', 0.5)
        participation = metrics.get('participation_rate', 0.5)

        evidence.append(f"涨跌比{ad_ratio:.2f}，反映{self._interpret_ad_ratio(ad_ratio)}")
        evidence.append(f"纯度指标{purity:.3f}，显示{self._interpret_purity(purity)}")
        evidence.append(f"参与率{participation:.1%}，表明{self._interpret_participation(participation)}")

        # 技术指标证据
        ma50_breadth = metrics.get('ma50_breadth', 0.5)
        ma200_breadth = metrics.get('ma200_breadth', 0.5)
        evidence.append(f"{ma50_breadth:.1%}股票在50日均线上，{ma200_breadth:.1%}在200日均线上")

        # 一致性证据
        coherence_details = metrics.get('coherence_details', {})
        if isinstance(coherence_details, dict):
            dir_coherence = coherence_details.get('direction_coherence', 0.5)
            mag_coherence = coherence_details.get('magnitude_coherence', 0.5)
            evidence.append(f"方向一致性{dir_coherence:.2f}，幅度一致性{mag_coherence:.2f}")

        # 背离证据
        if metrics.get('divergence_type') and metrics.get('divergence_type') != 'none':
            div_type = metrics.get('divergence_type')
            div_confidence = metrics.get('divergence_confidence', 0.5)
            evidence.append(f"背离检测：{div_type}，置信度{div_confidence:.2f}")

        return evidence

    def _generate_alternative_scenarios(self, metrics: Dict) -> List[Dict]:
        """生成替代场景"""
        scenarios = []
        health = metrics.get('internal_health', 50)

        # 乐观场景
        optimistic = {
            "scenario": "乐观情况",
            "probability": 0.3,
            "description": "",
            "implications": []
        }

        if health > 50:
            optimistic["probability"] = 0.4
            optimistic["description"] = "板块健康度持续改善，动量一致性增强"
            optimistic["implications"] = ["板块整体上涨", "个股普遍受益", "适合增加配置"]
        else:
            optimistic["description"] = "板块出现反转信号，内部结构改善"
            optimistic["implications"] = ["板块止跌企稳", "龙头股率先反弹", "可考虑试探性建仓"]

        scenarios.append(optimistic)

        # 悲观场景
        pessimistic = {
            "scenario": "悲观情况",
            "probability": 0.3,
            "description": "",
            "implications": []
        }

        if health < 50:
            pessimistic["probability"] = 0.4
            pessimistic["description"] = "板块健康度继续恶化，分化加剧"
            pessimistic["implications"] = ["板块整体下跌", "个股普遍承压", "建议减仓观望"]
        else:
            pessimistic["description"] = "板块出现调整压力，内部结构转弱"
            pessimistic["implications"] = ["板块短期调整", "强势股也可能回调", "控制仓位风险"]

        scenarios.append(pessimistic)

        # 中性场景
        neutral = {
            "scenario": "中性情况",
            "probability": 0.4,
            "description": "板块维持当前状态，结构性分化持续",
            "implications": ["板块整体震荡", "个股表现分化", "精选个股机会"]
        }

        scenarios.append(neutral)

        return scenarios

    def _get_core_insight(self, metrics: Dict) -> str:
        """获取核心洞察"""
        health = metrics.get('internal_health', 50)
        coherence = metrics.get('momentum_coherence', 0.5) or 0.5
        divergence_type = metrics.get('divergence_type', 'none')

        if health > 70 and coherence > 0.7:
            return "板块强势且一致性高，适合整体配置"
        elif health < 40 or coherence < 0.3:
            return "板块疲弱或分化严重，需要谨慎操作"
        elif divergence_type == 'positive':
            return "价格与内部结构背离，存在低估机会"
        elif divergence_type == 'negative':
            return "价格与内部结构背离，存在高估风险"
        else:
            return "板块表现中性，存在结构性机会"

    def _interpret_ad_ratio(self, ad_ratio: float) -> str:
        """解释涨跌比"""
        if ad_ratio > 2.0:
            return "强势上涨格局"
        elif ad_ratio > 1.5:
            return "偏多格局"
        elif ad_ratio > 0.8:
            return "相对均衡"
        elif ad_ratio > 0.5:
            return "偏空格局"
        else:
            return "明显下跌格局"

    def _interpret_purity(self, purity: float) -> str:
        """解释纯度指标"""
        if purity > 0.7:
            return "方向性明确"
        elif purity > 0.5:
            return "方向性较为明确"
        else:
            return "方向性不明确，分化明显"

    def _interpret_participation(self, participation: float) -> str:
        """解释参与率"""
        if participation > 0.8:
            return "市场参与度很高"
        elif participation > 0.6:
            return "市场参与度较高"
        elif participation > 0.4:
            return "市场参与度一般"
        else:
            return "市场参与度较低"

    def _load_context_templates(self) -> Dict:
        """加载上下文模板"""
        return {
            "market_phases": {
                "accumulation": "吸筹阶段",
                "markup": "上涨阶段",
                "distribution": "派发阶段",
                "markdown": "下跌阶段"
            },
            "risk_levels": {
                "low": "低风险",
                "medium": "中等风险",
                "high": "高风险"
            }
        }

    def generate_llm_prompt(self, llm_context: LLMContext,
                           user_question: Optional[str] = None) -> str:
        """
        生成LLM提示词

        Args:
            llm_context: LLM交互上下文
            user_question: 用户问题（可选）

        Returns:
            str: 格式化的LLM提示词
        """
        prompt_parts = [
            "# 市场广度分析报告",
            "",
            "## 执行摘要",
            llm_context.executive_summary,
            "",
            "## 市场状态分析",
            llm_context.market_state_explanation,
            "",
            "## 决策逻辑",
            llm_context.decision_logic_explanation,
            "",
            "## 风险收益分析",
            f"风险等级：{llm_context.risk_reward_analysis['risk_level']}",
            f"收益潜力：{llm_context.risk_reward_analysis['reward_potential']}",
            f"风险因素：{', '.join(llm_context.risk_reward_analysis['risk_factors'])}",
            f"收益驱动：{', '.join(llm_context.risk_reward_analysis['reward_drivers'])}",
            "",
            "## 操作建议",
        ]

        for rec in llm_context.actionable_recommendations:
            prompt_parts.append(f"- {rec['action']}：{rec['reasoning']} (置信度：{rec['confidence']:.1%})")

        prompt_parts.extend([
            "",
            "## 置信度分析",
            f"总体置信度：{llm_context.confidence_breakdown['overall_confidence']:.1%}",
            f"主要因素：{', '.join(llm_context.confidence_breakdown['factors'])}",
            "",
            "## 支持证据",
        ])

        for evidence in llm_context.supporting_evidence:
            prompt_parts.append(f"- {evidence}")

        if user_question:
            prompt_parts.extend([
                "",
                "## 用户问题",
                user_question,
                "",
                "请基于以上分析回答用户问题，确保回答准确、专业且易于理解。"
            ])

        return "\n".join(prompt_parts)
