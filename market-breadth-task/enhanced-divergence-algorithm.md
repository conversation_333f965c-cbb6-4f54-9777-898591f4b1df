# 增强版背离检测算法实现

## 一、核心算法实现

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


# ========================================
# 数据结构定义
# ========================================

class DivergenceType(Enum):
    """背离类型枚举"""
    NEGATIVE = "negative"  # 负背离（价涨内弱）
    POSITIVE = "positive"  # 正背离（价跌内强）
    VOLUME = "volume"      # 成交量背离
    COMPLEX = "complex"    # 复合背离
    NONE = "none"         # 无背离


@dataclass
class DivergenceResult:
    """背离检测结果"""
    sector: str
    divergence_type: DivergenceType
    base_severity: float
    enhanced_severity: float
    adjustments: Dict[str, float]
    confidence: float
    risk_level: str
    description: str
    recommendation: str
    supporting_evidence: List[str]


@dataclass
class MarketContext:
    """市场环境上下文"""
    volatility: float = 0.02          # 市场波动率
    trend_strength: float = 0.5       # 趋势强度
    market_phase: str = "normal"      # 市场阶段
    sector_correlation: float = 0.7   # 板块相关性
    
    
@dataclass
class HistoricalContext:
    """历史数据上下文"""
    divergence_frequency: float = 0.1  # 历史背离频率
    avg_severity: float = 0.02         # 平均严重度
    false_signal_rate: float = 0.2     # 误报率
    recovery_time: float = 5.0         # 平均恢复时间（天）


# ========================================
# 增强版背离检测算法
# ========================================

class EnhancedDivergenceDetector:
    """增强版背离检测器"""
    
    def __init__(self):
        # 基础阈值
        self.price_threshold = 0.01  # 1%价格变化阈值
        self.ad_neutral = 1.0       # AD比率中性值
        
        # 调整系数
        self.adjustment_factors = {
            'volume_divergence': 1.2,      # 成交量背离
            'nh_nl_confirmation': 1.3,     # 新高新低确认
            'ma_divergence': 1.15,         # 均线背离
            'rsi_divergence': 1.1,         # RSI背离
            'coherence_penalty': 0.8,      # 低一致性惩罚
            'frequent_divergence': 0.8,    # 频繁背离折扣
            'high_volatility': 0.9,        # 高波动率折扣
            'trend_against': 1.25,         # 逆势背离加重
            'correlation_factor': 1.1      # 高相关性加重
        }
        
    def detect_divergence(self, 
                         price_change: float,
                         breadth_metrics: Dict,
                         historical_context: Optional[HistoricalContext] = None,
                         market_context: Optional[MarketContext] = None) -> DivergenceResult:
        """
        综合背离检测
        
        参数:
            price_change: 价格变化率
            breadth_metrics: 广度指标字典
            historical_context: 历史上下文
            market_context: 市场环境
            
        返回:
            DivergenceResult: 背离检测结果
        """
        # 默认上下文
        if historical_context is None:
            historical_context = HistoricalContext()
        if market_context is None:
            market_context = MarketContext()
            
        # 1. 基础背离检测
        div_type, base_severity = self._detect_basic_divergence(
            price_change, breadth_metrics
        )
        
        if div_type == DivergenceType.NONE:
            return self._create_no_divergence_result(
                breadth_metrics.get('sector_name', 'Unknown')
            )
        
        # 2. 多维度验证和调整
        adjustments, evidences = self._calculate_adjustments(
            price_change, breadth_metrics, div_type,
            historical_context, market_context
        )
        
        # 3. 计算增强严重度
        total_adjustment = self._calculate_total_adjustment(adjustments)
        enhanced_severity = base_severity * total_adjustment
        
        # 4. 计算置信度
        confidence = self._calculate_confidence(
            adjustments, historical_context, len(evidences)
        )
        
        # 5. 确定风险等级
        risk_level = self._determine_risk_level(enhanced_severity, confidence)
        
        # 6. 生成描述和建议
        description = self._generate_description(
            div_type, price_change, breadth_metrics, adjustments
        )
        recommendation = self._generate_recommendation(
            div_type, risk_level, enhanced_severity, confidence
        )
        
        return DivergenceResult(
            sector=breadth_metrics.get('sector_name', 'Unknown'),
            divergence_type=div_type,
            base_severity=round(base_severity, 4),
            enhanced_severity=round(enhanced_severity, 4),
            adjustments=adjustments,
            confidence=round(confidence, 3),
            risk_level=risk_level,
            description=description,
            recommendation=recommendation,
            supporting_evidence=evidences
        )
    
    def _detect_basic_divergence(self, price_change: float, 
                                breadth_metrics: Dict) -> Tuple[DivergenceType, float]:
        """检测基础背离类型和严重度"""
        ad_ratio = breadth_metrics.get('ad_ratio', 1.0)
        
        # 负背离：价格上涨但内部结构弱
        if price_change > self.price_threshold and ad_ratio < self.ad_neutral:
            severity = abs(price_change * (1 - ad_ratio))
            return DivergenceType.NEGATIVE, severity
            
        # 正背离：价格下跌但内部结构强
        elif price_change < -self.price_threshold and ad_ratio > self.ad_neutral:
            severity = abs(price_change * ad_ratio)
            return DivergenceType.POSITIVE, severity
            
        # 成交量背离
        elif self._check_volume_divergence(price_change, breadth_metrics):
            severity = abs(price_change * 0.5)  # 成交量背离基础权重较低
            return DivergenceType.VOLUME, severity
            
        return DivergenceType.NONE, 0.0
    
    def _calculate_adjustments(self, price_change: float, breadth_metrics: Dict,
                             div_type: DivergenceType,
                             hist_ctx: HistoricalContext,
                             mkt_ctx: MarketContext) -> Tuple[Dict[str, float], List[str]]:
        """计算多维度调整系数"""
        adjustments = {}
        evidences = []
        
        # 1. 成交量背离确认
        volume_breadth = breadth_metrics.get('volume_breadth', 0)
        if volume_breadth * price_change < 0:  # 方向相反
            adjustments['volume_divergence'] = self.adjustment_factors['volume_divergence']
            evidences.append(f"成交量流向与价格相反(volume_breadth={volume_breadth:.2f})")
        
        # 2. 新高新低比率确认
        nh_nl_ratio = breadth_metrics.get('nh_nl_ratio', 1.0)
        if div_type == DivergenceType.NEGATIVE and nh_nl_ratio < 0.5:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新低多于新高(NH/NL={nh_nl_ratio:.2f})")
        elif div_type == DivergenceType.POSITIVE and nh_nl_ratio > 2.0:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新高多于新低(NH/NL={nh_nl_ratio:.2f})")
        
        # 3. 均线位置背离
        ma50_pct = breadth_metrics.get('ma50_breadth', 0.5)
        ma200_pct = breadth_metrics.get('ma200_breadth', 0.5)
        ma_health = (ma50_pct + ma200_pct) / 2
        
        if div_type == DivergenceType.NEGATIVE and ma_health < 0.4:
            adjustments['ma_divergence'] = self.adjustment_factors['ma_divergence']
            evidences.append(f"均线支撑弱(MA健康度={ma_health:.2f})")
        elif div_type == DivergenceType.POSITIVE and ma_health > 0.6:
            adjustments['ma_divergence'] = self.adjustment_factors['ma_divergence']
            evidences.append(f"均线支撑强(MA健康度={ma_health:.2f})")
        
        # 4. RSI背离
        avg_rsi = breadth_metrics.get('avg_rsi', 50)
        if div_type == DivergenceType.NEGATIVE and avg_rsi > 70:
            adjustments['rsi_divergence'] = self.adjustment_factors['rsi_divergence']
            evidences.append(f"RSI超买但价格仍涨(RSI={avg_rsi:.1f})")
        elif div_type == DivergenceType.POSITIVE and avg_rsi < 30:
            adjustments['rsi_divergence'] = self.adjustment_factors['rsi_divergence']
            evidences.append(f"RSI超卖但价格仍跌(RSI={avg_rsi:.1f})")
        
        # 5. 动量一致性惩罚
        coherence = breadth_metrics.get('momentum_coherence', 0.5)
        if coherence < 0.3:
            adjustments['coherence_penalty'] = self.adjustment_factors['coherence_penalty']
            evidences.append(f"板块内部高度分化(一致性={coherence:.2f})")
        
        # 6. 历史背离频率调整
        if hist_ctx.divergence_frequency > 0.3:
            adjustments['frequent_divergence'] = self.adjustment_factors['frequent_divergence']
            evidences.append(f"历史背离频繁(频率={hist_ctx.divergence_frequency:.1%})")
        
        # 7. 市场环境调整
        if mkt_ctx.volatility > 0.03:
            adjustments['high_volatility'] = self.adjustment_factors['high_volatility']
            evidences.append(f"市场高波动(波动率={mkt_ctx.volatility:.1%})")
        
        # 8. 趋势确认
        if self._is_against_trend(div_type, mkt_ctx.trend_strength):
            adjustments['trend_against'] = self.adjustment_factors['trend_against']
            evidences.append("背离方向与市场趋势相反")
        
        # 9. 板块相关性
        if mkt_ctx.sector_correlation > 0.8:
            adjustments['correlation_factor'] = self.adjustment_factors['correlation_factor']
            evidences.append(f"板块与市场高度相关(相关性={mkt_ctx.sector_correlation:.2f})")
        
        return adjustments, evidences
    
    def _check_volume_divergence(self, price_change: float, 
                               breadth_metrics: Dict) -> bool:
        """检查是否存在成交量背离"""
        volume_breadth = breadth_metrics.get('volume_breadth', 0)
        
        # 价格大涨但资金流出，或价格大跌但资金流入
        if abs(price_change) > 0.02 and volume_breadth * price_change < -0.01:
            return True
        return False
    
    def _calculate_total_adjustment(self, adjustments: Dict[str, float]) -> float:
        """计算总调整系数"""
        if not adjustments:
            return 1.0
        
        # 乘积方式组合所有调整
        total = 1.0
        for factor in adjustments.values():
            total *= factor
            
        # 限制最大调整幅度
        return max(0.5, min(3.0, total))
    
    def _calculate_confidence(self, adjustments: Dict[str, float],
                            hist_ctx: HistoricalContext,
                            evidence_count: int) -> float:
        """计算背离信号的置信度"""
        # 基础置信度
        base_confidence = 0.5
        
        # 证据数量加分
        evidence_bonus = min(0.3, evidence_count * 0.05)
        
        # 历史准确率加分
        accuracy_bonus = max(0, (1 - hist_ctx.false_signal_rate) * 0.2)
        
        # 调整因子一致性加分
        adjustment_values = list(adjustments.values())
        if adjustment_values:
            # 如果大多数因子都指向同一方向
            confirming_factors = sum(1 for v in adjustment_values if v > 1.0)
            consistency_bonus = (confirming_factors / len(adjustment_values)) * 0.2
        else:
            consistency_bonus = 0
        
        confidence = base_confidence + evidence_bonus + accuracy_bonus + consistency_bonus
        
        return min(0.95, confidence)  # 最高95%置信度
    
    def _determine_risk_level(self, severity: float, confidence: float) -> str:
        """确定风险等级"""
        # 综合风险分 = 严重度 × 置信度
        risk_score = severity * confidence
        
        if risk_score >= 0.05:
            return "extreme"
        elif risk_score >= 0.03:
            return "high"
        elif risk_score >= 0.015:
            return "medium"
        elif risk_score >= 0.008:
            return "low"
        else:
            return "minimal"
    
    def _is_against_trend(self, div_type: DivergenceType, 
                         trend_strength: float) -> bool:
        """判断是否逆势背离"""
        # 强上升趋势中的负背离，或强下降趋势中的正背离
        if div_type == DivergenceType.NEGATIVE and trend_strength > 0.7:
            return True
        elif div_type == DivergenceType.POSITIVE and trend_strength < -0.7:
            return True
        return False
    
    def _generate_description(self, div_type: DivergenceType,
                            price_change: float,
                            breadth_metrics: Dict,
                            adjustments: Dict) -> str:
        """生成背离描述"""
        ad_ratio = breadth_metrics.get('ad_ratio', 1.0)
        sector = breadth_metrics.get('sector_name', 'Unknown')
        
        if div_type == DivergenceType.NEGATIVE:
            desc = f"{sector}板块价格上涨{price_change:.1%}，但仅{ad_ratio*100:.0f}%个股上涨"
        elif div_type == DivergenceType.POSITIVE:
            desc = f"{sector}板块价格下跌{abs(price_change):.1%}，但{ad_ratio*100:.0f}%个股在上涨"
        elif div_type == DivergenceType.VOLUME:
            desc = f"{sector}板块价格与成交量流向背离"
        else:
            desc = f"{sector}板块出现复合背离"
        
        # 添加关键调整因素
        key_factors = [k for k, v in adjustments.items() if v > 1.1]
        if key_factors:
            desc += f"，{len(key_factors)}个因素确认背离"
            
        return desc
    
    def _generate_recommendation(self, div_type: DivergenceType,
                               risk_level: str,
                               severity: float,
                               confidence: float) -> str:
        """生成操作建议"""
        recommendations = {
            DivergenceType.NEGATIVE: {
                'extreme': "立即减仓或清仓，上涨缺乏支撑",
                'high': "减仓50%以上，密切关注",
                'medium': "逢高减仓30%，设置止损",
                'low': "谨慎持有，不追高",
                'minimal': "继续观察"
            },
            DivergenceType.POSITIVE: {
                'extreme': "积极建仓，下跌即将结束",
                'high': "分批买入，把握反弹机会",
                'medium': "小仓位试探，等待确认",
                'low': "关注但不急于行动",
                'minimal': "继续观察"
            },
            DivergenceType.VOLUME: {
                'extreme': "警惕资金异动，控制风险",
                'high': "降低仓位，观察资金流向",
                'medium': "谨慎操作，缩短持有期",
                'low': "正常持有，关注变化",
                'minimal': "可以忽略"
            }
        }
        
        base_rec = recommendations.get(div_type, {}).get(risk_level, "评估后操作")
        
        # 添加置信度说明
        if confidence < 0.6:
            base_rec += "（信号可靠性一般）"
        elif confidence > 0.8:
            base_rec += "（信号高度可靠）"
            
        return base_rec
    
    def _create_no_divergence_result(self, sector: str) -> DivergenceResult:
        """创建无背离的结果"""
        return DivergenceResult(
            sector=sector,
            divergence_type=DivergenceType.NONE,
            base_severity=0.0,
            enhanced_severity=0.0,
            adjustments={},
            confidence=0.0,
            risk_level="none",
            description=f"{sector}板块价格与内部结构一致",
            recommendation="正常操作",
            supporting_evidence=[]
        )


# ========================================
# 批量背离检测
# ========================================

class DivergenceBatchAnalyzer:
    """批量背离分析器"""
    
    def __init__(self):
        self.detector = EnhancedDivergenceDetector()
        
    def analyze_market_divergences(self,
                                 price_changes: Dict[str, float],
                                 sector_breadth: Dict[str, Dict],
                                 historical_data: Optional[Dict] = None,
                                 market_context: Optional[MarketContext] = None) -> Dict:
        """
        分析整个市场的背离情况
        
        返回:
            包含所有板块背离分析和市场总结的字典
        """
        results = {
            'timestamp': datetime.now(),
            'divergences': [],
            'summary': {},
            'risk_alerts': []
        }
        
        # 分析每个板块
        for sector, price_change in price_changes.items():
            if sector not in sector_breadth:
                continue
                
            # 准备数据
            breadth_metrics = sector_breadth[sector].copy()
            breadth_metrics['sector_name'] = sector
            
            # 历史上下文
            hist_ctx = None
            if historical_data and sector in historical_data:
                hist_ctx = HistoricalContext(**historical_data[sector])
            
            # 检测背离
            divergence = self.detector.detect_divergence(
                price_change,
                breadth_metrics,
                hist_ctx,
                market_context
            )
            
            if divergence.divergence_type != DivergenceType.NONE:
                results['divergences'].append(divergence)
        
        # 生成汇总
        results['summary'] = self._generate_summary(results['divergences'])
        
        # 生成风险警报
        results['risk_alerts'] = self._generate_risk_alerts(results['divergences'])
        
        return results
    
    def _generate_summary(self, divergences: List[DivergenceResult]) -> Dict:
        """生成背离汇总"""
        if not divergences:
            return {
                'total_count': 0,
                'risk_distribution': {},
                'most_severe': None,
                'market_implication': '市场结构健康，无明显背离'
            }
        
        # 统计各类背离
        type_counts = {}
        risk_counts = {}
        
        for div in divergences:
            # 类型统计
            div_type = div.divergence_type.value
            type_counts[div_type] = type_counts.get(div_type, 0) + 1
            
            # 风险等级统计
            risk_counts[div.risk_level] = risk_counts.get(div.risk_level, 0) + 1
        
        # 找出最严重的背离
        most_severe = max(divergences, key=lambda x: x.enhanced_severity)
        
        # 市场含义判断
        high_risk_count = risk_counts.get('high', 0) + risk_counts.get('extreme', 0)
        if high_risk_count >= 3:
            implication = '多个板块出现严重背离，市场结构恶化，建议降低仓位'
        elif high_risk_count >= 1:
            implication = '部分板块出现背离，需要调整持仓结构'
        elif len(divergences) >= 5:
            implication = '背离普遍但不严重，市场可能处于转折期'
        else:
            implication = '个别板块轻微背离，整体风险可控'
        
        return {
            'total_count': len(divergences),
            'type_distribution': type_counts,
            'risk_distribution': risk_counts,
            'most_severe': {
                'sector': most_severe.sector,
                'severity': most_severe.enhanced_severity,
                'type': most_severe.divergence_type.value
            },
            'average_severity': np.mean([d.enhanced_severity for d in divergences]),
            'market_implication': implication
        }
    
    def _generate_risk_alerts(self, divergences: List[DivergenceResult]) -> List[Dict]:
        """生成风险警报"""
        alerts = []
        
        # 检查极端风险
        extreme_divs = [d for d in divergences if d.risk_level == 'extreme']
        if extreme_divs:
            alerts.append({
                'level': 'CRITICAL',
                'message': f'{len(extreme_divs)}个板块出现极端背离',
                'sectors': [d.sector for d in extreme_divs],
                'action': '立即检查并调整相关持仓'
            })
        
        # 检查负背离集中
        neg_divs = [d for d in divergences 
                   if d.divergence_type == DivergenceType.NEGATIVE 
                   and d.risk_level in ['high', 'extreme']]
        if len(neg_divs) >= 3:
            alerts.append({
                'level': 'HIGH',
                'message': '多个板块同时出现负背离，上涨可能不可持续',
                'sectors': [d.sector for d in neg_divs],
                'action': '考虑系统性降低仓位'
            })
        
        # 检查高置信度信号
        high_conf_divs = [d for d in divergences if d.confidence > 0.8]
        if high_conf_divs:
            alerts.append({
                'level': 'MEDIUM',
                'message': f'{len(high_conf_divs)}个高置信度背离信号',
                'details': [{
                    'sector': d.sector,
                    'type': d.divergence_type.value,
                    'confidence': d.confidence
                } for d in high_conf_divs],
                'action': '重点关注这些板块的后续走势'
            })
        
        return alerts


# ========================================
# 使用示例
# ========================================

def example_usage():
    """演示如何使用增强版背离检测"""
    
    # 1. 准备数据
    price_changes = {
        'Technology': 0.025,   # 上涨2.5%
        'Healthcare': -0.018,  # 下跌1.8%
        'Energy': 0.032,       # 上涨3.2%
        'Financials': 0.008    # 上涨0.8%
    }
    
    sector_breadth = {
        'Technology': {
            'ad_ratio': 1.35,
            'volume_breadth': 0.15,
            'nh_nl_ratio': 2.1,
            'ma50_breadth': 0.72,
            'ma200_breadth': 0.68,
            'avg_rsi': 58.5,
            'momentum_coherence': 0.65
        },
        'Healthcare': {
            'ad_ratio': 1.42,
            'volume_breadth': 0.08,
            'nh_nl_ratio': 0.9,
            'ma50_breadth': 0.48,
            'ma200_breadth': 0.52,
            'avg_rsi': 45.3,
            'momentum_coherence': 0.35
        },
        'Energy': {
            'ad_ratio': 0.55,
            'volume_breadth': -0.22,
            'nh_nl_ratio': 0.4,
            'ma50_breadth': 0.35,
            'ma200_breadth': 0.38,
            'avg_rsi': 72.5,
            'momentum_coherence': 0.28
        },
        'Financials': {
            'ad_ratio': 1.05,
            'volume_breadth': 0.02,
            'nh_nl_ratio': 1.1,
            'ma50_breadth': 0.55,
            'ma200_breadth': 0.58,
            'avg_rsi': 52.1,
            'momentum_coherence': 0.72
        }
    }
    
    # 历史数据
    historical_data = {
        'Energy': {
            'divergence_frequency': 0.15,
            'avg_severity': 0.025,
            'false_signal_rate': 0.18
        }
    }
    
    # 市场环境
    market_context = MarketContext(
        volatility=0.018,
        trend_strength=0.6,
        market_phase='bullish',
        sector_correlation=0.75
    )
    
    # 2. 执行分析
    analyzer = DivergenceBatchAnalyzer()
    results = analyzer.analyze_market_divergences(
        price_changes,
        sector_breadth,
        historical_data,
        market_context
    )
    
    # 3. 输出结果
    print("=== 增强版背离检测结果 ===\n")
    
    print(f"检测时间: {results['timestamp']}")
    print(f"发现背离: {results['summary']['total_count']}个\n")
    
    # 详细背离信息
    for div in results['divergences']:
        print(f"{div.sector}板块:")
        print(f"  背离类型: {div.divergence_type.value}")
        print(f"  基础严重度: {div.base_severity:.4f}")
        print(f"  增强严重度: {div.enhanced_severity:.4f} (提升{(div.enhanced_severity/div.base_severity-1)*100:.0f}%)")
        print(f"  置信度: {div.confidence:.1%}")
        print(f"  风险等级: {div.risk_level}")
        print(f"  描述: {div.description}")
        print(f"  建议: {div.recommendation}")
        
        if div.adjustments:
            print(f"  调整因子:")
            for factor, value in div.adjustments.items():
                print(f"    - {factor}: {value:.2f}")
        
        if div.supporting_evidence:
            print(f"  支撑证据:")
            for evidence in div.supporting_evidence:
                print(f"    - {evidence}")
        print()
    
    # 市场总结
    print("\n市场背离总结:")
    summary = results['summary']
    print(f"  总体评估: {summary['market_implication']}")
    print(f"  平均严重度: {summary['average_severity']:.4f}")
    if summary['most_severe']:
        print(f"  最严重背离: {summary['most_severe']['sector']} ({summary['most_severe']['severity']:.4f})")
    
    # 风险警报
    if results['risk_alerts']:
        print("\n风险警报:")
        for alert in results['risk_alerts']:
            print(f"  [{alert['level']}] {alert['message']}")
            print(f"    行动建议: {alert['action']}")
    
    return results


def compare_with_basic():
    """对比基础算法和增强算法"""
    
    print("\n=== 算法效果对比 ===\n")
    
    # 测试数据：Energy板块
    test_case = {
        'price_change': 0.032,  # 涨3.2%
        'breadth': {
            'sector_name': 'Energy',
            'ad_ratio': 0.55,        # 45%涨55%跌
            'volume_breadth': -0.22,  # 资金流出
            'nh_nl_ratio': 0.4,      # 新低多
            'ma50_breadth': 0.35,    # 仅35%在MA50上
            'avg_rsi': 72.5,         # RSI超买
            'momentum_coherence': 0.28
        }
    }
    
    # 基础算法
    basic_severity = abs(test_case['price_change'] * (1 - test_case['breadth']['ad_ratio']))
    
    # 增强算法
    detector = EnhancedDivergenceDetector()
    enhanced_result = detector.detect_divergence(
        test_case['price_change'],
        test_case['breadth']
    )
    
    print("Energy板块背离分析:")
    print(f"  价格变化: +{test_case['price_change']:.1%}")
    print(f"  AD比率: {test_case['breadth']['ad_ratio']:.2f}")
    print(f"\n基础算法:")
    print(f"  严重度: {basic_severity:.4f}")
    print(f"  判断: 负背离")
    
    print(f"\n增强算法:")
    print(f"  基础严重度: {enhanced_result.base_severity:.4f}")
    print(f"  增强严重度: {enhanced_result.enhanced_severity:.4f}")
    print(f"  提升幅度: {(enhanced_result.enhanced_severity/enhanced_result.base_severity-1)*100:.0f}%")
    print(f"  置信度: {enhanced_result.confidence:.1%}")
    print(f"  风险等级: {enhanced_result.risk_level}")
    
    print(f"\n关键发现:")
    for evidence in enhanced_result.supporting_evidence:
        print(f"  - {evidence}")
    
    print(f"\n决策对比:")
    print(f"  基础算法: 一般负背离，观察")
    print(f"  增强算法: {enhanced_result.recommendation}")


if __name__ == "__main__":
    # 运行示例
    results = example_usage()
    
    # 对比效果
    compare_with_basic()
```

## 二、算法特性总结

### 2.1 核心改进点

| 维度 | 基础算法 | 增强算法 |
|------|---------|---------|
| 考虑因素 | 仅价格和AD比率 | 9个维度综合分析 |
| 严重度范围 | 固定计算 | 动态调整(0.5x-3x) |
| 置信度 | 无 | 0-95%量化评估 |
| 风险分级 | 无 | 5级风险体系 |
| 历史学习 | 无 | 考虑历史模式 |
| 市场适应 | 无 | 根据市场环境调整 |

### 2.2 新增的9个验证维度

1. **成交量背离** - 资金流向验证
2. **新高新低比率** - 中期趋势确认
3. **均线位置** - 技术面支撑
4. **RSI背离** - 超买超卖确认
5. **动量一致性** - 内部结构评估
6. **历史频率** - 避免频繁误报
7. **市场波动率** - 环境适应性
8. **趋势强度** - 逆势加重
9. **板块相关性** - 系统性风险

### 2.3 输出信息的丰富性

```python
DivergenceResult包含:
- 背离类型和严重度
- 置信度评分
- 风险等级
- 详细描述
- 具体建议
- 支撑证据列表
- 所有调整因子
```

### 2.4 实际应用价值

1. **更准确的风险识别** - 多维验证减少误判
2. **更及时的预警** - 置信度高的信号优先处理
3. **更精准的操作建议** - 根据风险等级给出具体建议
4. **更好的市场适应性** - 自动适应不同市场环境

这个增强版算法特别适合专业投资机构的风险管理系统使用。