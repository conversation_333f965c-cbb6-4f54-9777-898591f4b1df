"""
增强版板块轮动分析器
基于优化文档要求，优化权重分配、轮动阶段识别和统一RII计算
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class WeightAllocationDetails:
    """权重分配详细信息"""
    weights: pd.Series
    allocation_reason: str
    coherence_adjustment: Dict[str, float]
    diversification_metrics: Dict[str, float]
    alternative_allocations: Dict[str, pd.Series]
    risk_constraints: Dict[str, Any]


@dataclass
class RotationStageDetails:
    """轮动阶段详细信息"""
    stage: str
    stage_probabilities: Dict[str, float]
    fuzzy_scores: Dict[str, float]
    transition_indicators: Dict[str, float]
    decision_factors: List[str]
    confidence: float


@dataclass
class UnifiedRIIDetails:
    """统一RII详细信息"""
    unified_rii: float
    component_contributions: Dict[str, float]
    calculation_steps: List[Dict]
    intermediate_variables: Dict[str, float]
    normalization_factors: Dict[str, float]


class EnhancedSectorRotationAnalyzer:
    """增强版板块轮动分析器"""
    
    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化增强版板块轮动分析器
        
        Args:
            decision_tracker: 可选的决策追踪器
        """
        self.lookback_short = 5
        self.lookback_medium = 20
        self.lookback_long = 60
        self.tracker = decision_tracker
        
        # 一致性权重调整参数
        self.coherence_weight_limits = {
            'strong_consensus': {'max_single': 0.5, 'max_top3': 0.8},
            'moderate_coherence': {'max_single': 0.3, 'max_top3': 0.7},
            'high_dispersion': {'max_single': 0.15, 'max_top3': 0.5},
            'directional_divergence': {'max_single': 0.2, 'max_top3': 0.6}
        }
        
        # 模糊逻辑参数
        self.fuzzy_thresholds = {
            'rii_low': 0.2, 'rii_medium': 0.5, 'rii_high': 0.8,
            'velocity_low': 0.15, 'velocity_medium': 0.4, 'velocity_high': 0.7,
            'health_low': 40, 'health_medium': 60, 'health_high': 80
        }
    
    def calculate_optimal_weights_enhanced(self, relative_strength: pd.Series, 
                                         stage: str,
                                         breadth_metrics: Dict[str, Any],
                                         risk_level: str,
                                         coherence_analysis: Optional[Dict] = None) -> WeightAllocationDetails:
        """
        计算最优权重（增强版）
        
        Args:
            relative_strength: 相对强度
            stage: 轮动阶段
            breadth_metrics: 广度指标
            risk_level: 风险等级
            coherence_analysis: 一致性分析结果
            
        Returns:
            WeightAllocationDetails: 权重分配详细信息
        """
        if self.tracker:
            self.tracker.start_decision('weight_allocation', {
                'stage': stage,
                'risk_level': risk_level,
                'sectors_count': len(relative_strength)
            })
        
        sectors = relative_strength.index
        weights = pd.Series(0.0, index=sectors)
        
        # 1. 获取一致性类型和调整参数
        coherence_type, coherence_adjustment = self._analyze_coherence_impact(
            coherence_analysis, breadth_metrics
        )
        
        # 2. 计算综合评分（考虑一致性）
        combined_scores = self._calculate_enhanced_scores(
            relative_strength, breadth_metrics, coherence_type
        )
        
        # 3. 应用风险约束和一致性限制
        risk_constraints = self._get_risk_constraints(stage, risk_level, coherence_type)
        
        # 4. 执行权重分配
        weights, allocation_reason = self._allocate_weights_with_constraints(
            combined_scores, risk_constraints, coherence_type
        )
        
        # 5. 计算分散度指标
        diversification_metrics = self._calculate_diversification_metrics(weights)
        
        # 6. 生成备选分配方案
        alternative_allocations = self._generate_alternative_allocations(
            combined_scores, risk_constraints
        )
        
        # 7. 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'weight_calculation',
                {'weights': weights.to_dict(), 'allocation_reason': allocation_reason},
                f'基于{coherence_type}一致性类型，{allocation_reason}',
                confidence=0.85
            )
            
            self.tracker.complete_decision({
                'final_weights': weights.to_dict(),
                'coherence_type': coherence_type,
                'diversification': diversification_metrics
            }, diversification_metrics.get('overall_score', 0.5))
        
        return WeightAllocationDetails(
            weights=weights,
            allocation_reason=allocation_reason,
            coherence_adjustment=coherence_adjustment,
            diversification_metrics=diversification_metrics,
            alternative_allocations=alternative_allocations,
            risk_constraints=risk_constraints
        )
    
    def identify_rotation_stage_enhanced(self, rii: float, velocity: float,
                                       breadth_metrics: Dict[str, Any]) -> RotationStageDetails:
        """
        识别轮动阶段（增强版，使用模糊逻辑）
        
        Args:
            rii: 轮动强度指数
            velocity: 排名变化速度
            breadth_metrics: 广度指标
            
        Returns:
            RotationStageDetails: 轮动阶段详细信息
        """
        if self.tracker:
            self.tracker.start_decision('rotation_stage_identification', {
                'rii': rii,
                'velocity': velocity,
                'breadth_count': len(breadth_metrics)
            })
        
        # 1. 计算模糊隶属度
        fuzzy_scores = self._calculate_fuzzy_membership(rii, velocity, breadth_metrics)
        
        # 2. 计算阶段概率
        stage_probabilities = self._calculate_stage_probabilities(fuzzy_scores)
        
        # 3. 识别主要阶段
        primary_stage = max(stage_probabilities, key=stage_probabilities.get)
        
        # 4. 检测阶段转换
        transition_indicators = self._detect_stage_transitions(
            stage_probabilities, fuzzy_scores
        )
        
        # 5. 生成决策因素
        decision_factors = self._generate_decision_factors(
            fuzzy_scores, stage_probabilities, transition_indicators
        )
        
        # 6. 计算置信度
        confidence = self._calculate_stage_confidence(stage_probabilities, fuzzy_scores)
        
        # 7. 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'fuzzy_analysis',
                {'fuzzy_scores': fuzzy_scores, 'stage_probs': stage_probabilities},
                f'使用模糊逻辑分析，识别为{primary_stage}阶段',
                confidence=confidence
            )
            
            self.tracker.complete_decision({
                'primary_stage': primary_stage,
                'confidence': confidence,
                'transition_risk': transition_indicators.get('transition_risk', 0)
            }, confidence)
        
        return RotationStageDetails(
            stage=primary_stage,
            stage_probabilities=stage_probabilities,
            fuzzy_scores=fuzzy_scores,
            transition_indicators=transition_indicators,
            decision_factors=decision_factors,
            confidence=confidence
        )
    
    def calculate_unified_rii_enhanced(self, price_dispersion: float, 
                                     rank_velocity: float,
                                     volume_concentration: float, 
                                     breadth_metrics: Dict[str, Any]) -> UnifiedRIIDetails:
        """
        计算统一RII（增强版，详细分解）
        
        Args:
            price_dispersion: 价格离散度
            rank_velocity: 排名变化速度
            volume_concentration: 成交量集中度
            breadth_metrics: 广度指标
            
        Returns:
            UnifiedRIIDetails: 统一RII详细信息
        """
        if self.tracker:
            self.tracker.start_decision('unified_rii_calculation', {
                'price_dispersion': price_dispersion,
                'rank_velocity': rank_velocity,
                'volume_concentration': volume_concentration
            })
        
        calculation_steps = []
        intermediate_variables = {}
        
        # 步骤1: 价格维度RII计算
        price_rii, price_step = self._calculate_price_rii_component(
            price_dispersion, rank_velocity, volume_concentration
        )
        calculation_steps.append(price_step)
        intermediate_variables['price_rii'] = price_rii
        
        # 步骤2: 广度维度因子计算
        breadth_factor, breadth_step = self._calculate_breadth_factor_component(
            breadth_metrics
        )
        calculation_steps.append(breadth_step)
        intermediate_variables['breadth_factor'] = breadth_factor
        
        # 步骤3: 动态权重计算
        dynamic_weights, weight_step = self._calculate_dynamic_weights(
            price_rii, breadth_factor, breadth_metrics
        )
        calculation_steps.append(weight_step)
        intermediate_variables.update(dynamic_weights)
        
        # 步骤4: 最终RII合成
        unified_rii = (dynamic_weights['price_weight'] * price_rii + 
                      dynamic_weights['breadth_weight'] * breadth_factor)
        
        # 步骤5: 归一化处理
        normalized_rii, normalization_factors = self._normalize_rii(
            unified_rii, price_rii, breadth_factor
        )
        
        # 计算各分量贡献度
        component_contributions = {
            'price_dispersion': price_dispersion * dynamic_weights['price_weight'] * 0.5,
            'rank_velocity': rank_velocity * dynamic_weights['price_weight'] * 0.3,
            'volume_concentration': volume_concentration * dynamic_weights['price_weight'] * 0.2,
            'breadth_dispersion': breadth_factor * dynamic_weights['breadth_weight'] * 0.6,
            'health_risk': breadth_factor * dynamic_weights['breadth_weight'] * 0.4
        }
        
        # 记录最终步骤
        final_step = {
            'step_name': 'final_synthesis',
            'description': f'合成最终RII: {dynamic_weights["price_weight"]:.2f} * {price_rii:.3f} + {dynamic_weights["breadth_weight"]:.2f} * {breadth_factor:.3f}',
            'result': normalized_rii,
            'formula': 'unified_rii = price_weight * price_rii + breadth_weight * breadth_factor'
        }
        calculation_steps.append(final_step)
        
        # 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'rii_synthesis',
                {'unified_rii': normalized_rii, 'components': component_contributions},
                f'合成统一RII={normalized_rii:.3f}，价格权重{dynamic_weights["price_weight"]:.2f}',
                confidence=0.9
            )
            
            self.tracker.complete_decision({
                'unified_rii': normalized_rii,
                'price_contribution': price_rii * dynamic_weights['price_weight'],
                'breadth_contribution': breadth_factor * dynamic_weights['breadth_weight']
            }, 0.9)
        
        return UnifiedRIIDetails(
            unified_rii=normalized_rii,
            component_contributions=component_contributions,
            calculation_steps=calculation_steps,
            intermediate_variables=intermediate_variables,
            normalization_factors=normalization_factors
        )

    def _analyze_coherence_impact(self, coherence_analysis: Optional[Dict],
                                breadth_metrics: Dict[str, Any]) -> Tuple[str, Dict[str, float]]:
        """分析一致性对权重分配的影响"""
        if coherence_analysis and 'coherence_type' in coherence_analysis:
            coherence_type = coherence_analysis['coherence_type']
        else:
            # 从广度指标推断一致性类型
            if breadth_metrics:
                coherence_values = []
                for sector, metrics in breadth_metrics.items():
                    if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence:
                        coherence_values.append(metrics.momentum_coherence)

                if coherence_values:
                    avg_coherence = np.mean(coherence_values)
                    if avg_coherence > 0.7:
                        coherence_type = 'strong_consensus'
                    elif avg_coherence > 0.4:
                        coherence_type = 'moderate_coherence'
                    else:
                        coherence_type = 'high_dispersion'
                else:
                    coherence_type = 'moderate_coherence'
            else:
                coherence_type = 'moderate_coherence'

        # 计算一致性调整因子
        coherence_adjustment = {
            'concentration_factor': self._get_concentration_factor(coherence_type),
            'diversification_requirement': self._get_diversification_requirement(coherence_type),
            'max_single_weight': self.coherence_weight_limits[coherence_type]['max_single'],
            'max_top3_weight': self.coherence_weight_limits[coherence_type]['max_top3']
        }

        return coherence_type, coherence_adjustment

    def _calculate_enhanced_scores(self, relative_strength: pd.Series,
                                 breadth_metrics: Dict[str, Any],
                                 coherence_type: str) -> pd.Series:
        """计算增强版综合评分"""
        sectors = relative_strength.index
        combined_scores = pd.Series(index=sectors, dtype=float)

        for sector in sectors:
            # 基础相对强度分数
            rs_score = relative_strength[sector]

            # 内部健康度分数
            if sector in breadth_metrics:
                health_score = breadth_metrics[sector].internal_health / 100

                # 一致性调整
                if hasattr(breadth_metrics[sector], 'momentum_coherence'):
                    coherence_score = breadth_metrics[sector].momentum_coherence or 0.5
                else:
                    coherence_score = 0.5
            else:
                health_score = 0.5
                coherence_score = 0.5

            # 根据一致性类型调整权重
            if coherence_type == 'strong_consensus':
                # 强一致性：更重视相对强度
                combined_scores[sector] = rs_score * (0.7 + 0.3 * health_score)
            elif coherence_type == 'high_dispersion':
                # 高分化：更重视内部健康度
                combined_scores[sector] = rs_score * (0.3 + 0.7 * health_score)
            else:
                # 中等一致性：平衡权重
                combined_scores[sector] = rs_score * (0.5 + 0.5 * health_score)

            # 一致性奖励/惩罚
            if coherence_score > 0.7:
                combined_scores[sector] *= 1.1  # 高一致性奖励
            elif coherence_score < 0.3:
                combined_scores[sector] *= 0.9  # 低一致性惩罚

        return combined_scores

    def _get_risk_constraints(self, stage: str, risk_level: str,
                            coherence_type: str) -> Dict[str, Any]:
        """获取风险约束条件"""
        constraints = {
            'max_single_weight': 0.3,
            'max_top3_weight': 0.7,
            'min_diversification': 3,
            'cash_reserve': 0.0
        }

        # 阶段调整
        if stage == '混乱期':
            constraints['max_single_weight'] = 0.15
            constraints['max_top3_weight'] = 0.45
            constraints['min_diversification'] = 5
        elif stage == '稳定期':
            constraints['max_single_weight'] = 0.4
            constraints['max_top3_weight'] = 0.8
            constraints['min_diversification'] = 2

        # 风险等级调整
        if risk_level == 'high':
            constraints['max_single_weight'] *= 0.7
            constraints['max_top3_weight'] *= 0.8
            constraints['cash_reserve'] = 0.1
        elif risk_level == 'low':
            constraints['max_single_weight'] *= 1.2
            constraints['max_top3_weight'] *= 1.1

        # 一致性调整
        coherence_limits = self.coherence_weight_limits[coherence_type]
        constraints['max_single_weight'] = min(
            constraints['max_single_weight'],
            coherence_limits['max_single']
        )
        constraints['max_top3_weight'] = min(
            constraints['max_top3_weight'],
            coherence_limits['max_top3']
        )

        return constraints

    def _allocate_weights_with_constraints(self, combined_scores: pd.Series,
                                         risk_constraints: Dict[str, Any],
                                         coherence_type: str) -> Tuple[pd.Series, str]:
        """在约束条件下分配权重"""
        sectors = combined_scores.index
        weights = pd.Series(0.0, index=sectors)

        # 筛选正分板块
        positive_sectors = combined_scores[combined_scores > 0.05]

        if len(positive_sectors) == 0:
            return weights, "无合格板块，全部现金"

        # 排序
        sorted_sectors = positive_sectors.sort_values(ascending=False)

        # 确定参与板块数量
        min_sectors = max(risk_constraints['min_diversification'],
                         min(len(sorted_sectors), 3))
        max_sectors = min(len(sorted_sectors), 8)

        if coherence_type == 'strong_consensus':
            target_sectors = min(min_sectors + 1, max_sectors)
            allocation_reason = f"强一致性，集中配置{target_sectors}个板块"
        elif coherence_type == 'high_dispersion':
            target_sectors = max_sectors
            allocation_reason = f"高分化，分散配置{target_sectors}个板块"
        else:
            target_sectors = (min_sectors + max_sectors) // 2
            allocation_reason = f"中等一致性，平衡配置{target_sectors}个板块"

        # 选择目标板块
        target_sectors_list = sorted_sectors.head(target_sectors)

        # 计算初始权重
        raw_weights = target_sectors_list / target_sectors_list.sum()

        # 应用单一权重限制
        max_single = risk_constraints['max_single_weight']
        capped_weights = raw_weights.clip(upper=max_single)

        # 重新归一化
        available_weight = 1.0 - risk_constraints['cash_reserve']
        final_weights = capped_weights / capped_weights.sum() * available_weight

        # 检查top3权重限制
        top3_weight = final_weights.nlargest(3).sum()
        max_top3 = risk_constraints['max_top3_weight']

        if top3_weight > max_top3:
            # 调整权重
            scale_factor = max_top3 / top3_weight
            top3_indices = final_weights.nlargest(3).index
            final_weights[top3_indices] *= scale_factor

            # 重新分配剩余权重
            remaining_weight = available_weight - final_weights.sum()
            other_indices = [idx for idx in final_weights.index if idx not in top3_indices]
            if other_indices and remaining_weight > 0:
                final_weights[other_indices] += remaining_weight / len(other_indices)

        weights[final_weights.index] = final_weights

        return weights, allocation_reason

    def _calculate_diversification_metrics(self, weights: pd.Series) -> Dict[str, float]:
        """计算分散度指标"""
        non_zero_weights = weights[weights > 0]

        if len(non_zero_weights) == 0:
            return {'herfindahl_index': 1.0, 'effective_positions': 0, 'overall_score': 0.0}

        # Herfindahl指数（越小越分散）
        herfindahl_index = (non_zero_weights ** 2).sum()

        # 有效持仓数
        effective_positions = 1 / herfindahl_index if herfindahl_index > 0 else 0

        # 权重分布均匀度
        max_weight = non_zero_weights.max()
        weight_uniformity = 1 - (max_weight - 1/len(non_zero_weights))

        # 综合分散度评分
        overall_score = (
            (1 - herfindahl_index) * 0.4 +  # Herfindahl贡献
            min(effective_positions / 5, 1) * 0.3 +  # 有效持仓贡献
            max(0, weight_uniformity) * 0.3  # 均匀度贡献
        )

        return {
            'herfindahl_index': round(herfindahl_index, 4),
            'effective_positions': round(effective_positions, 2),
            'weight_uniformity': round(weight_uniformity, 3),
            'overall_score': round(overall_score, 3)
        }

    def _generate_alternative_allocations(self, combined_scores: pd.Series,
                                        risk_constraints: Dict[str, Any]) -> Dict[str, pd.Series]:
        """生成备选分配方案"""
        alternatives = {}
        positive_sectors = combined_scores[combined_scores > 0.05]

        if len(positive_sectors) == 0:
            return alternatives

        # 保守方案：高度分散
        conservative_weights = pd.Series(0.0, index=combined_scores.index)
        top_sectors = positive_sectors.nlargest(min(8, len(positive_sectors)))
        equal_weight = 0.9 / len(top_sectors)  # 保留10%现金
        conservative_weights[top_sectors.index] = equal_weight
        alternatives['conservative'] = conservative_weights

        # 激进方案：集中配置
        aggressive_weights = pd.Series(0.0, index=combined_scores.index)
        top3 = positive_sectors.nlargest(3)
        if len(top3) >= 3:
            aggressive_weights[top3.index[0]] = 0.5
            aggressive_weights[top3.index[1]] = 0.3
            aggressive_weights[top3.index[2]] = 0.2
        alternatives['aggressive'] = aggressive_weights

        # 平衡方案：中等集中度
        balanced_weights = pd.Series(0.0, index=combined_scores.index)
        top5 = positive_sectors.nlargest(5)
        raw_weights = top5 / top5.sum() * 0.95  # 保留5%现金
        balanced_weights[top5.index] = raw_weights
        alternatives['balanced'] = balanced_weights

        return alternatives

    def _calculate_fuzzy_membership(self, rii: float, velocity: float,
                                  breadth_metrics: Dict[str, Any]) -> Dict[str, float]:
        """计算模糊隶属度"""
        # 获取平均健康度
        if breadth_metrics:
            health_scores = []
            for metrics in breadth_metrics.values():
                if hasattr(metrics, 'internal_health'):
                    health_scores.append(metrics.internal_health)
            avg_health = np.mean(health_scores) if health_scores else 50
        else:
            avg_health = 50

        # RII模糊隶属度
        rii_low = max(0, min(1, (self.fuzzy_thresholds['rii_medium'] - rii) /
                            (self.fuzzy_thresholds['rii_medium'] - self.fuzzy_thresholds['rii_low'])))
        rii_medium = max(0, min(1, 1 - abs(rii - self.fuzzy_thresholds['rii_medium']) /
                               (self.fuzzy_thresholds['rii_high'] - self.fuzzy_thresholds['rii_low'])))
        rii_high = max(0, min(1, (rii - self.fuzzy_thresholds['rii_medium']) /
                             (self.fuzzy_thresholds['rii_high'] - self.fuzzy_thresholds['rii_medium'])))

        # 速度模糊隶属度
        vel_low = max(0, min(1, (self.fuzzy_thresholds['velocity_medium'] - velocity) /
                            (self.fuzzy_thresholds['velocity_medium'] - self.fuzzy_thresholds['velocity_low'])))
        vel_medium = max(0, min(1, 1 - abs(velocity - self.fuzzy_thresholds['velocity_medium']) /
                               (self.fuzzy_thresholds['velocity_high'] - self.fuzzy_thresholds['velocity_low'])))
        vel_high = max(0, min(1, (velocity - self.fuzzy_thresholds['velocity_medium']) /
                             (self.fuzzy_thresholds['velocity_high'] - self.fuzzy_thresholds['velocity_medium'])))

        # 健康度模糊隶属度
        health_low = max(0, min(1, (self.fuzzy_thresholds['health_medium'] - avg_health) /
                               (self.fuzzy_thresholds['health_medium'] - self.fuzzy_thresholds['health_low'])))
        health_medium = max(0, min(1, 1 - abs(avg_health - self.fuzzy_thresholds['health_medium']) /
                                  (self.fuzzy_thresholds['health_high'] - self.fuzzy_thresholds['health_low'])))
        health_high = max(0, min(1, (avg_health - self.fuzzy_thresholds['health_medium']) /
                                (self.fuzzy_thresholds['health_high'] - self.fuzzy_thresholds['health_medium'])))

        return {
            'rii_low': rii_low, 'rii_medium': rii_medium, 'rii_high': rii_high,
            'velocity_low': vel_low, 'velocity_medium': vel_medium, 'velocity_high': vel_high,
            'health_low': health_low, 'health_medium': health_medium, 'health_high': health_high
        }

    def _calculate_stage_probabilities(self, fuzzy_scores: Dict[str, float]) -> Dict[str, float]:
        """基于模糊逻辑计算阶段概率"""
        # 模糊规则
        stable_prob = min(fuzzy_scores['rii_low'], fuzzy_scores['velocity_low'],
                         fuzzy_scores['health_high'])

        startup_prob = min(fuzzy_scores['rii_medium'], fuzzy_scores['velocity_medium'],
                          fuzzy_scores['health_medium'])

        acceleration_prob = min(fuzzy_scores['rii_high'], fuzzy_scores['velocity_high'],
                               fuzzy_scores['health_medium'])

        chaos_prob = min(fuzzy_scores['rii_high'], fuzzy_scores['velocity_high'],
                        fuzzy_scores['health_low'])

        convergence_prob = min(fuzzy_scores['rii_medium'], fuzzy_scores['velocity_low'],
                              fuzzy_scores['health_medium'])

        # 归一化
        total_prob = stable_prob + startup_prob + acceleration_prob + chaos_prob + convergence_prob

        if total_prob > 0:
            return {
                '稳定期': stable_prob / total_prob,
                '启动期': startup_prob / total_prob,
                '加速期': acceleration_prob / total_prob,
                '混乱期': chaos_prob / total_prob,
                '收敛期': convergence_prob / total_prob
            }
        else:
            # 默认分布
            return {
                '稳定期': 0.2, '启动期': 0.2, '加速期': 0.2,
                '混乱期': 0.2, '收敛期': 0.2
            }

    def _detect_stage_transitions(self, stage_probabilities: Dict[str, float],
                                fuzzy_scores: Dict[str, float]) -> Dict[str, float]:
        """检测阶段转换"""
        # 计算概率分布的熵（不确定性）
        probs = list(stage_probabilities.values())
        entropy = -sum(p * np.log(p + 1e-10) for p in probs if p > 0)
        max_entropy = np.log(len(probs))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0

        # 计算最高概率与次高概率的差距
        sorted_probs = sorted(probs, reverse=True)
        prob_gap = sorted_probs[0] - sorted_probs[1] if len(sorted_probs) > 1 else 1.0

        # 转换风险评估
        transition_risk = normalized_entropy * (1 - prob_gap)

        return {
            'entropy': normalized_entropy,
            'probability_gap': prob_gap,
            'transition_risk': transition_risk,
            'stability_score': 1 - transition_risk
        }

    def _generate_decision_factors(self, fuzzy_scores: Dict[str, float],
                                 stage_probabilities: Dict[str, float],
                                 transition_indicators: Dict[str, float]) -> List[str]:
        """生成决策因素说明"""
        factors = []

        # RII因素
        if fuzzy_scores['rii_high'] > 0.6:
            factors.append("轮动强度指数高，市场活跃度强")
        elif fuzzy_scores['rii_low'] > 0.6:
            factors.append("轮动强度指数低，市场相对稳定")

        # 速度因素
        if fuzzy_scores['velocity_high'] > 0.6:
            factors.append("排名变化速度快，板块轮动频繁")
        elif fuzzy_scores['velocity_low'] > 0.6:
            factors.append("排名变化速度慢，板块格局稳定")

        # 健康度因素
        if fuzzy_scores['health_high'] > 0.6:
            factors.append("平均健康度高，市场整体强势")
        elif fuzzy_scores['health_low'] > 0.6:
            factors.append("平均健康度低，市场整体疲弱")

        # 转换因素
        if transition_indicators['transition_risk'] > 0.7:
            factors.append("阶段转换风险高，需要密切关注")
        elif transition_indicators['stability_score'] > 0.8:
            factors.append("阶段稳定性高，当前状态可持续")

        return factors

    def _calculate_stage_confidence(self, stage_probabilities: Dict[str, float],
                                  fuzzy_scores: Dict[str, float]) -> float:
        """计算阶段识别置信度"""
        # 最高概率
        max_prob = max(stage_probabilities.values())

        # 模糊分数的一致性
        fuzzy_consistency = 1 - np.std(list(fuzzy_scores.values()))

        # 综合置信度
        confidence = (max_prob * 0.7 + max(0, fuzzy_consistency) * 0.3)

        return round(max(0.1, min(0.95, confidence)), 3)

    def _calculate_price_rii_component(self, price_dispersion: float,
                                     rank_velocity: float,
                                     volume_concentration: float) -> Tuple[float, Dict]:
        """计算价格维度RII分量"""
        # 价格离散度权重：50%
        price_component = price_dispersion * 0.5

        # 排名变化速度权重：30%
        velocity_component = rank_velocity * 0.3

        # 成交量集中度权重：20%
        volume_component = volume_concentration * 0.2

        # 合成价格RII
        price_rii = price_component + velocity_component + volume_component

        step_info = {
            'step_name': 'price_rii_calculation',
            'description': '计算价格维度RII分量',
            'formula': 'price_rii = 0.5 * price_dispersion + 0.3 * rank_velocity + 0.2 * volume_concentration',
            'components': {
                'price_dispersion': price_dispersion,
                'rank_velocity': rank_velocity,
                'volume_concentration': volume_concentration
            },
            'weighted_components': {
                'price_component': price_component,
                'velocity_component': velocity_component,
                'volume_component': volume_component
            },
            'result': price_rii
        }

        return price_rii, step_info

    def _calculate_breadth_factor_component(self, breadth_metrics: Dict[str, Any]) -> Tuple[float, Dict]:
        """计算广度因子分量"""
        if not breadth_metrics:
            return 0.5, {
                'step_name': 'breadth_factor_calculation',
                'description': '无广度数据，使用默认值',
                'result': 0.5
            }

        # 收集健康度和一致性数据
        health_scores = []
        coherence_scores = []

        for sector, metrics in breadth_metrics.items():
            if hasattr(metrics, 'internal_health'):
                health_scores.append(metrics.internal_health)

            if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence:
                coherence_scores.append(metrics.momentum_coherence)

        # 计算广度分散度（健康度的标准差）
        if health_scores:
            health_dispersion = np.std(health_scores) / 100  # 归一化到0-1
            avg_health = np.mean(health_scores) / 100
        else:
            health_dispersion = 0.5
            avg_health = 0.5

        # 计算一致性分散度
        if coherence_scores:
            coherence_dispersion = np.std(coherence_scores)
            avg_coherence = np.mean(coherence_scores)
        else:
            coherence_dispersion = 0.5
            avg_coherence = 0.5

        # 综合广度因子
        # 分散度权重60%，健康度风险权重40%
        breadth_factor = (
            health_dispersion * 0.6 +  # 健康度分散度
            (1 - avg_health) * 0.4     # 健康度风险（越低风险越高）
        )

        step_info = {
            'step_name': 'breadth_factor_calculation',
            'description': '计算广度因子分量',
            'formula': 'breadth_factor = 0.6 * health_dispersion + 0.4 * (1 - avg_health)',
            'components': {
                'health_scores_count': len(health_scores),
                'coherence_scores_count': len(coherence_scores),
                'health_dispersion': health_dispersion,
                'avg_health': avg_health,
                'coherence_dispersion': coherence_dispersion,
                'avg_coherence': avg_coherence
            },
            'result': breadth_factor
        }

        return breadth_factor, step_info

    def _calculate_dynamic_weights(self, price_rii: float, breadth_factor: float,
                                 breadth_metrics: Dict[str, Any]) -> Tuple[Dict[str, float], Dict]:
        """计算动态权重"""
        # 基础权重
        base_price_weight = 0.7
        base_breadth_weight = 0.3

        # 根据市场状态调整权重
        if breadth_metrics:
            # 计算平均健康度
            health_scores = []
            for metrics in breadth_metrics.values():
                if hasattr(metrics, 'internal_health'):
                    health_scores.append(metrics.internal_health)

            if health_scores:
                avg_health = np.mean(health_scores)

                # 健康度低时，增加广度因子权重
                if avg_health < 40:
                    breadth_weight_adjustment = 0.2
                elif avg_health > 70:
                    breadth_weight_adjustment = -0.1
                else:
                    breadth_weight_adjustment = 0.0

                # 调整权重
                adjusted_breadth_weight = base_breadth_weight + breadth_weight_adjustment
                adjusted_price_weight = 1.0 - adjusted_breadth_weight
            else:
                adjusted_price_weight = base_price_weight
                adjusted_breadth_weight = base_breadth_weight
        else:
            adjusted_price_weight = base_price_weight
            adjusted_breadth_weight = base_breadth_weight

        # 确保权重在合理范围内
        adjusted_price_weight = max(0.5, min(0.9, adjusted_price_weight))
        adjusted_breadth_weight = 1.0 - adjusted_price_weight

        dynamic_weights = {
            'price_weight': adjusted_price_weight,
            'breadth_weight': adjusted_breadth_weight
        }

        step_info = {
            'step_name': 'dynamic_weight_calculation',
            'description': '根据市场健康度动态调整权重',
            'base_weights': {
                'price_weight': base_price_weight,
                'breadth_weight': base_breadth_weight
            },
            'adjustments': {
                'avg_health': np.mean(health_scores) if 'health_scores' in locals() and health_scores else 50,
                'breadth_weight_adjustment': breadth_weight_adjustment if 'breadth_weight_adjustment' in locals() else 0
            },
            'final_weights': dynamic_weights
        }

        return dynamic_weights, step_info

    def _normalize_rii(self, unified_rii: float, price_rii: float,
                      breadth_factor: float) -> Tuple[float, Dict[str, float]]:
        """归一化RII"""
        # 使用sigmoid函数进行归一化，确保结果在0-1范围内
        normalized_rii = 1 / (1 + np.exp(-5 * (unified_rii - 0.5)))

        normalization_factors = {
            'raw_unified_rii': unified_rii,
            'normalized_rii': normalized_rii,
            'price_rii_contribution': price_rii,
            'breadth_factor_contribution': breadth_factor,
            'normalization_method': 'sigmoid'
        }

        return normalized_rii, normalization_factors

    def _get_concentration_factor(self, coherence_type: str) -> float:
        """获取集中度因子"""
        factors = {
            'strong_consensus': 1.3,
            'moderate_coherence': 1.0,
            'high_dispersion': 0.7,
            'directional_divergence': 0.8
        }
        return factors.get(coherence_type, 1.0)

    def _get_diversification_requirement(self, coherence_type: str) -> int:
        """获取分散化要求"""
        requirements = {
            'strong_consensus': 3,
            'moderate_coherence': 4,
            'high_dispersion': 6,
            'directional_divergence': 5
        }
        return requirements.get(coherence_type, 4)
