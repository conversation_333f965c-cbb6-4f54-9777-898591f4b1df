"""
集成市场分析系统
整合所有优化组件，提供统一的系统接口
"""

import sys
import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import importlib.util
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class IntegratedAnalysisResult:
    """集成分析结果"""
    # 基础信息
    analysis_timestamp: datetime
    market_regime: str
    analysis_confidence: float
    
    # 广度分析结果
    breadth_analysis: Dict[str, Any]
    
    # 板块轮动分析结果
    rotation_analysis: Dict[str, Any]
    
    # 多时间框架分析结果
    timeframe_analysis: Dict[str, Any]
    
    # 统一决策
    unified_decision: Dict[str, Any]
    
    # 配置信息
    config_summary: Dict[str, Any]
    
    # 性能指标
    performance_metrics: Dict[str, Any]


class IntegratedMarketAnalysisSystem:
    """集成市场分析系统"""
    
    def __init__(self, config_dir: str = "config", 
                 enable_performance_monitoring: bool = True,
                 enable_decision_tracking: bool = True):
        """
        初始化集成系统
        
        Args:
            config_dir: 配置目录
            enable_performance_monitoring: 是否启用性能监控
            enable_decision_tracking: 是否启用决策追踪
        """
        self.config_dir = config_dir
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_decision_tracking = enable_decision_tracking
        
        # 性能监控
        self.performance_stats = {
            'total_analyses': 0,
            'average_analysis_time': 0.0,
            'last_analysis_time': None,
            'error_count': 0
        }
        
        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 1. 初始化配置管理器
            self._initialize_config_manager()
            
            # 2. 初始化决策追踪器
            self._initialize_decision_tracker()
            
            # 3. 初始化广度分析器
            self._initialize_breadth_analyzer()
            
            # 4. 初始化板块轮动分析器
            self._initialize_rotation_analyzer()
            
            # 5. 初始化多时间框架分析器
            self._initialize_timeframe_analyzer()
            
            logger.info("所有组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    def _initialize_config_manager(self):
        """初始化配置管理器"""
        try:
            # 动态导入配置管理器
            config_path = os.path.join(os.path.dirname(__file__), 'enhanced_config_manager.py')
            spec = importlib.util.spec_from_file_location("enhanced_config_manager", config_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            self.config_manager = config_module.EnhancedConfigManager(
                config_dir=self.config_dir,
                enable_versioning=True,
                enable_auto_backup=True
            )
            
            logger.info("配置管理器初始化完成")
            
        except Exception as e:
            logger.error(f"配置管理器初始化失败: {e}")
            # 创建默认配置管理器
            self.config_manager = None
    
    def _initialize_decision_tracker(self):
        """初始化决策追踪器"""
        if not self.enable_decision_tracking:
            self.decision_tracker = None
            return
        
        try:
            # 动态导入决策追踪器
            tracker_path = os.path.join(os.path.dirname(__file__), 'enhanced_decision_tracker.py')
            if os.path.exists(tracker_path):
                spec = importlib.util.spec_from_file_location("enhanced_decision_tracker", tracker_path)
                tracker_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(tracker_module)
                
                self.decision_tracker = tracker_module.EnhancedDecisionTracker()
                logger.info("决策追踪器初始化完成")
            else:
                self.decision_tracker = None
                logger.warning("决策追踪器文件不存在，跳过初始化")
                
        except Exception as e:
            logger.warning(f"决策追踪器初始化失败: {e}")
            self.decision_tracker = None
    
    def _initialize_breadth_analyzer(self):
        """初始化广度分析器"""
        try:
            # 动态导入增强广度分析器
            breadth_path = os.path.join(os.path.dirname(__file__), 'enhanced_breadth_analyzer.py')
            spec = importlib.util.spec_from_file_location("enhanced_breadth_analyzer", breadth_path)
            breadth_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(breadth_module)
            
            self.breadth_analyzer = breadth_module.EnhancedBreadthAnalyzer(
                decision_tracker=self.decision_tracker
            )
            
            logger.info("广度分析器初始化完成")
            
        except Exception as e:
            logger.error(f"广度分析器初始化失败: {e}")
            self.breadth_analyzer = None
    
    def _initialize_rotation_analyzer(self):
        """初始化板块轮动分析器"""
        try:
            # 动态导入增强板块轮动分析器
            rotation_path = os.path.join(os.path.dirname(__file__), 'enhanced_sector_rotation_analyzer.py')
            spec = importlib.util.spec_from_file_location("enhanced_sector_rotation_analyzer", rotation_path)
            rotation_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(rotation_module)
            
            self.rotation_analyzer = rotation_module.EnhancedSectorRotationAnalyzer(
                decision_tracker=self.decision_tracker
            )
            
            logger.info("板块轮动分析器初始化完成")
            
        except Exception as e:
            logger.error(f"板块轮动分析器初始化失败: {e}")
            self.rotation_analyzer = None
    
    def _initialize_timeframe_analyzer(self):
        """初始化多时间框架分析器"""
        try:
            # 动态导入多时间框架分析器兼容层
            mtf_path = os.path.join(os.path.dirname(__file__), 'mtf_compatibility_layer.py')
            spec = importlib.util.spec_from_file_location("mtf_compatibility_layer", mtf_path)
            mtf_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(mtf_module)
            
            self.timeframe_analyzer = mtf_module.MTFCompatibilityLayer(
                decision_tracker=self.decision_tracker
            )
            
            logger.info("多时间框架分析器初始化完成")
            
        except Exception as e:
            logger.error(f"多时间框架分析器初始化失败: {e}")
            self.timeframe_analyzer = None
    
    def analyze_market(self, market_data: Dict[str, Any],
                      timeframe_data: Optional[Dict[str, Any]] = None,
                      custom_config: Optional[Dict[str, Any]] = None) -> IntegratedAnalysisResult:
        """
        执行完整的市场分析
        
        Args:
            market_data: 市场数据
            timeframe_data: 多时间框架数据
            custom_config: 自定义配置
            
        Returns:
            IntegratedAnalysisResult: 集成分析结果
        """
        start_time = datetime.now()
        
        try:
            # 更新性能统计
            self.performance_stats['total_analyses'] += 1
            
            # 应用自定义配置
            if custom_config and self.config_manager:
                self._apply_custom_config(custom_config)
            
            # 获取当前市场状态
            market_regime = self._detect_market_regime(market_data)
            
            # 切换到相应的市场状态配置
            if self.config_manager:
                self.config_manager.switch_market_regime(market_regime)
            
            # 1. 广度分析
            breadth_result = self._perform_breadth_analysis(market_data)
            
            # 2. 板块轮动分析
            rotation_result = self._perform_rotation_analysis(market_data, breadth_result)
            
            # 3. 多时间框架分析
            timeframe_result = self._perform_timeframe_analysis(
                market_data, breadth_result, rotation_result, timeframe_data
            )
            
            # 4. 生成统一决策
            unified_decision = self._generate_unified_decision(
                breadth_result, rotation_result, timeframe_result, market_regime
            )
            
            # 5. 收集配置信息
            config_summary = self._get_config_summary()
            
            # 6. 计算性能指标
            analysis_time = (datetime.now() - start_time).total_seconds()
            performance_metrics = self._calculate_performance_metrics(analysis_time)
            
            # 7. 计算总体置信度
            analysis_confidence = self._calculate_analysis_confidence(
                breadth_result, rotation_result, timeframe_result
            )
            
            # 创建集成结果
            result = IntegratedAnalysisResult(
                analysis_timestamp=start_time,
                market_regime=market_regime,
                analysis_confidence=analysis_confidence,
                breadth_analysis=breadth_result,
                rotation_analysis=rotation_result,
                timeframe_analysis=timeframe_result,
                unified_decision=unified_decision,
                config_summary=config_summary,
                performance_metrics=performance_metrics
            )
            
            # 更新性能统计
            self._update_performance_stats(analysis_time)
            
            logger.info(f"市场分析完成，耗时 {analysis_time:.3f}s，置信度 {analysis_confidence:.3f}")
            
            return result
            
        except Exception as e:
            self.performance_stats['error_count'] += 1
            logger.error(f"市场分析失败: {e}")
            raise
    
    def _detect_market_regime(self, market_data: Dict[str, Any]) -> str:
        """检测市场状态"""
        # 简化的市场状态检测逻辑
        # 实际应用中应该基于更复杂的指标
        
        if 'market_volatility' in market_data:
            volatility = market_data['market_volatility']
            
            if volatility < 0.15:
                return 'trending_stable'
            elif volatility > 0.4:
                return 'short_term_stress'
            elif volatility > 0.3:
                return 'high_rotation'
            else:
                return 'normal_market'
        
        # 默认返回正常市场
        return 'normal_market'
    
    def _apply_custom_config(self, custom_config: Dict[str, Any]):
        """应用自定义配置"""
        if not self.config_manager:
            return
        
        for path, value in custom_config.items():
            try:
                self.config_manager.set_config(path, value, "临时自定义配置", "system")
            except Exception as e:
                logger.warning(f"应用自定义配置失败: {path} = {value}, 错误: {e}")
    
    def _perform_breadth_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行广度分析"""
        if not self.breadth_analyzer:
            return {'error': '广度分析器未初始化'}
        
        try:
            # 这里需要根据实际的广度分析器接口调用
            # 由于我们没有实际的市场数据结构，这里返回模拟结果
            return {
                'status': 'completed',
                'sectors_analyzed': len(market_data.get('sectors', {})),
                'analysis_type': 'enhanced_breadth_analysis',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"广度分析失败: {e}")
            return {'error': str(e)}
    
    def _perform_rotation_analysis(self, market_data: Dict[str, Any], 
                                 breadth_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行板块轮动分析"""
        if not self.rotation_analyzer:
            return {'error': '板块轮动分析器未初始化'}
        
        try:
            # 这里需要根据实际的轮动分析器接口调用
            return {
                'status': 'completed',
                'rotation_intensity': 0.6,  # 模拟值
                'top_sectors': ['Technology', 'Healthcare'],
                'analysis_type': 'enhanced_rotation_analysis',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"板块轮动分析失败: {e}")
            return {'error': str(e)}
    
    def _perform_timeframe_analysis(self, market_data: Dict[str, Any],
                                  breadth_result: Dict[str, Any],
                                  rotation_result: Dict[str, Any],
                                  timeframe_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """执行多时间框架分析"""
        if not self.timeframe_analyzer:
            return {'error': '多时间框架分析器未初始化'}
        
        try:
            # 这里需要根据实际的多时间框架分析器接口调用
            return {
                'status': 'completed',
                'signal_strength': 0.75,  # 模拟值
                'timeframes_analyzed': ['daily', 'weekly'],
                'analysis_type': 'enhanced_timeframe_analysis',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"多时间框架分析失败: {e}")
            return {'error': str(e)}

    def _generate_unified_decision(self, breadth_result: Dict[str, Any],
                                 rotation_result: Dict[str, Any],
                                 timeframe_result: Dict[str, Any],
                                 market_regime: str) -> Dict[str, Any]:
        """生成统一决策"""
        try:
            # 整合各分析结果生成统一决策
            decision_factors = []

            # 从广度分析提取因素
            if breadth_result.get('status') == 'completed':
                decision_factors.append('广度分析完成')

            # 从轮动分析提取因素
            if rotation_result.get('status') == 'completed':
                rotation_intensity = rotation_result.get('rotation_intensity', 0)
                if rotation_intensity > 0.7:
                    decision_factors.append('高轮动强度')
                elif rotation_intensity > 0.4:
                    decision_factors.append('中等轮动强度')
                else:
                    decision_factors.append('低轮动强度')

            # 从时间框架分析提取因素
            signal_strength = timeframe_result.get('signal_strength', 0.5)
            if signal_strength > 0.7:
                decision_factors.append('强信号强度')
            elif signal_strength > 0.5:
                decision_factors.append('中等信号强度')
            else:
                decision_factors.append('弱信号强度')

            # 基于市场状态调整决策
            regime_adjustments = {
                'trending_stable': {'position_bias': 0.1, 'risk_adjustment': -0.1},
                'normal_market': {'position_bias': 0.0, 'risk_adjustment': 0.0},
                'high_rotation': {'position_bias': 0.05, 'risk_adjustment': 0.1},
                'short_term_stress': {'position_bias': -0.2, 'risk_adjustment': 0.3},
                'regime_transition': {'position_bias': -0.1, 'risk_adjustment': 0.2},
                'divergent_market': {'position_bias': -0.05, 'risk_adjustment': 0.15}
            }

            adjustment = regime_adjustments.get(market_regime, {'position_bias': 0.0, 'risk_adjustment': 0.0})

            # 计算建议仓位
            base_position = 60  # 基础仓位
            signal_adjustment = (signal_strength - 0.5) * 40  # 信号调整
            regime_adjustment = adjustment['position_bias'] * 100  # 市场状态调整

            suggested_position = max(10, min(90, base_position + signal_adjustment + regime_adjustment))

            # 生成推荐板块
            top_sectors = rotation_result.get('top_sectors', [])

            # 生成操作建议
            if suggested_position > 70:
                action = "增仓"
            elif suggested_position < 40:
                action = "减仓"
            else:
                action = "维持"

            return {
                'suggested_position': round(suggested_position, 1),
                'action': action,
                'top_sectors': top_sectors,
                'decision_factors': decision_factors,
                'market_regime': market_regime,
                'signal_strength': signal_strength,
                'confidence': self._calculate_decision_confidence(breadth_result, rotation_result, timeframe_result),
                'risk_level': self._assess_risk_level(market_regime, signal_strength),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"统一决策生成失败: {e}")
            return {'error': str(e)}

    def _calculate_analysis_confidence(self, breadth_result: Dict[str, Any],
                                     rotation_result: Dict[str, Any],
                                     timeframe_result: Dict[str, Any]) -> float:
        """计算分析置信度"""
        confidence_factors = []

        # 广度分析置信度
        if breadth_result.get('status') == 'completed':
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.3)

        # 轮动分析置信度
        if rotation_result.get('status') == 'completed':
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.3)

        # 时间框架分析置信度
        if timeframe_result.get('status') == 'completed':
            signal_strength = timeframe_result.get('signal_strength', 0.5)
            confidence_factors.append(signal_strength)
        else:
            confidence_factors.append(0.3)

        # 计算加权平均置信度
        if confidence_factors:
            return round(sum(confidence_factors) / len(confidence_factors), 3)
        else:
            return 0.5

    def _calculate_decision_confidence(self, breadth_result: Dict[str, Any],
                                     rotation_result: Dict[str, Any],
                                     timeframe_result: Dict[str, Any]) -> float:
        """计算决策置信度"""
        # 基于各分析结果的完整性和一致性计算置信度
        base_confidence = 0.5

        # 分析完整性加分
        completed_analyses = 0
        total_analyses = 3

        if breadth_result.get('status') == 'completed':
            completed_analyses += 1
        if rotation_result.get('status') == 'completed':
            completed_analyses += 1
        if timeframe_result.get('status') == 'completed':
            completed_analyses += 1

        completeness_bonus = (completed_analyses / total_analyses) * 0.3

        # 信号强度加分
        signal_strength = timeframe_result.get('signal_strength', 0.5)
        signal_bonus = (signal_strength - 0.5) * 0.2

        final_confidence = base_confidence + completeness_bonus + signal_bonus
        return round(max(0.1, min(1.0, final_confidence)), 3)

    def _assess_risk_level(self, market_regime: str, signal_strength: float) -> str:
        """评估风险等级"""
        risk_scores = {
            'trending_stable': 0.2,
            'normal_market': 0.4,
            'high_rotation': 0.6,
            'short_term_stress': 0.8,
            'regime_transition': 0.7,
            'divergent_market': 0.75
        }

        base_risk = risk_scores.get(market_regime, 0.5)

        # 信号强度调整风险
        if signal_strength < 0.3:
            base_risk += 0.2  # 弱信号增加风险
        elif signal_strength > 0.8:
            base_risk -= 0.1  # 强信号降低风险

        final_risk = max(0.0, min(1.0, base_risk))

        if final_risk > 0.7:
            return 'high'
        elif final_risk > 0.5:
            return 'medium'
        elif final_risk > 0.3:
            return 'low_medium'
        else:
            return 'low'

    def _get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        if not self.config_manager:
            return {'error': '配置管理器未初始化'}

        try:
            return self.config_manager.get_config_summary()
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            return {'error': str(e)}

    def _calculate_performance_metrics(self, analysis_time: float) -> Dict[str, Any]:
        """计算性能指标"""
        return {
            'analysis_time_seconds': round(analysis_time, 3),
            'total_analyses': self.performance_stats['total_analyses'],
            'average_analysis_time': round(self.performance_stats['average_analysis_time'], 3),
            'error_count': self.performance_stats['error_count'],
            'success_rate': round(
                (self.performance_stats['total_analyses'] - self.performance_stats['error_count']) /
                max(1, self.performance_stats['total_analyses']), 3
            ),
            'performance_grade': self._get_performance_grade(analysis_time)
        }

    def _get_performance_grade(self, analysis_time: float) -> str:
        """获取性能等级"""
        if analysis_time < 1.0:
            return 'A'
        elif analysis_time < 3.0:
            return 'B'
        elif analysis_time < 5.0:
            return 'C'
        else:
            return 'D'

    def _update_performance_stats(self, analysis_time: float):
        """更新性能统计"""
        total = self.performance_stats['total_analyses']
        current_avg = self.performance_stats['average_analysis_time']

        # 计算新的平均时间
        new_avg = ((current_avg * (total - 1)) + analysis_time) / total
        self.performance_stats['average_analysis_time'] = new_avg
        self.performance_stats['last_analysis_time'] = datetime.now()

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'system_info': {
                'initialized_components': self._get_initialized_components(),
                'config_manager_status': 'active' if self.config_manager else 'inactive',
                'decision_tracker_status': 'active' if self.decision_tracker else 'inactive',
                'performance_monitoring': self.enable_performance_monitoring,
                'decision_tracking': self.enable_decision_tracking
            },
            'performance_stats': self.performance_stats.copy(),
            'current_config': self._get_config_summary(),
            'system_health': self._assess_system_health()
        }

    def _get_initialized_components(self) -> List[str]:
        """获取已初始化的组件列表"""
        components = []

        if self.config_manager:
            components.append('config_manager')
        if self.decision_tracker:
            components.append('decision_tracker')
        if self.breadth_analyzer:
            components.append('breadth_analyzer')
        if self.rotation_analyzer:
            components.append('rotation_analyzer')
        if self.timeframe_analyzer:
            components.append('timeframe_analyzer')

        return components

    def _assess_system_health(self) -> str:
        """评估系统健康状态"""
        initialized_count = len(self._get_initialized_components())
        total_components = 5  # 总组件数

        error_rate = self.performance_stats['error_count'] / max(1, self.performance_stats['total_analyses'])

        if initialized_count == total_components and error_rate < 0.1:
            return 'excellent'
        elif initialized_count >= 4 and error_rate < 0.2:
            return 'good'
        elif initialized_count >= 3 and error_rate < 0.3:
            return 'fair'
        else:
            return 'poor'

    def update_config(self, config_path: str, value: Any, description: str = "") -> bool:
        """更新配置"""
        if not self.config_manager:
            logger.error("配置管理器未初始化")
            return False

        return self.config_manager.set_config(config_path, value, description, "system_api")

    def switch_market_regime(self, regime_name: str) -> bool:
        """切换市场状态"""
        if not self.config_manager:
            logger.error("配置管理器未初始化")
            return False

        return self.config_manager.switch_market_regime(regime_name)

    def export_analysis_result(self, result: IntegratedAnalysisResult,
                             file_path: str, format: str = 'json') -> bool:
        """导出分析结果"""
        try:
            import json

            if format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(result), f, indent=2, ensure_ascii=False, default=str)

                logger.info(f"分析结果已导出到: {file_path}")
                return True
            else:
                logger.error(f"不支持的导出格式: {format}")
                return False

        except Exception as e:
            logger.error(f"导出分析结果失败: {e}")
            return False

    def validate_system_integrity(self) -> Dict[str, Any]:
        """验证系统完整性"""
        validation_results = {
            'overall_status': 'unknown',
            'component_status': {},
            'config_validation': {},
            'performance_check': {},
            'recommendations': []
        }

        try:
            # 检查组件状态
            components = {
                'config_manager': self.config_manager,
                'decision_tracker': self.decision_tracker,
                'breadth_analyzer': self.breadth_analyzer,
                'rotation_analyzer': self.rotation_analyzer,
                'timeframe_analyzer': self.timeframe_analyzer
            }

            for name, component in components.items():
                validation_results['component_status'][name] = 'active' if component else 'inactive'

            # 配置验证
            if self.config_manager:
                config_validation = self.config_manager.validate_all_configs()
                validation_results['config_validation'] = {
                    'errors': len(config_validation['errors']),
                    'warnings': len(config_validation['warnings']),
                    'details': config_validation
                }

            # 性能检查
            error_rate = self.performance_stats['error_count'] / max(1, self.performance_stats['total_analyses'])
            avg_time = self.performance_stats['average_analysis_time']

            validation_results['performance_check'] = {
                'error_rate': round(error_rate, 3),
                'average_analysis_time': round(avg_time, 3),
                'performance_grade': self._get_performance_grade(avg_time)
            }

            # 生成建议
            recommendations = []

            active_components = sum(1 for status in validation_results['component_status'].values() if status == 'active')
            if active_components < 4:
                recommendations.append("建议检查未初始化的组件")

            if error_rate > 0.2:
                recommendations.append("错误率较高，建议检查系统配置")

            if avg_time > 5.0:
                recommendations.append("分析时间较长，建议优化性能配置")

            validation_results['recommendations'] = recommendations

            # 确定总体状态
            if active_components >= 4 and error_rate < 0.1 and avg_time < 3.0:
                validation_results['overall_status'] = 'healthy'
            elif active_components >= 3 and error_rate < 0.2:
                validation_results['overall_status'] = 'warning'
            else:
                validation_results['overall_status'] = 'critical'

            return validation_results

        except Exception as e:
            logger.error(f"系统完整性验证失败: {e}")
            validation_results['overall_status'] = 'error'
            validation_results['error'] = str(e)
            return validation_results
