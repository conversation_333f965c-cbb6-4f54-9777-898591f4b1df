import concurrent.futures
import os
import sys
from typing import List, Union

import pandas as pd
import pymysql
from tqdm import tqdm

from us import RESTClient

# 添加配置路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

try:
    from db_settings import SECURITY_DB_CONFIG
    USE_CONFIG_FILE = True
except ImportError:
    USE_CONFIG_FILE = False
    print("⚠️ 无法导入数据库配置，将使用环境变量")

client = RESTClient(username="test01", password="test01")

__all__ = ['download_hist_price']


def interday_price(symbol, vhcid, multiplier: int, timespan: str, start: str, end: str, columns: List[str] = None):
    try:
        data = client.list_price(
            vhcid=vhcid,
            multiplier=multiplier,
            timespan=timespan,
            from_=start,
            to=end
        )['data']
        data = pd.DataFrame(data)
        data['dt'] = pd.to_datetime(data['dt'], format='%Y-%m-%dT%H:%M:%S')
        data = data.drop_duplicates(subset=['dt'])
        data = data.set_index('dt').sort_index()
        if columns is not None:
            data = data[columns]
        data = data.rename({
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume',
        }, axis=1)
        data = data.loc[start:end]
        return symbol, data
    except Exception as e:
        return None


def _cast_vhcid(symbols: Union[str, List[str]]):
    if isinstance(symbols, str):
        symbols = [symbols]

    # 使用配置文件或环境变量
    if USE_CONFIG_FILE:
        db_conn = pymysql.Connection(**SECURITY_DB_CONFIG)
    else:
        db_conn = pymysql.Connection(
            host=os.environ['SECURITY_DB_HOST'],
            port=int(os.environ['SECURITY_DB_PORT']),
            user=os.environ['SECURITY_DB_USER'],
            password=os.environ['SECURITY_DB_PASSWORD'],
            database=os.environ['SECURITY_DB_NAME'],
        )

    placeholder = ', '.join(['%s'] * len(symbols))
    sql = f"SELECT vhcid, Ticker FROM ticker_to_secid_lookup WHERE Ticker IN ({placeholder}) AND EndDate='29991231'"
    cursor = db_conn.cursor()
    cursor.execute(sql, symbols)
    mappings = cursor.fetchall()
    return mappings


def download_hist_price(symbols: Union[str, List[str]], interval: str, start: str, end: str = None,
                        columns: List[str] = None, threads: int = 5, verbose: bool = True):
    interval_map = {
        '1w': (1, 'week'),
        '1d': (1, 'day'),
        '1h': (1, 'hour'),
        '15min': (15, 'minute'),
        '5min': (5, 'minute'),
        '1min': (1, 'minute')
    }
    multiplier, timespan = interval_map[interval]

    if end is None:
        end = pd.Timestamp.now() + pd.Timedelta(days=7)
    end = pd.Timestamp(end).strftime('%Y-%m-%d')

    # Find the symbol corresponding to the vhcid
    mappings = _cast_vhcid(symbols)
    if verbose:
        pbar = tqdm(total=len(mappings))

    with concurrent.futures.ThreadPoolExecutor(max_workers=threads) as executor:
        futures = []
        for vhcid, symbol in mappings:
            futures.append(
                executor.submit(interday_price, symbol, vhcid, multiplier, timespan, start, end, columns)
            )

        results = {}
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            if result is not None:
                symbol, data = result
                results[symbol] = data

            if verbose:
                pbar.update(1)

    if verbose:
        pbar.close()

    return results

