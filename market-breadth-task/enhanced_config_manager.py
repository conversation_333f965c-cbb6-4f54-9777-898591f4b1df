"""
增强版配置管理器
提供集中化参数管理、版本控制、市场适应性调整和配置验证机制
"""

import json
import os
import copy
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class ConfigVersion:
    """配置版本信息"""
    version: str
    timestamp: datetime
    description: str
    checksum: str
    author: str
    changes: List[str]


@dataclass
class MarketRegimeConfig:
    """市场状态配置"""
    regime_name: str
    description: str
    parameter_adjustments: Dict[str, Any]
    validity_conditions: Dict[str, Any]
    auto_switch_enabled: bool = True


@dataclass
class ParameterValidation:
    """参数验证规则"""
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    allowed_values: Optional[List[Any]] = None
    data_type: Optional[type] = None
    custom_validator: Optional[Callable] = None
    description: str = ""


class EnhancedConfigManager:
    """增强版配置管理器"""
    
    def __init__(self, config_dir: str = "config", 
                 enable_versioning: bool = True,
                 enable_auto_backup: bool = True):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
            enable_versioning: 是否启用版本控制
            enable_auto_backup: 是否启用自动备份
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.enable_versioning = enable_versioning
        self.enable_auto_backup = enable_auto_backup
        
        # 配置存储
        self.configs: Dict[str, Any] = {}
        self.versions: List[ConfigVersion] = []
        self.market_regimes: Dict[str, MarketRegimeConfig] = {}
        self.validation_rules: Dict[str, ParameterValidation] = {}
        
        # 当前状态
        self.current_market_regime = "normal_market"
        self.active_config_name = "default"
        
        # 初始化默认配置
        self._initialize_default_configs()
        self._initialize_market_regimes()
        self._initialize_validation_rules()
        
        # 加载现有配置
        self._load_existing_configs()
    
    def _initialize_default_configs(self):
        """初始化默认配置"""
        # 算法核心参数
        self.configs["algorithm"] = {
            "version": "4.2",
            "breadth_calculation": {
                "min_stocks_threshold": 10,
                "data_quality_threshold": 0.8,
                "enhanced_divergence_enabled": True,
                "improved_momentum_coherence_enabled": True
            },
            "coherence": {
                "direction_threshold": 0.001,
                "cv_scale": 1.0,
                "weight_dynamic_threshold": 0.6,
                "coherence_types": {
                    "strong_consensus": {"max_single": 0.5, "max_top3": 0.8},
                    "moderate_coherence": {"max_single": 0.3, "max_top3": 0.7},
                    "high_dispersion": {"max_single": 0.15, "max_top3": 0.5},
                    "directional_divergence": {"max_single": 0.2, "max_top3": 0.6}
                }
            },
            "divergence": {
                "price_threshold": 0.01,
                "ad_neutral": 1.0,
                "severity_levels": {
                    "extreme": 0.05,
                    "high": 0.03,
                    "medium": 0.015,
                    "low": 0.008
                }
            },
            "signal_strength": {
                "components": {
                    "rii_strength": {"weight": 0.25, "non_linear_power": 1.2},
                    "health_strength": {"weight": 0.20, "non_linear_power": 0.8},
                    "momentum_strength": {"weight": 0.15, "non_linear_power": 1.5},
                    "coherence_strength": {"weight": 0.20, "non_linear_power": 1.0},
                    "divergence_strength": {"weight": 0.10, "non_linear_power": 2.0},
                    "participation_strength": {"weight": 0.10, "non_linear_power": 0.9}
                },
                "quality_thresholds": {
                    "data_completeness_min": 0.7,
                    "signal_consistency_min": 0.5,
                    "extreme_ratio_max": 0.3
                }
            }
        }
        
        # 风险管理参数
        self.configs["risk_management"] = {
            "position_limits": {
                "max_position": 90,
                "min_position": 10,
                "max_sector_weight": 0.4,
                "min_sector_weight": 0.05,
                "cash_reserve_min": 0.05
            },
            "decision_thresholds": {
                "strong_buy": 0.75,
                "buy": 0.60,
                "hold": 0.40,
                "sell": 0.25,
                "strong_sell": 0.15
            },
            "avoid_severity_levels": {
                "critical": {"threshold": 0.8, "description": "强烈避免"},
                "high": {"threshold": 0.6, "description": "谨慎避免"},
                "medium": {"threshold": 0.4, "description": "观望"},
                "low": {"threshold": 0.2, "description": "轻微关注"}
            }
        }
        
        # 时间框架配置
        self.configs["timeframes"] = {
            "daily": {"weight": 0.5, "confidence_factor": 0.9},
            "weekly": {"weight": 0.3, "confidence_factor": 0.85},
            "monthly": {"weight": 0.2, "confidence_factor": 0.8},
            "intraday_hourly": {"weight": 0.1, "confidence_factor": 0.75},
            "intraday_15min": {"weight": 0.05, "confidence_factor": 0.7},
            "intraday_5min": {"weight": 0.02, "confidence_factor": 0.6}
        }
        
        # 性能配置
        self.configs["performance"] = {
            "parallel_processing": {
                "enable_multiprocessing": True,
                "max_workers": 4,
                "batch_size": 100
            },
            "caching": {
                "enable_result_cache": True,
                "cache_ttl_seconds": 300,
                "max_cache_size": 1000
            },
            "monitoring": {
                "enable_performance_monitoring": True,
                "log_slow_operations": True,
                "slow_operation_threshold_ms": 1000
            }
        }
    
    def _initialize_market_regimes(self):
        """初始化市场状态配置"""
        self.market_regimes = {
            "trending_stable": MarketRegimeConfig(
                regime_name="trending_stable",
                description="趋势稳定市场",
                parameter_adjustments={
                    "signal_strength.components.rii_strength.weight": 0.3,
                    "risk_management.position_limits.max_position": 95,
                    "algorithm.coherence.weight_dynamic_threshold": 0.5
                },
                validity_conditions={
                    "market_volatility_max": 0.15,
                    "trend_consistency_min": 0.7
                }
            ),
            "normal_market": MarketRegimeConfig(
                regime_name="normal_market",
                description="正常市场环境",
                parameter_adjustments={},  # 使用默认参数
                validity_conditions={
                    "market_volatility_max": 0.25,
                    "trend_consistency_min": 0.4
                }
            ),
            "high_rotation": MarketRegimeConfig(
                regime_name="high_rotation",
                description="高轮动市场",
                parameter_adjustments={
                    "signal_strength.components.rii_strength.weight": 0.35,
                    "signal_strength.components.momentum_strength.weight": 0.2,
                    "risk_management.position_limits.max_sector_weight": 0.3
                },
                validity_conditions={
                    "rotation_intensity_min": 0.6,
                    "sector_dispersion_min": 0.4
                }
            ),
            "short_term_stress": MarketRegimeConfig(
                regime_name="short_term_stress",
                description="短期压力市场",
                parameter_adjustments={
                    "risk_management.position_limits.max_position": 70,
                    "risk_management.position_limits.cash_reserve_min": 0.15,
                    "signal_strength.quality_thresholds.data_completeness_min": 0.8
                },
                validity_conditions={
                    "market_stress_indicator_min": 0.6,
                    "volatility_spike_detected": True
                }
            ),
            "regime_transition": MarketRegimeConfig(
                regime_name="regime_transition",
                description="状态转换期",
                parameter_adjustments={
                    "risk_management.position_limits.max_position": 60,
                    "signal_strength.quality_thresholds.signal_consistency_min": 0.7,
                    "algorithm.coherence.weight_dynamic_threshold": 0.7
                },
                validity_conditions={
                    "regime_uncertainty_min": 0.5,
                    "signal_divergence_max": 0.4
                }
            ),
            "divergent_market": MarketRegimeConfig(
                regime_name="divergent_market",
                description="分化市场",
                parameter_adjustments={
                    "algorithm.coherence.coherence_types.high_dispersion.max_single": 0.1,
                    "signal_strength.components.divergence_strength.weight": 0.15,
                    "risk_management.avoid_severity_levels.medium.threshold": 0.3
                },
                validity_conditions={
                    "sector_divergence_min": 0.6,
                    "correlation_breakdown_detected": True
                }
            )
        }
    
    def _initialize_validation_rules(self):
        """初始化参数验证规则"""
        self.validation_rules = {
            "algorithm.breadth_calculation.min_stocks_threshold": ParameterValidation(
                min_value=5, max_value=50, data_type=int,
                description="最小股票数量阈值"
            ),
            "algorithm.breadth_calculation.data_quality_threshold": ParameterValidation(
                min_value=0.5, max_value=1.0, data_type=float,
                description="数据质量阈值"
            ),
            "risk_management.position_limits.max_position": ParameterValidation(
                min_value=50, max_value=100, data_type=int,
                description="最大仓位限制"
            ),
            "risk_management.position_limits.max_sector_weight": ParameterValidation(
                min_value=0.1, max_value=0.8, data_type=float,
                description="单板块最大权重"
            ),
            "signal_strength.components.*.weight": ParameterValidation(
                min_value=0.0, max_value=1.0, data_type=float,
                description="信号分量权重"
            )
        }
    
    def _load_existing_configs(self):
        """加载现有配置文件"""
        try:
            # 加载主配置文件
            main_config_file = self.config_dir / "enhanced_config.json"
            if main_config_file.exists():
                with open(main_config_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    self.configs.update(loaded_data.get('configs', {}))

                    # 加载当前市场状态
                    if 'current_market_regime' in loaded_data:
                        self.current_market_regime = loaded_data['current_market_regime']

                    # 加载活动配置名称
                    if 'active_config_name' in loaded_data:
                        self.active_config_name = loaded_data['active_config_name']

                    # 加载版本历史
                    if self.enable_versioning and 'versions' in loaded_data:
                        version_data = loaded_data['versions']
                        self.versions = [
                            ConfigVersion(
                                version=v['version'],
                                timestamp=datetime.fromisoformat(v['timestamp']),
                                description=v['description'],
                                checksum=v['checksum'],
                                author=v['author'],
                                changes=v['changes']
                            ) for v in version_data
                        ]
            
            logger.info(f"已加载配置文件，包含 {len(self.configs)} 个配置组")
            
        except Exception as e:
            logger.warning(f"加载配置文件失败: {e}，将使用默认配置")
    
    def get_config(self, path: str, default: Any = None) -> Any:
        """
        获取配置参数
        
        Args:
            path: 配置路径，如 'algorithm.coherence.direction_threshold'
            default: 默认值
            
        Returns:
            配置值
        """
        # 应用市场状态调整
        adjusted_configs = self._apply_market_regime_adjustments()
        
        keys = path.split('.')
        value = adjusted_configs
        
        try:
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            return value
        except Exception:
            return default
    
    def set_config(self, path: str, value: Any, 
                   description: str = "", 
                   author: str = "system") -> bool:
        """
        设置配置参数
        
        Args:
            path: 配置路径
            value: 配置值
            description: 变更描述
            author: 变更作者
            
        Returns:
            是否设置成功
        """
        # 验证参数
        if not self._validate_parameter(path, value):
            logger.error(f"参数验证失败: {path} = {value}")
            return False
        
        # 创建版本备份
        if self.enable_versioning:
            self._create_version_backup(f"设置 {path} = {value}", author)
        
        # 设置参数
        keys = path.split('.')
        config_group = keys[0]

        if config_group not in self.configs:
            self.configs[config_group] = {}

        current = self.configs[config_group]
        for key in keys[1:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        if len(keys) > 1:
            current[keys[-1]] = value
        else:
            # 如果只有一级路径，直接设置到配置组
            self.configs[config_group] = value
        
        # 保存配置
        self._save_configs()
        
        logger.info(f"配置已更新: {path} = {value}")
        return True

    def switch_market_regime(self, regime_name: str,
                           auto_validate: bool = True) -> bool:
        """
        切换市场状态

        Args:
            regime_name: 市场状态名称
            auto_validate: 是否自动验证状态有效性

        Returns:
            是否切换成功
        """
        if regime_name not in self.market_regimes:
            logger.error(f"未知的市场状态: {regime_name}")
            return False

        regime = self.market_regimes[regime_name]

        # 验证状态有效性
        if auto_validate and not self._validate_market_regime(regime):
            logger.warning(f"市场状态 {regime_name} 验证失败，但仍将切换")

        old_regime = self.current_market_regime
        self.current_market_regime = regime_name

        logger.info(f"市场状态已切换: {old_regime} -> {regime_name}")

        # 创建版本记录
        if self.enable_versioning:
            self._create_version_backup(
                f"切换市场状态: {old_regime} -> {regime_name}",
                "system"
            )

        # 保存配置
        self._save_configs()

        return True

    def get_current_regime_config(self) -> MarketRegimeConfig:
        """获取当前市场状态配置"""
        return self.market_regimes.get(
            self.current_market_regime,
            self.market_regimes["normal_market"]
        )

    def _apply_market_regime_adjustments(self) -> Dict[str, Any]:
        """应用市场状态调整"""
        # 深拷贝基础配置
        adjusted_configs = copy.deepcopy(self.configs)

        # 获取当前市场状态的调整参数
        current_regime = self.get_current_regime_config()
        adjustments = current_regime.parameter_adjustments

        # 应用调整
        for path, value in adjustments.items():
            keys = path.split('.')
            current = adjusted_configs

            try:
                # 导航到目标位置
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                # 设置调整值
                current[keys[-1]] = value

            except Exception as e:
                logger.warning(f"应用市场状态调整失败: {path} = {value}, 错误: {e}")

        return adjusted_configs

    def _validate_parameter(self, path: str, value: Any) -> bool:
        """验证参数"""
        # 查找匹配的验证规则
        validation_rule = None

        # 精确匹配
        if path in self.validation_rules:
            validation_rule = self.validation_rules[path]
        else:
            # 通配符匹配
            for rule_path, rule in self.validation_rules.items():
                if '*' in rule_path:
                    pattern = rule_path.replace('*', '.*')
                    import re
                    if re.match(pattern, path):
                        validation_rule = rule
                        break

        if not validation_rule:
            return True  # 没有验证规则，默认通过

        # 数据类型验证
        if validation_rule.data_type and not isinstance(value, validation_rule.data_type):
            logger.error(f"参数类型错误: {path} 期望 {validation_rule.data_type}, 实际 {type(value)}")
            return False

        # 数值范围验证
        if isinstance(value, (int, float)):
            if validation_rule.min_value is not None and value < validation_rule.min_value:
                logger.error(f"参数值过小: {path} = {value} < {validation_rule.min_value}")
                return False

            if validation_rule.max_value is not None and value > validation_rule.max_value:
                logger.error(f"参数值过大: {path} = {value} > {validation_rule.max_value}")
                return False

        # 允许值验证
        if validation_rule.allowed_values and value not in validation_rule.allowed_values:
            logger.error(f"参数值不在允许范围内: {path} = {value}, 允许值: {validation_rule.allowed_values}")
            return False

        # 自定义验证器
        if validation_rule.custom_validator:
            try:
                if not validation_rule.custom_validator(value):
                    logger.error(f"自定义验证失败: {path} = {value}")
                    return False
            except Exception as e:
                logger.error(f"自定义验证器执行失败: {path} = {value}, 错误: {e}")
                return False

        return True

    def _validate_market_regime(self, regime: MarketRegimeConfig) -> bool:
        """验证市场状态有效性"""
        # 这里可以添加实际的市场数据验证逻辑
        # 目前返回True，表示总是有效
        return True

    def _create_version_backup(self, description: str, author: str):
        """创建版本备份"""
        if not self.enable_versioning:
            return

        # 计算配置校验和
        config_str = json.dumps(self.configs, sort_keys=True)
        checksum = hashlib.md5(config_str.encode()).hexdigest()

        # 创建版本记录
        version = ConfigVersion(
            version=f"v{len(self.versions) + 1}",
            timestamp=datetime.now(),
            description=description,
            checksum=checksum,
            author=author,
            changes=[description]
        )

        self.versions.append(version)

        # 保留最近50个版本
        if len(self.versions) > 50:
            self.versions = self.versions[-50:]

        logger.debug(f"创建配置版本: {version.version}")

    def _save_configs(self):
        """保存配置到文件"""
        try:
            config_data = {
                'configs': self.configs,
                'current_market_regime': self.current_market_regime,
                'active_config_name': self.active_config_name,
                'last_updated': datetime.now().isoformat()
            }

            # 添加版本信息
            if self.enable_versioning:
                config_data['versions'] = [
                    {
                        'version': v.version,
                        'timestamp': v.timestamp.isoformat(),
                        'description': v.description,
                        'checksum': v.checksum,
                        'author': v.author,
                        'changes': v.changes
                    } for v in self.versions
                ]

            # 保存主配置文件
            main_config_file = self.config_dir / "enhanced_config.json"
            with open(main_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            # 自动备份
            if self.enable_auto_backup:
                backup_file = self.config_dir / f"backup_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)

                # 清理旧备份（保留最近10个）
                self._cleanup_old_backups()

            logger.debug("配置已保存到文件")

        except Exception as e:
            logger.error(f"保存配置失败: {e}")

    def _cleanup_old_backups(self):
        """清理旧备份文件"""
        try:
            backup_files = list(self.config_dir.glob("backup_config_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # 删除超过10个的旧备份
            for old_backup in backup_files[10:]:
                old_backup.unlink()
                logger.debug(f"删除旧备份: {old_backup.name}")

        except Exception as e:
            logger.warning(f"清理备份文件失败: {e}")

    def get_version_history(self) -> List[ConfigVersion]:
        """获取版本历史"""
        return self.versions.copy()

    def rollback_to_version(self, version: str) -> bool:
        """回滚到指定版本"""
        if not self.enable_versioning:
            logger.error("版本控制未启用")
            return False

        # 查找目标版本
        target_version = None
        for v in self.versions:
            if v.version == version:
                target_version = v
                break

        if not target_version:
            logger.error(f"未找到版本: {version}")
            return False

        # 这里需要实现实际的回滚逻辑
        # 由于我们只存储了变更描述，实际回滚需要更复杂的实现
        logger.warning("版本回滚功能需要进一步实现")
        return False

    def export_config(self, file_path: str,
                     include_versions: bool = False) -> bool:
        """导出配置到文件"""
        try:
            export_data = {
                'configs': self.configs,
                'market_regimes': {
                    name: asdict(regime) for name, regime in self.market_regimes.items()
                },
                'current_market_regime': self.current_market_regime,
                'export_timestamp': datetime.now().isoformat()
            }

            if include_versions:
                export_data['versions'] = [
                    {
                        'version': v.version,
                        'timestamp': v.timestamp.isoformat(),
                        'description': v.description,
                        'checksum': v.checksum,
                        'author': v.author,
                        'changes': v.changes
                    } for v in self.versions
                ]

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"配置已导出到: {file_path}")
            return True

        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False

    def import_config(self, file_path: str,
                     merge_mode: bool = True) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            if merge_mode:
                # 合并模式：更新现有配置
                for key, value in import_data.get('configs', {}).items():
                    if key in self.configs:
                        self.configs[key].update(value)
                    else:
                        self.configs[key] = value
            else:
                # 替换模式：完全替换配置
                self.configs = import_data.get('configs', {})

            # 更新市场状态
            if 'current_market_regime' in import_data:
                self.current_market_regime = import_data['current_market_regime']

            # 创建版本记录
            if self.enable_versioning:
                self._create_version_backup(f"导入配置: {file_path}", "import")

            # 保存配置
            self._save_configs()

            logger.info(f"配置已从 {file_path} 导入")
            return True

        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False

    def validate_all_configs(self) -> Dict[str, List[str]]:
        """验证所有配置"""
        validation_results = {
            'errors': [],
            'warnings': [],
            'info': []
        }

        # 验证配置完整性
        required_groups = ['algorithm', 'risk_management', 'timeframes', 'performance']
        for group in required_groups:
            if group not in self.configs:
                validation_results['errors'].append(f"缺少必需的配置组: {group}")

        # 验证参数值
        for path, rule in self.validation_rules.items():
            if '*' not in path:  # 跳过通配符规则
                value = self.get_config(path)
                if value is not None and not self._validate_parameter(path, value):
                    validation_results['errors'].append(f"参数验证失败: {path}")

        # 验证权重总和
        signal_weights = self.get_config('algorithm.signal_strength.components', {})
        if signal_weights:
            total_weight = sum(comp.get('weight', 0) for comp in signal_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                validation_results['warnings'].append(f"信号分量权重总和不为1: {total_weight}")

        # 验证市场状态配置
        for regime_name, regime in self.market_regimes.items():
            if not self._validate_market_regime(regime):
                validation_results['warnings'].append(f"市场状态配置可能无效: {regime_name}")

        return validation_results

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'total_config_groups': len(self.configs),
            'current_market_regime': self.current_market_regime,
            'version_count': len(self.versions),
            'last_updated': self.versions[-1].timestamp.isoformat() if self.versions else None,
            'config_groups': list(self.configs.keys()),
            'market_regimes': list(self.market_regimes.keys()),
            'validation_rules_count': len(self.validation_rules)
        }
