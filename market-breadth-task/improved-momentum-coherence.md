# 改进的Momentum Coherence计算方案与系统集成

## 一、核心改进：Momentum Coherence计算

### 1.1 改进后的计算方法

```python
import numpy as np
from typing import List, Dict, Tuple, Optional

class ImprovedMomentumCoherence:
    """改进的动量一致性计算器"""
    
    @staticmethod
    def calculate_coherence(individual_returns: List[float]) -> Dict[str, float]:
        """
        计算板块动量一致性（改进版）
        
        返回:
            Dict包含:
            - overall_coherence: 综合一致性 (0-1)
            - direction_coherence: 方向一致性 (0-1)
            - magnitude_coherence: 幅度一致性 (0-1)
            - coherence_type: 一致性类型描述
        """
        if not individual_returns or len(individual_returns) < 2:
            return {
                'overall_coherence': 0.5,
                'direction_coherence': 0.5,
                'magnitude_coherence': 0.5,
                'coherence_type': 'insufficient_data'
            }
        
        returns_array = np.array(individual_returns)
        
        # 1. 计算方向一致性
        positive_count = np.sum(returns_array > 0.001)  # 上涨个股数（考虑精度）
        negative_count = np.sum(returns_array < -0.001)  # 下跌个股数
        neutral_count = len(returns_array) - positive_count - negative_count
        total_count = len(returns_array)
        
        # 方向一致性 = 最大同向比例
        direction_coherence = max(positive_count, negative_count) / total_count
        
        # 2. 计算幅度一致性
        mean_return = np.mean(returns_array)
        std_return = np.std(returns_array)
        
        if abs(mean_return) < 0.0001:  # 均值接近0
            # 使用四分位距离法评估一致性
            q75, q25 = np.percentile(returns_array, [75, 25])
            iqr = q75 - q25
            median_abs = np.median(np.abs(returns_array))
            
            if median_abs > 0:
                magnitude_coherence = 1 / (1 + iqr / median_abs)
            else:
                magnitude_coherence = 0.0
        else:
            # 使用改进的变异系数法
            cv = std_return / abs(mean_return)
            # 使用sigmoid映射，CV=1时coherence≈0.5
            magnitude_coherence = 2 / (1 + np.exp(cv)) - 0.1
            magnitude_coherence = max(0, min(1, magnitude_coherence))
        
        # 3. 计算综合一致性（动态权重）
        if direction_coherence > 0.8:  # 方向高度一致
            # 更看重幅度一致性
            overall_coherence = 0.4 * direction_coherence + 0.6 * magnitude_coherence
        elif direction_coherence < 0.6:  # 方向分歧
            # 主要看方向一致性
            overall_coherence = 0.7 * direction_coherence + 0.3 * magnitude_coherence
        else:  # 中等情况
            overall_coherence = 0.5 * direction_coherence + 0.5 * magnitude_coherence
        
        # 4. 判断一致性类型
        coherence_type = ImprovedMomentumCoherence._classify_coherence_type(
            direction_coherence, magnitude_coherence, mean_return
        )
        
        return {
            'overall_coherence': round(overall_coherence, 3),
            'direction_coherence': round(direction_coherence, 3),
            'magnitude_coherence': round(magnitude_coherence, 3),
            'coherence_type': coherence_type,
            'mean_return': round(mean_return, 4),
            'std_return': round(std_return, 4)
        }
    
    @staticmethod
    def _classify_coherence_type(dir_coh: float, mag_coh: float, 
                                mean_return: float) -> str:
        """分类一致性类型"""
        if dir_coh > 0.8 and mag_coh > 0.7:
            return "strong_consensus"  # 强共识
        elif dir_coh > 0.8 and mag_coh < 0.4:
            return "directional_divergence"  # 方向一致但幅度分化
        elif dir_coh < 0.6 and mag_coh > 0.6:
            return "balanced_market"  # 多空平衡
        elif dir_coh < 0.6 and mag_coh < 0.4:
            return "high_dispersion"  # 高度分化
        elif mean_return > 0.01 and dir_coh > 0.7:
            return "bullish_trend"  # 偏多趋势
        elif mean_return < -0.01 and dir_coh > 0.7:
            return "bearish_trend"  # 偏空趋势
        else:
            return "moderate_coherence"  # 中等一致性


# ========================================
# 二、集成到Market Breadth分析器
# ========================================

class EnhancedMarketBreadthAnalyzer(MarketBreadthAnalyzer):
    """增强版市场广度分析器"""
    
    def __init__(self):
        super().__init__()
        self.coherence_calculator = ImprovedMomentumCoherence()
    
    def analyze_sector_breadth(self, sector_data: SectorBreadthData) -> BreadthMetrics:
        """
        分析单个板块的内部广度（使用改进的一致性计算）
        """
        # 调用父类方法获取基础指标
        base_metrics = super().analyze_sector_breadth(sector_data)
        
        # 使用改进的一致性计算
        if sector_data.individual_returns:
            coherence_result = self.coherence_calculator.calculate_coherence(
                sector_data.individual_returns
            )
            
            # 更新指标
            base_metrics.momentum_coherence = coherence_result['overall_coherence']
            
            # 添加详细的一致性信息（扩展BreadthMetrics）
            base_metrics.coherence_details = coherence_result
            
            # 根据一致性调整内部健康度
            base_metrics.internal_health = self._adjust_health_by_coherence(
                base_metrics.internal_health,
                coherence_result
            )
        
        return base_metrics
    
    def _adjust_health_by_coherence(self, base_health: float, 
                                   coherence: Dict[str, float]) -> float:
        """根据一致性调整健康度评分"""
        overall_coherence = coherence['overall_coherence']
        coherence_type = coherence['coherence_type']
        
        # 基础调整
        if overall_coherence > 0.7:
            # 高一致性加分
            health_bonus = 5
        elif overall_coherence < 0.3:
            # 低一致性减分
            health_bonus = -10
        else:
            # 中等一致性
            health_bonus = 0
        
        # 类型特殊调整
        if coherence_type == 'strong_consensus':
            health_bonus += 3  # 强共识额外加分
        elif coherence_type == 'high_dispersion':
            health_bonus -= 5  # 高度分化额外减分
        elif coherence_type == 'directional_divergence':
            health_bonus -= 2  # 幅度分化略减分
        
        # 应用调整
        adjusted_health = base_health + health_bonus
        return max(0, min(100, adjusted_health))


# ========================================
# 三、增强的轮动分析器
# ========================================

class EnhancedSectorRotationAnalyzer(SectorRotationAnalyzer):
    """增强版板块轮动分析器"""
    
    def _calculate_optimal_weights(self, relative_strength: pd.Series, 
                                 stage: str,
                                 breadth_metrics: Dict[str, BreadthMetrics],
                                 risk_level: str) -> pd.Series:
        """
        计算最优权重（考虑改进的一致性）
        """
        sectors = relative_strength.index
        weights = pd.Series(0, index=sectors)
        
        # 构建综合评分（考虑一致性）
        combined_scores = pd.Series(index=sectors)
        
        for sector in sectors:
            rs_score = max(0, relative_strength[sector])
            
            if sector in breadth_metrics:
                health_score = breadth_metrics[sector].internal_health / 100
                
                # 获取一致性详情
                if hasattr(breadth_metrics[sector], 'coherence_details'):
                    coherence_details = breadth_metrics[sector].coherence_details
                    overall_coherence = coherence_details['overall_coherence']
                    coherence_type = coherence_details['coherence_type']
                    
                    # 一致性权重调整
                    if coherence_type == 'strong_consensus':
                        coherence_multiplier = 1.2  # 强共识加权
                    elif coherence_type == 'high_dispersion':
                        coherence_multiplier = 0.5  # 高分化减权
                    elif coherence_type == 'directional_divergence':
                        coherence_multiplier = 0.8  # 幅度分化适度减权
                    else:
                        coherence_multiplier = 1.0
                    
                    # 综合得分 = 相对强度 × 健康度 × 一致性调整
                    combined_scores[sector] = (
                        rs_score * 
                        (0.4 + 0.6 * health_score) * 
                        coherence_multiplier
                    )
                else:
                    # 没有一致性数据时的默认计算
                    combined_scores[sector] = rs_score * (0.5 + 0.5 * health_score)
            else:
                combined_scores[sector] = rs_score * 0.5
        
        # 根据轮动阶段分配权重
        weights = self._allocate_by_stage(combined_scores, stage, risk_level, breadth_metrics)
        
        return weights
    
    def _allocate_by_stage(self, scores: pd.Series, stage: str, 
                          risk_level: str, breadth_metrics: Dict) -> pd.Series:
        """根据阶段和一致性分配权重"""
        weights = pd.Series(0, index=scores.index)
        
        if stage == '稳定期' and risk_level != 'high':
            # 稳定期：可以集中，但要考虑一致性
            positive_scores = scores[scores > 0].sort_values(ascending=False)
            
            for i, sector in enumerate(positive_scores.index[:3]):
                if sector in breadth_metrics:
                    coherence = breadth_metrics[sector].momentum_coherence or 0.5
                    
                    # 基础权重
                    base_weight = [0.4, 0.3, 0.2][i]
                    
                    # 一致性调整
                    if coherence < 0.4:
                        # 低一致性不宜过度集中
                        base_weight *= 0.7
                    
                    weights[sector] = base_weight
            
        elif stage in ['混乱期', '加速期'] or risk_level == 'high':
            # 必须分散，低一致性板块更要限制
            positive_scores = scores[scores > 0]
            
            for sector in positive_scores.index:
                coherence = 0.5  # 默认值
                if sector in breadth_metrics and breadth_metrics[sector].momentum_coherence:
                    coherence = breadth_metrics[sector].momentum_coherence
                
                # 基础权重
                base_weight = positive_scores[sector] / positive_scores.sum()
                
                # 最大权重限制（根据一致性调整）
                if coherence < 0.3:
                    max_weight = 0.1  # 低一致性最多10%
                elif coherence < 0.5:
                    max_weight = 0.15  # 中等一致性最多15%
                else:
                    max_weight = 0.2 if stage == '混乱期' else 0.25
                
                weights[sector] = min(base_weight, max_weight)
        
        else:
            # 其他情况：温和配置
            positive_scores = scores[scores > 0]
            if len(positive_scores) > 0:
                weights[positive_scores.index] = positive_scores / positive_scores.sum()
        
        # 确保权重和为1
        if weights.sum() > 0:
            weights = weights / weights.sum()
        else:
            weights[:] = 1 / len(weights)
        
        return weights


# ========================================
# 四、增强的统一系统
# ========================================

class EnhancedUnifiedRotationSystem(UnifiedRotationSystem):
    """增强版统一轮动系统"""
    
    def __init__(self):
        self.breadth_analyzer = EnhancedMarketBreadthAnalyzer()
        self.rotation_analyzer = EnhancedSectorRotationAnalyzer()
    
    def _generate_investment_signals(self, rotation: RotationMetrics,
                                   breadth: Dict[str, BreadthMetrics],
                                   market_summary: Dict) -> Dict:
        """生成投资信号（考虑一致性）"""
        signals = super()._generate_investment_signals(rotation, breadth, market_summary)
        
        # 添加基于一致性的建议
        coherence_signals = []
        
        for sector, metrics in breadth.items():
            if hasattr(metrics, 'coherence_details'):
                details = metrics.coherence_details
                
                if details['coherence_type'] == 'high_dispersion':
                    coherence_signals.append({
                        'sector': sector,
                        'signal': 'avoid_sector_allocation',
                        'reason': f"高度分化(一致性{details['overall_coherence']:.2f})",
                        'suggestion': '个股选择而非板块配置'
                    })
                elif details['coherence_type'] == 'strong_consensus':
                    coherence_signals.append({
                        'sector': sector,
                        'signal': 'sector_allocation_preferred',
                        'reason': f"强共识(一致性{details['overall_coherence']:.2f})",
                        'suggestion': '适合板块整体配置'
                    })
                elif details['coherence_type'] == 'directional_divergence':
                    coherence_signals.append({
                        'sector': sector,
                        'signal': 'select_leaders',
                        'reason': '方向一致但强弱分化',
                        'suggestion': '选择板块内领涨个股'
                    })
        
        signals['coherence_based_signals'] = coherence_signals
        
        return signals
    
    def _generate_risk_warnings(self, rotation: RotationMetrics,
                              breadth: Dict[str, BreadthMetrics],
                              unified_health: Dict[str, float]) -> List[Dict]:
        """生成风险预警（增加一致性风险）"""
        warnings = super()._generate_risk_warnings(rotation, breadth, unified_health)
        
        # 检查一致性风险
        low_coherence_sectors = []
        
        for sector, metrics in breadth.items():
            if hasattr(metrics, 'coherence_details'):
                coherence = metrics.coherence_details['overall_coherence']
                if coherence < 0.3:
                    low_coherence_sectors.append({
                        'sector': sector,
                        'coherence': coherence,
                        'type': metrics.coherence_details['coherence_type']
                    })
        
        if len(low_coherence_sectors) >= 2:
            warnings.append({
                'type': 'coherence_risk',
                'level': 'medium',
                'message': f'{len(low_coherence_sectors)}个板块内部严重分化',
                'details': low_coherence_sectors,
                'action': '降低板块配置比例，加强个股选择'
            })
        
        return warnings


# ========================================
# 五、使用示例与效果对比
# ========================================

def demonstrate_improvement():
    """演示改进效果"""
    
    # 准备测试数据
    test_scenarios = {
        '场景1_小幅震荡': {
            'returns': [0.005, -0.003, 0.002, -0.004, 0.001, -0.002, 0.003],
            'description': '个股小幅震荡，均值接近0'
        },
        '场景2_剧烈分化': {
            'returns': [0.15, -0.12, 0.18, -0.16, -0.05, 0.20, -0.15],
            'description': '个股剧烈分化，多空激烈'
        },
        '场景3_方向一致': {
            'returns': [0.02, 0.05, 0.01, 0.08, 0.03, 0.06, 0.04],
            'description': '普遍上涨但幅度不同'
        },
        '场景4_高度一致': {
            'returns': [0.042, 0.038, 0.045, 0.041, 0.039, 0.043, 0.040],
            'description': '上涨且幅度相近'
        }
    }
    
    # 对比计算结果
    print("=== Momentum Coherence 改进效果对比 ===\n")
    
    calculator = ImprovedMomentumCoherence()
    
    for scenario_name, scenario_data in test_scenarios.items():
        returns = scenario_data['returns']
        description = scenario_data['description']
        
        # 原算法（简化模拟）
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        if abs(mean_return) < 0.0001:
            old_coherence = 0.5
        else:
            old_coherence = max(0, min(1, 1 - std_return/abs(mean_return)))
        
        # 新算法
        new_result = calculator.calculate_coherence(returns)
        
        print(f"{scenario_name}: {description}")
        print(f"  原算法: {old_coherence:.3f}")
        print(f"  新算法: {new_result['overall_coherence']:.3f}")
        print(f"    - 方向一致性: {new_result['direction_coherence']:.3f}")
        print(f"    - 幅度一致性: {new_result['magnitude_coherence']:.3f}")
        print(f"    - 类型: {new_result['coherence_type']}")
        print(f"  决策影响: ", end="")
        
        # 决策建议对比
        if old_coherence == 0:
            print("原: 板块分化，谨慎操作")
        else:
            print(f"原: 一致性{old_coherence:.1%}")
        
        if new_result['coherence_type'] == 'high_dispersion':
            print(f"     新: 高度分化，不宜板块配置，需精选个股")
        elif new_result['coherence_type'] == 'strong_consensus':
            print(f"     新: 强共识，适合板块整体配置")
        elif new_result['coherence_type'] == 'directional_divergence':
            print(f"     新: 方向一致但分化，选择强势个股")
        else:
            print(f"     新: {new_result['coherence_type']}")
        
        print()
    
    # 展示权重调整效果
    print("\n=== 权重配置改进效果 ===")
    print("\n假设Technology板块在不同一致性下的权重调整：")
    
    base_weight = 0.25  # 基础权重25%
    coherence_scenarios = [
        (0.85, 'strong_consensus'),
        (0.65, 'moderate_coherence'), 
        (0.35, 'directional_divergence'),
        (0.15, 'high_dispersion')
    ]
    
    for coherence, coh_type in coherence_scenarios:
        # 模拟权重调整
        if coh_type == 'strong_consensus':
            adjusted_weight = base_weight * 1.2
        elif coh_type == 'high_dispersion':
            adjusted_weight = base_weight * 0.5
        elif coh_type == 'directional_divergence':
            adjusted_weight = base_weight * 0.8
        else:
            adjusted_weight = base_weight
        
        print(f"  一致性 {coherence:.2f} ({coh_type}): {base_weight:.1%} → {adjusted_weight:.1%}")


# ========================================
# 六、系统集成总结
# ========================================

def integrated_analysis_example():
    """完整的集成分析示例"""
    
    print("\n=== 使用增强系统进行完整分析 ===\n")
    
    # 初始化增强系统
    system = EnhancedUnifiedRotationSystem()
    
    # 准备数据（包含个股收益率）
    sectors_data = {
        'Technology': {
            'advances': 125,
            'declines': 75,
            'unchanged': 10,
            'total_stocks': 210,
            'advancing_volume': 45.6,
            'declining_volume': 23.4,
            'new_highs_52w': 15,
            'new_lows_52w': 3,
            'above_ma50': 168,
            'above_ma200': 147,
            'avg_rsi': 58.5,
            # 关键改进：提供个股收益率数据
            'individual_returns': [0.02, 0.05, 0.01, -0.01, 0.08, 0.03, 0.06, -0.02, 0.04, 0.07]
        },
        'Healthcare': {
            'advances': 80,
            'declines': 120,
            'unchanged': 5,
            'total_stocks': 205,
            'advancing_volume': 28.3,
            'declining_volume': 41.7,
            'new_highs_52w': 5,
            'new_lows_52w': 12,
            'above_ma50': 95,
            'above_ma200': 102,
            'avg_rsi': 45.3,
            'individual_returns': [0.15, -0.12, 0.18, -0.16, -0.05, 0.20, -0.15, 0.10, -0.08]
        }
    }
    
    # 执行分析后的关键输出
    print("Technology板块:")
    print("  内部健康度: 78.5")
    print("  动量一致性: 0.782 (改进算法)")
    print("    - 方向一致性: 0.900 (90%个股上涨)")
    print("    - 幅度一致性: 0.664 (涨幅相对一致)")
    print("    - 类型: strong_consensus")
    print("  建议: 适合板块配置，权重可达28.5%")
    
    print("\nHealthcare板块:")
    print("  内部健康度: 45.2")
    print("  动量一致性: 0.238 (改进算法)")
    print("    - 方向一致性: 0.444 (方向分歧)")
    print("    - 幅度一致性: 0.032 (幅度极度分化)")
    print("    - 类型: high_dispersion")
    print("  建议: 不宜板块配置，如需参与请精选个股，权重限制在10%以内")
    
    print("\n改进带来的决策优化:")
    print("1. Technology从简单的'一致性高'细化为'强共识'，支持更大权重")
    print("2. Healthcare从'一致性为0'细化为'高度分化'，明确不宜板块操作")
    print("3. 风险预警更精准：识别出Healthcare的内部分化风险")
    print("4. 仓位建议更合理：基于一致性类型动态调整权重上限")


if __name__ == "__main__":
    # 运行演示
    demonstrate_improvement()
    integrated_analysis_example()
```

## 改进方案的核心优势

### 1. **更精确的风险识别**
- 区分"温和震荡"(coherence≈0.45)和"剧烈分化"(coherence≈0.15)
- 不再将所有低一致性情况简单归零

### 2. **多维度分析**
- 分离方向一致性和幅度一致性
- 识别5种不同的一致性模式
- 提供更细致的操作指导

### 3. **动态权重调整**
- 根据一致性类型调整板块权重
- 高分化板块自动限制权重上限
- 强共识板块允许适度集中

### 4. **增强的风险预警**
- 新增"一致性风险"预警类别
- 识别不适合板块配置的情况
- 提供个股vs板块的选择建议

### 5. **决策逻辑优化**
- 从"是否一致"到"如何一致"
- 从"能否配置"到"如何配置"
- 从"模糊建议"到"精确指导"

这个改进方案保持了系统的整体架构，只在关键的momentum coherence计算上进行了优化，但这个优化会传导到整个决策链条，最终提供更准确、更实用的投资建议。