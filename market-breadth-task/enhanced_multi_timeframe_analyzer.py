"""
增强版多时间框架分析器
基于优化文档要求，增强信号强度计算、统一决策生成和避免清单生成
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class SignalStrengthDetails:
    """信号强度详细信息"""
    overall_strength: float
    component_contributions: Dict[str, float]
    quality_assessment: Dict[str, float]
    non_linear_adjustments: Dict[str, float]
    confidence_factors: Dict[str, float]
    signal_grade: str


@dataclass
class UnifiedDecisionDetails:
    """统一决策详细信息"""
    primary_decision: str
    decision_confidence: float
    decision_steps: List[Dict]
    alternative_scenarios: List[Dict]
    risk_assessment: Dict[str, Any]
    supporting_evidence: List[str]
    decision_rationale: str


@dataclass
class AvoidListDetails:
    """避免清单详细信息"""
    avoid_list: List[str]
    avoid_reasons: Dict[str, Dict]
    severity_levels: Dict[str, str]
    improvement_conditions: Dict[str, List[str]]
    reassessment_triggers: Dict[str, List[str]]
    alternative_opportunities: List[str]


class EnhancedMultiTimeframeAnalyzer:
    """增强版多时间框架分析器"""
    
    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化增强版多时间框架分析器
        
        Args:
            decision_tracker: 可选的决策追踪器
        """
        self.tracker = decision_tracker
        
        # 信号强度计算参数
        self.signal_components = {
            'rii_strength': {'weight': 0.25, 'non_linear_power': 1.2},
            'health_strength': {'weight': 0.20, 'non_linear_power': 0.8},
            'momentum_strength': {'weight': 0.15, 'non_linear_power': 1.5},
            'coherence_strength': {'weight': 0.20, 'non_linear_power': 1.0},
            'divergence_strength': {'weight': 0.10, 'non_linear_power': 2.0},
            'participation_strength': {'weight': 0.10, 'non_linear_power': 0.9}
        }
        
        # 决策阈值
        self.decision_thresholds = {
            'strong_buy': 0.75,
            'buy': 0.60,
            'hold': 0.40,
            'sell': 0.25,
            'strong_sell': 0.15
        }
        
        # 避免清单严重度分级
        self.avoid_severity_levels = {
            'critical': {'threshold': 0.8, 'description': '强烈避免'},
            'high': {'threshold': 0.6, 'description': '谨慎避免'},
            'medium': {'threshold': 0.4, 'description': '观望'},
            'low': {'threshold': 0.2, 'description': '轻微关注'}
        }
    
    def calculate_signal_strength_enhanced(self, rotation_metrics: Any, 
                                         breadth_metrics: Dict[str, Any],
                                         timeframe: str = 'daily') -> SignalStrengthDetails:
        """
        计算增强版信号强度
        
        Args:
            rotation_metrics: 轮动指标
            breadth_metrics: 广度指标字典
            timeframe: 时间框架
            
        Returns:
            SignalStrengthDetails: 信号强度详细信息
        """
        if self.tracker:
            self.tracker.start_decision('signal_strength_calculation', {
                'timeframe': timeframe,
                'sectors_count': len(breadth_metrics)
            })
        
        # 1. 计算各维度原始强度
        raw_components = self._calculate_raw_signal_components(
            rotation_metrics, breadth_metrics
        )
        
        # 2. 应用非线性映射
        non_linear_components = self._apply_non_linear_mapping(raw_components)
        
        # 3. 计算质量评估
        quality_assessment = self._assess_signal_quality(
            raw_components, breadth_metrics
        )
        
        # 4. 计算置信度因子
        confidence_factors = self._calculate_confidence_factors(
            raw_components, breadth_metrics, timeframe
        )
        
        # 5. 计算加权综合强度
        weighted_strength = self._calculate_weighted_strength(
            non_linear_components, quality_assessment, confidence_factors
        )
        
        # 6. 信号分级
        signal_grade = self._grade_signal_strength(weighted_strength, quality_assessment)
        
        # 7. 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'signal_synthesis',
                {
                    'raw_components': raw_components,
                    'final_strength': weighted_strength,
                    'signal_grade': signal_grade
                },
                f'综合信号强度{weighted_strength:.3f}，等级{signal_grade}',
                confidence=quality_assessment.get('overall_quality', 0.5)
            )
            
            self.tracker.complete_decision({
                'signal_strength': weighted_strength,
                'signal_grade': signal_grade,
                'quality_score': quality_assessment.get('overall_quality', 0.5)
            }, quality_assessment.get('overall_quality', 0.5))
        
        return SignalStrengthDetails(
            overall_strength=weighted_strength,
            component_contributions=non_linear_components,
            quality_assessment=quality_assessment,
            non_linear_adjustments=self._calculate_non_linear_adjustments(raw_components, non_linear_components),
            confidence_factors=confidence_factors,
            signal_grade=signal_grade
        )
    
    def generate_unified_decision_enhanced(self, timeframe_results: Dict,
                                         dynamic_weights: Dict,
                                         market_regime: str) -> UnifiedDecisionDetails:
        """
        生成增强版统一决策
        
        Args:
            timeframe_results: 各时间框架结果
            dynamic_weights: 动态权重
            market_regime: 市场状态
            
        Returns:
            UnifiedDecisionDetails: 统一决策详细信息
        """
        if self.tracker:
            self.tracker.start_decision('unified_decision_generation', {
                'timeframes_count': len(timeframe_results),
                'market_regime': market_regime
            })
        
        decision_steps = []
        
        # 步骤1: 收集各时间框架信号
        timeframe_signals, step1 = self._collect_timeframe_signals(
            timeframe_results, dynamic_weights
        )
        decision_steps.append(step1)
        
        # 步骤2: 计算加权综合信号
        weighted_signal, step2 = self._calculate_weighted_signal(
            timeframe_signals, dynamic_weights
        )
        decision_steps.append(step2)
        
        # 步骤3: 应用市场状态调整
        adjusted_signal, step3 = self._apply_market_regime_adjustment(
            weighted_signal, market_regime
        )
        decision_steps.append(step3)
        
        # 步骤4: 生成主要决策
        primary_decision, decision_confidence, step4 = self._generate_primary_decision(
            adjusted_signal, timeframe_signals
        )
        decision_steps.append(step4)
        
        # 步骤5: 风险评估
        risk_assessment, step5 = self._assess_decision_risks(
            primary_decision, timeframe_results, market_regime
        )
        decision_steps.append(step5)
        
        # 步骤6: 生成备选场景
        alternative_scenarios = self._generate_alternative_scenarios(
            adjusted_signal, timeframe_signals, risk_assessment
        )
        
        # 步骤7: 收集支持证据
        supporting_evidence = self._collect_supporting_evidence(
            primary_decision, timeframe_results, risk_assessment
        )
        
        # 步骤8: 生成决策理由
        decision_rationale = self._generate_decision_rationale(
            primary_decision, decision_steps, risk_assessment
        )
        
        # 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'decision_synthesis',
                {
                    'primary_decision': primary_decision,
                    'confidence': decision_confidence,
                    'alternatives_count': len(alternative_scenarios)
                },
                f'统一决策：{primary_decision}，置信度{decision_confidence:.2f}',
                confidence=decision_confidence
            )
            
            self.tracker.complete_decision({
                'unified_decision': primary_decision,
                'decision_confidence': decision_confidence,
                'risk_level': risk_assessment.get('overall_risk_level', 'medium')
            }, decision_confidence)
        
        return UnifiedDecisionDetails(
            primary_decision=primary_decision,
            decision_confidence=decision_confidence,
            decision_steps=decision_steps,
            alternative_scenarios=alternative_scenarios,
            risk_assessment=risk_assessment,
            supporting_evidence=supporting_evidence,
            decision_rationale=decision_rationale
        )
    
    def generate_avoid_list_enhanced(self, breadth_metrics: Dict[str, Any],
                                   rotation_metrics: Any,
                                   market_regime: str) -> AvoidListDetails:
        """
        生成增强版避免清单
        
        Args:
            breadth_metrics: 广度指标字典
            rotation_metrics: 轮动指标
            market_regime: 市场状态
            
        Returns:
            AvoidListDetails: 避免清单详细信息
        """
        if self.tracker:
            self.tracker.start_decision('avoid_list_generation', {
                'sectors_count': len(breadth_metrics),
                'market_regime': market_regime
            })
        
        # 1. 计算各板块避免分数
        avoid_scores = self._calculate_avoid_scores(breadth_metrics, rotation_metrics)
        
        # 2. 应用市场状态调整
        adjusted_scores = self._apply_market_regime_avoid_adjustment(
            avoid_scores, market_regime
        )
        
        # 3. 分级避免建议
        avoid_list, severity_levels = self._classify_avoid_severity(adjusted_scores)
        
        # 4. 生成避免原因
        avoid_reasons = self._generate_avoid_reasons(
            avoid_list, breadth_metrics, adjusted_scores
        )
        
        # 5. 生成改善条件
        improvement_conditions = self._generate_improvement_conditions(
            avoid_list, breadth_metrics, avoid_reasons
        )
        
        # 6. 生成重新评估触发条件
        reassessment_triggers = self._generate_reassessment_triggers(
            avoid_list, avoid_reasons, market_regime
        )
        
        # 7. 寻找替代机会
        alternative_opportunities = self._find_alternative_opportunities(
            avoid_list, breadth_metrics, rotation_metrics
        )
        
        # 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'avoid_list_synthesis',
                {
                    'avoid_count': len(avoid_list),
                    'severity_distribution': {level: len([s for s in severity_levels.values() if s == level]) 
                                            for level in set(severity_levels.values())},
                    'alternatives_count': len(alternative_opportunities)
                },
                f'生成避免清单：{len(avoid_list)}个板块，{len(alternative_opportunities)}个替代机会',
                confidence=0.8
            )
            
            self.tracker.complete_decision({
                'avoid_list': avoid_list,
                'total_avoided': len(avoid_list),
                'alternatives_available': len(alternative_opportunities)
            }, 0.8)
        
        return AvoidListDetails(
            avoid_list=avoid_list,
            avoid_reasons=avoid_reasons,
            severity_levels=severity_levels,
            improvement_conditions=improvement_conditions,
            reassessment_triggers=reassessment_triggers,
            alternative_opportunities=alternative_opportunities
        )

    def _calculate_raw_signal_components(self, rotation_metrics: Any,
                                       breadth_metrics: Dict[str, Any]) -> Dict[str, float]:
        """计算原始信号分量"""
        components = {}

        # 1. RII强度分量
        if hasattr(rotation_metrics, 'unified_rii'):
            components['rii_strength'] = min(rotation_metrics.unified_rii, 1.0)
        else:
            components['rii_strength'] = 0.5

        # 2. 健康度分量
        if breadth_metrics:
            health_scores = []
            for sector, metrics in breadth_metrics.items():
                if hasattr(metrics, 'internal_health'):
                    health_scores.append(metrics.internal_health)

            if health_scores:
                avg_health = np.mean(health_scores) / 100
                components['health_strength'] = max(0, min(1, avg_health))
            else:
                components['health_strength'] = 0.5
        else:
            components['health_strength'] = 0.5

        # 3. 动量强度分量
        if hasattr(rotation_metrics, 'sector_momentum'):
            max_momentum = abs(rotation_metrics.sector_momentum.max())
            components['momentum_strength'] = min(max_momentum / 10, 1.0)
        else:
            components['momentum_strength'] = 0.5

        # 4. 一致性强度分量
        if breadth_metrics:
            coherence_scores = []
            for sector, metrics in breadth_metrics.items():
                if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence:
                    coherence_scores.append(metrics.momentum_coherence)

            if coherence_scores:
                avg_coherence = np.mean(coherence_scores)
                components['coherence_strength'] = max(0, min(1, avg_coherence))
            else:
                components['coherence_strength'] = 0.5
        else:
            components['coherence_strength'] = 0.5

        # 5. 背离强度分量
        divergence_count = 0
        total_sectors = len(breadth_metrics) if breadth_metrics else 1

        if breadth_metrics:
            for sector, metrics in breadth_metrics.items():
                if hasattr(metrics, 'price_breadth_divergence') and metrics.price_breadth_divergence:
                    if metrics.price_breadth_divergence.get('type') != 'none':
                        divergence_count += 1

        components['divergence_strength'] = divergence_count / total_sectors

        # 6. 参与率强度分量
        if breadth_metrics:
            participation_rates = []
            for sector, metrics in breadth_metrics.items():
                if hasattr(metrics, 'participation_rate'):
                    participation_rates.append(metrics.participation_rate)

            if participation_rates:
                avg_participation = np.mean(participation_rates)
                components['participation_strength'] = max(0, min(1, avg_participation))
            else:
                components['participation_strength'] = 0.5
        else:
            components['participation_strength'] = 0.5

        return components

    def _apply_non_linear_mapping(self, raw_components: Dict[str, float]) -> Dict[str, float]:
        """应用非线性映射"""
        non_linear_components = {}

        for component, raw_value in raw_components.items():
            if component in self.signal_components:
                power = self.signal_components[component]['non_linear_power']
                # 应用幂函数映射
                non_linear_value = raw_value ** power
                non_linear_components[component] = non_linear_value
            else:
                non_linear_components[component] = raw_value

        return non_linear_components

    def _assess_signal_quality(self, raw_components: Dict[str, float],
                             breadth_metrics: Dict[str, Any]) -> Dict[str, float]:
        """评估信号质量"""
        quality_factors = {}

        # 1. 数据完整性
        if breadth_metrics:
            valid_sectors = sum(1 for metrics in breadth_metrics.values()
                              if hasattr(metrics, 'internal_health'))
            data_completeness = valid_sectors / len(breadth_metrics)
        else:
            data_completeness = 0.0

        quality_factors['data_completeness'] = data_completeness

        # 2. 信号一致性
        component_values = list(raw_components.values())
        if len(component_values) > 1:
            signal_consistency = 1 - np.std(component_values)
        else:
            signal_consistency = 0.5

        quality_factors['signal_consistency'] = max(0, signal_consistency)

        # 3. 极值检测
        extreme_count = sum(1 for v in component_values if v > 0.9 or v < 0.1)
        extreme_ratio = extreme_count / len(component_values)
        quality_factors['extreme_ratio'] = extreme_ratio

        # 4. 综合质量评分
        overall_quality = (
            data_completeness * 0.4 +
            signal_consistency * 0.4 +
            (1 - extreme_ratio) * 0.2
        )

        quality_factors['overall_quality'] = max(0.1, min(1.0, overall_quality))

        return quality_factors

    def _calculate_confidence_factors(self, raw_components: Dict[str, float],
                                    breadth_metrics: Dict[str, Any],
                                    timeframe: str) -> Dict[str, float]:
        """计算置信度因子"""
        confidence_factors = {}

        # 1. 时间框架置信度
        timeframe_confidence = {
            'intraday_5min': 0.6,
            'intraday_15min': 0.7,
            'intraday_hourly': 0.75,
            'daily': 0.9,
            'weekly': 0.85,
            'monthly': 0.8
        }

        confidence_factors['timeframe_confidence'] = timeframe_confidence.get(timeframe, 0.8)

        # 2. 样本量置信度
        sample_size = len(breadth_metrics) if breadth_metrics else 0
        if sample_size > 20:
            sample_confidence = 0.95
        elif sample_size > 10:
            sample_confidence = 0.85
        elif sample_size > 5:
            sample_confidence = 0.75
        else:
            sample_confidence = 0.6

        confidence_factors['sample_confidence'] = sample_confidence

        # 3. 信号强度置信度
        avg_signal_strength = np.mean(list(raw_components.values()))
        if avg_signal_strength > 0.7:
            strength_confidence = 0.9
        elif avg_signal_strength > 0.5:
            strength_confidence = 0.8
        elif avg_signal_strength > 0.3:
            strength_confidence = 0.7
        else:
            strength_confidence = 0.6

        confidence_factors['strength_confidence'] = strength_confidence

        # 4. 综合置信度
        overall_confidence = (
            confidence_factors['timeframe_confidence'] * 0.3 +
            confidence_factors['sample_confidence'] * 0.4 +
            confidence_factors['strength_confidence'] * 0.3
        )

        confidence_factors['overall_confidence'] = overall_confidence

        return confidence_factors

    def _calculate_weighted_strength(self, non_linear_components: Dict[str, float],
                                   quality_assessment: Dict[str, float],
                                   confidence_factors: Dict[str, float]) -> float:
        """计算加权综合强度"""
        weighted_sum = 0.0
        total_weight = 0.0

        for component, value in non_linear_components.items():
            if component in self.signal_components:
                weight = self.signal_components[component]['weight']

                # 应用质量调整
                quality_adjusted_weight = weight * quality_assessment.get('overall_quality', 1.0)

                # 应用置信度调整
                confidence_adjusted_weight = quality_adjusted_weight * confidence_factors.get('overall_confidence', 1.0)

                weighted_sum += value * confidence_adjusted_weight
                total_weight += confidence_adjusted_weight

        if total_weight > 0:
            final_strength = weighted_sum / total_weight
        else:
            final_strength = 0.5

        return round(max(0.0, min(1.0, final_strength)), 4)

    def _grade_signal_strength(self, strength: float, quality_assessment: Dict[str, float]) -> str:
        """信号强度分级"""
        quality_score = quality_assessment.get('overall_quality', 0.5)

        # 基于强度和质量的综合评级
        if strength >= 0.8 and quality_score >= 0.8:
            return 'A+'
        elif strength >= 0.7 and quality_score >= 0.7:
            return 'A'
        elif strength >= 0.6 and quality_score >= 0.6:
            return 'B+'
        elif strength >= 0.5 and quality_score >= 0.5:
            return 'B'
        elif strength >= 0.4 and quality_score >= 0.4:
            return 'C+'
        elif strength >= 0.3 and quality_score >= 0.3:
            return 'C'
        else:
            return 'D'

    def _calculate_non_linear_adjustments(self, raw_components: Dict[str, float],
                                        non_linear_components: Dict[str, float]) -> Dict[str, float]:
        """计算非线性调整量"""
        adjustments = {}

        for component in raw_components:
            if component in non_linear_components:
                raw_value = raw_components[component]
                adjusted_value = non_linear_components[component]
                adjustment = adjusted_value - raw_value
                adjustments[component] = round(adjustment, 4)

        return adjustments

    def _collect_timeframe_signals(self, timeframe_results: Dict,
                                 dynamic_weights: Dict) -> Tuple[Dict, Dict]:
        """收集各时间框架信号"""
        timeframe_signals = {}

        for timeframe, results in timeframe_results.items():
            if hasattr(results, 'signal_strength'):
                signal_strength = results.signal_strength
            else:
                signal_strength = 0.5

            if hasattr(results, 'confidence'):
                confidence = results.confidence
            else:
                confidence = 0.5

            timeframe_signals[timeframe] = {
                'strength': signal_strength,
                'confidence': confidence,
                'weight': dynamic_weights.get(timeframe, 0.0)
            }

        step_info = {
            'step_name': 'timeframe_signal_collection',
            'description': f'收集{len(timeframe_signals)}个时间框架的信号',
            'signals': timeframe_signals
        }

        return timeframe_signals, step_info

    def _calculate_weighted_signal(self, timeframe_signals: Dict,
                                 dynamic_weights: Dict) -> Tuple[float, Dict]:
        """计算加权综合信号"""
        weighted_sum = 0.0
        total_weight = 0.0

        for timeframe, signal_info in timeframe_signals.items():
            strength = signal_info['strength']
            confidence = signal_info['confidence']
            weight = signal_info['weight']

            # 置信度调整权重
            adjusted_weight = weight * confidence

            weighted_sum += strength * adjusted_weight
            total_weight += adjusted_weight

        if total_weight > 0:
            weighted_signal = weighted_sum / total_weight
        else:
            weighted_signal = 0.5

        step_info = {
            'step_name': 'weighted_signal_calculation',
            'description': f'计算加权综合信号：{weighted_signal:.3f}',
            'weighted_signal': weighted_signal,
            'total_weight': total_weight
        }

        return weighted_signal, step_info

    def _apply_market_regime_adjustment(self, weighted_signal: float,
                                      market_regime: str) -> Tuple[float, Dict]:
        """应用市场状态调整"""
        # 市场状态调整因子
        regime_adjustments = {
            'trending_stable': 1.1,
            'normal_market': 1.0,
            'high_rotation': 0.9,
            'short_term_stress': 0.8,
            'regime_transition': 0.7,
            'divergent_market': 0.6
        }

        adjustment_factor = regime_adjustments.get(market_regime, 1.0)
        adjusted_signal = weighted_signal * adjustment_factor
        adjusted_signal = max(0.0, min(1.0, adjusted_signal))

        step_info = {
            'step_name': 'market_regime_adjustment',
            'description': f'市场状态调整：{market_regime}，因子{adjustment_factor}',
            'original_signal': weighted_signal,
            'adjusted_signal': adjusted_signal,
            'adjustment_factor': adjustment_factor
        }

        return adjusted_signal, step_info

    def _generate_primary_decision(self, adjusted_signal: float,
                                 timeframe_signals: Dict) -> Tuple[str, float, Dict]:
        """生成主要决策"""
        # 基于信号强度确定决策
        if adjusted_signal >= self.decision_thresholds['strong_buy']:
            decision = 'strong_buy'
        elif adjusted_signal >= self.decision_thresholds['buy']:
            decision = 'buy'
        elif adjusted_signal >= self.decision_thresholds['hold']:
            decision = 'hold'
        elif adjusted_signal >= self.decision_thresholds['sell']:
            decision = 'sell'
        else:
            decision = 'strong_sell'

        # 计算决策置信度
        signal_consistency = self._calculate_signal_consistency(timeframe_signals)
        decision_confidence = adjusted_signal * signal_consistency

        step_info = {
            'step_name': 'primary_decision_generation',
            'description': f'生成主要决策：{decision}',
            'decision': decision,
            'signal_strength': adjusted_signal,
            'decision_confidence': decision_confidence,
            'signal_consistency': signal_consistency
        }

        return decision, decision_confidence, step_info

    def _assess_decision_risks(self, primary_decision: str,
                             timeframe_results: Dict,
                             market_regime: str) -> Tuple[Dict, Dict]:
        """评估决策风险"""
        risk_assessment = {
            'overall_risk_level': 'medium',
            'risk_factors': [],
            'risk_mitigation': [],
            'risk_score': 0.5
        }

        risk_score = 0.3  # 降低基础风险分数，让评估更敏感
        risk_factors = []

        # 1. 决策类型风险
        if primary_decision in ['strong_buy', 'strong_sell']:
            risk_score += 0.25  # 增加极端决策的风险权重
            risk_factors.append('极端决策增加风险')

        # 2. 市场状态风险
        market_risk_weights = {
            'regime_transition': 0.35,
            'divergent_market': 0.4,
            'short_term_stress': 0.3,
            'high_rotation': 0.2,
            'normal_market': 0.0,
            'trending_stable': -0.1  # 稳定市场降低风险
        }

        market_risk = market_risk_weights.get(market_regime, 0.1)
        risk_score += market_risk

        if market_risk > 0.15:
            risk_factors.append(f'市场状态不稳定：{market_regime}')

        # 3. 时间框架分歧风险
        if len(timeframe_results) > 1:
            signals = [r.signal_strength for r in timeframe_results.values() if hasattr(r, 'signal_strength')]
            if signals and len(signals) > 1:
                signal_std = np.std(signals)
                if signal_std > 0.3:
                    risk_score += 0.25  # 增加分歧风险权重
                    risk_factors.append(f'时间框架信号分歧较大(标准差:{signal_std:.3f})')
                elif signal_std > 0.2:
                    risk_score += 0.15
                    risk_factors.append(f'时间框架信号存在分歧(标准差:{signal_std:.3f})')

        # 4. 置信度风险
        confidences = [r.confidence for r in timeframe_results.values() if hasattr(r, 'confidence')]
        if confidences:
            avg_confidence = np.mean(confidences)
            if avg_confidence < 0.6:
                risk_score += 0.2
                risk_factors.append(f'平均置信度较低({avg_confidence:.2f})')

        # 5. 确定风险等级 - 调整阈值使其更准确
        final_risk_score = min(1.0, max(0.0, risk_score))

        if final_risk_score >= 0.75:
            risk_level = 'high'
        elif final_risk_score >= 0.6:
            risk_level = 'medium_high'
        elif final_risk_score >= 0.45:
            risk_level = 'medium'
        elif final_risk_score >= 0.25:
            risk_level = 'low_medium'
        else:
            risk_level = 'low'

        risk_assessment.update({
            'overall_risk_level': risk_level,
            'risk_factors': risk_factors,
            'risk_score': final_risk_score,
            'risk_mitigation': self._generate_risk_mitigation(risk_factors, primary_decision)
        })

        step_info = {
            'step_name': 'risk_assessment',
            'description': f'风险评估：{risk_level} (评分:{final_risk_score:.3f})',
            'risk_assessment': risk_assessment
        }

        return risk_assessment, step_info

    def _generate_alternative_scenarios(self, adjusted_signal: float,
                                      timeframe_signals: Dict,
                                      risk_assessment: Dict) -> List[Dict]:
        """生成备选场景"""
        scenarios = []

        # 场景1：保守策略
        conservative_signal = adjusted_signal * 0.7
        conservative_decision = self._signal_to_decision(conservative_signal)
        scenarios.append({
            'scenario': 'conservative',
            'description': '保守策略，降低风险敞口',
            'decision': conservative_decision,
            'signal_strength': conservative_signal,
            'probability': 0.3,
            'rationale': '适合风险厌恶投资者或不确定市场环境'
        })

        # 场景2：激进策略
        aggressive_signal = min(1.0, adjusted_signal * 1.3)
        aggressive_decision = self._signal_to_decision(aggressive_signal)
        scenarios.append({
            'scenario': 'aggressive',
            'description': '激进策略，最大化收益潜力',
            'decision': aggressive_decision,
            'signal_strength': aggressive_signal,
            'probability': 0.2,
            'rationale': '适合风险偏好投资者和强势市场环境'
        })

        # 场景3：等待策略
        scenarios.append({
            'scenario': 'wait_and_see',
            'description': '等待更明确信号',
            'decision': 'hold',
            'signal_strength': 0.5,
            'probability': 0.3,
            'rationale': '当信号不够明确或风险较高时的谨慎选择'
        })

        return scenarios

    def _collect_supporting_evidence(self, primary_decision: str,
                                   timeframe_results: Dict,
                                   risk_assessment: Dict) -> List[str]:
        """收集支持证据"""
        evidence = []

        # 1. 时间框架一致性证据
        consistent_timeframes = 0
        total_timeframes = len(timeframe_results)

        for tf, results in timeframe_results.items():
            if hasattr(results, 'signal_strength'):
                tf_decision = self._signal_to_decision(results.signal_strength)
                if tf_decision == primary_decision:
                    consistent_timeframes += 1

        if consistent_timeframes > 0:
            consistency_rate = consistent_timeframes / total_timeframes
            evidence.append(f"{consistency_rate:.1%}的时间框架支持{primary_decision}决策")

        # 2. 信号强度证据
        avg_strength = np.mean([r.signal_strength for r in timeframe_results.values()
                               if hasattr(r, 'signal_strength')])
        evidence.append(f"平均信号强度{avg_strength:.2f}，支持当前决策方向")

        # 3. 风险控制证据
        if risk_assessment['overall_risk_level'] in ['low', 'low_medium']:
            evidence.append("风险评估显示当前决策风险可控")
        elif risk_assessment['overall_risk_level'] in ['high', 'medium_high']:
            evidence.append("已识别主要风险因素并制定缓解措施")

        return evidence

    def _generate_decision_rationale(self, primary_decision: str,
                                   decision_steps: List[Dict],
                                   risk_assessment: Dict) -> str:
        """生成决策理由"""
        rationale_parts = [
            f"基于多时间框架分析，推荐{primary_decision}策略。",
            f"决策过程包含{len(decision_steps)}个步骤的综合评估。",
            f"风险等级为{risk_assessment['overall_risk_level']}，已制定相应风险控制措施。"
        ]

        if risk_assessment['risk_factors']:
            rationale_parts.append(f"主要风险因素包括：{', '.join(risk_assessment['risk_factors'][:2])}。")

        return " ".join(rationale_parts)

    def _calculate_avoid_scores(self, breadth_metrics: Dict[str, Any],
                              rotation_metrics: Any) -> Dict[str, float]:
        """计算各板块避免分数"""
        avoid_scores = {}

        for sector, metrics in breadth_metrics.items():
            score = 0.0

            # 1. 健康度因子（权重40%）
            if hasattr(metrics, 'internal_health'):
                health_score = (100 - metrics.internal_health) / 100  # 健康度越低，避免分数越高
                score += health_score * 0.4

            # 2. 一致性因子（权重25%）
            if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence:
                coherence_score = 1 - metrics.momentum_coherence  # 一致性越低，避免分数越高
                score += coherence_score * 0.25

            # 3. 背离因子（权重20%）
            if hasattr(metrics, 'price_breadth_divergence') and metrics.price_breadth_divergence:
                if metrics.price_breadth_divergence.get('type') == 'negative':
                    divergence_score = metrics.price_breadth_divergence.get('severity', 0)
                    score += divergence_score * 0.2

            # 4. 参与率因子（权重15%）
            if hasattr(metrics, 'participation_rate'):
                participation_score = 1 - metrics.participation_rate  # 参与率越低，避免分数越高
                score += participation_score * 0.15

            avoid_scores[sector] = min(1.0, score)

        return avoid_scores

    def _apply_market_regime_avoid_adjustment(self, avoid_scores: Dict[str, float],
                                            market_regime: str) -> Dict[str, float]:
        """应用市场状态的避免调整"""
        # 市场状态调整因子
        regime_factors = {
            'trending_stable': 0.8,      # 稳定市场，降低避免倾向
            'normal_market': 1.0,        # 正常市场，不调整
            'high_rotation': 1.2,        # 高轮动，增加避免倾向
            'short_term_stress': 1.4,    # 短期压力，显著增加避免倾向
            'regime_transition': 1.3,    # 状态转换，增加避免倾向
            'divergent_market': 1.5      # 分化市场，最大避免倾向
        }

        adjustment_factor = regime_factors.get(market_regime, 1.0)

        adjusted_scores = {}
        for sector, score in avoid_scores.items():
            adjusted_score = score * adjustment_factor
            adjusted_scores[sector] = min(1.0, adjusted_score)

        return adjusted_scores

    def _classify_avoid_severity(self, adjusted_scores: Dict[str, float]) -> Tuple[List[str], Dict[str, str]]:
        """分级避免建议"""
        avoid_list = []
        severity_levels = {}

        for sector, score in adjusted_scores.items():
            if score >= self.avoid_severity_levels['critical']['threshold']:
                avoid_list.append(sector)
                severity_levels[sector] = 'critical'
            elif score >= self.avoid_severity_levels['high']['threshold']:
                avoid_list.append(sector)
                severity_levels[sector] = 'high'
            elif score >= self.avoid_severity_levels['medium']['threshold']:
                avoid_list.append(sector)
                severity_levels[sector] = 'medium'
            elif score >= self.avoid_severity_levels['low']['threshold']:
                severity_levels[sector] = 'low'  # 不加入避免清单，但标记关注

        return avoid_list, severity_levels

    def _generate_avoid_reasons(self, avoid_list: List[str],
                              breadth_metrics: Dict[str, Any],
                              adjusted_scores: Dict[str, float]) -> Dict[str, Dict]:
        """生成避免原因"""
        avoid_reasons = {}

        for sector in avoid_list:
            if sector not in breadth_metrics:
                continue

            metrics = breadth_metrics[sector]
            reasons = {
                'primary_issues': [],
                'secondary_issues': [],
                'severity_score': adjusted_scores.get(sector, 0),
                'detailed_analysis': {}
            }

            # 分析主要问题
            if hasattr(metrics, 'internal_health') and metrics.internal_health < 40:
                reasons['primary_issues'].append(f"内部健康度低({metrics.internal_health:.1f})")
                reasons['detailed_analysis']['health'] = {
                    'score': metrics.internal_health,
                    'status': 'poor',
                    'impact': 'high'
                }

            if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence and metrics.momentum_coherence < 0.3:
                reasons['primary_issues'].append(f"动量一致性差({metrics.momentum_coherence:.2f})")
                reasons['detailed_analysis']['coherence'] = {
                    'score': metrics.momentum_coherence,
                    'status': 'poor',
                    'impact': 'high'
                }

            # 分析次要问题
            if hasattr(metrics, 'participation_rate') and metrics.participation_rate < 0.5:
                reasons['secondary_issues'].append(f"参与率低({metrics.participation_rate:.1%})")
                reasons['detailed_analysis']['participation'] = {
                    'score': metrics.participation_rate,
                    'status': 'low',
                    'impact': 'medium'
                }

            if hasattr(metrics, 'price_breadth_divergence') and metrics.price_breadth_divergence:
                if metrics.price_breadth_divergence.get('type') == 'negative':
                    reasons['secondary_issues'].append("负背离信号")
                    reasons['detailed_analysis']['divergence'] = {
                        'type': 'negative',
                        'severity': metrics.price_breadth_divergence.get('severity', 0),
                        'impact': 'medium'
                    }

            avoid_reasons[sector] = reasons

        return avoid_reasons

    def _generate_improvement_conditions(self, avoid_list: List[str],
                                       breadth_metrics: Dict[str, Any],
                                       avoid_reasons: Dict[str, Dict]) -> Dict[str, List[str]]:
        """生成改善条件"""
        improvement_conditions = {}

        for sector in avoid_list:
            conditions = []

            if sector in avoid_reasons:
                reasons = avoid_reasons[sector]

                # 基于主要问题生成改善条件
                if 'health' in reasons['detailed_analysis']:
                    conditions.append("内部健康度回升至50以上")

                if 'coherence' in reasons['detailed_analysis']:
                    conditions.append("动量一致性改善至0.4以上")

                if 'participation' in reasons['detailed_analysis']:
                    conditions.append("市场参与率提升至60%以上")

                if 'divergence' in reasons['detailed_analysis']:
                    conditions.append("负背离信号消失或转为正背离")

                # 添加通用改善条件
                conditions.append("连续3个交易日表现优于大盘")
                conditions.append("成交量放大且价格突破关键阻力位")

            improvement_conditions[sector] = conditions

        return improvement_conditions

    def _generate_reassessment_triggers(self, avoid_list: List[str],
                                      avoid_reasons: Dict[str, Dict],
                                      market_regime: str) -> Dict[str, List[str]]:
        """生成重新评估触发条件"""
        reassessment_triggers = {}

        for sector in avoid_list:
            triggers = []

            # 基于严重度的触发条件
            if sector in avoid_reasons:
                severity = avoid_reasons[sector]['severity_score']

                if severity > 0.8:
                    triggers.append("等待市场环境根本性改善")
                    triggers.append("板块基本面出现重大利好")
                elif severity > 0.6:
                    triggers.append("技术指标出现明显改善信号")
                    triggers.append("相对强度指标转正")
                else:
                    triggers.append("短期技术反弹确认")
                    triggers.append("成交量异常放大")

            # 基于市场状态的触发条件
            if market_regime in ['regime_transition', 'divergent_market']:
                triggers.append("市场状态转为稳定")

            triggers.append("每周定期重新评估")

            reassessment_triggers[sector] = triggers

        return reassessment_triggers

    def _find_alternative_opportunities(self, avoid_list: List[str],
                                      breadth_metrics: Dict[str, Any],
                                      rotation_metrics: Any) -> List[str]:
        """寻找替代机会"""
        alternatives = []

        # 计算各板块的机会分数
        opportunity_scores = {}

        for sector, metrics in breadth_metrics.items():
            if sector in avoid_list:
                continue  # 跳过避免清单中的板块

            score = 0.0

            # 健康度加分
            if hasattr(metrics, 'internal_health'):
                score += metrics.internal_health / 100 * 0.4

            # 一致性加分
            if hasattr(metrics, 'momentum_coherence') and metrics.momentum_coherence:
                score += metrics.momentum_coherence * 0.3

            # 参与率加分
            if hasattr(metrics, 'participation_rate'):
                score += metrics.participation_rate * 0.2

            # 正背离加分
            if hasattr(metrics, 'price_breadth_divergence') and metrics.price_breadth_divergence:
                if metrics.price_breadth_divergence.get('type') == 'positive':
                    score += metrics.price_breadth_divergence.get('severity', 0) * 0.1

            opportunity_scores[sector] = score

        # 选择前5个机会
        sorted_opportunities = sorted(opportunity_scores.items(), key=lambda x: x[1], reverse=True)
        alternatives = [sector for sector, score in sorted_opportunities[:5] if score > 0.6]

        return alternatives

    # 辅助方法
    def _calculate_signal_consistency(self, timeframe_signals: Dict) -> float:
        """计算信号一致性"""
        if len(timeframe_signals) < 2:
            return 1.0

        strengths = [signal['strength'] for signal in timeframe_signals.values()]
        consistency = 1 - np.std(strengths)
        return max(0.1, min(1.0, consistency))

    def _signal_to_decision(self, signal_strength: float) -> str:
        """将信号强度转换为决策"""
        if signal_strength >= self.decision_thresholds['strong_buy']:
            return 'strong_buy'
        elif signal_strength >= self.decision_thresholds['buy']:
            return 'buy'
        elif signal_strength >= self.decision_thresholds['hold']:
            return 'hold'
        elif signal_strength >= self.decision_thresholds['sell']:
            return 'sell'
        else:
            return 'strong_sell'

    def _generate_risk_mitigation(self, risk_factors: List[str], decision: str) -> List[str]:
        """生成风险缓解措施"""
        mitigation = []

        if '极端决策增加风险' in risk_factors:
            mitigation.append("分批执行，避免一次性大幅调整")

        if '市场状态不稳定' in risk_factors:
            mitigation.append("密切监控市场状态变化，准备调整策略")

        if '时间框架信号分歧较大' in risk_factors:
            mitigation.append("等待更多时间框架信号一致后再行动")

        # 基于决策类型的通用缓解措施
        if decision in ['strong_buy', 'buy']:
            mitigation.append("设置止损位，控制下行风险")
        elif decision in ['strong_sell', 'sell']:
            mitigation.append("保留部分仓位，防止错失反弹机会")

        return mitigation
