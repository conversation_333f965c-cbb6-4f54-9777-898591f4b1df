#!/usr/bin/env python3
"""
导出价格广度背离数据到CSV
执行SQL查询并将结果保存为CSV文件

作者：Financial Master
日期：2025-07-24
"""

import os
import pandas as pd
import pymysql
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def export_divergence_data_to_csv():
    """
    导出价格广度背离数据到CSV文件
    """
    try:
        print("🔍 开始导出价格广度背离数据...")
        
        # 数据库连接配置
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
            charset='utf8mb4'
        )
        
        # 执行SQL查询
        sql_query = """
        SELECT *
        FROM market_breadth_metrics_gics
        WHERE divergence_type IS NOT NULL
          AND divergence_type <> 'none'
        ORDER BY recorded_at DESC, market
        """
        
        print("📈 执行SQL查询...")
        df = pd.read_sql(sql_query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 未找到价格广度背离数据")
            print("💡 提示：这可能是因为：")
            print("   1. 1%阈值较高，很少触发背离信号")
            print("   2. 数据库中还没有背离记录")
            print("   3. 需要先运行hist_data.py生成历史数据")
            return
        
        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'market_breadth_divergence_{timestamp}.csv'
        
        # 导出到CSV
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"✅ 导出成功！")
        print(f"📁 文件名: {filename}")
        print(f"📊 记录数: {len(df)}")
        
        # 显示统计信息
        print(f"\n=== 背离数据统计 ===")
        print(f"日期范围: {df['recorded_at'].min()} 到 {df['recorded_at'].max()}")
        print(f"涉及市场: {df['market'].nunique()} 个")
        
        # 背离类型统计
        divergence_stats = df['divergence_type'].value_counts()
        print(f"\n📊 背离类型分布:")
        for div_type, count in divergence_stats.items():
            percentage = count / len(df) * 100
            print(f"  {div_type}: {count} 次 ({percentage:.1f}%)")
        
        # 背离严重程度统计
        avg_severity = df['divergence_severity'].mean()
        max_severity = df['divergence_severity'].max()
        min_severity = df['divergence_severity'].min()
        print(f"\n📈 背离严重程度:")
        print(f"  平均: {avg_severity:.4f}")
        print(f"  最大: {max_severity:.4f}")
        print(f"  最小: {min_severity:.4f}")
        
        # 市场分布
        market_stats = df['market'].value_counts()
        print(f"\n🏛️ 市场分布 (前10):")
        for market, count in market_stats.head(10).items():
            percentage = count / len(df) * 100
            print(f"  {market}: {count} 次 ({percentage:.1f}%)")
        
        # 显示前几条记录作为预览
        print(f"\n📋 数据预览 (前5条):")
        preview_columns = ['recorded_at', 'market', 'divergence_type', 'divergence_severity', 
                          'advances', 'declines', 'equal_weighted_return']
        print(df[preview_columns].head().to_string(index=False))
        
        print(f"\n💾 CSV文件已保存: {filename}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()


def export_all_breadth_data_to_csv():
    """
    导出所有市场广度数据到CSV文件（包含新增指标）- 全部数据无限制
    """
    try:
        print("🔍 开始导出所有市场广度数据（全部数据，无条数限制）...")
        
        # 数据库连接配置
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
            charset='utf8mb4'
        )
        
        # 执行SQL查询（导出全部数据）
        sql_query = """
        SELECT *
        FROM market_breadth_metrics_gics
        ORDER BY recorded_at DESC, market
        """
        
        print("📈 执行SQL查询（全部数据，可能需要较长时间）...")
        df = pd.read_sql(sql_query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 未找到市场广度数据")
            return
        
        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'market_breadth_full_data_{timestamp}.csv'
        
        # 导出到CSV
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"✅ 导出成功！")
        print(f"📁 文件名: {filename}")
        print(f"📊 记录数: {len(df)} (全部数据)")
        print(f"📅 日期范围: {df['recorded_at'].min()} 到 {df['recorded_at'].max()}")
        print(f"🏛️ 涉及市场: {df['market'].nunique()} 个")
        
        # 数据量提醒
        if len(df) > 10000:
            print(f"⚠️ 数据量较大: {len(df)} 条记录")
            file_size_mb = os.path.getsize(filename) / (1024 * 1024)
            print(f"📊 文件大小: {file_size_mb:.2f} MB")
        
        # 检查新增指标
        new_indicators = ['purity', 'internal_health', 'momentum_coherence', 'divergence_type', 'divergence_severity']
        missing_indicators = [col for col in new_indicators if col not in df.columns]
        
        if missing_indicators:
            print(f"⚠️ 缺少新增指标: {missing_indicators}")
            print("💡 提示: 可能需要先运行更新后的hist_data.py来生成包含新增指标的数据")
        else:
            print(f"✅ 包含所有新增指标: {new_indicators}")
        
        print(f"\n💾 CSV文件已保存: {filename}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("=== 市场广度数据导出工具 ===")
    print("1. 导出价格广度背离数据")
    print("2. 导出所有市场广度数据 (全部数据)")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择导出选项 (0-2): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                export_divergence_data_to_csv()
                break
            elif choice == '2':
                export_all_breadth_data_to_csv()
                break
            else:
                print("❌ 无效选择，请输入 0-2")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 输入错误: {e}")


if __name__ == '__main__':
    main() 