import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
from hist_data import get_companies, get_price
import pandas as pd

load_dotenv()

def debug_data():
    # 获取少量公司进行测试
    markets = ['SP500']
    companies = get_companies(markets)[:5]  # 只取前5个公司
    print(f"Testing with companies: {companies}")
    
    # 下载数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=10)
    
    company_price_data = get_price(
        companies, 
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d')
    )
    
    print(f"Downloaded data for {len(company_price_data)} companies")
    
    # 检查第一个公司的数据结构
    if company_price_data:
        first_company = list(company_price_data.keys())[0]
        first_data = company_price_data[first_company]
        print(f"\nFirst company: {first_company}")
        print(f"Data shape: {first_data.shape}")
        print(f"Columns: {list(first_data.columns)}")
        print(f"Index type: {type(first_data.index)}")
        print(f"Index sample: {first_data.index[:3]}")
        print(f"Data sample:")
        print(first_data.head())
        
        # 测试日期筛选
        target_date = '2025-07-18'
        print(f"\nTesting date filtering for {target_date}")
        first_data['date'] = pd.to_datetime(first_data.index)
        filtered_df = first_data[first_data['date'] <= target_date]
        print(f"Filtered data shape: {filtered_df.shape}")
        print(f"Filtered data sample:")
        print(filtered_df.tail())

if __name__ == '__main__':
    debug_data()