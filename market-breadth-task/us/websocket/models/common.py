from enum import Enum


class Feed(Enum):
    Delayed = "delayed.polygon.io"
    RealTime = "192.168.1.66:8001"
    # RealTimeFundFlow = "192.168.1.67:8000"
    Nasdaq = "nasdaqfeed.polygon.io"
    PolyFeed = "polyfeed.polygon.io"
    PolyFeedPlus = "polyfeedplus.polygon.io"
    StarterFeed = "starterfeed.polygon.io"
    RealTimePrice = "192.168.1.103:30000"
    RealTimePrice1Min = "192.168.1.103:30001"
    RealTimeNew = "192.168.1.84:30808"
    # RealTimePrice = "192.168.1.66:8765"


class Market(Enum):
    Stocks = "intraday/price/stock/"
    F1minuteUnzero = "fundflow/stock/1minute_unzero/"
    F1minuteZero = "fundflow/stock/1minute_zero/"
    F1minuteNbbo1s = "fundflow/stock/1minute_nbbo1s/"
    F5minuteUnzero = "fundflow/stock/5minute_unzero/"
    F5minuteZero = "fundflow/stock/5minute_zero/"
    F5minuteNbbo1s = "fundflow/stock/5minute_nbbo1s/"
    F15minuteUnzero = "fundflow/stock/15minute_unzero/"
    F15minuteZero = "fundflow/stock/15minute_zero/"
    F15minuteNbbo1s = "fundflow/stock/15minute_nbbo1s/"
    F1hourNbbo1s = "fundflow/stock/1hour_nbbo1s/"
    F1dayNbbo1s = "fundflow/stock/1day_nbbo1s/"
    F1hourZero = "fundflow/stock/1hour_zero/"
    F1dayZero = "fundflow/stock/1day_zero/"
    F1hourUnzero = "fundflow/stock/1hour_unzero/"
    F1dayUnzero = "fundflow/stock/1day_unzero/"
    Options = "options"
    Forex = "forex/stock/1min/"
    Trade = "trade/stock/"
    Crypto = "crypto"
    StocksOriginal = "intraday/price/stock/original/"
    Stocks1Min = "intraday/price/stock/"
    Price = 'price/stock/'
    Price1Min = 'price/stock/1min/'
    Indices = 'indices/stock/1min/'


class EventType(Enum):
    EquityAgg = "A"
    EquityAggMin = "AM"
    CryptoAgg = "XA"
    ForexAgg = "CA"
    EquityTrade = "T"
    CryptoTrade = "XT"
    EquityQuote = "Q"
    ForexQuote = "C"
    CryptoQuote = "XQ"
    Imbalances = "NOI"
    LimitUpLimitDown = "LULD"
    CryptoL2 = "XL2"
    Value = "V"
