import datetime

from .settings import settings
from clickhouse_driver import Client
from queue import Queue, Empty


class CKConnectionPool:
    def __init__(self, maxsize=5, **kwargs):
        self._maxsize = maxsize
        self._pool = Queue(maxsize=maxsize)
        self._client_kwargs = kwargs

        for _ in range(maxsize):
            self._pool.put(self._create_client())

    def _create_client(self):
        return Client(**self._client_kwargs)

    def acquire(self):
        try:
            return self._pool.get(timeout=3)
        except Empty:
            raise RuntimeError("连接池已耗尽，无法获取连接")

    def release(self, client):
        try:
            self._pool.put(client, timeout=3)
        except:
            # 忽略放回失败
            pass

    def execute(self, sql, params=None):
        client = None
        try:
            client = self.acquire()
            result = client.execute(sql, params)
            self.release(client)
            return result
        except Exception as e:
            # 如果连接出问题，则丢弃当前连接并补充一个新的连接
            if client:
                try:
                    client.disconnect()
                except:
                    pass
                self._pool.put(self._create_client())  # 补一个新的进去
            raise e  # 抛出异常让上层处理


class ClickHouseService:
    def __init__(self):
        self.pool = CKConnectionPool(
            host=settings.CLICKHOUSE_HOST,
            port=settings.CLICKHOUSE_PORT,
            user=settings.CLICKHOUSE_USER,
            password=settings.CLICKHOUSE_PASSWORD,
            database=settings.CLICKHOUSE_DB,
            maxsize=settings.CLICKHOUSE_POOL_MAX,
        )

    def get_minute_aggregation(self, freq, tk, vhcid):
        """使用连接池获取分钟级聚合数据"""
        try:
            sql = f"""
                SELECT
                    {settings.SQL_FREQ.get(freq)}(dt) AS t,
                    tk,
                    argMin(o, dt) AS open_first,
                    max(h) AS high_max,
                    min(l) AS low_min,
                    argMax(c, dt) AS close_last,
                    sum(v) AS sum_volume
                FROM ws_price 
                WHERE tk='{tk}' and  dt >= today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE AND dt <= today() + INTERVAL 16 HOUR
                GROUP BY t, tk
                order by t, tk;
                """
            # sql = settings.FREQUENCY_CONFIG.get(freq).get('sql')
            data_list = [{
                    'vhcid': vhcid,
                    'tk': row[1],
                    'o': float(row[2]),
                    'h': float(row[3]),
                    'l': float(row[4]),
                    'c': float(row[5]),
                    'v': float(row[6]),
                    'dt': row[0].strftime('%Y-%m-%dT%H:%M:%S')}
                for row in self.pool.execute(sql)
                ]
            print(f'{datetime.datetime.utcnow()} load ck {freq} data {len(data_list)} {sql}')
            return data_list
        except Exception as e:
            print(f"{datetime.datetime.utcnow()} select data error {e}")
            return []


if __name__ == '__main__':
    test = ClickHouseService()
    data = test