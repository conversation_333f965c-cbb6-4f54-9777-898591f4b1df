from .client_redis import RobustRedisSubscriber
from typing import Callable
from .. import RESTClient


class NewsSub:

    def __init__(self, username, password):
        self.clent = RESTClient(username=username, password=password)

    def sub_news(
            self,
            tk_list: list,
            on_realtime_data: Callable[[dict, str], None] = None
    ):
        """
        订阅新闻数据
        tk: 股票代码列表
        - `on_realtime_data`: 实时接收到 Redis 消息时调用
        """
        if on_realtime_data:
            class CustomSubscriber(RobustRedisSubscriber):
                def on_message(self_inne, data, channel):
                    on_realtime_data(data, channel)
            subscriber = CustomSubscriber()
        else:
            subscriber = RobustRedisSubscriber()
        # subscriber = RobustRedisSubscriber()
        subscriber.subscribe([f"news_{tk}" for tk in tk_list])  # 自定义频道
        subscriber.listen()


if __name__ == '__main__':
    NewsSub().sub_news(['AAPL'], print)