import datetime
import threading
import redis
import json
import time
from typing import List
from .settings import settings


class RobustRedisSubscriber:
    def __init__(self, on_message_callback=None):
        self.redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            decode_responses=True
        )
        self.pubsub = self.redis.pubsub()
        self._active = False
        self._listen_thread = None
        self._on_message_callback = on_message_callback

    def set_callback(self, callback):
        """动态设置回调"""
        self._on_message_callback = callback

    def subscribe(self, channels: List[str]):
        """确保订阅成功"""
        try:
            if not channels:
                raise ValueError("频道列表不能为空")

            # 清除旧订阅
            if self.pubsub.channels:
                self.pubsub.unsubscribe()

            # 新订阅
            self.pubsub.subscribe(*channels)

            # 验证订阅
            start_time = time.time()
            while not self.pubsub.channels and time.time() - start_time < 3:
                time.sleep(0.1)

            if not self.pubsub.channels:
                raise ConnectionError("订阅频道失败")

            print(f"成功订阅频道: {list(self.pubsub.channels.keys())}")
            self._active = True

        except redis.ConnectionError as e:
            print(f"Redis连接失败: {str(e)}")
            self._reconnect()

    def listen(self):
        """增强的消息监听循环"""
        if not self._active:
            raise RuntimeError("未订阅任何频道")

        try:
            while self._active:
                message = self.pubsub.get_message(
                    ignore_subscribe_messages=True,
                    timeout=1.0  # 非阻塞模式
                )
                if message:
                    self._process_message(message)
                time.sleep(0.01)  # 防止CPU跑满

        except Exception as e:
            print(f"监听中断: {str(e)}")
        finally:
            self.unsubscribe()

    def _process_message(self, message):
        """处理原始消息"""
        try:
            data = json.loads(message['data'])
            print(f"\n[频道 {message['channel']}] 收到数据:")
            if self._on_message_callback:
                self._on_message_callback(data, message['channel'])
            else:
                self.on_message(data, message['channel'])
        except json.JSONDecodeError:
            print(f"无效JSON数据: {message['data']}")
        except Exception as e:
            print(f"处理消息错误: {str(e)}")

    def on_message(self, data: dict, channel: str):
        """供子类重写的消息处理方法"""
        print(f"{datetime.datetime.utcnow()} {channel} {data}")

    def _reconnect(self):
        """重新连接"""
        try:
            self.redis.close()
            self.redis.ping()  # 测试连接
            self._active = True
            print("Redis重新连接成功")
        except Exception as e:
            print(f"重连失败: {str(e)}")
            self._active = False

    def unsubscribe(self):
        """安全取消订阅"""
        if self._active:
            self.pubsub.unsubscribe()
            self._active = False
        self.redis.close()