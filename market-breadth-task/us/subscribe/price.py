import datetime
import time
from .clickhouse_service import ClickHouseService
from .settings import settings
import pytz
import pandas_market_calendars as mcal
from .. import RESTClient
from .client_redis import RobustRedisSubscriber
from typing import Callable


class DataLoader:
    def __init__(self, username, password, market='stock'):
        self.clickhouse = ClickHouseService()
        self.clent = RESTClient(username=username, password=password)
        self.market = market

    @staticmethod
    def is_trading_time_with_holiday_check() -> bool:
        from datetime import datetime, time
        eastern = pytz.timezone("US/Eastern")
        cn_calendar = mcal.get_calendar('NYSE')
        dt_ = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(eastern).replace(tzinfo=None)
        schedule = cn_calendar.schedule(start_date=dt_.date(), end_date=dt_.date())
        if schedule.empty:
            return False  # 非交易日

        trading_start = time(9, 30)
        trading_end = time(16, 0)
        return trading_start <= dt_.time() <= trading_end

    def get_all_price_data(
            self,
            vhcid,
            timespan,
            multiplier,
            from_,
            to=datetime.datetime.utcnow().strftime('%Y-%m-%d'),
            on_history_data: Callable[[dict], None] = None,
            on_realtime_data: Callable[[dict, str], None] = None
    ):
        """
        拉取历史数据并启动实时订阅监听
        - `on_history_data`: 逐条返回历史数据
        - `on_realtime_data`: 实时接收到 Redis 消息时调用
        """
        freq = f"{multiplier}{settings.CATEGORY_MAP_DB.get(timespan)}"
        # print(vhcid, timespan, multiplier, from_, to)
        histroy_data = self.clent.list_price(vhcid=vhcid, timespan=timespan, multiplier=multiplier, from_=from_, to=to,params={'price_field': 'vhcid,tk,o,h,l,c,v,dt'}).get('data')
        if not histroy_data:
            raise Exception(f'{vhcid} not history data')
        tk = histroy_data[-1].get('tk')
        ck_data = None
        if self.is_trading_time_with_holiday_check():
            ck_data = self.clickhouse.get_minute_aggregation(freq, tk, vhcid)
        if ck_data:
            histroy_data.extend(ck_data)
        # 历史数据回调处理
        if on_history_data:
            for item in histroy_data:
                on_history_data(item)
        else:
            print(histroy_data)
        if on_realtime_data:
            class CustomSubscriber(RobustRedisSubscriber):
                def on_message(self_inner, data, channel):
                    on_realtime_data(data, channel)
            subscriber = CustomSubscriber()
        else:
            subscriber = RobustRedisSubscriber()
        # subscriber = RobustRedisSubscriber()
        subscriber.subscribe([f"{self.market}_{freq}_{tk}"])  # 自定义频道
        subscriber.listen()


