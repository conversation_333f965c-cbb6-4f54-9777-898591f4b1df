import os


class Settings:
    # ClickHouse 配置
    CLICKHOUSE_HOST = os.getenv("CLICKHOUSE_HOST", "************")
    CLICKHOUSE_PORT = int(os.getenv("CLICKHOUSE_PORT", "9000"))
    CLICKHOUSE_USER = os.getenv("CLICKHOUSE_USER", "default")
    CLICKHOUSE_PASSWORD = os.getenv("CLICKHOUSE_PASSWORD", "Dx784vs7")
    CLICKHOUSE_DB = os.getenv("CLICKHOUSE_DB", "test")
    # ClickHouse 连接池配置
    CLICKHOUSE_POOL_MIN = int(os.getenv("CLICKHOUSE_POOL_MIN", "2"))
    CLICKHOUSE_POOL_MAX = int(os.getenv("CLICKHOUSE_POOL_MAX", "5"))

    # Redis 配置
    REDIS_HOST = os.getenv("REDIS_HOST", "************")
    REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "redis01")
    # REDIS_CHANNEL = os.getenv("REDIS_CHANNEL", "stock_minute_data")
    REDIS_DB = int(os.getenv("REDIS_DB", "10"))
    # Redis 连接池配置
    REDIS_POOL_MIN = int(os.getenv("REDIS_POOL_MIN", "2"))
    REDIS_POOL_MAX = int(os.getenv("REDIS_POOL_MAX", "10"))

    # 查询配置
    QUERY_INTERVAL = int(os.getenv("QUERY_INTERVAL", "5"))

    # 频率匹配
    CATEGORY_MAP_DB = {
        "minute": "min",
        "hour": "h",
        "day": "d",
        "week": "w"
    }
    SQL_FREQ = {
        '1min': 'toStartOfMinute',
        '5min': 'toStartOfFiveMinutes',
        '15min': 'toStartOfFifteenMinutes',
        '1h': 'toStartOfHour',
        '1d': 'toStartOfDay'
    }
    FREQUENCY_CONFIG = {
        '1min': {
            'channel': 'stock_1min',
            'interval': 10,
            'sql': """
    SELECT
        toStartOfMinute(dt) AS t,
        tk,
        argMin(o, dt) AS open_first,
        max(h) AS high_max,
        min(l) AS low_min,
        argMax(c, dt) AS close_last,
        sum(v) AS sum_volume
    FROM ws_price 
    WHERE dt >= today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE AND dt <= today() + INTERVAL 16 HOUR
    GROUP BY t, tk
    order by t, tk;
            """
        },
        '5min': {
            'channel': 'stock_5min',
            'interval': 10,
            'sql': """
                        SELECT
                            toStartOfFiveMinutes(dt) AS t,
                            tk,
                            argMin(o, dt) AS open_first,
                            max(h) AS high_max,
                            min(l) AS low_min,
                            argMax(c, dt) AS close_last,
                            sum(v) AS sum_volume
                        FROM ws_price
                        WHERE dt > (SELECT toStartOfFiveMinutes(max(dt)) FROM ws_price)
                        GROUP BY t, tk
            """
        },
        '15min': {
            'channel': 'stock_15min',
            'interval': 10,
            'sql': """
    SELECT
        toStartOfFifteenMinutes(dt) AS t,
        tk,
        argMin(o, dt) AS open_first,
        max(h) AS high_max,
        min(l) AS low_min,
        argMax(c, dt) AS close_last,
        sum(v) AS sum_volume
    FROM ws_price 
    WHERE dt >= today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE AND dt <= today() + INTERVAL 16 HOUR
    GROUP BY t, tk
    order by t, tk;
            """
        },
        '1h': {
            'channel': 'stock_1h',
            'interval': 10,
            'sql': """
    SELECT
        toStartOfHour(dt) AS t,
        tk,
        argMin(o, dt) AS open_first,
        max(h) AS high_max,
        min(l) AS low_min,
        argMax(c, dt) AS close_last,
        sum(v) AS sum_volume
    FROM ws_price 
    WHERE dt >= today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE AND dt <= today() + INTERVAL 16 HOUR
    GROUP BY t, tk
    order by t, tk;
        """
        },
        '1d': {
            'channel': 'stock_1d',
            'interval': 10,
            'sql': """

                SELECT
                    toStartOfDay(dt) AS t,
                    tk,
                    argMin(o, dt) AS open_first,
                    max(h) AS high_max,
                    min(l) AS low_min,
                    argMax(c, dt) AS close_last,
                    sum(v) AS sum_volume
                FROM ws_price 
                WHERE dt >= today() + INTERVAL 9 HOUR + INTERVAL 30 MINUTE AND dt <= today() + INTERVAL 16 HOUR
                GROUP BY t, tk
                order by t, tk;
                """
        },
    }


settings = Settings()
