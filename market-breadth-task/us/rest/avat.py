from typing import Optional, Any, Dict, Union
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine


class AvatClient(BaseClient):
    @confine
    def interday_avat(
            self,
            vhcid: int,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        List price bars for a vhcid over a given date range in custom time window sizes.
        :param vhcid: The size of the vhcid_b.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: dict of avats
        """
        url_path = f"us/us_garch/inner/avat/d5/interday/{vhcid}/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.interday_avat, locals()),
            deserializer=Price.from_dict,
            options=options,
        )
