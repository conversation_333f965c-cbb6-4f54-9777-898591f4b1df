from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine


class GarchClient(BaseClient):
    @confine
    def interday_garch(
            self,
            vhcid_a: int,
            vhcid_b: int,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        List price bars for a vhcid over a given date range in custom time window sizes.
        :param vhcid_a: The size of the vhcid_a.
        :param vhcid_b: The size of the vhcid_b.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        """
        url_path = f"us/us_garch/inner/garch/day/1/interday/{vhcid_a}/{vhcid_b}/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.interday_garch, locals()),
            deserializer=Price.from_dict,
            options=options,
        )
