from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine_price


class RtFundFlowClient(BaseClient):
    @confine_price
    def intraday_fundflow(
            self,
            vhcid: int,
            category: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param vhcid: The size of the vhcid.
        :param category: The res of the raw data
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of fundflows
        """
        url_path = f"us/rt/inner/fundflow_{category}/{timespan}/{multiplier}/vhcid/{vhcid}/intraday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.intraday_fundflow, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )


class FundFlowClient(BaseClient):
    @confine_price
    def interday_fundflow(
            self,
            vhcid: int,
            category: str,  # category --> unzero/zero/nbbo1s
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param vhcid: The size of the vhcid.
        :param category: The res of the raw data
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: dict fundflows data
        """
        url_path = f"us/us_fundflow/inner/flow_{category}/{timespan}/{multiplier}/vhcid/{vhcid}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.interday_fundflow, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )