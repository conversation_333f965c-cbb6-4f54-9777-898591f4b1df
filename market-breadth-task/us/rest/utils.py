from datetime import datetime, timedelta
import pytz
from concurrent.futures import ThreadPoolExecutor, as_completed


def split_dates(start_date, end_date, is_hour_limit=False, limit_range_hour=6):
    if is_hour_limit:
        # 如果总共的天数小于等于
        if end_date - start_date <= timedelta(hours=limit_range_hour):
            return [(start_date, end_date)]
        # 否则将时间段拆分为多个子时间段
        intervals = []
        interval_start = start_date
        while True:
            interval_end = interval_start + timedelta(hours=limit_range_hour) - timedelta(microseconds=1)
            if interval_end > end_date:
                interval_end = end_date
            intervals.append((interval_start, interval_end))
            if interval_end == end_date:
                break
            interval_start = interval_end + timedelta(microseconds=1)
    else:
        # 计算总共的天数
        delta = end_date - start_date
        total_days = delta.days

        # 如果总共的天数小于等于一年直接返回原始时间段
        if total_days <= 365:
            return [(start_date, end_date)]

        # 否则将时间段拆分为多个子时间段
        intervals = []
        interval_start = start_date
        while True:
            interval_end = interval_start + timedelta(days=365)
            if interval_end > end_date:
                interval_end = end_date
            intervals.append((interval_start, interval_end))
            if interval_end == end_date:
                break
            interval_start = interval_end + timedelta(days=1)
    return intervals


# def confine_price(func):
#     def inner(*args, **kwargs):
#         try:
#             start = datetime.fromtimestamp(int(kwargs.get('from_')))
#             end = datetime.fromtimestamp(int(kwargs.get('to')))
#         except Exception as e:
#             try:
#                 start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
#                 end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d') + timedelta(days=1) - timedelta(microseconds=1)
#             except ValueError:
#                 raise Exception('time format error')
#         if start > end:
#             raise Exception('Check the start and end times')
#         res = {'data': []}
#         data_list = split_dates(start, end)
#         for start, end in data_list:
#             kwargs['from_'] = int(start.replace(tzinfo=pytz.UTC).timestamp())
#             kwargs['to'] = int(end.replace(tzinfo=pytz.UTC).timestamp())
#             res_dev = func(*args, **kwargs)
#             if res_dev:
#                 res['data'].extend(res_dev.get('data'))
#         return res
#     return inner

def confine_price(func):
    def inner(*args, **kwargs):
        try:
            # 处理时间戳参数
            start = datetime.fromtimestamp(int(kwargs.get('from_')))
            end = datetime.fromtimestamp(int(kwargs.get('to')))
        except Exception as e:
            try:
                # 如果是日期格式，则解析为 datetime 对象
                start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
                end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d') + timedelta(days=1) - timedelta(microseconds=1)
            except ValueError:
                raise Exception('time format error')

        if start > end:
            raise Exception('Check the start and end times')

        res = {'data': []}

        # 将时间段拆分成多个日期段
        data_list = split_dates(start, end)

        # 用于存储并行执行的所有 Future 对象
        futures = []

        # 使用 ThreadPoolExecutor 执行并行请求
        with ThreadPoolExecutor(len(data_list)) as executor:
            for start, end in data_list:
                kwargs['from_'] = int(start.replace(tzinfo=pytz.UTC).timestamp())
                kwargs['to'] = int(end.replace(tzinfo=pytz.UTC).timestamp())

                # 提交任务到线程池
                futures.append(executor.submit(func, *args, **kwargs))

            # 处理所有的并行结果
            for future in as_completed(futures):
                res_dev = future.result()  # 获取返回结果
                if res_dev:
                    res['data'].extend(res_dev.get('data'))  # 合并数据

        return res

    return inner


def confine(func):
    def inner(*args, **kwargs):
        try:
            start = datetime.fromtimestamp(int(kwargs.get('from_')))
            end = datetime.fromtimestamp(int(kwargs.get('to')))
        except Exception as e:
            try:
                start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
                end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d')
            except ValueError:
                raise Exception('time format error')
        if start > end:
            raise Exception('Check the start and end times')
        res = {'data': []}
        data_list = split_dates(start, end)
        for start, end in data_list:
            kwargs['from_'] = start.strftime("%Y-%m-%d")
            kwargs['to'] = end.strftime("%Y-%m-%d")
            res_dev = func(*args, **kwargs)
            if res_dev:
                res['data'].extend(res_dev['data'])
        return res
    return inner


def confine_trade(func):
    def inner(*args, **kwargs):
        try:
            start = datetime.fromtimestamp(int(kwargs.get('from_')))
            end = datetime.fromtimestamp(int(kwargs.get('to')))
        except Exception as e:
            try:
                start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
                end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d') + timedelta(days=1) - timedelta(microseconds=1)
            except ValueError:
                raise Exception('time format error')
        if start > end:
            raise Exception('Check the start and end times')
        res = {'data': []}
        data_list = split_dates(start, end, is_hour_limit=True)
        for start, end in data_list:
            kwargs['from_'] = int(start.replace(tzinfo=pytz.UTC).timestamp())
            kwargs['to'] = int(end.replace(tzinfo=pytz.UTC).timestamp())
            res_dev = func(*args, **kwargs)
            if res_dev:
                res['data'].extend(res_dev.get('data'))
        return res
    return inner


def custom_indice_confine(func):
    def inner(*args, **kwargs):
        try:
            start = datetime.fromtimestamp(int(kwargs.get('from_')))
            end = datetime.fromtimestamp(int(kwargs.get('to')))
        except Exception as e:
            try:
                start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
                end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d')
            except ValueError:
                raise Exception('time format error')
        if start > end:
            raise Exception('Check the start and end times')
        res = {'meta': {}, 'data': []}
        data_list = split_dates(start, end)
        for start, end in data_list:
            kwargs['from_'] = start.strftime("%Y-%m-%d")
            kwargs['to'] = end.strftime("%Y-%m-%d")
            res_dev = func(*args, **kwargs)
            if res_dev:
                res['data'].extend(res_dev['data'])
                if not res['meta']:
                    res['meta'].update(res_dev['meta'])
        return res
    return inner


def new_confine(func):
    def inner(*args, **kwargs):
        try:
            # 处理时间戳参数
            start = datetime.fromtimestamp(int(kwargs.get('from_')))
            end = datetime.fromtimestamp(int(kwargs.get('to')))
        except Exception as e:
            try:
                # 如果是日期格式，则解析为 datetime 对象
                start = datetime.strptime(kwargs.get('from_'), '%Y-%m-%d')
                end = datetime.strptime(kwargs.get('to'), '%Y-%m-%d')
            except ValueError:
                raise Exception('time format error')

        if start > end:
            raise Exception('Check the start and end times')

        res = {'data': []}

        # 将时间段拆分成多个日期段
        data_list = split_dates(start, end)

        # 用于存储并行执行的所有 Future 对象
        futures = []

        # 使用 ThreadPoolExecutor 执行并行请求
        with ThreadPoolExecutor(len(data_list)) as executor:
            for start, end in data_list:
                kwargs['from_'] = int(start.replace(tzinfo=pytz.UTC).timestamp())
                kwargs['to'] = int(end.replace(tzinfo=pytz.UTC).timestamp())

                # 提交任务到线程池
                futures.append(executor.submit(func, *args, **kwargs))

            # 处理所有的并行结果
            for future in as_completed(futures):
                res_dev = future.result()  # 获取返回结果
                if res_dev:
                    res['data'].extend(res_dev.get('data'))  # 合并数据

        return res

    return inner