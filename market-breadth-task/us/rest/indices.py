from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import H<PERSON>PResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price, IntradayPrice, InterdayIndices
from .models.request import RequestOptionBuilder
from .utils import confine, confine_price, custom_indice_confine
from typing import Literal


class IndicesInterClient(BaseClient):
    @confine
    def get_interday_indices(
            self,
            tk: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[InterdayIndices], HTTPResponse]:
        """
        List indices bars for a tk over a given date range in custom time window sizes.
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        http://************:8000/us/inter_indices/inner/indices/minute/1/tk/I:AVT5EQTB/interday/2023-05-01/2023-05-31/
        """
        url_path = f"us/us_indices/inner/indices/{timespan}/{multiplier}/tk/{tk}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_interday_indices, locals()),
            deserializer=InterdayIndices.from_dict,
            options=options,
        )


class IndicesIntraClient(BaseClient):
    @confine
    def get_intraday_indices(
            self,
            # vhcid: int,
            tk: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[IntradayPrice], HTTPResponse]:
        """
        List price bars for a tk over a given date range in custom time window sizes.
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        http://************:8000/rt/inner/price/day/1/tk/I:NQDXCHCHF/intraday/2023-06-06/2023-06-06
        """
        # url_path = f"us/rt/inner/price/{timespan}/{multiplier}/vhcid/{vhcid}/intraday/{from_}/{to}"
        url_path = f"us/rt/inner/indices/{timespan}/{multiplier}/tk/{tk}/intraday/{from_}/{to}"
        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_intraday_indices, locals()),
            deserializer=IntradayPrice.from_dict,
            options=options,
        )


class TickerListClient(BaseClient):
    def list_ticker(
            self,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        url_path = f"us/us_indices/inner/indices/tickers/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.list_ticker, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )

    def ticker_detail(
            self,
            ticker: str,
            dt: str or int,
            timespan: Literal['1d', '15min'],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        url_path = f"us/us_indices/inner/indices/ticker/detail/{ticker}/{dt}/{timespan}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.ticker_detail, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )

    def index_tickers(
            self,
            ticker: str,
            dt: str or int,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        url_path = f"us/us_indices/inner/indices/ticker/component/{ticker}/{dt}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.list_ticker, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )


class CustomIndicesClient(BaseClient):
    @custom_indice_confine
    def get_interday_custom_indices(
            self,
            tk: Union[str, int],
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[InterdayIndices], HTTPResponse]:
        """
        List indices bars for a tk over a given date range in custom time window sizes.
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        """
        url_path = f"us/us_indices/inner/custom_indices/{timespan}/{multiplier}/tk/{tk}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_interday_custom_indices, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class RtCustomIndicesClient(BaseClient):
    @custom_indice_confine
    def get_intraday_custom_indices(
            self,
            tk: Union[str, int],
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[InterdayIndices], HTTPResponse]:
        """
        List indices bars for a tk over a given date range in custom time window sizes.
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        """
        url_path = f"us/rt/inner/custom_indices/{timespan}/{multiplier}/tk/{tk}/intraday/{from_}/{to}/"
        #*************:8000/us/rt/inner/custom_indices/minute/1/tk/:tk/intraday/:start/:end/
        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_intraday_custom_indices, locals()),
            deserializer=Price.from_dict,
            options=options,
        )