import os
# 请求数据ip和端口
BASE_URL = os.getenv('BASE_URL', 'http://************:30802/')
# BASE_URL = 'http://127.0.0.1:30802/'
# 登录接口
TEST_LOGIN_URL = os.getenv('TEST_LOGIN_URL', 'http://*************:8002/api/users/login/')
PRO_LOGIN_URL = os.getenv('PRO_LOGIN_URL', 'http://************/api/users/login/')

# 测试ip和端口
# TEST_URL = 'http://************:8000/'
TEST_URL = os.getenv('TEST_URL', 'http://************:30802/')

# 生产
PRO_URL = os.getenv('PRO_URL', 'http://************/')

