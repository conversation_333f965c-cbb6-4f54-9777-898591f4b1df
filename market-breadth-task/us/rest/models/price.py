from typing import Optional
from ...modelclass import modelclass


@modelclass
class Price:
    "Contains price data for a given ticker vhcid over a given date range in a custom time window size."
    data:Optional[list] = None

    @staticmethod
    def from_dict(d):
        return Price(d.get("data", None))


@modelclass
class IntradayPrice:
    "Contains intraday price change data for a given ticker vhcid over a given date range in a custom time window size."
    v: Optional[float] = None
    vw: Optional[float] = None
    o: Optional[float] = None
    c: Optional[float] = None
    h: Optional[float] = None
    l: Optional[float] = None
    dt: Optional[str] = None
    n: Optional[float] = None
    tk: Optional[str] = None
    vhcid: Optional[str] = None

    @staticmethod
    def from_dict(d):
        return IntradayPrice(d)


@modelclass
class InterdayIndices:
    "Contains interday indices data for a given ticker vhcid over a given date range in a custom time window size."
    tk: Optional[str] = None
    c: Optional[float] = None
    h: Optional[float] = None
    l: Optional[float] = None
    o: Optional[float] = None
    t: Optional[str] = None

    @staticmethod
    def from_dict(d):
        return InterdayIndices(d)
