from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine_price


class ForexClient(BaseClient):
    @confine_price
    def list_forex(
            self,
            tk: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param tk: The size of the ticker.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of forexs
        """
        url_path = f"us/us_forex/inner/forex/{timespan}/{multiplier}/tk/{tk}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.list_forex, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )


class RtForexClient(BaseClient):
    @confine_price
    def intraday_forex(
            self,
            tk: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of forexs
        """
        url_path = f"us/rt/inner/forex/{timespan}/{multiplier}/tk/{tk}/intraday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.intraday_forex, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )