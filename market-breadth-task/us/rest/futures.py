from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import H<PERSON>PResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import new_confine


class FuturesClient(BaseClient):
    @new_confine
    def get_interday_futures(
            self,
            tk: str,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        List price bars for a vhcid over a given date range in custom time window sizes.
        :param tk: The size of the tk.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: dict data
        """
        url_path = f"us/us_futures/inner/futures/{timespan}/{multiplier}/tk/{tk}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_interday_futures, locals()),
            deserializer=Price.from_dict,
            options=options,
        )