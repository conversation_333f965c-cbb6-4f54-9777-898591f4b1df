from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine_price, confine_trade


class TradeClient(BaseClient):
    @confine_trade
    def list_trade(
            self,
            vhcid: int,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param vhcid: The size of the vhcid.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: dict of trades
        """
        url_path = f"us/us_fundflow/inner/trade/vhcid/{vhcid}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.list_trade, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )

    def hdf5_trade(
            self,
            vhcid: int,
            date_: str,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param vhcid: The size of the vhcid.
        :param date_: get trade datetime
        :return: dict of trades
        """
        url_path = f"us/hdf5/inner/trade/vhcid/{vhcid}/interday/{date_}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.hdf5_trade, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )


class RtTradeClient(BaseClient):
    @confine_price
    def intraday_trade(
            self,
            vhcid: int,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        :param vhcid: The size of the vhcid.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: dict of trades
        """
        url_path = f"us/rt/inner/trade/vhcid/{vhcid}/intraday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.intraday_trade, locals()),
            deserializer=Price.from_dict,  # 未使用序列化
            options=options,
        )