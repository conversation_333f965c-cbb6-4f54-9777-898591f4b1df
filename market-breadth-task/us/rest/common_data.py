from typing import Optional, Any, Dict, Union
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price
from .models.request import RequestOptionBuilder
from .utils import confine


class InvestmentClient(BaseClient):
    @confine
    def get_investment(
            self,
            name: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        获取investment数据
        """
        url_path = f"us/us_common/inner/investment/interday/{name}/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_investment, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class PeersClient(BaseClient):
    def get_peers(
            self,
            name: str,
            grouping: str,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        获取peers数据
        """
        url_path = f"us/us_common/inner/peers/interday/{name}/{grouping}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_peers, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class SentimentClient(BaseClient):
    def get_sentiment(
            self,
            symbol: str,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        获取sentiment数据
        """
        url_path = f"us/us_common/inner/sentiment/interday/{symbol}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_sentiment, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class StatementsClient(BaseClient):
    @confine
    def get_statements(
            self,
            vhcid: int,
            statement: str,
            freq: str,
            from_: str,
            to: str,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        获取statements数据
        """
        url_path = f"us/us_common/inner/statements/interday/{vhcid}/{statement}/{freq}/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_statements, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class ReportedClient(BaseClient):
    @confine
    def get_reported(
            self,
            vhcid: int,
            freq: str,
            from_: str,
            to: str,
            form: str = '10-K',
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[HTTPResponse, dict]:
        """
        获取reported数据
        """
        url_path = f"us/us_common/inner/reported/interday/{vhcid}/{freq}/{form}/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_reported, locals()),
            deserializer=Price.from_dict,
            options=options,
        )