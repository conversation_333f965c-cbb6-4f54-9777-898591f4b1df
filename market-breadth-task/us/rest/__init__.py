from .price import PriceClient, IntradayPrice, RtPriceClient
from .forex import ForexClient, RtForexClient
from .fundflow import RtFundFlowClient, FundFlowClient
from .indices import IndicesInterClient, IndicesIntraClient, TickerListClient, CustomIndicesClient, RtCustomIndicesClient
from .trade import TradeClient, RtTradeClient
from .garch import GarchClient
from .avat import AvatClient
from . import settings
from typing import Optional, Any, Literal
from .common_data import InvestmentClient, ReportedClient, PeersClient, SentimentClient, StatementsClient
import os, base64
from .options import OptionsClient
from .futures import FuturesClient

ENV = os.getenv('ENV')


def singleton(cls):
    instances = {}

    def getinstance(*args, **kw):
        if cls not in instances:
            instances[cls] = cls(*args, **kw)
        return instances[cls]
    return getinstance


@singleton
class RESTClient(
    PriceClient,
    ForexClient,
    RtFundFlowClient,
    RtForexClient,
    TradeClient,
    RtTradeClient,
    FundFlowClient,
    GarchClient,
    AvatClient,
    InvestmentClient,
    ReportedClient,
    PeersClient,
    SentimentClient,
    StatementsClient,
    TickerListClient,
    CustomIndicesClient,
    RtCustomIndicesClient,
    OptionsClient,
    FuturesClient
):
    def __init__(
            self,
            username,
            password,
            environment: Literal['test', 'pro'] = None,  # 仅允许固定值
            connect_timeout: float = 10.0,
            read_timeout: float = 10.0,
            num_pools: int = 10,
            retries: int = 3,
            verbose: bool = False,
            custom_json: Optional[Any] = None,
    ):
        password = base64.b64encode(password.encode())
        environment = ENV or environment or 'test'
        print(f"debug use environment {environment}")
        if environment == "test":
            self.base = settings.TEST_URL
            self.login_url = settings.TEST_LOGIN_URL
        elif environment == "pro":
            self.base = settings.PRO_URL
            self.login_url = settings.PRO_LOGIN_URL

        super().__init__(
            username=username,
            password=password,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            num_pools=num_pools,
            retries=retries,
            base=self.base,
            login_url=self.login_url,
            verbose=verbose,
            custom_json=custom_json,
        )


@singleton
class RtRESTClient(
    RtPriceClient,
    IndicesInterClient,
    IndicesIntraClient,
):
    def __init__(
            self,
            username,
            password,
            environment: Literal['test', 'pro'] = None,  # 仅允许固定值
            connect_timeout: float = 10.0,
            read_timeout: float = 10.0,
            num_pools: int = 10,
            retries: int = 3,
            verbose: bool = False,
            custom_json: Optional[Any] = None,
    ):
        environment = ENV or environment or 'test'
        password = base64.b64encode(password.encode())
        print(f"debug use environment {environment}")
        if environment == "test":
            self.base = settings.TEST_URL
            self.login_url = settings.TEST_LOGIN_URL
        elif environment == "pro":
            self.base = settings.PRO_URL
            self.login_url = settings.PRO_LOGIN_URL

        super().__init__(
            username=username,
            password=password,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            num_pools=num_pools,
            retries=retries,
            base=self.base,
            login_url=self.login_url,
            verbose=verbose,
            custom_json=custom_json,
        )
