from typing import Optional, Any, Dict, Union, Iterator
from urllib3 import HTTPResponse
from datetime import datetime, date
from .base import BaseClient
from .models import Price, IntradayPrice, InterdayIndices
from .models.request import RequestOptionBuilder
from .utils import confine, confine_price


class PriceClient(BaseClient):
    @confine_price
    def list_price(
            self,
            vhcid: int,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        List price bars for a vhcid over a given date range in custom time window sizes.
        :param vhcid: The size of the vhcid.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        """
        url_path = f"us/us_price/inner/price/{timespan}/{multiplier}/vhcid/{vhcid}/interday/{from_}/{to}/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.list_price, locals()),
            deserializer=Price.from_dict,
            options=options,
        )

    def get_us_active_ticker(
            self,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        get active ticker
        :return: Iterator of tickers
        """
        url_path = f"us/us_price/inner/price/get_active_ticker/"

        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_us_active_ticker, locals()),
            deserializer=Price.from_dict,
            options=options,
        )

    def get_ticker_vhcid(
            self,
            ticker: str,
            start: str,
            end: str,
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[Price], HTTPResponse]:
        """
        get active ticker
        :return: Iterator of tickers
        """
        url_path = f"us/us_price/inner/price/get_ticker_vhcid/"

        params = {'ticker': ticker, 'start': start, 'end': end} if not params else params.update({'ticker': ticker, 'start': start, 'end': end})
        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_ticker_vhcid, locals()),
            deserializer=Price.from_dict,
            options=options,
        )


class RtPriceClient(BaseClient):
    @confine
    def get_intraday_price(
            self,
            vhcid: int,
            multiplier: int,
            timespan: str,
            from_: Union[str, int, datetime, date],
            to: Union[str, int, datetime, date],
            params: Optional[Dict[str, Any]] = None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Union[Iterator[IntradayPrice], HTTPResponse]:
        """
        List price bars for a vhcid over a given date range in custom time window sizes.
        :param vhcid: The size of the vhcid.
        :param multiplier: The size of the timespan multiplier.
        :param timespan: The size of the time window.
        :param from_: The start of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :param to: The end of the aggregate time window as YYYY-MM-DD, a date, Unix MS Timestamp, or a datetime.
        :return: Iterator of prices
        http://************:8000/rt/inner/price/day/1/vhcid/100070/intraday/2023-06-06/2023-06-06
        """
        url_path = f"us/rt/inner/price/{timespan}/{multiplier}/vhcid/{vhcid}/intraday/{from_}/{to}"
        return self._paginate(
            path=url_path,
            params=self._get_params(self.get_intraday_price, locals()),
            deserializer=IntradayPrice.from_dict,
            options=options,
        )
