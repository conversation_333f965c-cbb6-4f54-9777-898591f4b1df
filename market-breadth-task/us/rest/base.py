import certifi
import json
import urllib3
import inspect
from enum import Enum
from typing import Optional, Any, Dict
from datetime import datetime
import pkg_resources  # part of setuptools
from .models.request import RequestOptionBuilder
from ..logging import get_logger
import logging
from ..exceptions import AuthError, BadResponse, NoResultsError
import requests

logger = get_logger("RESTClient")
version = "unknown"
try:
    version = pkg_resources.require("")[0].version
except:
    pass


class BaseClient:
    def __init__(
            self,
            username: Optional[str],
            password: Optional[str],
            connect_timeout: float,
            read_timeout: float,
            num_pools: int,
            retries: int,
            base: str,
            login_url:str,
            verbose: bool,
            custom_json: Optional[Any] = None,
    ):
        if username is None:
            raise AuthError(
                f"Must specify env var DATA_SOURCE_USERNAME or pass username in constructor"
            )
        if password is None:
            raise AuthError(
                f"Must specify env var DATA_SOURCE_PASSWORD or pass password in constructor"
            )
        self.USERNAME = username
        self.PASSWORD = password
        self.BASE = base
        self.login_url = login_url
        self.token = ''
        if not self.token:
            self.get_token()

        self.headers = {
            "Authorization": "Bearer " + self.token,
            "Accept-Encoding": "gzip",
            "User-Agent": f"DataSource.io PythonClient/{version}",
        }

        self.client = urllib3.PoolManager(
            num_pools=num_pools,
            headers=self.headers,  # default headers sent with each request.
            ca_certs=certifi.where(),
            cert_reqs="CERT_REQUIRED",
        )

        self.timeout = urllib3.Timeout(connect=connect_timeout, read=read_timeout)
        self.retries = retries
        if verbose:
            logger.setLevel(logging.DEBUG)
        if custom_json:
            self.json = custom_json
        else:
            self.json = json

    def get_token(self):
        response = requests.post(self.login_url,
                                 data={'username': self.USERNAME, 'password': self.PASSWORD})
        if response.json().get('code') == 200:
            self.token = response.json().get('data')['access']
            return self.token
        else:
            raise Exception('Failed to authenticate user')

    def _decode(self, resp):
        return self.json.loads(resp.data.decode("utf-8"))

    def _get(
            self,
            path: str,
            params: Optional[dict] = None,
            result_key: Optional[str] = None,
            deserializer=None,
            options: Optional[RequestOptionBuilder] = None,
    ) -> Any:
        option = options if options is not None else RequestOptionBuilder()
        resp = self.client.request(
            "GET",
            self.BASE + path,
            fields=params,
            retries=self.retries,
            headers=self._concat_headers(option.headers),
        )

        if resp.status != 200:
            raise BadResponse(resp.data.decode("utf-8"))

        obj = self._decode(resp)
        if obj.get('code') != 200:
            raise Exception(f"err {obj.get('data')}")

        if result_key:
            if result_key not in obj:
                raise NoResultsError(
                    f'Expected key "{result_key}" in response {obj}.'
                    + "Make sure you have sufficient permissions and your request parameters are valid."
                    + f"This is the url that returned no results: {resp.geturl()}"
                )
            obj = obj[result_key]

        return obj

    @staticmethod
    def time_mult(timestamp_res: str) -> int:
        if timestamp_res == "nanos":
            return 1000000000
        elif timestamp_res == "micros":
            return 1000000
        elif timestamp_res == "millis":
            return 1000

        return 1

    def _get_params(
            self, fn, caller_locals: Dict[str, Any], datetime_res: str = "nanos"
    ):
        params = caller_locals["params"]
        if params is None:
            params = {}
        # https://docs.python.org/3.8/library/inspect.html#inspect.Signature
        for argname, v in inspect.signature(fn).parameters.items():
            # https://docs.python.org/3.8/library/inspect.html#inspect.Parameter
            if argname in ["params", "raw"]:
                continue
            if v.default != v.empty:
                # timestamp_lt -> timestamp.lt
                val = caller_locals.get(argname, v.default)
                if isinstance(val, Enum):
                    val = val.value
                elif isinstance(val, bool):
                    val = str(val).lower()
                elif isinstance(val, datetime):
                    val = int(val.timestamp() * self.time_mult(datetime_res))
                if val is not None:
                    for ext in ["lt", "lte", "gt", "gte", "any_of"]:
                        if argname.endswith(f"_{ext}"):
                            # lop off ext, then rebuild argname with ext,
                            # using ., and not _ (removesuffix would work)
                            # but that is python 3.9+
                            argname = argname[: -len(f"_{ext}")] + f".{ext}"
                    if argname.endswith("any_of"):
                        val = ",".join(val)
                    params[argname] = val

        return params

    def _concat_headers(self, headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        if headers is None:
            return self.headers
        return {**headers, **self.headers}

    def _paginate_iter(
            self,
            path: str,
            params: dict,
            deserializer,
            result_key: str = "data",
            options: Optional[RequestOptionBuilder] = None,
    ):
        resp = self._get(
            path=path,
            params=params,
            deserializer=deserializer,
            result_key=result_key,
            options=options,
        )
        return resp

    def _paginate(
            self,
            path: str,
            params: dict,
            deserializer,
            result_key: str = "data",
            options: Optional[RequestOptionBuilder] = None,
    ):

        return self._paginate_iter(
            path=path,
            params=params,
            deserializer=deserializer,
            result_key=result_key,
            options=options,
        )
