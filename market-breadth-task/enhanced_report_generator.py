"""
增强版多时间框架报告生成器
基于优化文档要求，添加决策路径和风险场景分析，提升LLM交互能力
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json
import logging

from llm_interaction_enhancer import LLMInteractionEnhancer, LLMContext

logger = logging.getLogger(__name__)


@dataclass
class DecisionPath:
    """决策路径记录"""
    step_name: str
    input_data: Dict
    analysis_method: str
    result: Any
    reasoning: str
    confidence: float
    timestamp: datetime


@dataclass
class RiskScenario:
    """风险场景"""
    scenario_name: str
    probability: float
    impact_level: str
    description: str
    triggers: List[str]
    mitigation_strategies: List[str]
    monitoring_indicators: List[str]


class EnhancedReportGenerator:
    """增强版报告生成器 - 集成决策透明度和LLM交互"""
    
    def __init__(self):
        """初始化增强版报告生成器"""
        self.llm_enhancer = LLMInteractionEnhancer()
        self.decision_paths = []
        self.risk_scenarios = []
        
    def generate_enhanced_report(self, analysis_data: Dict, 
                               decision_tracker: Optional[object] = None) -> Dict:
        """
        生成增强版综合报告
        
        Args:
            analysis_data: 分析数据
            decision_tracker: 可选的决策追踪器
            
        Returns:
            Dict: 增强版报告
        """
        # 1. 生成基础报告结构
        base_report = self._generate_base_report(analysis_data)
        
        # 2. 添加决策路径章节
        decision_path_section = self._generate_decision_path_section(decision_tracker)
        
        # 3. 添加风险场景分析
        risk_scenario_section = self._generate_risk_scenario_section(analysis_data)
        
        # 4. 添加置信度区间说明
        confidence_section = self._generate_confidence_section(analysis_data)
        
        # 5. 生成LLM友好的上下文
        llm_context = self._generate_llm_context(analysis_data)
        
        # 6. 添加交互式问答预设
        qa_section = self._generate_interactive_qa_section(analysis_data, llm_context)
        
        # 7. 构建增强版报告
        enhanced_report = {
            **base_report,
            'decision_transparency': decision_path_section,
            'risk_scenario_analysis': risk_scenario_section,
            'confidence_analysis': confidence_section,
            'llm_interaction': {
                'context': llm_context.__dict__ if llm_context else {},
                'interactive_qa': qa_section,
                'prompt_template': self._generate_prompt_template(llm_context)
            },
            'enhancement_metadata': {
                'version': '2.0_enhanced',
                'enhancement_features': [
                    'decision_path_tracking',
                    'risk_scenario_analysis', 
                    'confidence_intervals',
                    'llm_interaction_ready'
                ],
                'generation_timestamp': datetime.now().isoformat()
            }
        }
        
        return enhanced_report
    
    def _generate_base_report(self, analysis_data: Dict) -> Dict:
        """生成基础报告结构"""
        return {
            'metadata': {
                'report_type': '增强版多时间框架分析',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'version': '2.0_enhanced',
                'data_explanation': '本报告集成决策透明度和风险场景分析，提供完整的决策路径记录'
            },
            'executive_summary': self._generate_executive_summary(analysis_data),
            'market_analysis': self._generate_market_analysis(analysis_data),
            'investment_recommendations': self._generate_investment_recommendations(analysis_data)
        }
    
    def _generate_decision_path_section(self, decision_tracker: Optional[object]) -> Dict:
        """生成决策路径章节"""
        section = {
            'title': '决策路径透明度分析',
            'description': '完整记录分析过程中的每个决策步骤，确保结果可解释和可回溯',
            'decision_steps': [],
            'key_decision_points': [],
            'confidence_evolution': [],
            'alternative_paths': []
        }
        
        if decision_tracker and hasattr(decision_tracker, 'get_full_decision_history'):
            # 获取决策历史
            decision_history = decision_tracker.get_full_decision_history()
            
            for decision_record in decision_history:
                decision_step = {
                    'decision_type': decision_record.get('type', 'unknown'),
                    'timestamp': decision_record.get('timestamp', ''),
                    'steps': [],
                    'overall_confidence': decision_record.get('overall_confidence', 0.5),
                    'duration': decision_record.get('duration', 0)
                }
                
                # 处理决策步骤
                for step in decision_record.get('steps', []):
                    step_info = {
                        'name': step.get('name', ''),
                        'reasoning': step.get('reasoning', ''),
                        'confidence': step.get('confidence', 0.5),
                        'result_summary': self._summarize_result(step.get('result', {}))
                    }
                    decision_step['steps'].append(step_info)
                
                section['decision_steps'].append(decision_step)
            
            # 提取关键决策点
            section['key_decision_points'] = self._extract_key_decision_points(decision_history)
        
        else:
            section['decision_steps'] = [{
                'note': '决策追踪器未提供，无法生成详细决策路径',
                'recommendation': '建议集成DecisionTracker以获得完整的决策透明度'
            }]
        
        return section
    
    def _generate_risk_scenario_section(self, analysis_data: Dict) -> Dict:
        """生成风险场景分析章节"""
        section = {
            'title': '风险场景分析',
            'description': '基于当前市场状态和历史模式，分析可能的风险场景及应对策略',
            'scenarios': [],
            'risk_matrix': {},
            'monitoring_framework': {},
            'contingency_plans': {}
        }
        
        # 基于分析数据生成风险场景
        scenarios = self._generate_risk_scenarios(analysis_data)
        section['scenarios'] = [scenario.__dict__ for scenario in scenarios]
        
        # 生成风险矩阵
        section['risk_matrix'] = self._generate_risk_matrix(scenarios)
        
        # 生成监控框架
        section['monitoring_framework'] = self._generate_monitoring_framework(scenarios)
        
        # 生成应急计划
        section['contingency_plans'] = self._generate_contingency_plans(scenarios)
        
        return section
    
    def _generate_confidence_section(self, analysis_data: Dict) -> Dict:
        """生成置信度区间说明章节"""
        section = {
            'title': '置信度区间分析',
            'description': '详细说明分析结果的可信度和不确定性范围',
            'overall_confidence': 0.5,
            'confidence_breakdown': {},
            'uncertainty_sources': [],
            'confidence_intervals': {},
            'reliability_assessment': {}
        }
        
        # 计算总体置信度
        health = analysis_data.get('internal_health', 50)
        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5
        sample_size = analysis_data.get('total_stocks', 0)
        
        # 置信度分解
        data_confidence = min(0.95, 0.5 + sample_size / 200)  # 基于样本量
        algorithm_confidence = 0.85  # 算法可靠性
        market_confidence = health / 100  # 基于市场健康度
        
        overall_confidence = (data_confidence * 0.3 + 
                            algorithm_confidence * 0.4 + 
                            market_confidence * 0.3)
        
        section['overall_confidence'] = round(overall_confidence, 3)
        section['confidence_breakdown'] = {
            'data_quality': round(data_confidence, 3),
            'algorithm_reliability': round(algorithm_confidence, 3),
            'market_conditions': round(market_confidence, 3)
        }
        
        # 不确定性来源
        uncertainty_sources = []
        if sample_size < 50:
            uncertainty_sources.append("样本量较小，统计显著性有限")
        if coherence < 0.3:
            uncertainty_sources.append("市场分化严重，预测难度增加")
        if health < 40:
            uncertainty_sources.append("市场环境恶劣，模型适用性降低")
        
        section['uncertainty_sources'] = uncertainty_sources
        
        # 置信度区间
        section['confidence_intervals'] = {
            'internal_health': {
                'point_estimate': health,
                'confidence_interval_95': [max(0, health - 10), min(100, health + 10)],
                'explanation': '基于历史波动性估算的95%置信区间'
            },
            'momentum_coherence': {
                'point_estimate': coherence,
                'confidence_interval_95': [max(0, coherence - 0.1), min(1, coherence + 0.1)],
                'explanation': '基于算法稳定性估算的95%置信区间'
            }
        }
        
        return section
    
    def _generate_llm_context(self, analysis_data: Dict) -> Optional[LLMContext]:
        """生成LLM交互上下文"""
        try:
            return self.llm_enhancer.enhance_breadth_analysis(analysis_data)
        except Exception as e:
            logger.warning(f"生成LLM上下文失败: {e}")
            return None
    
    def _generate_interactive_qa_section(self, analysis_data: Dict, 
                                       llm_context: Optional[LLMContext]) -> Dict:
        """生成交互式问答章节"""
        section = {
            'title': '交互式问答预设',
            'description': '预设常见问题和回答模板，便于LLM交互',
            'preset_questions': [],
            'dynamic_qa_generator': {},
            'context_aware_responses': {}
        }
        
        if llm_context:
            section['preset_questions'] = llm_context.interactive_qa_presets
            
            # 动态问答生成器
            section['dynamic_qa_generator'] = {
                'market_state_questions': self._generate_market_state_questions(analysis_data),
                'risk_questions': self._generate_risk_questions(analysis_data),
                'strategy_questions': self._generate_strategy_questions(analysis_data)
            }
            
            # 上下文感知回答
            section['context_aware_responses'] = {
                'response_templates': self._generate_response_templates(),
                'context_variables': self._extract_context_variables(analysis_data),
                'personalization_hints': self._generate_personalization_hints(analysis_data)
            }
        
        return section
    
    def _generate_prompt_template(self, llm_context: Optional[LLMContext]) -> str:
        """生成LLM提示词模板"""
        if llm_context:
            return self.llm_enhancer.generate_llm_prompt(llm_context)
        else:
            return "LLM上下文生成失败，无法提供提示词模板"

    def _generate_executive_summary(self, analysis_data: Dict) -> Dict:
        """生成执行摘要"""
        market = analysis_data.get('market', 'Unknown')
        health = analysis_data.get('internal_health', 50)
        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5

        return {
            'market_name': market,
            'overall_assessment': self._assess_overall_market(health, coherence),
            'key_metrics': {
                'internal_health': health,
                'momentum_coherence': coherence,
                'risk_level': self._assess_risk_level(health, coherence)
            },
            'primary_recommendation': self._generate_primary_recommendation(health, coherence),
            'confidence_level': self._calculate_summary_confidence(analysis_data)
        }

    def _generate_market_analysis(self, analysis_data: Dict) -> Dict:
        """生成市场分析"""
        return {
            'current_state': self._analyze_current_state(analysis_data),
            'trend_analysis': self._analyze_trends(analysis_data),
            'structural_analysis': self._analyze_structure(analysis_data),
            'comparative_analysis': self._analyze_comparative_metrics(analysis_data)
        }

    def _generate_investment_recommendations(self, analysis_data: Dict) -> Dict:
        """生成投资建议"""
        health = analysis_data.get('internal_health', 50)
        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5

        return {
            'primary_strategy': self._determine_primary_strategy(health, coherence),
            'position_sizing': self._recommend_position_sizing(health, coherence),
            'entry_timing': self._recommend_entry_timing(analysis_data),
            'exit_strategy': self._recommend_exit_strategy(analysis_data),
            'risk_management': self._recommend_risk_management(analysis_data)
        }

    def _generate_risk_scenarios(self, analysis_data: Dict) -> List[RiskScenario]:
        """生成风险场景"""
        scenarios = []
        health = analysis_data.get('internal_health', 50)
        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5

        # 场景1：市场急剧恶化
        if health < 60:
            scenarios.append(RiskScenario(
                scenario_name="市场急剧恶化",
                probability=0.25,
                impact_level="高",
                description="板块健康度进一步下降，引发连锁反应",
                triggers=["健康度跌破30", "一致性降至0.2以下", "外部冲击事件"],
                mitigation_strategies=["立即减仓", "转向防御性资产", "设置止损"],
                monitoring_indicators=["日内健康度变化", "成交量异常", "波动率飙升"]
            ))

        # 场景2：分化加剧
        if coherence < 0.4:
            scenarios.append(RiskScenario(
                scenario_name="板块分化加剧",
                probability=0.35,
                impact_level="中",
                description="板块内部分化持续恶化，个股选择难度增加",
                triggers=["一致性持续下降", "龙头股表现分化", "行业轮动加速"],
                mitigation_strategies=["精选个股", "分散投资", "增加研究深度"],
                monitoring_indicators=["个股相关性", "行业轮动速度", "资金流向"]
            ))

        # 场景3：假突破
        scenarios.append(RiskScenario(
            scenario_name="技术面假突破",
            probability=0.20,
            impact_level="中",
            description="技术指标出现假信号，导致错误决策",
            triggers=["成交量不配合", "背离信号出现", "外部环境变化"],
            mitigation_strategies=["等待确认", "分批建仓", "设置验证条件"],
            monitoring_indicators=["成交量确认", "多时间框架一致性", "基本面支撑"]
        ))

        return scenarios

    def _generate_risk_matrix(self, scenarios: List[RiskScenario]) -> Dict:
        """生成风险矩阵"""
        matrix = {
            'high_probability_high_impact': [],
            'high_probability_low_impact': [],
            'low_probability_high_impact': [],
            'low_probability_low_impact': []
        }

        for scenario in scenarios:
            prob_high = scenario.probability > 0.3
            impact_high = scenario.impact_level in ['高', 'high']

            if prob_high and impact_high:
                matrix['high_probability_high_impact'].append(scenario.scenario_name)
            elif prob_high and not impact_high:
                matrix['high_probability_low_impact'].append(scenario.scenario_name)
            elif not prob_high and impact_high:
                matrix['low_probability_high_impact'].append(scenario.scenario_name)
            else:
                matrix['low_probability_low_impact'].append(scenario.scenario_name)

        return matrix

    def _generate_monitoring_framework(self, scenarios: List[RiskScenario]) -> Dict:
        """生成监控框架"""
        all_indicators = []
        for scenario in scenarios:
            all_indicators.extend(scenario.monitoring_indicators)

        # 去重并分类
        unique_indicators = list(set(all_indicators))

        return {
            'primary_indicators': unique_indicators[:5],  # 前5个最重要的
            'secondary_indicators': unique_indicators[5:],
            'monitoring_frequency': {
                'real_time': ['成交量异常', '波动率飙升'],
                'daily': ['健康度变化', '一致性变化'],
                'weekly': ['资金流向', '行业轮动']
            },
            'alert_thresholds': {
                '健康度': '低于35或单日下降超过5点',
                '一致性': '低于0.25或单日下降超过0.1',
                '成交量': '异常放大超过平均值2倍'
            }
        }

    def _generate_contingency_plans(self, scenarios: List[RiskScenario]) -> Dict:
        """生成应急计划"""
        plans = {}

        for scenario in scenarios:
            plans[scenario.scenario_name] = {
                'immediate_actions': scenario.mitigation_strategies[:2],
                'medium_term_actions': scenario.mitigation_strategies[2:],
                'decision_criteria': f"当{scenario.triggers[0]}时触发",
                'review_frequency': '每日' if scenario.impact_level == '高' else '每周'
            }

        return plans

    def _summarize_result(self, result: Any) -> str:
        """总结结果"""
        if isinstance(result, dict):
            if len(result) <= 3:
                return str(result)
            else:
                return f"Dict包含{len(result)}个键值对"
        elif isinstance(result, (list, tuple)):
            return f"List包含{len(result)}个元素"
        else:
            return str(result)[:100]  # 限制长度

    def _extract_key_decision_points(self, decision_history: List[Dict]) -> List[Dict]:
        """提取关键决策点"""
        key_points = []

        for decision in decision_history:
            if decision.get('overall_confidence', 0) > 0.8:
                key_points.append({
                    'type': decision.get('type', ''),
                    'confidence': decision.get('overall_confidence', 0),
                    'reasoning': '高置信度决策点',
                    'timestamp': decision.get('timestamp', '')
                })

        return key_points

    def _assess_overall_market(self, health: float, coherence: float) -> str:
        """评估整体市场"""
        if health > 70 and coherence > 0.7:
            return "强势且一致"
        elif health < 40 or coherence < 0.3:
            return "疲弱或分化"
        else:
            return "中性分化"

    def _assess_risk_level(self, health: float, coherence: float) -> str:
        """评估风险等级"""
        if health < 40 or coherence < 0.3:
            return "高风险"
        elif health > 70 and coherence > 0.7:
            return "低风险"
        else:
            return "中等风险"

    def _generate_primary_recommendation(self, health: float, coherence: float) -> str:
        """生成主要建议"""
        if health > 70 and coherence > 0.7:
            return "积极配置"
        elif health < 40 or coherence < 0.3:
            return "谨慎观望"
        else:
            return "精选个股"

    def _calculate_summary_confidence(self, analysis_data: Dict) -> float:
        """计算摘要置信度"""
        health = analysis_data.get('internal_health', 50)
        sample_size = analysis_data.get('total_stocks', 0)

        base_confidence = 0.5
        if health > 70:
            base_confidence += 0.2
        elif health < 40:
            base_confidence -= 0.2

        if sample_size > 100:
            base_confidence += 0.1
        elif sample_size < 50:
            base_confidence -= 0.1

        return round(max(0.1, min(0.9, base_confidence)), 2)

    def _analyze_current_state(self, analysis_data: Dict) -> Dict:
        """分析当前状态"""
        return {
            'health_assessment': f"内部健康度{analysis_data.get('internal_health', 50):.1f}分",
            'coherence_assessment': f"动量一致性{analysis_data.get('momentum_coherence', 0.5):.2f}",
            'participation_assessment': f"参与率{analysis_data.get('participation_rate', 0.5):.1%}",
            'overall_sentiment': self._determine_sentiment(analysis_data)
        }

    def _analyze_trends(self, analysis_data: Dict) -> Dict:
        """分析趋势"""
        return {
            'short_term_trend': '基于当前数据无法确定短期趋势',
            'medium_term_trend': '需要更多历史数据进行趋势分析',
            'trend_strength': '中等',
            'trend_reliability': 0.6
        }

    def _analyze_structure(self, analysis_data: Dict) -> Dict:
        """分析结构"""
        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5

        return {
            'market_structure': 'fragmented' if coherence < 0.4 else 'cohesive' if coherence > 0.7 else 'mixed',
            'leadership_quality': 'strong' if coherence > 0.7 else 'weak' if coherence < 0.4 else 'moderate',
            'breadth_quality': 'good' if analysis_data.get('internal_health', 50) > 60 else 'poor',
            'structural_integrity': coherence
        }

    def _analyze_comparative_metrics(self, analysis_data: Dict) -> Dict:
        """分析比较指标"""
        return {
            'vs_historical_average': '需要历史数据进行比较',
            'vs_sector_peers': '需要同行业数据进行比较',
            'relative_strength': '中等',
            'percentile_ranking': 50
        }

    def _determine_primary_strategy(self, health: float, coherence: float) -> str:
        """确定主要策略"""
        if health > 70 and coherence > 0.7:
            return "积极成长策略"
        elif health < 40 or coherence < 0.3:
            return "防御保守策略"
        else:
            return "平衡选择策略"

    def _recommend_position_sizing(self, health: float, coherence: float) -> str:
        """建议仓位大小"""
        if health > 70 and coherence > 0.7:
            return "标准仓位(60-80%)"
        elif health < 40 or coherence < 0.3:
            return "轻仓位(10-30%)"
        else:
            return "中等仓位(30-60%)"

    def _recommend_entry_timing(self, analysis_data: Dict) -> str:
        """建议入场时机"""
        health = analysis_data.get('internal_health', 50)

        if health > 70:
            return "可以考虑入场"
        elif health < 40:
            return "等待更好时机"
        else:
            return "分批入场"

    def _recommend_exit_strategy(self, analysis_data: Dict) -> str:
        """建议出场策略"""
        health = analysis_data.get('internal_health', 50)

        if health > 70:
            return "趋势跟踪止盈"
        elif health < 40:
            return "及时止损"
        else:
            return "灵活调整"

    def _recommend_risk_management(self, analysis_data: Dict) -> Dict:
        """建议风险管理"""
        health = analysis_data.get('internal_health', 50)

        return {
            'stop_loss': "5-10%" if health > 60 else "3-5%",
            'position_limit': "单一板块不超过20%" if health > 60 else "单一板块不超过10%",
            'diversification': "适度集中" if health > 60 else "高度分散",
            'monitoring_frequency': "每日" if health < 50 else "每周"
        }

    def _determine_sentiment(self, analysis_data: Dict) -> str:
        """确定情绪"""
        health = analysis_data.get('internal_health', 50)
        ad_ratio = analysis_data.get('ad_ratio', 1.0)

        if health > 60 and ad_ratio > 1.5:
            return "乐观"
        elif health < 40 or ad_ratio < 0.7:
            return "悲观"
        else:
            return "中性"

    def _generate_market_state_questions(self, analysis_data: Dict) -> List[str]:
        """生成市场状态问题"""
        market = analysis_data.get('market', 'Unknown')
        return [
            f"{market}板块现在处于什么状态？",
            "当前市场健康度如何解读？",
            "动量一致性说明了什么？",
            "这种市场状态通常持续多久？"
        ]

    def _generate_risk_questions(self, analysis_data: Dict) -> List[str]:
        """生成风险问题"""
        return [
            "主要风险因素有哪些？",
            "如何控制投资风险？",
            "什么情况下应该止损？",
            "风险和收益如何平衡？"
        ]

    def _generate_strategy_questions(self, analysis_data: Dict) -> List[str]:
        """生成策略问题"""
        return [
            "最佳的投资策略是什么？",
            "应该如何配置仓位？",
            "什么时候是最佳入场时机？",
            "如何制定出场策略？"
        ]

    def _generate_response_templates(self) -> Dict[str, str]:
        """生成回答模板"""
        return {
            'market_state': "基于当前分析，{market}板块{state_description}。主要特征包括{key_features}。",
            'risk_assessment': "主要风险包括{risk_factors}，建议{mitigation_strategies}。",
            'strategy_recommendation': "推荐采用{strategy_type}，具体操作为{specific_actions}。",
            'timing_advice': "考虑到{market_conditions}，建议{timing_recommendation}。"
        }

    def _extract_context_variables(self, analysis_data: Dict) -> Dict:
        """提取上下文变量"""
        return {
            'market': analysis_data.get('market', 'Unknown'),
            'health': analysis_data.get('internal_health', 50),
            'coherence': analysis_data.get('momentum_coherence', 0.5),
            'ad_ratio': analysis_data.get('ad_ratio', 1.0),
            'participation': analysis_data.get('participation_rate', 0.5),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _generate_personalization_hints(self, analysis_data: Dict) -> List[str]:
        """生成个性化提示"""
        hints = []

        health = analysis_data.get('internal_health', 50)
        if health > 70:
            hints.append("适合风险偏好较高的投资者")
        elif health < 40:
            hints.append("适合保守型投资者观望")

        coherence = analysis_data.get('momentum_coherence', 0.5) or 0.5
        if coherence < 0.4:
            hints.append("需要较强的个股选择能力")
        elif coherence > 0.7:
            hints.append("适合板块配置策略")

        return hints
