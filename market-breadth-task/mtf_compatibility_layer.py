"""
多时间框架分析器兼容层
将增强版实现转换为文档期望的输出格式，确保与现有系统完全兼容
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import numpy as np

try:
    from .enhanced_multi_timeframe_analyzer import (
        EnhancedMultiTimeframeAnalyzer,
        SignalStrengthDetails,
        UnifiedDecisionDetails,
        AvoidListDetails
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    import importlib.util

    # 获取当前文件目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 导入增强版多时间框架分析器
    analyzer_path = os.path.join(current_dir, 'enhanced_multi_timeframe_analyzer.py')
    spec = importlib.util.spec_from_file_location("enhanced_multi_timeframe_analyzer", analyzer_path)
    analyzer_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(analyzer_module)

    EnhancedMultiTimeframeAnalyzer = analyzer_module.EnhancedMultiTimeframeAnalyzer
    SignalStrengthDetails = analyzer_module.SignalStrengthDetails
    UnifiedDecisionDetails = analyzer_module.UnifiedDecisionDetails
    AvoidListDetails = analyzer_module.AvoidListDetails


@dataclass
class CompatibleUnifiedDecision:
    """兼容的统一决策格式"""
    suggested_position: float
    top_sectors: List[Dict]
    avoid_sectors: List[Dict]
    position_recommendation: Dict
    signal_strength: float
    confidence: float
    market_regime: str
    decision_rationale: str
    
    # 增强字段（可选）
    enhanced_details: Optional[UnifiedDecisionDetails] = None
    signal_details: Optional[SignalStrengthDetails] = None
    avoid_details: Optional[AvoidListDetails] = None


class MTFCompatibilityLayer:
    """多时间框架分析器兼容层"""
    
    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化兼容层
        
        Args:
            decision_tracker: 可选的决策追踪器
        """
        self.enhanced_analyzer = EnhancedMultiTimeframeAnalyzer(decision_tracker)
        self.tracker = decision_tracker
    
    def analyze_timeframes(self, timeframe_results: Dict,
                          dynamic_weights: Dict,
                          breadth_metrics: Dict[str, Any],
                          rotation_metrics: Any,
                          market_regime: str = 'normal_market') -> CompatibleUnifiedDecision:
        """
        执行多时间框架分析，返回兼容格式的结果
        
        Args:
            timeframe_results: 各时间框架结果
            dynamic_weights: 动态权重
            breadth_metrics: 广度指标字典
            rotation_metrics: 轮动指标
            market_regime: 市场状态
            
        Returns:
            CompatibleUnifiedDecision: 兼容格式的统一决策
        """
        if self.tracker:
            self.tracker.start_decision('mtf_compatibility_analysis', {
                'timeframes_count': len(timeframe_results),
                'sectors_count': len(breadth_metrics),
                'market_regime': market_regime
            })
        
        # 1. 计算信号强度
        signal_details = self.enhanced_analyzer.calculate_signal_strength_enhanced(
            rotation_metrics=rotation_metrics,
            breadth_metrics=breadth_metrics,
            timeframe='daily'  # 主要时间框架
        )
        
        # 2. 生成统一决策
        decision_details = self.enhanced_analyzer.generate_unified_decision_enhanced(
            timeframe_results=timeframe_results,
            dynamic_weights=dynamic_weights,
            market_regime=market_regime
        )
        
        # 3. 生成避免清单
        avoid_details = self.enhanced_analyzer.generate_avoid_list_enhanced(
            breadth_metrics=breadth_metrics,
            rotation_metrics=rotation_metrics,
            market_regime=market_regime
        )
        
        # 4. 转换为兼容格式
        compatible_decision = self._convert_to_compatible_format(
            signal_details, decision_details, avoid_details, 
            breadth_metrics, market_regime
        )
        
        # 5. 记录决策过程
        if self.tracker:
            self.tracker.complete_decision({
                'suggested_position': compatible_decision.suggested_position,
                'top_sectors_count': len(compatible_decision.top_sectors),
                'avoid_sectors_count': len(compatible_decision.avoid_sectors),
                'signal_strength': compatible_decision.signal_strength
            }, compatible_decision.confidence)
        
        return compatible_decision
    
    def _convert_to_compatible_format(self, signal_details: SignalStrengthDetails,
                                    decision_details: UnifiedDecisionDetails,
                                    avoid_details: AvoidListDetails,
                                    breadth_metrics: Dict[str, Any],
                                    market_regime: str) -> CompatibleUnifiedDecision:
        """转换为兼容格式"""
        
        # 1. 计算建议仓位
        suggested_position = self._calculate_suggested_position(
            signal_details, decision_details, market_regime
        )
        
        # 2. 生成推荐板块列表
        top_sectors = self._generate_top_sectors_list(
            breadth_metrics, avoid_details.avoid_list, signal_details
        )
        
        # 3. 生成避免板块列表
        avoid_sectors = self._generate_avoid_sectors_list(
            avoid_details, breadth_metrics
        )
        
        # 4. 生成仓位建议
        position_recommendation = self._generate_position_recommendation(
            suggested_position, decision_details, signal_details
        )
        
        return CompatibleUnifiedDecision(
            suggested_position=suggested_position,
            top_sectors=top_sectors,
            avoid_sectors=avoid_sectors,
            position_recommendation=position_recommendation,
            signal_strength=signal_details.overall_strength,
            confidence=decision_details.decision_confidence,
            market_regime=market_regime,
            decision_rationale=decision_details.decision_rationale,
            enhanced_details=decision_details,
            signal_details=signal_details,
            avoid_details=avoid_details
        )
    
    def _calculate_suggested_position(self, signal_details: SignalStrengthDetails,
                                    decision_details: UnifiedDecisionDetails,
                                    market_regime: str) -> float:
        """计算建议仓位百分比"""
        base_position = 50.0  # 基础仓位
        
        # 基于信号强度调整
        signal_adjustment = (signal_details.overall_strength - 0.5) * 60  # -30% to +30%
        
        # 基于决策类型调整
        decision_adjustments = {
            'strong_buy': 25,
            'buy': 15,
            'hold': 0,
            'sell': -15,
            'strong_sell': -25
        }
        
        decision_adjustment = decision_adjustments.get(decision_details.primary_decision, 0)
        
        # 基于市场状态调整
        regime_adjustments = {
            'trending_stable': 10,
            'normal_market': 0,
            'high_rotation': -5,
            'short_term_stress': -15,
            'regime_transition': -10,
            'divergent_market': -20
        }
        
        regime_adjustment = regime_adjustments.get(market_regime, 0)
        
        # 基于置信度调整
        confidence_adjustment = (decision_details.decision_confidence - 0.5) * 20
        
        # 计算最终仓位
        final_position = (base_position + signal_adjustment + 
                         decision_adjustment + regime_adjustment + confidence_adjustment)
        
        # 限制在合理范围内
        return round(max(10.0, min(90.0, final_position)), 1)
    
    def _generate_top_sectors_list(self, breadth_metrics: Dict[str, Any],
                                 avoid_list: List[str],
                                 signal_details: SignalStrengthDetails) -> List[Dict]:
        """生成推荐板块列表"""
        top_sectors = []
        
        # 计算各板块评分
        sector_scores = {}
        for sector, metrics in breadth_metrics.items():
            if sector in avoid_list:
                continue  # 跳过避免清单中的板块
            
            # 计算综合评分
            health_score = getattr(metrics, 'internal_health', 50) / 100
            coherence_score = getattr(metrics, 'momentum_coherence', 0.5) or 0.5
            participation_score = getattr(metrics, 'participation_rate', 0.5)
            
            # 检查背离情况
            divergence_penalty = 0
            if hasattr(metrics, 'price_breadth_divergence') and metrics.price_breadth_divergence:
                if metrics.price_breadth_divergence.get('type') == 'negative':
                    divergence_penalty = 0.2
            
            overall_score = (health_score * 0.4 + coherence_score * 0.3 + 
                           participation_score * 0.3 - divergence_penalty)
            
            sector_scores[sector] = overall_score
        
        # 选择前5个板块
        sorted_sectors = sorted(sector_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (sector, score) in enumerate(sorted_sectors[:5]):
            metrics = breadth_metrics[sector]
            
            # 计算权重（总和为100%）
            weight = max(10, 30 - i * 5)  # 30%, 25%, 20%, 15%, 10%
            
            top_sectors.append({
                'sector': sector,
                'weight': weight,
                'health': getattr(metrics, 'internal_health', 50),
                'score': round(score * 100, 1),
                'coherence': getattr(metrics, 'momentum_coherence', 0.5) or 0.5,
                'recommendation': self._generate_sector_recommendation(metrics, score)
            })
        
        # 归一化权重
        total_weight = sum(s['weight'] for s in top_sectors)
        if total_weight > 0:
            for sector in top_sectors:
                sector['weight'] = round(sector['weight'] / total_weight * 100, 1)
        
        return top_sectors
    
    def _generate_avoid_sectors_list(self, avoid_details: AvoidListDetails,
                                   breadth_metrics: Dict[str, Any]) -> List[Dict]:
        """生成避免板块列表"""
        avoid_sectors = []
        
        for sector in avoid_details.avoid_list:
            if sector not in breadth_metrics:
                continue
            
            metrics = breadth_metrics[sector]
            severity = avoid_details.severity_levels.get(sector, 'medium')
            reasons = avoid_details.avoid_reasons.get(sector, {})
            
            # 确定风险等级
            risk_level_map = {
                'critical': 'extreme',
                'high': 'high',
                'medium': 'moderate',
                'low': 'low'
            }
            
            risk_level = risk_level_map.get(severity, 'moderate')
            
            # 生成建议
            if severity == 'critical':
                recommendation = "强烈建议立即清仓，风险极高"
            elif severity == 'high':
                recommendation = "建议减仓或清仓，风险较高"
            elif severity == 'medium':
                recommendation = "谨慎持有，密切关注风险变化"
            else:
                recommendation = "可适度持有，但需要监控"
            
            avoid_sectors.append({
                'sector': sector,
                'risk_level': risk_level,
                'severity': severity,
                'health': getattr(metrics, 'internal_health', 50),
                'primary_risk': reasons.get('primary_issues', ['综合风险较高'])[0] if reasons.get('primary_issues') else '综合风险较高',
                'recommendation': recommendation,
                'improvement_conditions': avoid_details.improvement_conditions.get(sector, [])[:2]  # 前2个条件
            })
        
        return avoid_sectors
    
    def _generate_position_recommendation(self, suggested_position: float,
                                        decision_details: UnifiedDecisionDetails,
                                        signal_details: SignalStrengthDetails) -> Dict:
        """生成仓位建议"""
        # 确定操作类型
        if suggested_position >= 70:
            action = "增仓"
            urgency = "建议"
        elif suggested_position >= 50:
            action = "维持"
            urgency = "可以"
        else:
            action = "减仓"
            urgency = "建议"
        
        # 生成理由
        reasoning_parts = [
            f"基于{signal_details.signal_grade}级信号强度({signal_details.overall_strength:.3f})",
            f"决策置信度{decision_details.decision_confidence:.2f}",
            f"风险等级{decision_details.risk_assessment['overall_risk_level']}"
        ]
        
        reasoning = f"{urgency}{action}至{suggested_position}%。" + "，".join(reasoning_parts)
        
        return {
            'action': action,
            'target_position': suggested_position,
            'current_recommendation': f"{action}至{suggested_position}%",
            'reasoning': reasoning,
            'urgency': urgency,
            'risk_level': decision_details.risk_assessment['overall_risk_level'],
            'confidence': decision_details.decision_confidence
        }
    
    def _generate_sector_recommendation(self, metrics: Any, score: float) -> str:
        """生成板块推荐理由"""
        health = getattr(metrics, 'internal_health', 50)
        coherence = getattr(metrics, 'momentum_coherence', 0.5) or 0.5
        
        if score > 0.8:
            return f"强烈推荐：健康度{health:.0f}，一致性{coherence:.2f}，综合表现优秀"
        elif score > 0.6:
            return f"推荐：健康度{health:.0f}，一致性{coherence:.2f}，表现良好"
        elif score > 0.4:
            return f"可考虑：健康度{health:.0f}，一致性{coherence:.2f}，表现一般"
        else:
            return f"谨慎：健康度{health:.0f}，一致性{coherence:.2f}，需要观察"
    
    # 兼容性方法：提供与原始接口相同的方法名
    def calculate_signal_strength(self, rotation_metrics: Any, 
                                breadth_metrics: Dict[str, Any],
                                timeframe: str = 'daily') -> float:
        """兼容性方法：计算信号强度（返回简化格式）"""
        details = self.enhanced_analyzer.calculate_signal_strength_enhanced(
            rotation_metrics, breadth_metrics, timeframe
        )
        return details.overall_strength
    
    def generate_unified_decision(self, timeframe_results: Dict,
                                dynamic_weights: Dict,
                                market_regime: str) -> Dict:
        """兼容性方法：生成统一决策（返回字典格式）"""
        # 需要模拟 breadth_metrics 和 rotation_metrics
        # 这里简化处理，实际使用时应该传入完整参数
        breadth_metrics = {}
        rotation_metrics = type('MockRotation', (), {'unified_rii': 0.5})()
        
        compatible_decision = self.analyze_timeframes(
            timeframe_results, dynamic_weights, breadth_metrics, 
            rotation_metrics, market_regime
        )
        
        # 转换为字典格式
        return {
            'suggested_position': compatible_decision.suggested_position,
            'top_sectors': compatible_decision.top_sectors,
            'avoid_sectors': compatible_decision.avoid_sectors,
            'position_recommendation': compatible_decision.position_recommendation,
            'signal_strength': compatible_decision.signal_strength,
            'confidence': compatible_decision.confidence,
            'market_regime': compatible_decision.market_regime,
            'decision_rationale': compatible_decision.decision_rationale
        }
