from us import DataLoader


def handle_history(item):
    pass


def handle_realtime(data, channel):
    pass


if __name__ == '__main__':
    vhcid = 100355  # 股票唯一 ID（由接口系统分配）
    DataLoader(username='test01', password='test01').get_all_price_data(
        vhcid=vhcid,
        timespan="minute",
        multiplier=1,
        from_="2025-05-01",
        on_history_data=handle_history,
        on_realtime_data=handle_realtime
    )
