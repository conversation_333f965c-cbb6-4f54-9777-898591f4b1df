import os
from datetime import datetime
from typing import List, Dict
import time
import threading
import logging
from logging.handlers import RotatingFileHandler

import numpy as np
import numba as nb
import pandas as pd
import pymysql
import pytz
import talib
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger

from dotenv import load_dotenv

load_dotenv()

# 配置日志记录
def setup_logging():
    """
    配置日志记录系统
    """
    # 创建logs目录（如果不存在）
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根logger
    logging.basicConfig(level=logging.INFO, format=log_format)
    
    # 过滤第三方库的警告日志
    logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests.packages.urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # 创建专门的logger
    logger = logging.getLogger('market_breadth_metrics_gics')
    logger.setLevel(logging.INFO)
    
    # 移除默认handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件handler - 使用RotatingFileHandler避免日志文件过大
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'market_breadth_metrics_gics.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(log_format)
    file_handler.setFormatter(file_formatter)
    
    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 添加handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 初始化日志记录器
logger = setup_logging()

@nb.njit()
def calculate_rsi(close: np.array, period: int = 14) -> np.array:
    """
    计算RSI指标
    
    :param close: 收盘价数组
    :param period: RSI计算周期，默认14
    :return: RSI值数组
    """
    n = len(close)
    if n < period + 1:
        return np.full(n, np.nan)
    
    # 计算价格变化
    delta = np.diff(close)
    
    # 分离涨跌
    gains = np.where(delta > 0, delta, 0.0)
    losses = np.where(delta < 0, -delta, 0.0)
    
    # 初始化RSI数组
    rsi = np.full(n, np.nan)
    
    # 计算第一个RSI值
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])
    
    if avg_loss == 0:
        rsi[period] = 100.0
    else:
        rs = avg_gain / avg_loss
        rsi[period] = 100.0 - (100.0 / (1.0 + rs))
    
    # 使用指数移动平均计算后续RSI值
    for i in range(period + 1, n):
        gain = gains[i - 1]
        loss = losses[i - 1]
        
        # 更新平均涨跌幅（使用Wilder's smoothing）
        avg_gain = (avg_gain * (period - 1) + gain) / period
        avg_loss = (avg_loss * (period - 1) + loss) / period
        
        if avg_loss == 0:
            rsi[i] = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi[i] = 100.0 - (100.0 / (1.0 + rs))
    
    return rsi


@nb.njit()
def calculate_stock_data(close: np.array, high: np.array, low: np.array, volume: np.array, mcap: float) -> np.array:
    """
    计算股票数据

    :param close:
    :param high:
    :param low:
    :param volume:
    :param mcap:
    :return:
    14个指标数组
    0: current_price, 1: daily_return, 2: current_volume, 3: avg_volume,
    4: mcap, 5: high_52w, 6: low_52w, 7: ma50,
    8: ma200, 9: current_rsi, 10: is_new_high, 11: is_new_low,
    12: above_ma50, 13: above_ma200
    """
    # 计算指标
    current_price = close[-1]
    prev_close = close[-2]
    daily_return = (current_price - prev_close) / prev_close

    # 成交量
    current_volume = volume[-1]
    avg_volume = np.mean(volume[-20:])

    # 52周高点和低点
    high_52w = np.max(high[-252:])
    low_52w = np.min(low[-252:])

    # 均线
    ma50 = np.mean(close[-50:])
    ma200 = np.mean(close[-200:])

    # RSI
    rsi_values = calculate_rsi(close)
    current_rsi = rsi_values[-1]

    # 计算其他指标
    is_new_high = 1.0 if current_price >= high_52w * 0.98 else 0.0
    is_new_low = 1.0 if current_price <= low_52w * 1.02 else 0.0
    above_ma50 = 1.0 if current_price > ma50 else 0.0
    above_ma200 = 1.0 if current_price > ma200 else 0.0
    
    # 构建返回数组 (14个指标)
    # 索引对应关系：
    # 0: current_price, 1: daily_return, 2: current_volume, 3: avg_volume
    # 4: mcap, 5: high_52w, 6: low_52w, 7: ma50
    # 8: ma200, 9: current_rsi, 10: is_new_high, 11: is_new_low
    # 12: above_ma50, 13: above_ma200
    res = np.array([
        current_price,
        daily_return,
        current_volume,
        avg_volume,
        mcap,
        high_52w,
        low_52w,
        ma50,
        ma200,
        current_rsi,
        is_new_high,
        is_new_low,
        above_ma50,
        above_ma200
    ])
    
    return res


def get_companies(markets: List[str]):
    conn = pymysql.Connection(
        host=os.environ['DEFAULT_DB_HOST'],
        port=int(os.environ['DEFAULT_DB_PORT']),
        user=os.environ['DEFAULT_DB_USER'],
        password=os.environ['DEFAULT_DB_PASSWORD'],
        database=os.environ['DEFAULT_DB_NAME'],
    )

    placeholders = ','.join([f"'{m}'" for m in markets])

    sql = f"SELECT DISTINCT company FROM index_company_mapping_gics WHERE market IN ({placeholders})"
    cursor = conn.cursor()
    cursor.execute(sql)
    companies = cursor.fetchall()
    cursor.close()
    conn.close()
    companies = [c[0] for c in companies]
    return companies


def get_company_mcap(companies: List[str]):
    sql = "SELECT vhcid,tk,`value`,t_insert FROM daily_ratio_realtime WHERE item_name='mcap'"
    conn = pymysql.Connection(
        host=os.environ['ALGO_DB_HOST'],
        port=int(os.environ['ALGO_DB_PORT']),
        user=os.environ['ALGO_DB_USER'],
        password=os.environ['ALGO_DB_PASSWORD'],
        database=os.environ['ALGO_DB_NAME'],
    )
    cursor = conn.cursor()
    cursor.execute(sql)
    data = cursor.fetchall()
    cursor.close()
    conn.close()

    data = pd.DataFrame(data, columns=['vhcid', 'tk', 'value', 'insert'])
    data['value'] = data['value'].astype(float)
    data['insert'] = pd.to_datetime(data['insert'])
    data = data.sort_values('insert')
    data = data.drop_duplicates(subset=['tk'], keep='last')
    data = data[data['tk'].isin(companies)]  # 过滤公司
    # tk: mcap
    data = data[['tk', 'value']].set_index('tk').to_dict()['value']
    return data


def stock_data_worker(company: str, price_df: pd.DataFrame, mcap: float) -> dict:
    """
    计算单个公司的股票数据指标
    :param company: 公司名称
    :param price_df: 价格数据 DataFrame
    :param mcap: 市值
    :return: 包含指标的 Series
    """
    close = price_df['close'].values
    high = price_df['high'].values
    low = price_df['low'].values
    volume = price_df['volume'].values

    indicators = calculate_stock_data(close, high, low, volume, mcap)

    return {
        'symbol': company,
        'price': indicators[0],
        'daily_return': indicators[1],
        'volume': indicators[2],
        'avg_volume': indicators[3],
        'market_mcap': indicators[4],
        'high_52w': indicators[5],
        'low_52w': indicators[6],
        'ma50': indicators[7],
        'ma200': indicators[8],
        'rsi': indicators[9],
        'is_new_high': indicators[10],
        'is_new_low': indicators[11],
        'above_ma50': indicators[12],
        'above_ma200': indicators[13]
    }


def multiprocess_calculate_stock_data(company_price: Dict[str, pd.DataFrame], company_mcap: Dict[str, float],
                                      cpu_counts: int = 4) -> pd.DataFrame:
    import multiprocessing

    with multiprocessing.Pool(cpu_counts) as pool:
        results = pool.starmap(
            stock_data_worker,
            [(company, price_df, company_mcap.get(company, 0.0)) for company, price_df in company_price.items()]
        )

    results = pd.DataFrame(results)
    return results


def get_price(companies: List[str]):
    from utils import download_hist_price

    company_price = download_hist_price(
        companies,
        interval='1d',
        start='2024-01-01',
        columns=['h', 'l', 'c', 'v'],
        threads=10
    )
    return company_price


def calculate_breadth_metrics(current_time ,market: str, stock_data: pd.DataFrame) -> Dict[str, float]:
    """
    计算市场的广度指标

    :param current_time:
    :param market:
    :param stock_data:
    :return:
    """
    sql = f"""
    SELECT company FROM index_company_mapping_gics WHERE market='{market}'
    """
    conn = pymysql.Connection(
        host=os.environ['DEFAULT_DB_HOST'],
        port=int(os.environ['DEFAULT_DB_PORT']),
        user=os.environ['DEFAULT_DB_USER'],
        password=os.environ['DEFAULT_DB_PASSWORD'],
        database=os.environ['DEFAULT_DB_NAME'],
    )
    cursor = conn.cursor()
    cursor.execute(sql)
    companies = cursor.fetchall()
    cursor.close()
    companies = [c[0] for c in companies]

    market_stock_data = stock_data[stock_data['symbol'].isin(companies)].copy()

    # 基础数据
    total_stocks = len(companies)
    advances = market_stock_data[market_stock_data['daily_return'] > 0].shape[0]
    declines = market_stock_data[market_stock_data['daily_return'] < 0].shape[0]
    unchanged = total_stocks - advances - declines

    # 成交量
    market_stock_data['volume_value'] = market_stock_data['volume'] * market_stock_data['price']
    advancing_volume = market_stock_data[market_stock_data['daily_return'] > 0]['volume_value'].sum()
    declining_volume = market_stock_data[market_stock_data['daily_return'] < 0]['volume_value'].sum()
    total_volume = market_stock_data['volume_value'].sum()

    # 新高和新低
    new_highs = market_stock_data[market_stock_data['is_new_high'] > 0].shape[0]
    new_lows = market_stock_data[market_stock_data['is_new_low'] > 0].shape[0]

    # 均线统计
    above_ma50 = market_stock_data[market_stock_data['above_ma50'] > 0].shape[0]
    above_ma200 = market_stock_data[market_stock_data['above_ma200'] > 0].shape[0]

    # 平均RSI
    avg_rsi = market_stock_data['rsi'].mean()

    # 市值加权收益率
    market_stock_data['weighted_return'] = market_stock_data['daily_return'] * market_stock_data['market_mcap']
    market_cap_weighted_return = market_stock_data['weighted_return'].sum() / market_stock_data['market_mcap'].sum()

    # 等权重收益率
    equal_weighted_return = market_stock_data['daily_return'].mean()

    # 保存到数据库
    insert_sql = """
    INSERT INTO market_breadth_metrics_gics (
        recorded_at, market, total_stocks, advances, declines, unchanged,
        advancing_volume, declining_volume, total_volume,
        new_highs_52w, new_lows_52w,
        above_ma50, above_ma200,
        avg_rsi,
        market_cap_weighted_return, equal_weighted_return
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        total_stocks = VALUES(total_stocks),
        advances = VALUES(advances),
        declines = VALUES(declines),
        unchanged = VALUES(unchanged),
        advancing_volume = VALUES(advancing_volume),
        declining_volume = VALUES(declining_volume),
        total_volume = VALUES(total_volume),
        new_highs_52w = VALUES(new_highs_52w),
        new_lows_52w = VALUES(new_lows_52w),
        above_ma50 = VALUES(above_ma50),
        above_ma200 = VALUES(above_ma200),
        avg_rsi = VALUES(avg_rsi),
        market_cap_weighted_return = VALUES(market_cap_weighted_return),
        equal_weighted_return = VALUES(equal_weighted_return)
    """
    
    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, (
            current_time, market, total_stocks, advances, declines, unchanged,
            float(advancing_volume), float(declining_volume), float(total_volume),
            new_highs, new_lows,
            above_ma50, above_ma200,
            float(avg_rsi),
            float(market_cap_weighted_return), float(equal_weighted_return)
        ))
        conn.commit()
        cursor.close()
    except Exception as e:
        logger.error(f"Error inserting market breadth metrics: {e}")
        conn.rollback()
    finally:
        conn.close()


def get_stock_data_for_markets(markets: List[str], batch_size: int = 2000):
    """
    获取所有市场需要的股票数据，分批处理以控制内存使用
    
    :param markets: 市场列表
    :param batch_size: 每批处理的公司数量，默认1000
    :return: 合并后的股票数据DataFrame
    """
    companies = get_companies(markets)
    batch_company_mcap = get_company_mcap(companies)
    logger.info(f"Found {len(companies)} companies in the index constituents.")
    
    # 分批处理
    all_stock_data = []
    for i in range(0, len(companies), batch_size):
        batch_companies = companies[i:i + batch_size]
        try:
            # 获取当前批次的市值和价格数据
            batch_company_price = get_price(batch_companies)
            # 计算当前批次的股票数据
            batch_stock_data = multiprocess_calculate_stock_data(batch_company_price, batch_company_mcap)
            all_stock_data.append(batch_stock_data)
        except Exception as e:
            logger.error(f"Error processing batch {i // batch_size + 1}: {e}")
            continue
    
    # 合并所有批次的结果
    if all_stock_data:
        stock_data = pd.concat(all_stock_data, ignore_index=True)
        logger.info(f"Successfully processed {len(stock_data)} stocks in total")
        return stock_data
    else:
        logger.error("No stock data was successfully processed")
        return pd.DataFrame()  # 返回空DataFrame


def calculate_market_breadth_job(market: str, stock_data: pd.DataFrame = None):
    """
    为单个市场计算广度指标的任务
    """
    try:
        current_time = datetime.now(tz=pytz.UTC)
        calculate_breadth_metrics(current_time, market, stock_data)
        
    except Exception as e:
        logger.error(f"Error calculating breadth metrics for {market}: {e}")


def get_markets_from_config():
    """
    从market_config_gics表中获取所有市场分组
    """
    conn = pymysql.Connection(
        host=os.environ['DEFAULT_DB_HOST'],
        port=int(os.environ['DEFAULT_DB_PORT']),
        user=os.environ['DEFAULT_DB_USER'],
        password=os.environ['DEFAULT_DB_PASSWORD'],
        database=os.environ['DEFAULT_DB_NAME'],
        charset='utf8mb4'
    )
    
    try:
        with conn.cursor() as cursor:
            sql = "SELECT DISTINCT market FROM market_config_gics ORDER BY market"
            cursor.execute(sql)
            results = cursor.fetchall()
            markets = [row[0] for row in results]
            logger.info(f"从market_config_gics表中获取到{len(markets)}个市场分组: {markets}")
            return markets
    except Exception as e:
        logger.error(f"获取市场配置失败: {e}")
        # 如果查询失败，返回默认的市场列表
        return ['SP500', 'NASDAQ100', 'RUSSELL2000', 'NASDAQ', 'NYSE']
    finally:
        conn.close()


def calculate_high_frequency_markets():
    """
    计算高频市场的广度指标
    """
    try:
        # 从数据库获取所有市场分组
        all_markets = get_markets_from_config()
        # 高频市场默认为SP500和NASDAQ100，如果配置中有其他高频市场也会包含
        high_freq_markets = [m for m in all_markets if m in ['SP500', 'NASDAQ100']]
        
        if not high_freq_markets:
            logger.warning("未找到高频市场配置，使用默认配置: SP500, NASDAQ100")
            high_freq_markets = ['SP500', 'NASDAQ100']
            
        logger.info(f"计算高频市场广度指标: {high_freq_markets}")
        stock_data = get_stock_data_for_markets(high_freq_markets)
        
        # 并行计算高频市场
        for market in high_freq_markets:
            calculate_market_breadth_job(market, stock_data)
            
    except Exception as e:
        logger.error(f"Error in high frequency markets calculation: {e}")


def calculate_low_frequency_markets():
    """
    计算低频市场的广度指标
    """
    try:
        # 从数据库获取所有市场分组
        all_markets = get_markets_from_config()
        # 低频市场为除了SP500和NASDAQ100之外的其他市场
        low_freq_markets = [m for m in all_markets if m not in ['SP500', 'NASDAQ100']]
        
        if not low_freq_markets:
            logger.warning("未找到低频市场配置，使用默认配置: RUSSELL2000, NASDAQ, NYSE")
            low_freq_markets = ['RUSSELL2000', 'NASDAQ', 'NYSE']
            
        logger.info(f"计算低频市场广度指标: {low_freq_markets}")
        stock_data = get_stock_data_for_markets(low_freq_markets)
        
        for market in low_freq_markets:
            calculate_market_breadth_job(market, stock_data)
            
    except Exception as e:
        logger.error(f"Error in low frequency markets calculation: {e}")


def main():
    """
    主函数，设置调度器
    """
    logger.info("Starting market breadth calculation scheduler...")
    
    # 创建调度器
    scheduler = BlockingScheduler(timezone=pytz.UTC)
    
    # 添加高频任务（SP500, NASDAQ100 - 每5分钟）
    scheduler.add_job(
        func=calculate_high_frequency_markets,
        trigger=IntervalTrigger(minutes=5),
        id='high_frequency_markets',
        name='SP500 and NASDAQ100 Market Breadth (5min)',
        replace_existing=True
    )
    
    # 添加低频任务（RUSSELL2000 - 每10分钟）
    scheduler.add_job(
        func=calculate_low_frequency_markets,
        trigger=IntervalTrigger(minutes=10),
        id='low_frequency_markets',
        name='RUSSELL2000 Market Breadth (10min)',
        replace_existing=True
    )
    
    # 立即执行一次所有任务
    try:
        calculate_high_frequency_markets()
        calculate_low_frequency_markets()
    except Exception as e:
        logger.error(f"Error in initial calculation: {e}")
    
    logger.info("Scheduler started. High freq: 5min, Low freq: 10min. Press Ctrl+C to exit.")
    
    try:
        scheduler.start()
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user.")
        scheduler.shutdown()


if __name__ == '__main__':
    main()

