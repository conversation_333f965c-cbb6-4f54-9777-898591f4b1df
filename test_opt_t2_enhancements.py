#!/usr/bin/env python3
"""
测试 OPT-T2 决策透明度与可解释性增强
验证LLM交互增强器和增强版报告生成器
"""

import sys
import os
from datetime import datetime
import json

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 直接导入模块
import importlib.util

# 导入 LLM 交互增强器
llm_enhancer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'llm_interaction_enhancer.py')
spec = importlib.util.spec_from_file_location("llm_interaction_enhancer", llm_enhancer_path)
llm_enhancer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(llm_enhancer_module)

LLMInteractionEnhancer = llm_enhancer_module.LLMInteractionEnhancer
LLMContext = llm_enhancer_module.LLMContext
MarketContext = llm_enhancer_module.MarketContext

# 导入增强版报告生成器
report_gen_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_report_generator.py')
spec = importlib.util.spec_from_file_location("enhanced_report_generator", report_gen_path)
report_gen_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(report_gen_module)

EnhancedReportGenerator = report_gen_module.EnhancedReportGenerator
DecisionPath = report_gen_module.DecisionPath
RiskScenario = report_gen_module.RiskScenario


def test_llm_interaction_enhancer():
    """测试LLM交互增强器"""
    print("=== 测试LLM交互增强器 ===\n")
    
    # 创建测试数据
    test_breadth_metrics = {
        'market': 'Technology',
        'timestamp': datetime.now(),
        'internal_health': 65.5,
        'momentum_coherence': 0.42,
        'ad_ratio': 1.8,
        'purity': 0.65,
        'participation_rate': 0.75,
        'ma50_breadth': 0.68,
        'ma200_breadth': 0.55,
        'total_stocks': 150,
        'advances': 95,
        'declines': 45,
        'divergence_type': 'none',
        'divergence_severity': 0.0,
        'divergence_confidence': 0.0,
        'coherence_details': {
            'coherence_type': 'moderate_coherence',
            'direction_coherence': 0.63,
            'magnitude_coherence': 0.38,
            'interpretation': '板块表现分化但不极端，存在个股机会',
            'action_suggestion': '适度参与，均衡配置',
            'calculation_method': 'cv_based'
        }
    }
    
    # 创建市场上下文
    market_context = MarketContext(
        market_name='Technology',
        timestamp=datetime.now(),
        overall_sentiment='neutral',
        key_metrics={'volatility': 0.25, 'volume': 1.2},
        risk_factors=['市场分化', '不确定性增加'],
        opportunities=['个股选择机会', '结构性投资'],
        market_phase='consolidation'
    )
    
    # 测试LLM交互增强器
    enhancer = LLMInteractionEnhancer()
    
    print("🧠 生成LLM交互上下文...")
    llm_context = enhancer.enhance_breadth_analysis(test_breadth_metrics, market_context)
    
    print("✅ LLM上下文生成成功")
    print(f"📊 执行摘要:")
    print(llm_context.executive_summary)
    
    print(f"\n🔍 市场状态解释:")
    print(llm_context.market_state_explanation)
    
    print(f"\n🧮 决策逻辑:")
    print(llm_context.decision_logic_explanation)
    
    print(f"\n⚖️ 风险收益分析:")
    risk_reward = llm_context.risk_reward_analysis
    print(f"  风险等级: {risk_reward['risk_level']}")
    print(f"  收益潜力: {risk_reward['reward_potential']}")
    print(f"  风险因素: {', '.join(risk_reward['risk_factors'])}")
    print(f"  收益驱动: {', '.join(risk_reward['reward_drivers'])}")
    
    print(f"\n💡 操作建议:")
    for i, rec in enumerate(llm_context.actionable_recommendations, 1):
        print(f"  {i}. {rec['action']}: {rec['reasoning']} (置信度: {rec['confidence']:.1%})")
    
    print(f"\n❓ 交互式问答预设:")
    for i, qa in enumerate(llm_context.interactive_qa_presets[:3], 1):
        print(f"  {i}. {qa['question']}")
    
    print(f"\n📈 置信度分解:")
    confidence = llm_context.confidence_breakdown
    print(f"  总体置信度: {confidence['overall_confidence']:.1%}")
    print(f"  主要因素: {', '.join(confidence['factors'][:2])}")
    
    print(f"\n🎯 替代场景:")
    for scenario in llm_context.alternative_scenarios:
        print(f"  - {scenario['scenario']}: {scenario['probability']:.1%} 概率")
    
    # 测试LLM提示词生成
    print(f"\n📝 生成LLM提示词...")
    prompt = enhancer.generate_llm_prompt(llm_context, "Technology板块现在适合投资吗？")
    print(f"提示词长度: {len(prompt)} 字符")
    print(f"提示词预览: {prompt[:200]}...")
    
    return True


def test_enhanced_report_generator():
    """测试增强版报告生成器"""
    print("\n=== 测试增强版报告生成器 ===\n")
    
    # 创建测试数据
    test_analysis_data = {
        'market': 'Healthcare',
        'timestamp': datetime.now(),
        'internal_health': 72.3,
        'momentum_coherence': 0.68,
        'ad_ratio': 2.1,
        'purity': 0.78,
        'participation_rate': 0.82,
        'ma50_breadth': 0.75,
        'ma200_breadth': 0.68,
        'total_stocks': 200,
        'advances': 145,
        'declines': 45,
        'divergence_type': 'positive',
        'divergence_severity': 0.15,
        'divergence_confidence': 0.85,
        'coherence_details': {
            'coherence_type': 'bullish_trend',
            'direction_coherence': 0.72,
            'magnitude_coherence': 0.65,
            'interpretation': '板块偏多趋势，72%个股上涨，平均涨幅1.8%',
            'action_suggestion': '可以跟随趋势，但注意涨幅分化情况',
            'calculation_method': 'cv_based'
        }
    }
    
    # 创建模拟决策追踪器
    class MockDecisionTracker:
        def get_full_decision_history(self):
            return [
                {
                    'type': 'breadth_analysis',
                    'timestamp': datetime.now().isoformat(),
                    'overall_confidence': 0.85,
                    'duration': 2.5,
                    'steps': [
                        {
                            'name': 'data_validation',
                            'reasoning': '数据验证通过，样本量充足',
                            'confidence': 0.95,
                            'result': {'valid': True, 'sample_size': 200}
                        },
                        {
                            'name': 'coherence_calculation',
                            'reasoning': '使用改进算法计算，结果稳定',
                            'confidence': 0.88,
                            'result': {'coherence': 0.68, 'type': 'bullish_trend'}
                        },
                        {
                            'name': 'health_assessment',
                            'reasoning': '综合多项指标，健康度良好',
                            'confidence': 0.82,
                            'result': {'health': 72.3, 'grade': 'good'}
                        }
                    ]
                }
            ]
    
    # 测试增强版报告生成器
    generator = EnhancedReportGenerator()
    mock_tracker = MockDecisionTracker()
    
    print("📊 生成增强版报告...")
    enhanced_report = generator.generate_enhanced_report(test_analysis_data, mock_tracker)
    
    print("✅ 增强版报告生成成功")
    
    # 显示报告结构
    print(f"\n📋 报告结构:")
    for key in enhanced_report.keys():
        print(f"  - {key}")
    
    # 显示执行摘要
    print(f"\n📊 执行摘要:")
    exec_summary = enhanced_report['executive_summary']
    print(f"  市场: {exec_summary['market_name']}")
    print(f"  整体评估: {exec_summary['overall_assessment']}")
    print(f"  主要建议: {exec_summary['primary_recommendation']}")
    print(f"  置信度: {exec_summary['confidence_level']:.1%}")
    
    # 显示决策透明度
    print(f"\n🔍 决策透明度:")
    decision_section = enhanced_report['decision_transparency']
    print(f"  决策步骤数: {len(decision_section['decision_steps'])}")
    if decision_section['decision_steps']:
        first_decision = decision_section['decision_steps'][0]
        print(f"  第一个决策类型: {first_decision.get('decision_type', 'unknown')}")
        print(f"  决策置信度: {first_decision.get('overall_confidence', 0):.2f}")
        print(f"  步骤数: {len(first_decision.get('steps', []))}")
    
    # 显示风险场景分析
    print(f"\n⚠️ 风险场景分析:")
    risk_section = enhanced_report['risk_scenario_analysis']
    scenarios = risk_section['scenarios']
    print(f"  识别场景数: {len(scenarios)}")
    for i, scenario in enumerate(scenarios[:2], 1):
        print(f"  {i}. {scenario['scenario_name']}: {scenario['probability']:.1%} 概率, {scenario['impact_level']}影响")
    
    # 显示置信度分析
    print(f"\n📈 置信度分析:")
    confidence_section = enhanced_report['confidence_analysis']
    print(f"  总体置信度: {confidence_section['overall_confidence']:.1%}")
    breakdown = confidence_section['confidence_breakdown']
    print(f"  数据质量: {breakdown['data_quality']:.1%}")
    print(f"  算法可靠性: {breakdown['algorithm_reliability']:.1%}")
    print(f"  市场条件: {breakdown['market_conditions']:.1%}")
    
    # 显示LLM交互
    print(f"\n🤖 LLM交互:")
    llm_section = enhanced_report['llm_interaction']
    print(f"  上下文可用: {'是' if llm_section['context'] else '否'}")
    print(f"  预设问题数: {len(llm_section['interactive_qa'].get('preset_questions', []))}")
    print(f"  提示词长度: {len(llm_section['prompt_template'])} 字符")
    
    # 显示增强功能
    print(f"\n🚀 增强功能:")
    enhancement = enhanced_report['enhancement_metadata']
    print(f"  版本: {enhancement['version']}")
    print(f"  增强特性: {', '.join(enhancement['enhancement_features'])}")
    
    return True


def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===\n")
    
    # 创建完整的测试场景
    test_data = {
        'market': 'Energy',
        'internal_health': 45.2,
        'momentum_coherence': 0.28,
        'ad_ratio': 0.65,
        'total_stocks': 80,
        'advances': 25,
        'declines': 50,
        'divergence_type': 'negative',
        'divergence_severity': 0.22,
        'divergence_confidence': 0.78,
        'coherence_details': {
            'coherence_type': 'high_dispersion',
            'interpretation': '板块严重分化，仅31%个股同向',
            'action_suggestion': '避免板块操作，个股机会需要深度研究'
        }
    }
    
    print("🔗 测试完整集成流程...")
    
    # 1. LLM交互增强
    enhancer = LLMInteractionEnhancer()
    llm_context = enhancer.enhance_breadth_analysis(test_data)
    
    # 2. 增强版报告生成
    generator = EnhancedReportGenerator()
    enhanced_report = generator.generate_enhanced_report(test_data)
    
    # 3. 生成用户友好的摘要
    print("📋 集成分析摘要:")
    print(f"  板块: {test_data['market']}")
    print(f"  健康度: {test_data['internal_health']:.1f} (疲弱)")
    print(f"  一致性: {test_data['momentum_coherence']:.2f} (严重分化)")
    print(f"  背离状态: {test_data['divergence_type']} (负背离)")
    
    print(f"\n💡 LLM增强洞察:")
    print(f"  核心观点: {llm_context.executive_summary.split('💡 核心观点：')[1] if '💡 核心观点：' in llm_context.executive_summary else '板块疲弱且分化严重'}")
    
    print(f"\n📊 风险评估:")
    risk_analysis = enhanced_report['risk_scenario_analysis']
    high_risk_scenarios = [s for s in risk_analysis['scenarios'] if s['impact_level'] == '高']
    print(f"  高影响风险场景: {len(high_risk_scenarios)}个")
    
    print(f"\n🎯 操作建议:")
    recommendations = llm_context.actionable_recommendations
    if recommendations:
        primary_rec = recommendations[0]
        print(f"  主要建议: {primary_rec['action']}")
        print(f"  理由: {primary_rec['reasoning']}")
        print(f"  置信度: {primary_rec['confidence']:.1%}")
    
    print(f"\n✅ 集成测试完成")
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T2 决策透明度与可解释性增强\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试LLM交互增强器
        test_results.append(("LLM交互增强器", test_llm_interaction_enhancer()))
        
        print("=" * 60)
        
        # 2. 测试增强版报告生成器
        test_results.append(("增强版报告生成器", test_enhanced_report_generator()))
        
        print("=" * 60)
        
        # 3. 测试集成功能
        test_results.append(("集成功能", test_integration()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有 OPT-T2 功能测试通过！")
            print("🚀 决策透明度与可解释性增强已完成")
            print("💡 LLM交互能力显著提升")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
