# 预计算系统定时任务配置指南

## 🕐 推荐运行时间表

### 美股交易时间参考
- **美股交易时间**: 美东时间 9:30 AM - 4:00 PM
- **对应北京时间**: 
  - 夏令时：21:30 - 4:00（次日）
  - 冬令时：22:30 - 5:00（次日）

### 最佳更新时间点

| 更新类型 | 北京时间 | UTC时间 | 美东时间 | 频率 | 说明 |
|---------|---------|---------|---------|------|------|
| **日终更新** | 5:30 AM | 21:30 | 4:30 PM | 每日 | 美股收盘后30分钟 |
| **下午更新** | 14:30 PM | 6:30 AM | 1:30 AM | 每日 | 白天工作时间 |
| **盘中更新** | 每小时 | 每小时 | 交易时间内 | 可选 | 实时数据更新 |
| **周末维护** | 周日 2:00 AM | 周六 18:00 | 周六 1:00 PM | 每周 | 完整数据重建 |
| **月度清理** | 每月1日 3:00 AM | 每月1日 19:00 | - | 每月 | 清理过期数据 |

## 🐧 Linux/Mac 配置（crontab）

### 1. 编辑crontab
```bash
crontab -e
```

### 2. 基础配置（推荐）

**配置方案A：美股收盘后更新**

*UTC时间配置（推荐，适用于大多数云服务器）：*
```bash
# 预计算系统定时任务配置（UTC时间）
# 项目路径（请修改为实际路径）
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 每日美股收盘后更新（UTC 21:30 = 北京时间5:30）
30 21 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/daily_update.log 2>&1

# 每周日凌晨完整重建（UTC 18:00 = 北京时间周一2:00）
0 18 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1

# 每日状态检查（UTC 22:00 = 北京时间6:00）
0 22 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/status_check.log 2>&1
```

*北京时间配置（仅当服务器时区为Asia/Shanghai时使用）：*
```bash
# 预计算系统定时任务配置（北京时间）
# 项目路径（请修改为实际路径）
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 每日美股收盘后更新（北京时间5:30）
30 5 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/daily_update.log 2>&1

# 每周日凌晨完整重建（北京时间周日2:00）
0 2 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1

# 每日状态检查（北京时间6:00）
0 6 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/status_check.log 2>&1
```

**配置方案B：下午时段更新**

*UTC时间配置（推荐）：*
```bash
# 预计算系统定时任务配置（下午更新版 - UTC时间）
# 项目路径（请修改为实际路径）
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 每日下午更新（UTC 6:30 = 北京时间14:30）
30 6 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 每日状态检查（下午更新后，UTC 7:00 = 北京时间15:00）
0 7 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1

# 每周日凌晨完整重建（UTC 18:00 = 北京时间周一2:00）
0 18 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
```

*北京时间配置：*
```bash
# 预计算系统定时任务配置（下午更新版 - 北京时间）
# 项目路径（请修改为实际路径）
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 每日下午更新（北京时间14:30）
30 14 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 每日状态检查（下午更新后，北京时间15:00）
0 15 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1

# 每周日凌晨完整重建（北京时间周日2:00）
0 2 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
```

**配置方案C：双重保障更新**
```bash
# 预计算系统定时任务配置（双重更新版）
# 项目路径（请修改为实际路径）
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 主要更新：美股收盘后（北京时间5:30）
30 5 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/morning_update.log 2>&1

# 备用更新：下午时段（北京时间14:30）
30 14 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 状态检查：早上更新后
0 6 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/morning_status.log 2>&1

# 状态检查：下午更新后
0 15 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1

# 每周日凌晨完整重建
0 2 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
```

### 3. 高频配置（盘中更新）
```bash
# 交易时间内每小时更新（夏令时：21:30-4:30）
30 21-23 * * 1-5 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1
30 0-4 * * 2-6 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1

# 冬令时调整（22:30-5:30）
# 30 22-23 * * 1-5 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1
# 30 0-5 * * 2-6 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1
```

### 4. 完整配置（包含监控）
```bash
# ================================
# 预计算系统完整定时任务配置
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="/home/<USER>/breadth-pulse"

# 1. 日常更新任务
# 每日美股收盘后更新
30 5 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/daily_update.log 2>&1

# 2. 状态监控任务
# 每日状态检查
0 6 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/status_check.log 2>&1

# 每4小时检查一次数据新鲜度
0 */4 * * * cd $PROJECT_PATH && python -c "from core.precomputed_query import check_precomputed_data_status; status = check_precomputed_data_status(); print('Status:', status['status'])" >> logs/freshness_check.log 2>&1

# 3. 维护任务
# 每周日完整重建
0 2 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1

# 每月清理日志文件
0 3 1 * * find $PROJECT_PATH/logs -name "*.log" -mtime +30 -delete

# 4. 测试任务
# 每周一测试系统功能
0 7 * * 1 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --test >> logs/weekly_test.log 2>&1

# 5. 备份任务（如果需要）
# 每日备份预计算数据
0 4 * * * mysqldump -u username -p'password' database_name precomputed_ma_indicators precomputed_52w_indicators > $PROJECT_PATH/backups/precomputed_$(date +\%Y\%m\%d).sql 2>> logs/backup.log
```

## 🪟 Windows 任务计划程序配置

### 1. 创建基本任务

#### 步骤1：打开任务计划程序
- 按 `Win + R`，输入 `taskschd.msc`
- 或在开始菜单搜索"任务计划程序"

#### 步骤2：创建任务
1. 右键点击"任务计划程序库"
2. 选择"创建基本任务"
3. 输入任务名称：`预计算系统日更新`

#### 步骤3：设置触发器
- **触发器类型**: 每天
- **开始时间**: 05:30:00
- **重复间隔**: 不重复

#### 步骤4：设置操作
- **操作**: 启动程序
- **程序或脚本**: `python`
- **添加参数**: `scripts/setup_precomputed_system.py --update`
- **起始于**: `C:\Users\<USER>\Desktop\breadth-pulse`

### 2. PowerShell 脚本方式

创建 `update_precomputed.ps1` 文件：
```powershell
# 预计算系统更新脚本
$ProjectPath = "C:\Users\<USER>\Desktop\breadth-pulse"
$LogPath = "$ProjectPath\logs"

# 确保日志目录存在
if (!(Test-Path $LogPath)) {
    New-Item -ItemType Directory -Path $LogPath
}

# 切换到项目目录
Set-Location $ProjectPath

# 记录开始时间
$StartTime = Get-Date
Write-Output "[$StartTime] 开始预计算系统更新" | Out-File -Append "$LogPath\powershell_update.log"

try {
    # 执行更新
    python scripts/setup_precomputed_system.py --update 2>&1 | Out-File -Append "$LogPath\powershell_update.log"
    
    $EndTime = Get-Date
    $Duration = $EndTime - $StartTime
    Write-Output "[$EndTime] 更新完成，耗时: $($Duration.TotalSeconds) 秒" | Out-File -Append "$LogPath\powershell_update.log"
    
} catch {
    $ErrorTime = Get-Date
    Write-Output "[$ErrorTime] 更新失败: $($_.Exception.Message)" | Out-File -Append "$LogPath\powershell_update.log"
}
```

然后在任务计划程序中：
- **程序或脚本**: `powershell`
- **添加参数**: `-ExecutionPolicy Bypass -File "C:\Users\<USER>\Desktop\breadth-pulse\update_precomputed.ps1"`

## 🐳 Docker 环境配置

### 1. Dockerfile 添加 cron
```dockerfile
# 在现有 Dockerfile 基础上添加
RUN apt-get update && apt-get install -y cron

# 复制 crontab 文件
COPY crontab /etc/cron.d/precomputed-cron
RUN chmod 0644 /etc/cron.d/precomputed-cron
RUN crontab /etc/cron.d/precomputed-cron

# 启动 cron 服务
CMD cron && python your_main_app.py
```

### 2. crontab 文件内容
```bash
# Docker 环境下的 crontab 配置
30 5 * * * cd /app && python scripts/setup_precomputed_system.py --update >> logs/docker_update.log 2>&1
0 2 * * 0 cd /app && python scripts/setup_precomputed_system.py --init >> logs/docker_rebuild.log 2>&1
```

## 📊 监控脚本

### 1. 健康检查脚本
创建 `health_check.py`：
```python
#!/usr/bin/env python3
"""
预计算系统健康检查脚本
"""
import sys
import os
from datetime import datetime, timedelta

sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

def health_check():
    """执行健康检查"""
    try:
        from precomputed_query import check_precomputed_data_status
        
        status = check_precomputed_data_status()
        
        print(f"[{datetime.now()}] 健康检查结果:")
        print(f"  整体状态: {status['status']}")
        
        if status['status'] == 'ready':
            print("  ✅ 系统正常")
            return True
        else:
            print("  ❌ 系统异常")
            print(f"  详细信息: {status}")
            return False
            
    except Exception as e:
        print(f"[{datetime.now()}] 健康检查失败: {e}")
        return False

if __name__ == "__main__":
    success = health_check()
    sys.exit(0 if success else 1)
```

### 2. 告警脚本
创建 `alert_script.py`：
```python
#!/usr/bin/env python3
"""
预计算系统告警脚本
"""
import smtplib
from email.mime.text import MIMEText
from datetime import datetime

def send_alert(subject, message):
    """发送告警邮件"""
    try:
        # 邮件配置（请修改为实际配置）
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = "<EMAIL>"
        sender_password = "your_password"
        receiver_email = "<EMAIL>"
        
        msg = MIMEText(message)
        msg['Subject'] = f"[预计算系统告警] {subject}"
        msg['From'] = sender_email
        msg['To'] = receiver_email
        
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()
        
        print(f"[{datetime.now()}] 告警邮件已发送")
        
    except Exception as e:
        print(f"[{datetime.now()}] 发送告警邮件失败: {e}")

if __name__ == "__main__":
    # 检查系统状态
    from health_check import health_check
    
    if not health_check():
        send_alert(
            "系统异常",
            f"预计算系统在 {datetime.now()} 检测到异常，请及时处理。"
        )
```

## 🔧 故障恢复

### 自动恢复脚本
创建 `auto_recovery.sh`：
```bash
#!/bin/bash
# 预计算系统自动恢复脚本

PROJECT_PATH="/home/<USER>/breadth-pulse"
LOG_FILE="$PROJECT_PATH/logs/auto_recovery.log"

echo "[$(date)] 开始自动恢复检查" >> $LOG_FILE

cd $PROJECT_PATH

# 检查系统状态
python scripts/setup_precomputed_system.py --status > /tmp/status_check.txt 2>&1

if grep -q "ready" /tmp/status_check.txt; then
    echo "[$(date)] 系统状态正常" >> $LOG_FILE
else
    echo "[$(date)] 系统状态异常，开始恢复" >> $LOG_FILE
    
    # 尝试增量更新
    python scripts/setup_precomputed_system.py --update >> $LOG_FILE 2>&1
    
    # 再次检查
    python scripts/setup_precomputed_system.py --status > /tmp/status_check2.txt 2>&1
    
    if grep -q "ready" /tmp/status_check2.txt; then
        echo "[$(date)] 增量更新成功" >> $LOG_FILE
    else
        echo "[$(date)] 增量更新失败，执行完整重建" >> $LOG_FILE
        python scripts/setup_precomputed_system.py --init >> $LOG_FILE 2>&1
    fi
fi

# 清理临时文件
rm -f /tmp/status_check*.txt
```

在 crontab 中添加：
```bash
# 每2小时执行一次自动恢复检查
0 */2 * * * /home/<USER>/breadth-pulse/auto_recovery.sh
```

## 📝 日志管理

### 日志轮转配置
创建 `/etc/logrotate.d/precomputed-system`：
```
/home/<USER>/breadth-pulse/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 user user
}
```

---

**配置完成后，建议先手动测试一次，确保所有路径和权限设置正确。**
