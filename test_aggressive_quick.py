#!/usr/bin/env python3
"""
快速测试激进并行计算
只计算最近3天的数据，验证算法和并行性能
"""

import sys
import os
import time
import multiprocessing
import psutil
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, as_completed

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.hist_data import (
    get_markets_from_config, 
    get_stock_data_for_markets,
    calculate_breadth_metrics,
    get_companies,
    get_company_mcap,
    get_price,
    process_single_date
)

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()


def quick_aggressive_test():
    """快速激进并行测试"""
    print("🚀 快速激进并行计算测试\n")
    
    # 性能监控开始
    total_start_time = time.time()
    initial_memory = psutil.virtual_memory().used / 1024 / 1024 / 1024  # GB
    
    cpu_count = multiprocessing.cpu_count()
    print(f"💻 服务器配置：{cpu_count}核CPU，{psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB内存")
    print(f"📊 初始内存使用：{initial_memory:.1f}GB")
    
    # 激进配置
    if cpu_count >= 64:
        process_count = 48
        batch_size = 15
        stock_batch = 5000
        threads = 50
    elif cpu_count >= 32:
        process_count = 24
        batch_size = 12
        stock_batch = 3000
        threads = 30
    elif cpu_count >= 16:
        process_count = 12
        batch_size = 8
        stock_batch = 2000
        threads = 20
    else:
        process_count = max(4, cpu_count - 2)
        batch_size = 5
        stock_batch = 1000
        threads = 10
    
    print(f"🔥 激进配置：{process_count}进程，{stock_batch}股票/批，{threads}线程")
    
    # 获取市场数据
    markets = get_markets_from_config()
    print(f"📈 可用市场数：{len(markets)}")
    
    # 选择几个主要市场进行测试
    test_markets = ['SP500', 'NASDAQ100', 'RUSSELL2000']
    available_test_markets = [m for m in test_markets if m in markets]
    
    if not available_test_markets:
        available_test_markets = markets[:3]  # 使用前3个市场
    
    print(f"🎯 测试市场：{available_test_markets}")
    
    # 获取公司数据
    companies = get_companies(available_test_markets)
    company_mcap = get_company_mcap(companies)
    print(f"🏢 总股票数：{len(companies)}")
    
    # 生成最近3个工作日的日期
    end_date = datetime.now()
    work_dates = []
    current_date = end_date
    days_count = 0
    
    while days_count < 3:
        if current_date.weekday() < 5:  # 工作日
            work_dates.append(current_date)
            days_count += 1
        current_date -= timedelta(days=1)
    
    work_dates.reverse()
    print(f"📅 计算日期：{[d.strftime('%Y-%m-%d') for d in work_dates]}")
    
    # 下载历史数据
    print(f"\n📥 下载历史数据（{threads}线程）...")
    download_start = time.time()
    
    start_date = work_dates[0] - timedelta(days=30)  # 多下载一些数据确保计算准确
    company_price_data = get_price(
        companies,
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d')
    )
    
    download_time = time.time() - download_start
    print(f"✅ 数据下载完成，耗时 {download_time:.1f}秒")
    print(f"📊 成功下载 {len(company_price_data)} 只股票的数据")
    
    if not company_price_data:
        print("❌ 无法获取价格数据")
        return False
    
    # 准备多进程任务数据
    task_data = [(date, company_price_data, company_mcap, available_test_markets) for date in work_dates]
    
    print(f"\n🚀 开始激进并行计算（{process_count}进程）...")
    calc_start = time.time()
    
    all_results = []
    
    # 使用ProcessPoolExecutor进行多进程计算
    try:
        with ProcessPoolExecutor(max_workers=process_count) as executor:
            # 提交所有任务
            future_to_date = {
                executor.submit(process_single_date, task): task[0]
                for task in task_data
            }
            
            # 收集结果
            for future in as_completed(future_to_date):
                date = future_to_date[future]
                try:
                    results = future.result()
                    if results:
                        all_results.extend(results)
                        print(f"✅ {date.strftime('%Y-%m-%d')} 计算完成：{len(results)} 个市场")
                    else:
                        print(f"⚠️  {date.strftime('%Y-%m-%d')} 无结果")
                except Exception as e:
                    print(f"❌ {date.strftime('%Y-%m-%d')} 计算失败：{e}")
    
    except Exception as e:
        print(f"❌ 多进程计算失败：{e}")
        return False
    
    calc_time = time.time() - calc_start
    total_time = time.time() - total_start_time
    final_memory = psutil.virtual_memory().used / 1024 / 1024 / 1024
    memory_used = final_memory - initial_memory
    
    print(f"\n🎉 === 激进并行计算完成 ===")
    print(f"📊 总结果数：{len(all_results)}")
    print(f"⏱️  下载耗时：{download_time:.1f}秒")
    print(f"⚡ 计算耗时：{calc_time:.1f}秒")
    print(f"🕐 总耗时：{total_time:.1f}秒")
    print(f"💾 内存增量：{memory_used:.1f}GB")
    print(f"🔥 处理效率：{len(all_results)/calc_time:.1f} 记录/秒")
    
    # 显示一些结果样本
    if all_results:
        print(f"\n📈 结果样本：")
        for i, result in enumerate(all_results[:3]):
            print(f"  {i+1}. {result['market']} ({result['recorded_at'].strftime('%Y-%m-%d')}):")
            print(f"     股票数：{result['total_stocks']}")
            print(f"     涨跌：{result['advances']}/{result['declines']}")
            print(f"     纯度：{result['purity']:.3f}")
            print(f"     健康度：{result['internal_health']:.1f}")
            if result['momentum_coherence'] is not None:
                print(f"     一致性：{result['momentum_coherence']:.3f}")
            print(f"     背离：{result['divergence_type']}")
        
        # 统计改进算法的效果
        coherence_values = [r['momentum_coherence'] for r in all_results if r['momentum_coherence'] is not None]
        if coherence_values:
            avg_coherence = sum(coherence_values) / len(coherence_values)
            print(f"\n🧠 改进算法统计：")
            print(f"   平均动量一致性：{avg_coherence:.3f}")
            print(f"   有效计算率：{len(coherence_values)}/{len(all_results)} ({len(coherence_values)/len(all_results)*100:.1f}%)")
        
        # 背离检测统计
        divergence_types = {}
        for result in all_results:
            div_type = result['divergence_type']
            divergence_types[div_type] = divergence_types.get(div_type, 0) + 1
        
        print(f"\n🔍 背离检测统计：")
        for div_type, count in divergence_types.items():
            percentage = count / len(all_results) * 100
            print(f"   {div_type}：{count} ({percentage:.1f}%)")
    
    # 性能评估
    estimated_single_thread_time = calc_time * process_count
    speedup_ratio = estimated_single_thread_time / calc_time
    
    print(f"\n⚡ 性能评估：")
    print(f"   预估单线程耗时：{estimated_single_thread_time:.1f}秒")
    print(f"   实际多进程耗时：{calc_time:.1f}秒")
    print(f"   性能提升倍数：{speedup_ratio:.1f}x")
    print(f"   CPU利用率：{process_count/cpu_count*100:.1f}%")
    
    if cpu_count >= 64:
        print(f"🏆 64核服务器激进并行计算成功！")
    else:
        print(f"💪 {cpu_count}核服务器高效运行！")
    
    return True


def main():
    """主函数"""
    try:
        success = quick_aggressive_test()
        
        if success:
            print(f"\n✅ 快速测试成功！激进并行计算配置正常")
            print(f"🚀 可以运行完整版本：python core/hist_data.py")
        else:
            print(f"\n❌ 快速测试失败，需要检查配置")
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
