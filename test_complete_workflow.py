#!/usr/bin/env python3
"""
完整工作流程测试
模拟 web 界面的完整调用流程，确保所有组件正常工作
"""

import sys
import os
import tempfile
import importlib.util
import traceback
from datetime import datetime
import pandas as pd
import numpy as np

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'market-breadth-task'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'docs'))

def serialize_timestamp(obj):
    """序列化时间戳对象"""
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    elif hasattr(obj, 'strftime'):
        return obj.strftime('%Y-%m-%d %H:%M:%S')
    else:
        return str(obj)

def test_v41_system():
    """测试 v41 系统"""
    print("🧪 测试 v41 系统...")
    
    try:
        # 导入v41系统
        v41_path = os.path.join(os.path.dirname(__file__), 'docs', '多时间框架板块轮动与MarketBreadth系统_v41.py')
        spec = importlib.util.spec_from_file_location("v41_system", v41_path)
        v41_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(v41_module)
        
        print("✅ v41 系统导入成功")
        
        # 创建模拟数据
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        mock_prices = pd.DataFrame({
            'AAPL': np.random.randn(100).cumsum() + 150,
            'MSFT': np.random.randn(100).cumsum() + 300,
            'GOOGL': np.random.randn(100).cumsum() + 2500,
        }, index=dates)
        
        mock_volumes = pd.DataFrame({
            'AAPL': np.random.randint(1000000, 10000000, 100),
            'MSFT': np.random.randint(1000000, 10000000, 100),
            'GOOGL': np.random.randint(500000, 5000000, 100),
        }, index=dates)
        
        # 创建模拟板块广度数据
        mock_sectors_breadth = {}
        for sector_name in ['Technology', 'Healthcare']:
            mock_sectors_breadth[sector_name] = v41_module.SectorBreadthData(
                sector_name=sector_name,
                timestamp=datetime.now(),
                advances=np.random.randint(30, 80),
                declines=np.random.randint(20, 70),
                unchanged=np.random.randint(0, 10),
                total_stocks=100,
                advancing_volume=float(np.random.randint(1000000, 10000000)),
                declining_volume=float(np.random.randint(1000000, 10000000)),
                new_highs_52w=np.random.randint(0, 20),
                new_lows_52w=np.random.randint(0, 15),
                above_ma50=np.random.randint(20, 80),
                above_ma200=np.random.randint(15, 70),
                avg_rsi=np.random.uniform(30, 70),
                individual_returns=[np.random.uniform(-0.05, 0.05) for _ in range(10)]
            )
        
        daily_data = {
            'prices': mock_prices,
            'volumes': mock_volumes,
            'sectors_breadth': mock_sectors_breadth
        }
        
        print("✅ 模拟数据创建成功")
        
        # 调用 v41 分析
        print("🔍 调用 v41 分析...")
        original_report = v41_module.analyze_market_mtf(
            daily_data=daily_data,
            output_format='full'
        )
        
        print("✅ v41 分析完成")
        print(f"报告类型: {type(original_report)}")
        
        # 检查报告内容
        if isinstance(original_report, dict):
            print("✅ 报告是字典格式")
            for key in original_report.keys():
                print(f"  - {key}: {type(original_report[key])}")
        else:
            print(f"⚠️ 报告不是字典格式: {original_report}")
        
        return True, original_report
        
    except Exception as e:
        print(f"❌ v41 系统测试失败: {e}")
        print(f"错误类型: {type(e)}")
        traceback.print_exc()
        return False, None

def test_enhanced_system_with_real_data(original_report, sector):
    """使用真实数据测试增强版系统"""
    print("\n🧪 测试增强版系统（使用真实数据）...")
    
    try:
        # 导入增强版组件
        from integrated_market_analysis_system import IntegratedMarketAnalysisSystem
        
        print("✅ 增强版组件导入成功")
        
        # 初始化系统
        config_dir = tempfile.mkdtemp(prefix="test_enhanced_")
        enhanced_system = IntegratedMarketAnalysisSystem(config_dir=config_dir)
        
        print("✅ 增强版系统初始化成功")
        
        # 数据转换
        print("🔍 转换数据格式...")
        
        market_data = {
            'market_volatility': 0.2,
            'sectors': {},
            'market_indicators': {}
        }
        
        # 从原版报告中提取数据
        if isinstance(original_report, dict):
            if 'breadth_analysis' in original_report:
                breadth = original_report['breadth_analysis']
                market_data['sectors'][sector] = {
                    'price_change': breadth.get('price_change', 0.0),
                    'volume_ratio': breadth.get('volume_ratio', 1.0),
                    'breadth_score': breadth.get('internal_health', 50) / 100.0
                }
            
            if 'rotation_analysis' in original_report:
                rotation = original_report['rotation_analysis']
                if 'unified_rii' in rotation:
                    rii = rotation['unified_rii']
                    market_data['market_volatility'] = min(0.5, max(0.1, abs(rii - 0.5) * 2))
        
        # 确保有基本数据
        if not market_data['sectors']:
            market_data['sectors'][sector] = {
                'price_change': 0.01,
                'volume_ratio': 1.0,
                'breadth_score': 0.5
            }
        
        if not market_data['market_indicators']:
            market_data['market_indicators'] = {
                'advance_decline_ratio': 1.0,
                'new_highs_lows_ratio': 1.0
            }
        
        print("✅ 数据转换完成")
        print(f"转换后数据: {market_data}")
        
        # 执行增强版分析
        print("🔍 执行增强版分析...")
        enhanced_result = enhanced_system.analyze_market(market_data)
        
        print("✅ 增强版分析完成")
        print(f"结果类型: {type(enhanced_result)}")
        print(f"分析时间戳: {enhanced_result.analysis_timestamp}")
        print(f"时间戳类型: {type(enhanced_result.analysis_timestamp)}")
        
        # 测试结果合并
        print("🔍 测试结果合并...")
        
        # 序列化时间戳
        enhanced_timestamp = serialize_timestamp(enhanced_result.analysis_timestamp)
        print(f"序列化时间戳: {enhanced_timestamp}")
        
        # 合并结果
        merged_result = {
            'original_analysis': original_report,
            'enhanced_analysis': {
                'analysis_timestamp': enhanced_timestamp,
                'market_regime': enhanced_result.market_regime,
                'analysis_confidence': enhanced_result.analysis_confidence,
                'unified_decision': enhanced_result.unified_decision,
                'performance_metrics': enhanced_result.performance_metrics
            },
            'integration_info': {
                'enhanced_available': True,
                'data_source': 'real_market_data',
                'analysis_method': 'integrated_enhanced'
            }
        }
        
        print("✅ 结果合并完成")
        
        # 测试JSON序列化
        print("🔍 测试JSON序列化...")
        import json
        
        try:
            json_str = json.dumps(merged_result, indent=2, default=str)
            print("✅ JSON序列化成功")
            print(f"JSON长度: {len(json_str)} 字符")
        except Exception as json_error:
            print(f"❌ JSON序列化失败: {json_error}")
            return False, None
        
        return True, merged_result
        
    except Exception as e:
        print(f"❌ 增强版系统测试失败: {e}")
        print(f"错误类型: {type(e)}")
        traceback.print_exc()
        return False, None

def test_complete_workflow():
    """测试完整工作流程"""
    print("🧪 完整工作流程测试")
    print("=" * 60)
    
    sector = "Information Technology"
    timeframes = ["5m", "15m", "1h"]
    output_format = "full"
    
    print(f"测试参数:")
    print(f"  板块: {sector}")
    print(f"  时间框架: {timeframes}")
    print(f"  输出格式: {output_format}")
    print()
    
    # 1. 测试 v41 系统
    v41_success, original_report = test_v41_system()
    if not v41_success:
        print("❌ v41 系统测试失败，无法继续")
        return False
    
    # 2. 测试增强版系统
    enhanced_success, merged_result = test_enhanced_system_with_real_data(original_report, sector)
    if not enhanced_success:
        print("❌ 增强版系统测试失败")
        return False
    
    # 3. 测试完整流程
    print("\n🧪 测试完整流程...")
    
    try:
        # 模拟 web 界面的完整调用
        print("🔍 模拟 web 界面调用...")
        
        # 这里应该返回与 web 界面相同的结果
        final_result = {
            'status': 'success',
            'report': merged_result,
            'sector': sector,
            'timeframes': timeframes,
            'timestamp': datetime.now().isoformat()
        }
        
        print("✅ 完整流程测试成功")
        print(f"最终结果类型: {type(final_result)}")
        
        # 最终JSON测试
        import json
        json_str = json.dumps(final_result, indent=2, default=str)
        print(f"✅ 最终JSON序列化成功，长度: {len(json_str)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 完整工作流程测试")
    print("模拟 web 界面的完整调用流程")
    print("=" * 60)
    
    success = test_complete_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！")
        print("✅ web 界面应该能正常工作")
        print("🌐 现在可以启动 web 界面进行测试")
    else:
        print("❌ 测试失败，需要修复问题")
        print("⚠️ 不建议启动 web 界面")
    
    return success

if __name__ == "__main__":
    main()
