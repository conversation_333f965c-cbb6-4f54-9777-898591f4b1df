<!DOCTYPE html>
<html>
<head>
    <title>Web界面修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Web界面JavaScript修复测试</h1>
    
    <div class="test-section">
        <h3>测试1: operation_guidance 数据处理</h3>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: risk_assessment 数据处理</h3>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 完整数据结构处理</h3>
        <div id="test3-result"></div>
    </div>

    <script>
        // 复制Web界面中的修复后的函数
        function generateUnifiedDecisionDisplay(unifiedData) {
            if (!unifiedData) return '';
            
            // 安全处理 operation_guidance (它是一个对象，不是数组)
            let operationGuidance = '';
            if (unifiedData.operation_guidance) {
                const guidance = unifiedData.operation_guidance;
                let guidanceItems = [];
                
                if (guidance.entry_timing) {
                    guidanceItems.push(`入场时机: ${guidance.entry_timing}`);
                }
                if (guidance.exit_strategy) {
                    guidanceItems.push(`退出策略: ${guidance.exit_strategy}`);
                }
                if (guidance.execution) {
                    guidanceItems.push(`执行建议: ${guidance.execution}`);
                }
                if (guidance.key_points && Array.isArray(guidance.key_points)) {
                    guidanceItems.push(...guidance.key_points.slice(0, 2));
                } else if (guidance.key_points && typeof guidance.key_points === 'string') {
                    guidanceItems.push(guidance.key_points);
                }
                
                operationGuidance = guidanceItems.length > 0 
                    ? guidanceItems.slice(0, 3).map(item => `• ${item}`).join('<br>')
                    : '• 暂无具体指导';
            } else {
                operationGuidance = '• 暂无操作指导';
            }
            
            let html = `
                <div class="mtf-summary">
                    <h4>🎯 多时间框架统一决策</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div>
                            <strong>仓位建议:</strong> ${unifiedData.position_recommendation?.suggested || 'N/A'}<br>
                            <strong>置信度:</strong> ${((unifiedData.position_recommendation?.confidence || 0) * 100).toFixed(1)}%<br>
                            <strong>建议仓位:</strong> ${unifiedData.position_recommendation?.position_size || 'N/A'}%
                        </div>
                        <div>
                            <strong>操作指导:</strong><br>
                            <div style="font-size: 0.9em; margin-top: 5px;">
                                ${operationGuidance}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            return html;
        }

        function generateRiskAssessmentDisplay(riskData) {
            if (!riskData) return '';
            
            // 安全处理 risk_factors
            let riskFactors = 'N/A';
            if (riskData.risk_factors) {
                if (Array.isArray(riskData.risk_factors)) {
                    riskFactors = riskData.risk_factors.slice(0, 3).join(', ') || 'N/A';
                } else if (typeof riskData.risk_factors === 'string') {
                    riskFactors = riskData.risk_factors;
                }
            }
            
            // 安全处理 mitigation_strategies
            let mitigationStrategies = 'N/A';
            if (riskData.mitigation_strategies) {
                if (Array.isArray(riskData.mitigation_strategies)) {
                    mitigationStrategies = riskData.mitigation_strategies.slice(0, 2).join('; ') || 'N/A';
                } else if (typeof riskData.mitigation_strategies === 'string') {
                    mitigationStrategies = riskData.mitigation_strategies;
                }
            }
            
            let html = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 15px 0;">
                    <h4>⚠️ 风险评估</h4>
                    <div><strong>整体风险:</strong> ${riskData.overall_risk?.level || 'N/A'} (${((riskData.overall_risk?.score || 0) * 100).toFixed(1)}%)</div>
                    <div><strong>主要风险:</strong> ${riskFactors}</div>
                    <div><strong>风险建议:</strong> ${mitigationStrategies}</div>
                </div>
            `;
            
            return html;
        }

        // 测试数据
        const testData = {
            unified_decision: {
                position_recommendation: {
                    suggested: '40-60%',
                    confidence: 0.65,
                    position_size: '50%'
                },
                operation_guidance: {
                    entry_timing: '等待回调至支撑位',
                    exit_strategy: '分批止盈',
                    execution: '分时间框架执行',
                    key_points: ['关注成交量配合', '注意风险控制']
                }
            },
            risk_assessment: {
                overall_risk: {
                    level: 'medium',
                    score: 0.6
                },
                risk_factors: ['市场波动加大', '板块轮动加速', '流动性风险'],
                mitigation_strategies: ['分散投资', '控制仓位']
            }
        };

        // 测试异常数据
        const badTestData = {
            unified_decision: {
                position_recommendation: {
                    suggested: '40-60%',
                    confidence: 0.65,
                    position_size: '50%'
                },
                operation_guidance: {
                    entry_timing: '等待回调至支撑位',
                    exit_strategy: '分批止盈',
                    execution: '分时间框架执行',
                    key_points: '这是一个字符串而不是数组'  // 故意设置错误类型
                }
            },
            risk_assessment: {
                overall_risk: {
                    level: 'medium',
                    score: 0.6
                },
                risk_factors: '这是一个字符串而不是数组',  // 故意设置错误类型
                mitigation_strategies: '这也是一个字符串'  // 故意设置错误类型
            }
        };

        // 执行测试
        function runTests() {
            // 测试1: 正常数据
            try {
                const result1 = generateUnifiedDecisionDisplay(testData.unified_decision);
                document.getElementById('test1-result').innerHTML = 
                    '<div class="success">✅ 测试通过</div>' + result1;
            } catch (error) {
                document.getElementById('test1-result').innerHTML = 
                    `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }

            // 测试2: 正常数据
            try {
                const result2 = generateRiskAssessmentDisplay(testData.risk_assessment);
                document.getElementById('test2-result').innerHTML = 
                    '<div class="success">✅ 测试通过</div>' + result2;
            } catch (error) {
                document.getElementById('test2-result').innerHTML = 
                    `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }

            // 测试3: 异常数据处理
            try {
                const result3a = generateUnifiedDecisionDisplay(badTestData.unified_decision);
                const result3b = generateRiskAssessmentDisplay(badTestData.risk_assessment);
                document.getElementById('test3-result').innerHTML = 
                    '<div class="success">✅ 异常数据处理测试通过</div>' + 
                    '<h5>异常数据处理结果:</h5>' + result3a + result3b;
            } catch (error) {
                document.getElementById('test3-result').innerHTML = 
                    `<div class="error">❌ 异常数据处理失败: ${error.message}</div>`;
            }
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
