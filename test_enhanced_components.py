#!/usr/bin/env python3
"""
测试增强版组件，隔离时间戳问题
"""

import sys
import os
import tempfile
import importlib.util
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'market-breadth-task'))

def test_enhanced_system():
    """测试增强版系统"""
    try:
        print("🧪 开始测试增强版组件...")
        
        # 导入集成系统
        from integrated_market_analysis_system import IntegratedMarketAnalysisSystem
        
        print("✅ 成功导入 IntegratedMarketAnalysisSystem")
        
        # 初始化系统
        config_dir = tempfile.mkdtemp(prefix="test_enhanced_")
        enhanced_system = IntegratedMarketAnalysisSystem(config_dir=config_dir)
        
        print("✅ 成功初始化增强系统")
        
        # 准备测试数据
        market_data = {
            'market_volatility': 0.2,
            'sectors': {
                'Information Technology': {
                    'price_change': 0.01,
                    'volume_ratio': 1.0,
                    'breadth_score': 0.5
                }
            },
            'market_indicators': {
                'advance_decline_ratio': 1.0,
                'new_highs_lows_ratio': 1.0
            }
        }
        
        print("✅ 准备测试数据完成")
        print(f"测试数据: {market_data}")
        
        # 执行分析
        print("🔍 开始执行分析...")
        result = enhanced_system.analyze_market(market_data)
        
        print("✅ 分析执行成功")
        print(f"结果类型: {type(result)}")
        print(f"分析时间戳: {result.analysis_timestamp}")
        print(f"分析时间戳类型: {type(result.analysis_timestamp)}")
        print(f"市场状态: {result.market_regime}")
        print(f"分析置信度: {result.analysis_confidence}")
        
        # 测试时间戳序列化
        print("🔍 测试时间戳序列化...")
        
        if hasattr(result.analysis_timestamp, 'isoformat'):
            iso_format = result.analysis_timestamp.isoformat()
            print(f"✅ isoformat() 成功: {iso_format}")
        else:
            print(f"❌ 没有 isoformat() 方法")
        
        if hasattr(result.analysis_timestamp, 'strftime'):
            str_format = result.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')
            print(f"✅ strftime() 成功: {str_format}")
        else:
            print(f"❌ 没有 strftime() 方法")
        
        # 测试JSON序列化
        print("🔍 测试JSON序列化...")
        import json
        
        try:
            # 尝试序列化整个结果
            result_dict = {
                'analysis_timestamp': result.analysis_timestamp.isoformat(),
                'market_regime': result.market_regime,
                'analysis_confidence': result.analysis_confidence,
                'unified_decision': result.unified_decision,
                'performance_metrics': result.performance_metrics
            }
            
            json_str = json.dumps(result_dict, indent=2)
            print("✅ JSON序列化成功")
            print(f"JSON长度: {len(json_str)} 字符")
            
        except Exception as json_error:
            print(f"❌ JSON序列化失败: {json_error}")
            print(f"错误类型: {type(json_error)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_timestamp_handling():
    """测试时间戳处理"""
    print("\n🧪 测试时间戳处理...")
    
    try:
        from datetime import datetime
        import pandas as pd
        
        # 测试不同类型的时间戳
        dt_now = datetime.now()
        pd_timestamp = pd.Timestamp.now()
        
        print(f"datetime.now(): {dt_now} (类型: {type(dt_now)})")
        print(f"pd.Timestamp.now(): {pd_timestamp} (类型: {type(pd_timestamp)})")
        
        # 测试序列化
        print("🔍 测试序列化...")
        
        try:
            dt_iso = dt_now.isoformat()
            print(f"✅ datetime isoformat: {dt_iso}")
        except Exception as e:
            print(f"❌ datetime isoformat 失败: {e}")
        
        try:
            pd_iso = pd_timestamp.isoformat()
            print(f"✅ pandas timestamp isoformat: {pd_iso}")
        except Exception as e:
            print(f"❌ pandas timestamp isoformat 失败: {e}")
        
        # 测试字符串转换
        try:
            dt_str = str(dt_now)
            print(f"✅ datetime str: {dt_str}")
        except Exception as e:
            print(f"❌ datetime str 失败: {e}")
        
        try:
            pd_str = str(pd_timestamp)
            print(f"✅ pandas timestamp str: {pd_str}")
        except Exception as e:
            print(f"❌ pandas timestamp str 失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间戳测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 增强版组件测试")
    print("=" * 50)
    
    # 测试时间戳处理
    timestamp_ok = test_timestamp_handling()
    
    # 测试增强版系统
    enhanced_ok = test_enhanced_system()
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print(f"时间戳处理: {'✅ 通过' if timestamp_ok else '❌ 失败'}")
    print(f"增强版系统: {'✅ 通过' if enhanced_ok else '❌ 失败'}")
    
    if timestamp_ok and enhanced_ok:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    main()
