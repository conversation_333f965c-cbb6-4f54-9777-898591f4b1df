#!/usr/bin/env python3
"""
OPT-T5.3 系统集成测试
综合测试脚本，验证所有优化组件的集成效果
包含算法正确性验证、决策透明度测试、性能基准测试、边界条件测试
"""

import sys
import os
import time
import tempfile
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import importlib.util
import logging

# 设置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出
logger = logging.getLogger(__name__)

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))


class OptimizationIntegrationTester:
    """优化集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        self.performance_benchmarks = {}
        self.error_log = []
        self.temp_dir = None
        
        # 测试数据
        self.test_scenarios = self._create_test_scenarios()
        
        # 初始化组件
        self._initialize_test_components()
    
    def _initialize_test_components(self):
        """初始化测试组件"""
        try:
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp()
            
            # 初始化集成系统
            integration_path = os.path.join(os.path.dirname(__file__), 'market-breadth-task', 'integrated_market_analysis_system.py')
            spec = importlib.util.spec_from_file_location("integrated_market_analysis_system", integration_path)
            integration_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(integration_module)
            
            self.IntegratedSystem = integration_module.IntegratedMarketAnalysisSystem
            
            # 初始化配置管理器
            config_path = os.path.join(os.path.dirname(__file__), 'market-breadth-task', 'enhanced_config_manager.py')
            spec = importlib.util.spec_from_file_location("enhanced_config_manager", config_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            self.ConfigManager = config_module.EnhancedConfigManager
            
            print("✅ 测试组件初始化完成")
            
        except Exception as e:
            print(f"❌ 测试组件初始化失败: {e}")
            self.error_log.append(f"组件初始化失败: {e}")
    
    def _create_test_scenarios(self) -> List[Dict[str, Any]]:
        """创建测试场景"""
        return [
            {
                'name': '牛市稳定场景',
                'market_data': {
                    'market_volatility': 0.12,
                    'sectors': {
                        'Technology': {'price_change': 0.025, 'volume_ratio': 1.3, 'breadth_score': 0.85},
                        'Healthcare': {'price_change': 0.018, 'volume_ratio': 1.2, 'breadth_score': 0.78},
                        'Finance': {'price_change': 0.015, 'volume_ratio': 1.1, 'breadth_score': 0.72},
                        'Consumer': {'price_change': 0.012, 'volume_ratio': 1.0, 'breadth_score': 0.68}
                    },
                    'market_indicators': {'advance_decline_ratio': 2.1, 'new_highs_lows_ratio': 3.2}
                },
                'expected_regime': 'trending_stable',
                'expected_position_range': (70, 90),
                'expected_action': '增仓'
            },
            {
                'name': '熊市压力场景',
                'market_data': {
                    'market_volatility': 0.45,
                    'sectors': {
                        'Technology': {'price_change': -0.035, 'volume_ratio': 1.5, 'breadth_score': 0.25},
                        'Healthcare': {'price_change': -0.015, 'volume_ratio': 1.2, 'breadth_score': 0.45},
                        'Finance': {'price_change': -0.042, 'volume_ratio': 1.8, 'breadth_score': 0.18},
                        'Energy': {'price_change': -0.055, 'volume_ratio': 2.1, 'breadth_score': 0.12}
                    },
                    'market_indicators': {'advance_decline_ratio': 0.3, 'new_highs_lows_ratio': 0.1}
                },
                'expected_regime': 'short_term_stress',
                'expected_position_range': (10, 40),
                'expected_action': '减仓'
            },
            {
                'name': '高轮动场景',
                'market_data': {
                    'market_volatility': 0.32,
                    'sectors': {
                        'Technology': {'price_change': 0.028, 'volume_ratio': 1.8, 'breadth_score': 0.82},
                        'Energy': {'price_change': 0.045, 'volume_ratio': 2.2, 'breadth_score': 0.75},
                        'Finance': {'price_change': -0.018, 'volume_ratio': 0.8, 'breadth_score': 0.35},
                        'Materials': {'price_change': 0.032, 'volume_ratio': 1.6, 'breadth_score': 0.68}
                    },
                    'market_indicators': {'advance_decline_ratio': 1.1, 'new_highs_lows_ratio': 1.3}
                },
                'expected_regime': 'high_rotation',
                'expected_position_range': (50, 75),
                'expected_action': '维持'
            },
            {
                'name': '正常市场场景',
                'market_data': {
                    'market_volatility': 0.22,
                    'sectors': {
                        'Technology': {'price_change': 0.008, 'volume_ratio': 1.05, 'breadth_score': 0.62},
                        'Healthcare': {'price_change': 0.005, 'volume_ratio': 0.98, 'breadth_score': 0.58},
                        'Finance': {'price_change': 0.003, 'volume_ratio': 1.02, 'breadth_score': 0.55},
                        'Consumer': {'price_change': 0.006, 'volume_ratio': 1.01, 'breadth_score': 0.60}
                    },
                    'market_indicators': {'advance_decline_ratio': 1.15, 'new_highs_lows_ratio': 1.25}
                },
                'expected_regime': 'normal_market',
                'expected_position_range': (45, 70),
                'expected_action': '维持'
            },
            {
                'name': '边界条件场景',
                'market_data': {
                    'market_volatility': 0.0,  # 极低波动
                    'sectors': {
                        'OnlySector': {'price_change': 0.0, 'volume_ratio': 1.0, 'breadth_score': 0.5}
                    },
                    'market_indicators': {'advance_decline_ratio': 1.0, 'new_highs_lows_ratio': 1.0}
                },
                'expected_regime': 'trending_stable',
                'expected_position_range': (40, 80),
                'expected_action': None  # 任何操作都可接受
            }
        ]
    
    def run_algorithm_correctness_tests(self) -> Dict[str, Any]:
        """运行算法正确性验证测试"""
        print("\n🧪 === 算法正确性验证测试 ===")
        
        test_results = {
            'total_scenarios': len(self.test_scenarios),
            'passed_scenarios': 0,
            'failed_scenarios': [],
            'scenario_results': {}
        }
        
        system = self.IntegratedSystem(config_dir=self.temp_dir)
        
        for i, scenario in enumerate(self.test_scenarios, 1):
            print(f"\n--- 场景 {i}: {scenario['name']} ---")
            
            try:
                # 执行分析
                result = system.analyze_market(scenario['market_data'])
                
                # 验证市场状态检测
                detected_regime = result.market_regime
                expected_regime = scenario['expected_regime']
                regime_correct = detected_regime == expected_regime
                
                print(f"市场状态: 检测={detected_regime}, 期望={expected_regime} {'✅' if regime_correct else '❌'}")
                
                # 验证仓位建议
                suggested_position = result.unified_decision.get('suggested_position', 0)
                expected_range = scenario['expected_position_range']
                position_correct = expected_range[0] <= suggested_position <= expected_range[1]
                
                print(f"建议仓位: {suggested_position}%, 期望范围={expected_range} {'✅' if position_correct else '❌'}")
                
                # 验证操作建议
                action = result.unified_decision.get('action', '')
                expected_action = scenario['expected_action']
                action_correct = expected_action is None or action == expected_action
                
                print(f"操作建议: {action}, 期望={expected_action} {'✅' if action_correct else '❌'}")
                
                # 验证分析置信度
                confidence = result.analysis_confidence
                confidence_reasonable = 0.1 <= confidence <= 1.0
                
                print(f"分析置信度: {confidence:.3f} {'✅' if confidence_reasonable else '❌'}")
                
                # 综合评估
                scenario_passed = all([regime_correct, position_correct, action_correct, confidence_reasonable])
                
                if scenario_passed:
                    test_results['passed_scenarios'] += 1
                    print(f"场景结果: ✅ 通过")
                else:
                    test_results['failed_scenarios'].append(scenario['name'])
                    print(f"场景结果: ❌ 失败")
                
                test_results['scenario_results'][scenario['name']] = {
                    'passed': scenario_passed,
                    'regime_correct': regime_correct,
                    'position_correct': position_correct,
                    'action_correct': action_correct,
                    'confidence_reasonable': confidence_reasonable,
                    'detected_regime': detected_regime,
                    'suggested_position': suggested_position,
                    'action': action,
                    'confidence': confidence
                }
                
            except Exception as e:
                print(f"场景执行失败: ❌ {e}")
                test_results['failed_scenarios'].append(scenario['name'])
                self.error_log.append(f"场景 {scenario['name']} 执行失败: {e}")
        
        # 计算通过率
        pass_rate = test_results['passed_scenarios'] / test_results['total_scenarios']
        test_results['pass_rate'] = pass_rate
        
        print(f"\n📊 算法正确性测试结果:")
        print(f"通过场景: {test_results['passed_scenarios']}/{test_results['total_scenarios']}")
        print(f"通过率: {pass_rate:.1%}")
        
        if test_results['failed_scenarios']:
            print(f"失败场景: {test_results['failed_scenarios']}")
        
        return test_results
    
    def run_decision_transparency_tests(self) -> Dict[str, Any]:
        """运行决策透明度测试"""
        print("\n🔍 === 决策透明度测试 ===")
        
        test_results = {
            'transparency_score': 0.0,
            'required_fields_present': {},
            'decision_traceability': False,
            'explanation_quality': 0.0
        }
        
        system = self.IntegratedSystem(config_dir=self.temp_dir)
        
        # 使用第一个测试场景
        test_scenario = self.test_scenarios[0]
        result = system.analyze_market(test_scenario['market_data'])
        
        # 检查必需字段
        required_fields = [
            'analysis_timestamp', 'market_regime', 'analysis_confidence',
            'unified_decision', 'performance_metrics', 'config_summary'
        ]
        
        print(f"📋 必需字段检查:")
        for field in required_fields:
            present = hasattr(result, field)
            test_results['required_fields_present'][field] = present
            status = "✅" if present else "❌"
            print(f"  {field}: {status}")
        
        # 检查决策可追溯性
        decision = result.unified_decision
        traceability_elements = [
            'decision_factors', 'signal_strength', 'confidence', 'risk_level'
        ]
        
        print(f"\n🔗 决策可追溯性检查:")
        traceability_count = 0
        for element in traceability_elements:
            present = element in decision
            if present:
                traceability_count += 1
            status = "✅" if present else "❌"
            print(f"  {element}: {status}")
        
        test_results['decision_traceability'] = traceability_count >= len(traceability_elements) * 0.75
        
        # 评估解释质量
        explanation_score = 0.0
        
        # 检查决策因素的详细程度
        if 'decision_factors' in decision:
            factors = decision['decision_factors']
            if isinstance(factors, list) and len(factors) >= 2:
                explanation_score += 0.3
                print(f"  决策因素数量: {len(factors)} ✅")
            else:
                print(f"  决策因素数量: {len(factors) if isinstance(factors, list) else 0} ❌")
        
        # 检查置信度的合理性
        if 'confidence' in decision:
            confidence = decision['confidence']
            if 0.1 <= confidence <= 1.0:
                explanation_score += 0.3
                print(f"  置信度合理性: {confidence:.3f} ✅")
            else:
                print(f"  置信度合理性: {confidence:.3f} ❌")
        
        # 检查风险评估
        if 'risk_level' in decision:
            risk_level = decision['risk_level']
            valid_levels = ['low', 'low_medium', 'medium', 'medium_high', 'high']
            if risk_level in valid_levels:
                explanation_score += 0.4
                print(f"  风险评估有效性: {risk_level} ✅")
            else:
                print(f"  风险评估有效性: {risk_level} ❌")
        
        test_results['explanation_quality'] = explanation_score
        
        # 计算总体透明度评分
        field_score = sum(test_results['required_fields_present'].values()) / len(required_fields)
        traceability_score = 1.0 if test_results['decision_traceability'] else 0.0
        
        transparency_score = (field_score * 0.4 + traceability_score * 0.3 + explanation_score * 0.3)
        test_results['transparency_score'] = transparency_score
        
        print(f"\n📊 决策透明度评分:")
        print(f"字段完整性: {field_score:.1%}")
        print(f"可追溯性: {traceability_score:.1%}")
        print(f"解释质量: {explanation_score:.1%}")
        print(f"总体透明度: {transparency_score:.1%}")
        
        return test_results
    
    def run_performance_benchmark_tests(self) -> Dict[str, Any]:
        """运行性能基准测试"""
        print("\n⚡ === 性能基准测试 ===")
        
        test_results = {
            'single_analysis_time': 0.0,
            'batch_analysis_time': 0.0,
            'memory_usage': 0.0,
            'throughput': 0.0,
            'performance_grade': 'D'
        }
        
        system = self.IntegratedSystem(config_dir=self.temp_dir)
        test_scenario = self.test_scenarios[0]
        
        # 单次分析性能测试
        print(f"📊 单次分析性能测试:")
        
        start_time = time.time()
        result = system.analyze_market(test_scenario['market_data'])
        single_time = time.time() - start_time
        
        test_results['single_analysis_time'] = single_time
        print(f"单次分析时间: {single_time:.4f}s")
        
        # 批量分析性能测试
        print(f"\n📊 批量分析性能测试 (10次):")
        
        batch_times = []
        start_time = time.time()
        
        for i in range(10):
            batch_start = time.time()
            system.analyze_market(test_scenario['market_data'])
            batch_time = time.time() - batch_start
            batch_times.append(batch_time)
        
        total_batch_time = time.time() - start_time
        avg_batch_time = np.mean(batch_times)
        throughput = 10 / total_batch_time
        
        test_results['batch_analysis_time'] = avg_batch_time
        test_results['throughput'] = throughput
        
        print(f"平均分析时间: {avg_batch_time:.4f}s")
        print(f"总批量时间: {total_batch_time:.4f}s")
        print(f"吞吐量: {throughput:.2f} 分析/秒")
        
        # 性能等级评估
        if avg_batch_time < 0.1:
            grade = 'A+'
        elif avg_batch_time < 0.5:
            grade = 'A'
        elif avg_batch_time < 1.0:
            grade = 'B'
        elif avg_batch_time < 2.0:
            grade = 'C'
        else:
            grade = 'D'
        
        test_results['performance_grade'] = grade
        
        print(f"\n📈 性能评估:")
        print(f"性能等级: {grade}")
        
        # 性能基准对比
        benchmarks = {
            'excellent': 0.1,
            'good': 0.5,
            'acceptable': 1.0,
            'poor': 2.0
        }
        
        print(f"性能基准对比:")
        for level, threshold in benchmarks.items():
            status = "✅" if avg_batch_time <= threshold else "❌"
            print(f"  {level} (<{threshold}s): {status}")
        
        return test_results

    def run_boundary_condition_tests(self) -> Dict[str, Any]:
        """运行边界条件测试"""
        print("\n🔬 === 边界条件测试 ===")

        test_results = {
            'boundary_tests_passed': 0,
            'boundary_tests_total': 0,
            'error_handling_score': 0.0,
            'robustness_score': 0.0
        }

        system = self.IntegratedSystem(config_dir=self.temp_dir)

        # 边界条件测试用例
        boundary_tests = [
            {
                'name': '空数据测试',
                'data': {},
                'should_handle_gracefully': True
            },
            {
                'name': '极值波动率测试',
                'data': {'market_volatility': 999.0, 'sectors': {}},
                'should_handle_gracefully': True
            },
            {
                'name': '负波动率测试',
                'data': {'market_volatility': -0.5, 'sectors': {}},
                'should_handle_gracefully': True
            },
            {
                'name': '单板块测试',
                'data': {
                    'market_volatility': 0.2,
                    'sectors': {
                        'OnlySector': {'price_change': 0.01, 'volume_ratio': 1.0, 'breadth_score': 0.5}
                    }
                },
                'should_handle_gracefully': True
            },
            {
                'name': '极端价格变化测试',
                'data': {
                    'market_volatility': 0.2,
                    'sectors': {
                        'ExtremeSector': {'price_change': 10.0, 'volume_ratio': 100.0, 'breadth_score': 2.0}
                    }
                },
                'should_handle_gracefully': True
            }
        ]

        test_results['boundary_tests_total'] = len(boundary_tests)

        print(f"📊 边界条件测试用例:")

        for i, test_case in enumerate(boundary_tests, 1):
            print(f"\n--- 测试 {i}: {test_case['name']} ---")

            try:
                result = system.analyze_market(test_case['data'])

                # 检查结果的合理性
                has_valid_result = (
                    hasattr(result, 'analysis_timestamp') and
                    hasattr(result, 'market_regime') and
                    hasattr(result, 'unified_decision')
                )

                if has_valid_result:
                    # 检查关键数值的合理性
                    confidence = result.analysis_confidence
                    position = result.unified_decision.get('suggested_position', 0)

                    values_reasonable = (
                        0 <= confidence <= 1 and
                        0 <= position <= 100
                    )

                    if values_reasonable:
                        print(f"结果: ✅ 正常处理")
                        test_results['boundary_tests_passed'] += 1
                    else:
                        print(f"结果: ⚠️  处理但数值异常 (置信度:{confidence:.3f}, 仓位:{position:.1f})")
                else:
                    print(f"结果: ⚠️  处理但结果不完整")

            except Exception as e:
                if test_case['should_handle_gracefully']:
                    print(f"结果: ❌ 未能优雅处理异常: {e}")
                    self.error_log.append(f"边界测试 {test_case['name']} 失败: {e}")
                else:
                    print(f"结果: ✅ 正确抛出异常: {e}")
                    test_results['boundary_tests_passed'] += 1

        # 错误处理评分
        error_handling_score = test_results['boundary_tests_passed'] / test_results['boundary_tests_total']
        test_results['error_handling_score'] = error_handling_score

        # 鲁棒性评分 (基于错误处理和系统稳定性)
        robustness_score = error_handling_score * 0.7 + (1.0 if len(self.error_log) == 0 else 0.5) * 0.3
        test_results['robustness_score'] = robustness_score

        print(f"\n📊 边界条件测试结果:")
        print(f"通过测试: {test_results['boundary_tests_passed']}/{test_results['boundary_tests_total']}")
        print(f"错误处理评分: {error_handling_score:.1%}")
        print(f"鲁棒性评分: {robustness_score:.1%}")

        return test_results

    def run_configuration_consistency_tests(self) -> Dict[str, Any]:
        """运行配置一致性测试"""
        print("\n⚙️ === 配置一致性测试 ===")

        test_results = {
            'config_validation_passed': False,
            'market_regime_consistency': False,
            'parameter_persistence': False,
            'consistency_score': 0.0
        }

        # 测试配置管理器
        config_manager = self.ConfigManager(config_dir=self.temp_dir)

        # 1. 配置验证测试
        print(f"📋 配置验证测试:")
        validation_results = config_manager.validate_all_configs()

        config_errors = len(validation_results['errors'])
        config_warnings = len(validation_results['warnings'])

        print(f"配置错误数: {config_errors}")
        print(f"配置警告数: {config_warnings}")

        test_results['config_validation_passed'] = config_errors == 0
        print(f"配置验证: {'✅' if test_results['config_validation_passed'] else '❌'}")

        # 2. 市场状态一致性测试
        print(f"\n🔄 市场状态一致性测试:")

        original_regime = config_manager.current_market_regime
        test_regimes = ['trending_stable', 'high_rotation', 'normal_market']

        regime_consistency_count = 0
        for regime in test_regimes:
            success = config_manager.switch_market_regime(regime)
            if success:
                current_regime = config_manager.current_market_regime
                if current_regime == regime:
                    regime_consistency_count += 1
                    print(f"  {regime}: ✅")
                else:
                    print(f"  {regime}: ❌ (期望:{regime}, 实际:{current_regime})")
            else:
                print(f"  {regime}: ❌ (切换失败)")

        test_results['market_regime_consistency'] = regime_consistency_count == len(test_regimes)

        # 恢复原始状态
        config_manager.switch_market_regime(original_regime)

        # 3. 参数持久性测试
        print(f"\n💾 参数持久性测试:")

        test_param_path = 'test.persistence_check'
        test_param_value = f'test_value_{datetime.now().timestamp()}'

        # 设置测试参数
        set_success = config_manager.set_config(test_param_path, test_param_value, "持久性测试")

        if set_success:
            # 创建新的配置管理器实例来测试持久性
            new_config_manager = self.ConfigManager(config_dir=self.temp_dir)
            retrieved_value = new_config_manager.get_config(test_param_path)

            persistence_success = retrieved_value == test_param_value
            test_results['parameter_persistence'] = persistence_success

            print(f"参数设置: ✅")
            print(f"参数持久性: {'✅' if persistence_success else '❌'}")
            print(f"  设置值: {test_param_value}")
            print(f"  检索值: {retrieved_value}")
        else:
            print(f"参数设置: ❌")
            test_results['parameter_persistence'] = False

        # 计算一致性评分
        consistency_factors = [
            test_results['config_validation_passed'],
            test_results['market_regime_consistency'],
            test_results['parameter_persistence']
        ]

        consistency_score = sum(consistency_factors) / len(consistency_factors)
        test_results['consistency_score'] = consistency_score

        print(f"\n📊 配置一致性评分: {consistency_score:.1%}")

        return test_results

    def generate_comprehensive_report(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合测试报告"""
        print("\n📋 === 综合测试报告 ===")

        # 计算总体评分
        algorithm_score = all_results['algorithm']['pass_rate']
        transparency_score = all_results['transparency']['transparency_score']
        performance_score = 1.0 if all_results['performance']['performance_grade'] in ['A+', 'A', 'B'] else 0.5
        boundary_score = all_results['boundary']['robustness_score']
        config_score = all_results['config']['consistency_score']

        overall_score = (
            algorithm_score * 0.3 +
            transparency_score * 0.2 +
            performance_score * 0.2 +
            boundary_score * 0.15 +
            config_score * 0.15
        )

        # 确定总体等级
        if overall_score >= 0.9:
            overall_grade = 'A+'
        elif overall_score >= 0.8:
            overall_grade = 'A'
        elif overall_score >= 0.7:
            overall_grade = 'B'
        elif overall_score >= 0.6:
            overall_grade = 'C'
        else:
            overall_grade = 'D'

        report = {
            'test_summary': {
                'overall_score': overall_score,
                'overall_grade': overall_grade,
                'test_timestamp': datetime.now().isoformat(),
                'total_errors': len(self.error_log)
            },
            'detailed_scores': {
                'algorithm_correctness': algorithm_score,
                'decision_transparency': transparency_score,
                'performance_benchmark': performance_score,
                'boundary_robustness': boundary_score,
                'configuration_consistency': config_score
            },
            'recommendations': [],
            'critical_issues': [],
            'performance_metrics': {
                'avg_analysis_time': all_results['performance']['batch_analysis_time'],
                'throughput': all_results['performance']['throughput'],
                'performance_grade': all_results['performance']['performance_grade']
            }
        }

        # 生成建议
        if algorithm_score < 0.8:
            report['recommendations'].append("算法正确性需要改进，建议检查市场状态检测和仓位计算逻辑")

        if transparency_score < 0.7:
            report['recommendations'].append("决策透明度需要提升，建议增加更详细的决策解释")

        if performance_score < 0.8:
            report['recommendations'].append("性能需要优化，建议检查计算效率和资源使用")

        if boundary_score < 0.8:
            report['recommendations'].append("边界条件处理需要加强，建议增加异常处理机制")

        if config_score < 0.8:
            report['recommendations'].append("配置管理需要完善，建议检查参数验证和持久化机制")

        # 识别关键问题
        if len(self.error_log) > 0:
            report['critical_issues'].extend(self.error_log)

        if algorithm_score < 0.6:
            report['critical_issues'].append("算法正确性严重不足")

        if performance_score < 0.5:
            report['critical_issues'].append("性能严重不达标")

        # 打印报告
        print(f"📊 总体评分: {overall_score:.1%} (等级: {overall_grade})")
        print(f"📅 测试时间: {report['test_summary']['test_timestamp']}")
        print(f"❌ 错误总数: {report['test_summary']['total_errors']}")

        print(f"\n📈 详细评分:")
        for category, score in report['detailed_scores'].items():
            print(f"  {category}: {score:.1%}")

        if report['recommendations']:
            print(f"\n💡 改进建议:")
            for rec in report['recommendations']:
                print(f"  - {rec}")

        if report['critical_issues']:
            print(f"\n🚨 关键问题:")
            for issue in report['critical_issues']:
                print(f"  - {issue}")

        print(f"\n⚡ 性能指标:")
        print(f"  平均分析时间: {report['performance_metrics']['avg_analysis_time']:.4f}s")
        print(f"  吞吐量: {report['performance_metrics']['throughput']:.2f} 分析/秒")
        print(f"  性能等级: {report['performance_metrics']['performance_grade']}")

        return report

    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有集成测试"""
        print("🚀 开始系统集成测试")
        print("=" * 60)

        all_results = {}

        try:
            # 1. 算法正确性验证
            all_results['algorithm'] = self.run_algorithm_correctness_tests()

            print("=" * 60)

            # 2. 决策透明度测试
            all_results['transparency'] = self.run_decision_transparency_tests()

            print("=" * 60)

            # 3. 性能基准测试
            all_results['performance'] = self.run_performance_benchmark_tests()

            print("=" * 60)

            # 4. 边界条件测试
            all_results['boundary'] = self.run_boundary_condition_tests()

            print("=" * 60)

            # 5. 配置一致性测试
            all_results['config'] = self.run_configuration_consistency_tests()

            print("=" * 60)

            # 6. 生成综合报告
            comprehensive_report = self.generate_comprehensive_report(all_results)
            all_results['comprehensive_report'] = comprehensive_report

            return all_results

        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            self.error_log.append(f"测试执行失败: {e}")
            return {'error': str(e), 'error_log': self.error_log}

        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)

    def export_test_results(self, results: Dict[str, Any], file_path: str) -> bool:
        """导出测试结果"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            print(f"📤 测试结果已导出到: {file_path}")
            return True

        except Exception as e:
            print(f"❌ 导出测试结果失败: {e}")
            return False


def main():
    """主函数"""
    print("🧪 OPT-T5.3 系统集成测试")
    print("验证所有优化组件的集成效果")
    print("=" * 60)

    # 创建测试器
    tester = OptimizationIntegrationTester()

    # 运行所有测试
    results = tester.run_all_tests()

    # 导出结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"integration_test_results_{timestamp}.json"
    tester.export_test_results(results, results_file)

    # 返回测试是否成功
    if 'comprehensive_report' in results:
        overall_score = results['comprehensive_report']['test_summary']['overall_score']
        return overall_score >= 0.7  # 70%以上认为测试通过
    else:
        return False


if __name__ == "__main__":
    success = main()
    exit_code = 0 if success else 1
    print(f"\n🎯 测试{'成功' if success else '失败'}，退出码: {exit_code}")
    sys.exit(exit_code)
