#!/usr/bin/env python3
"""
批量计算历史板块轮动数据
利用已有的90天市场广度数据，回填历史轮动指标
"""

import sys
import os
import time
from datetime import datetime, timedelta
import pandas as pd

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def get_available_dates():
    """获取可用的市场广度数据日期"""
    print("📅 获取可用的市场广度数据日期...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取所有有数据的日期
        cursor.execute("""
        SELECT DISTINCT DATE(recorded_at) as date, COUNT(*) as records
        FROM market_breadth_metrics_gics 
        GROUP BY DATE(recorded_at)
        HAVING COUNT(*) >= 10  -- 至少10条记录才算有效
        ORDER BY date DESC
        """)
        
        results = cursor.fetchall()
        conn.close()
        
        if results:
            print(f"✅ 找到 {len(results)} 个有效数据日期")
            print(f"📊 日期范围: {results[-1][0]} 到 {results[0][0]}")
            return [row[0] for row in results]
        else:
            print("❌ 未找到有效的市场广度数据")
            return []
            
    except Exception as e:
        print(f"❌ 获取日期失败: {e}")
        return []

def get_existing_rotation_dates():
    """获取已有的板块轮动数据日期"""
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT DISTINCT DATE(recorded_at) as date
        FROM sector_rotation_metrics
        ORDER BY date DESC
        """)
        
        results = cursor.fetchall()
        conn.close()
        
        return [row[0] for row in results]
        
    except Exception as e:
        print(f"⚠️  获取已有轮动数据日期失败: {e}")
        return []

def calculate_rotation_for_date(target_date):
    """为指定日期计算板块轮动指标"""
    print(f"🔄 计算 {target_date} 的板块轮动指标...")
    
    try:
        # 这里需要修改sector_rotation_scheduler来支持指定日期计算
        # 暂时使用模拟计算
        from sector_rotation_scheduler import SectorRotationScheduler
        import pymysql
        from db_settings import get_default_db_config
        
        # 获取该日期的市场广度数据
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT market, advances, declines, total_stocks, 
               new_highs_52w, new_lows_52w, avg_rsi, internal_health,
               timeframe
        FROM market_breadth_metrics_gics 
        WHERE DATE(recorded_at) = %s
        AND timeframe = '1d'  -- 使用日线数据
        """, (target_date,))
        
        breadth_data = cursor.fetchall()
        
        if not breadth_data:
            print(f"⚠️  {target_date} 无市场广度数据")
            conn.close()
            return False
        
        print(f"📊 找到 {len(breadth_data)} 条市场广度记录")
        
        # 计算基础轮动指标
        rotation_metrics = {}
        
        for row in breadth_data:
            market, advances, declines, total_stocks, new_highs, new_lows, avg_rsi, internal_health, timeframe = row
            
            if total_stocks > 0:
                # 计算基础指标
                advance_decline_ratio = advances / total_stocks if total_stocks > 0 else 0
                new_high_low_ratio = (new_highs - new_lows) / total_stocks if total_stocks > 0 else 0
                
                # 简化的轮动强度计算
                rotation_intensity = abs(advance_decline_ratio - 0.5) * 2  # 0-1范围
                
                # 基于RSI计算价格离散度（简化版）
                price_dispersion = abs(avg_rsi - 50) / 50 if avg_rsi else 0
                
                # 基于内部健康度计算成交量集中度
                volume_concentration = internal_health / 100 if internal_health else 0.5
                
                # 简化的轮动阶段判断
                if rotation_intensity < 0.2:
                    rotation_stage = 'stable'
                elif rotation_intensity < 0.4:
                    rotation_stage = 'convergence'
                elif rotation_intensity < 0.6:
                    rotation_stage = 'startup'
                elif rotation_intensity < 0.8:
                    rotation_stage = 'acceleration'
                else:
                    rotation_stage = 'chaos'
                
                # 风险等级
                if rotation_intensity < 0.3:
                    risk_level = 'low'
                elif rotation_intensity < 0.6:
                    risk_level = 'medium'
                elif rotation_intensity < 0.8:
                    risk_level = 'high'
                else:
                    risk_level = 'extreme'
                
                rotation_metrics[market] = {
                    'sector': market,
                    'rotation_intensity_index': rotation_intensity,
                    'price_dispersion': price_dispersion,
                    'rank_velocity': 0.1,  # 历史数据暂时设为固定值
                    'volume_concentration': volume_concentration,
                    'rotation_stage': rotation_stage,
                    'risk_level': risk_level,
                    'sector_rank': 1,  # 暂时设为1，后续可以计算排名
                    'composite_score': rotation_intensity * 0.4 + price_dispersion * 0.3 + volume_concentration * 0.3,
                    'recorded_at': target_date
                }
        
        # 计算排名
        sorted_sectors = sorted(rotation_metrics.items(), 
                              key=lambda x: x[1]['composite_score'], 
                              reverse=True)
        
        for rank, (sector, metrics) in enumerate(sorted_sectors, 1):
            rotation_metrics[sector]['sector_rank'] = rank
        
        # 保存到数据库
        if rotation_metrics:
            insert_sql = """
            INSERT INTO sector_rotation_metrics (
                sector, rotation_intensity_index, price_dispersion, rank_velocity,
                volume_concentration, rotation_stage, risk_level, sector_rank,
                composite_score, recorded_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            for sector, metrics in rotation_metrics.items():
                cursor.execute(insert_sql, (
                    metrics['sector'],
                    metrics['rotation_intensity_index'],
                    metrics['price_dispersion'],
                    metrics['rank_velocity'],
                    metrics['volume_concentration'],
                    metrics['rotation_stage'],
                    metrics['risk_level'],
                    metrics['sector_rank'],
                    metrics['composite_score'],
                    f"{target_date} 12:00:00"  # 设置为中午时间
                ))
            
            conn.commit()
            print(f"✅ 成功保存 {len(rotation_metrics)} 个板块的轮动数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def batch_calculate_historical_rotation(days_back=30):
    """批量计算历史轮动数据"""
    print("🚀 开始批量计算历史板块轮动数据")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📅 回填天数: {days_back} 天")
    print()
    
    # 获取可用日期
    available_dates = get_available_dates()
    if not available_dates:
        print("❌ 没有可用的市场广度数据")
        return False
    
    # 获取已有的轮动数据日期
    existing_dates = get_existing_rotation_dates()
    print(f"📊 已有轮动数据: {len(existing_dates)} 天")
    
    # 确定需要计算的日期
    target_dates = []
    for date in available_dates[:days_back]:  # 取最近N天
        if date not in existing_dates:
            target_dates.append(date)
    
    if not target_dates:
        print("✅ 所有日期的轮动数据都已存在")
        return True
    
    print(f"🎯 需要计算 {len(target_dates)} 个日期的轮动数据")
    print(f"📅 日期范围: {target_dates[-1]} 到 {target_dates[0]}")
    
    # 批量计算
    success_count = 0
    total_count = len(target_dates)
    
    for i, date in enumerate(reversed(target_dates), 1):  # 从最早日期开始
        print(f"\n[{i}/{total_count}] 处理日期: {date}")
        
        if calculate_rotation_for_date(date):
            success_count += 1
            print(f"✅ 成功 ({success_count}/{total_count})")
        else:
            print(f"❌ 失败")
        
        # 避免过快请求
        if i < total_count:
            time.sleep(1)
    
    print(f"\n🎉 批量计算完成!")
    print(f"📊 成功: {success_count}/{total_count}")
    print(f"⏱️  总耗时: {time.time() - start_time:.1f} 秒")
    
    return success_count > 0

def verify_historical_data():
    """验证历史数据计算结果"""
    print("\n🔍 验证历史轮动数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 统计轮动数据
        cursor.execute("""
        SELECT 
            COUNT(DISTINCT DATE(recorded_at)) as days,
            COUNT(DISTINCT sector) as sectors,
            AVG(rotation_intensity_index) as avg_rii,
            AVG(price_dispersion) as avg_price_disp,
            MIN(recorded_at) as earliest,
            MAX(recorded_at) as latest
        FROM sector_rotation_metrics
        """)
        
        result = cursor.fetchone()
        
        if result:
            days, sectors, avg_rii, avg_price_disp, earliest, latest = result
            print(f"📊 轮动数据统计:")
            print(f"   数据天数: {days} 天")
            print(f"   板块数量: {sectors} 个")
            print(f"   平均RII: {avg_rii:.4f}")
            print(f"   平均价格离散度: {avg_price_disp:.4f}")
            print(f"   时间范围: {earliest} 到 {latest}")
            
            # 检查最近几天的数据
            cursor.execute("""
            SELECT DATE(recorded_at) as date, 
                   COUNT(*) as sectors,
                   AVG(rotation_intensity_index) as avg_rii
            FROM sector_rotation_metrics
            WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(recorded_at)
            ORDER BY date DESC
            """)
            
            recent_results = cursor.fetchall()
            if recent_results:
                print(f"\n📅 最近7天数据:")
                for date, sector_count, avg_rii in recent_results:
                    print(f"   {date}: {sector_count} 板块, 平均RII: {avg_rii:.4f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    global start_time
    start_time = time.time()
    
    print("🎯 历史板块轮动数据批量计算")
    print("利用90天市场广度数据回填轮动指标")
    print()
    
    # 询问回填天数
    try:
        days_input = input("请输入要回填的天数 (默认30天，最多90天): ").strip()
        days_back = int(days_input) if days_input else 30
        days_back = min(days_back, 90)  # 限制最多90天
    except ValueError:
        days_back = 30
    
    print(f"📅 将回填最近 {days_back} 天的轮动数据")
    
    # 确认执行
    confirm = input("\n是否继续执行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("👋 用户取消操作")
        return
    
    # 执行批量计算
    success = batch_calculate_historical_rotation(days_back)
    
    if success:
        # 验证结果
        verify_historical_data()
        
        print("\n🎉 历史数据回填完成!")
        print("\n💡 下一步操作:")
        print("1. 启动Web界面: python web_interface.py")
        print("2. 选择板块进行轮动分析")
        print("3. 现在应该能看到有意义的轮动指标了!")
        print("4. 价格离散度、排名变化速度等指标应该不再是0")
    else:
        print("\n❌ 历史数据回填失败")
        print("💡 故障排除:")
        print("1. 检查数据库连接")
        print("2. 确认市场广度数据完整")
        print("3. 查看错误日志")
    
    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
