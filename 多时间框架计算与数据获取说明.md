# 多时间框架计算与数据获取说明

## 🔄 多时间框架板块轮动指标计算

### 1. 计算脚本说明

**主要脚本**: `scripts/run_all_timeframes.py`

这个脚本的作用是：
- **计算市场广度指标**（不是轮动指标）
- **支持6个时间框架**: 5分钟、15分钟、1小时、日线、周线、月线
- **保存到数据库**: `market_breadth_metrics_gics` 表

```bash
# 手动运行一次（计算所有时间框架的市场广度）
python scripts/run_all_timeframes.py
```

### 2. 自动化运行配置

使用我们之前配置的定时任务：

```bash
# 配置自动化任务
python scripts/setup_cron_jobs.py
# 选择 "4. 市宽计算配置"
```

**自动化时间安排**:
- **每日运行**: UTC 21:30 (北京时间5:30) - 计算所有6个时间框架
- **每周运行**: UTC 18:00 (北京时间周一2:00) - 额外保障运行

### 3. 计算内容详解

#### 市场广度指标计算
```
5分钟时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
15分钟时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
1小时时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
日线时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
周线时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
月线时间框架 → 计算涨跌家数、新高新低、RSI等 → 保存到数据库
```

#### 板块轮动指标计算
**重要**: 板块轮动指标（RII、价格离散度等）是在**Web界面分析时实时计算**的，不是预先计算的。

## 📊 Web界面数据获取逻辑

### 1. 数据获取流程

当您在Web界面点击"分析"时：

```
用户选择板块和时间框架 
    ↓
Web界面调用 /analyze_v41 API
    ↓
multi_timeframe_analyzer.analyze_market_mtf()
    ↓
获取最新的市场广度数据（从数据库）
    ↓
基于市场广度数据计算板块轮动指标
    ↓
生成完整报告返回给Web界面
```

### 2. 最新数据获取机制

#### 自动获取最新数据
Web界面**默认使用最新数据**，具体逻辑：

```python
# 在market_breadth_calculator.py中
cursor.execute("""
SELECT * FROM market_breadth_metrics_gics 
WHERE timeframe = %s AND market = %s 
ORDER BY recorded_at DESC LIMIT 1
""", (timeframe, market))
```

**关键点**:
- `ORDER BY recorded_at DESC LIMIT 1` - **自动获取最新记录**
- 无需指定日期，系统自动使用最新可用数据
- 如果当天没有数据，会使用最近一次的数据

#### 数据新鲜度检查
系统会检查数据是否新鲜：

```python
# 在precomputed_query.py中
def check_data_freshness():
    # 检查数据是否在24小时内更新
    if (datetime.now() - latest_data_time).total_seconds() > 86400:
        return False  # 数据不新鲜
    return True  # 数据新鲜
```

### 3. 数据优先级

系统按以下优先级获取数据：

1. **预计算数据** (如果可用) - 最快
2. **数据库缓存数据** - 较快  
3. **实时计算数据** - 较慢但最准确

```python
# 在market_breadth_calculator.py中
if PRECOMPUTED_AVAILABLE:
    try:
        # 优先使用预计算数据
        precomputed_metrics = get_precomputed_breadth_metrics(companies)
        if precomputed_metrics.total_stocks > 0:
            # 使用预计算数据
            return precomputed_metrics
    except:
        # 预计算失败，使用实时计算
        pass

# 实时计算数据
return calculate_real_time_metrics(companies)
```

## ⚡ 性能优化建议

### 1. 确保数据及时更新

```bash
# 检查数据状态
python scripts/setup_precomputed_system.py --status

# 手动更新数据
python scripts/run_all_timeframes.py
```

### 2. 启用预计算系统

```bash
# 配置预计算系统
python scripts/setup_cron_jobs.py
# 选择 "1. 基础配置" 或 "4. 市宽计算配置"
```

### 3. 监控数据新鲜度

```bash
# 持续监控
python scripts/monitor_precomputed.py --continuous
```

## 🔍 数据流向图

```
定时任务 (每日5:30)
    ↓
run_all_timeframes.py
    ↓
计算6个时间框架的市场广度
    ↓
保存到 market_breadth_metrics_gics 表
    ↓
Web界面分析时自动获取最新数据
    ↓
基于最新市场广度数据计算轮动指标
    ↓
显示完整的轮动分析报告
```

## 🚨 常见问题解答

### Q1: Web界面显示的数据是实时的吗？
**A**: Web界面使用的是**最新可用数据**，通常是：
- 如果定时任务正常运行：当天早上5:30计算的数据
- 如果定时任务未运行：最近一次成功计算的数据

### Q2: 轮动指标是预先计算的吗？
**A**: **不是**。轮动指标（RII、价格离散度等）是基于市场广度数据**实时计算**的，确保分析的准确性。

### Q3: 如何确保数据是最新的？
**A**: 
1. 配置定时任务每日自动更新
2. 手动运行 `python scripts/run_all_timeframes.py`
3. 使用监控脚本检查数据新鲜度

### Q4: 多时间框架分析需要多长时间？
**A**: 
- **有预计算数据**: 2-5秒
- **无预计算数据**: 30秒-2分钟（需要实时获取股价数据）

## 💡 最佳实践

1. **配置自动化**: 使用定时任务确保数据每日更新
2. **启用预计算**: 提升Web界面响应速度
3. **监控数据**: 定期检查数据新鲜度和完整性
4. **合理选择时间框架**: 根据分析需求选择合适的时间框架组合

---

**总结**: Web界面默认使用最新数据，轮动指标实时计算，市场广度数据通过定时任务预先计算并存储。
