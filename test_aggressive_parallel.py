#!/usr/bin/env python3
"""
测试激进并行计算方案
验证64核服务器的性能优化是否正常工作
"""

import sys
import os
import time
import multiprocessing
import psutil
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.hist_data import (
    get_markets_from_config, 
    get_stock_data_for_markets,
    calculate_breadth_metrics,
    multiprocess_calculate_stock_data,
    get_price,
    get_companies,
    get_company_mcap
)

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()


def test_server_specs():
    """测试服务器规格"""
    print("=== 服务器规格检测 ===\n")
    
    cpu_count = multiprocessing.cpu_count()
    memory = psutil.virtual_memory()
    
    print(f"🖥️  CPU核心数: {cpu_count}")
    print(f"💾 总内存: {memory.total / 1024 / 1024 / 1024:.1f} GB")
    print(f"📊 可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")
    print(f"⚡ 内存使用率: {memory.percent:.1f}%")
    
    # 判断服务器类型
    if cpu_count >= 64 and memory.total >= 60 * 1024**3:
        server_type = "🏆 高性能服务器 (64核64G+)"
        recommended_processes = 48
        recommended_batch = 5000
    elif cpu_count >= 32:
        server_type = "💪 中高性能服务器 (32核+)"
        recommended_processes = 24
        recommended_batch = 3000
    elif cpu_count >= 16:
        server_type = "✅ 中等性能服务器 (16核+)"
        recommended_processes = 12
        recommended_batch = 2000
    else:
        server_type = "📱 标准服务器"
        recommended_processes = max(4, cpu_count - 2)
        recommended_batch = 1000
    
    print(f"\n{server_type}")
    print(f"🚀 推荐进程数: {recommended_processes}")
    print(f"📦 推荐批大小: {recommended_batch}")
    
    return cpu_count, memory.total, recommended_processes, recommended_batch


def test_parallel_configuration():
    """测试并行配置"""
    print("\n=== 并行配置测试 ===\n")
    
    cpu_count = multiprocessing.cpu_count()
    
    # 测试激进配置逻辑
    if cpu_count >= 64:
        process_count = 48
        batch_size = 15
        stock_batch = 5000
        threads = 50
    elif cpu_count >= 32:
        process_count = 24
        batch_size = 12
        stock_batch = 3000
        threads = 30
    elif cpu_count >= 16:
        process_count = 12
        batch_size = 8
        stock_batch = 2000
        threads = 20
    else:
        process_count = max(4, cpu_count - 2)
        batch_size = 5
        stock_batch = 1000
        threads = 10
    
    print(f"📊 主进程数: {process_count} (CPU利用率: {process_count/cpu_count*100:.1f}%)")
    print(f"📅 日期批大小: {batch_size} 天/批")
    print(f"📈 股票批大小: {stock_batch} 只/批")
    print(f"🌐 下载线程数: {threads}")
    
    # 估算内存使用
    estimated_memory_per_process = 100  # MB
    estimated_total_memory = process_count * estimated_memory_per_process * batch_size
    
    print(f"💾 预估内存使用: {estimated_total_memory:.0f} MB ({estimated_total_memory/1024:.1f} GB)")
    
    return process_count, batch_size, stock_batch, threads


def test_database_connection():
    """测试数据库连接"""
    print("\n=== 数据库连接测试 ===\n")
    
    try:
        import pymysql
        
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM market_breadth_metrics_gics")
        count = cursor.fetchone()[0]
        
        print(f"✅ 数据库连接成功")
        print(f"📊 现有记录数: {count:,}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_market_data_access():
    """测试市场数据访问"""
    print("\n=== 市场数据访问测试 ===\n")
    
    try:
        # 获取市场列表
        markets = get_markets_from_config()
        print(f"📈 可用市场: {markets}")
        
        if not markets:
            print("❌ 无法获取市场列表")
            return False
        
        # 测试获取公司列表
        test_market = markets[0]
        companies = get_companies([test_market])
        print(f"🏢 {test_market} 包含 {len(companies)} 只股票")
        
        # 测试获取市值数据
        company_mcap = get_company_mcap(companies[:10])  # 只测试前10只
        print(f"💰 成功获取 {len(company_mcap)} 只股票的市值数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 市场数据访问失败: {e}")
        return False


def test_small_batch_calculation():
    """测试小批量计算（验证算法正确性）"""
    print("\n=== 小批量计算测试 ===\n")
    
    try:
        # 获取少量股票进行测试
        markets = get_markets_from_config()
        test_market = markets[0]
        
        print(f"🧪 测试市场: {test_market}")
        
        # 获取小批量股票数据
        start_time = time.time()
        stock_data = get_stock_data_for_markets([test_market], batch_size=100)
        data_time = time.time() - start_time
        
        if stock_data.empty:
            print("❌ 无法获取股票数据")
            return False
        
        print(f"📊 获取 {len(stock_data)} 只股票数据，耗时 {data_time:.1f}秒")
        
        # 计算广度指标
        calc_start = time.time()
        current_time = datetime.now()
        result = calculate_breadth_metrics(current_time, test_market, stock_data)
        calc_time = time.time() - calc_start
        
        print(f"⚡ 计算广度指标耗时 {calc_time:.2f}秒")
        
        # 显示结果
        print(f"\n📈 {test_market} 计算结果:")
        print(f"  股票总数: {result['total_stocks']}")
        print(f"  涨跌家数: {result['advances']}/{result['declines']}")
        print(f"  纯度指标: {result['purity']:.3f}")
        print(f"  内部健康度: {result['internal_health']:.1f}")
        
        if result['momentum_coherence'] is not None:
            print(f"  动量一致性: {result['momentum_coherence']:.3f}")
        
        print(f"  背离类型: {result['divergence_type']}")
        
        if result['coherence_details']:
            details = result['coherence_details']
            print(f"  一致性类型: {details['coherence_type']}")
        
        print(f"✅ 小批量计算测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 小批量计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_benchmark():
    """性能基准测试"""
    print("\n=== 性能基准测试 ===\n")
    
    try:
        # 获取市场数据
        markets = get_markets_from_config()
        test_market = markets[0] if markets else "SP500"
        
        # 测试不同批大小的性能
        batch_sizes = [500, 1000, 2000]
        
        for batch_size in batch_sizes:
            print(f"🧪 测试批大小: {batch_size}")
            
            start_time = time.time()
            initial_memory = psutil.virtual_memory().used / 1024 / 1024 / 1024
            
            # 获取股票数据
            stock_data = get_stock_data_for_markets([test_market], batch_size=batch_size)
            
            end_time = time.time()
            final_memory = psutil.virtual_memory().used / 1024 / 1024 / 1024
            
            duration = end_time - start_time
            memory_used = final_memory - initial_memory
            
            print(f"  ⏱️  耗时: {duration:.1f}秒")
            print(f"  💾 内存增量: {memory_used:.2f}GB")
            print(f"  📊 处理股票数: {len(stock_data)}")
            print(f"  ⚡ 处理速度: {len(stock_data)/duration:.1f} 股票/秒")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 激进并行计算方案测试\n")
    print("=" * 60)
    
    # 1. 服务器规格检测
    cpu_count, total_memory, rec_processes, rec_batch = test_server_specs()
    
    print("=" * 60)
    
    # 2. 并行配置测试
    process_count, batch_size, stock_batch, threads = test_parallel_configuration()
    
    print("=" * 60)
    
    # 3. 数据库连接测试
    if not test_database_connection():
        print("❌ 数据库连接失败，无法继续测试")
        return
    
    print("=" * 60)
    
    # 4. 市场数据访问测试
    if not test_market_data_access():
        print("❌ 市场数据访问失败，无法继续测试")
        return
    
    print("=" * 60)
    
    # 5. 小批量计算测试
    if not test_small_batch_calculation():
        print("❌ 计算测试失败")
        return
    
    print("=" * 60)
    
    # 6. 性能基准测试
    test_performance_benchmark()
    
    print("=" * 60)
    
    # 总结
    print("🎉 === 测试总结 ===")
    print(f"✅ 服务器规格: {cpu_count}核, {total_memory/1024**3:.1f}GB")
    print(f"✅ 推荐配置: {rec_processes}进程, {rec_batch}批大小")
    print(f"✅ 算法验证: 改进的动量一致性 + 背离检测")
    print(f"✅ 数据库连接: 正常")
    print(f"✅ 市场数据: 可访问")
    
    if cpu_count >= 64:
        print(f"🏆 64核服务器已准备好激进并行计算！")
        print(f"🚀 建议运行: python core/hist_data.py")
    else:
        print(f"💪 {cpu_count}核服务器配置优化完成！")
        print(f"🚀 可以运行: python core/hist_data.py")


if __name__ == "__main__":
    main()
