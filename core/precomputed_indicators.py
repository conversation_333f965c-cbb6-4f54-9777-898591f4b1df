#!/usr/bin/env python3
"""
预计算指标系统
支持MA50/MA200和52周新高新低的预计算和增量更新
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
import pymysql
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config

class PrecomputedIndicators:
    """预计算指标管理器"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.logger = self._setup_logger()
        self._ensure_tables_exist()
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('precomputed_indicators')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_tables_exist(self):
        """确保预计算表存在"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # MA指标表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS precomputed_ma_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                date DATE NOT NULL,
                close_price DECIMAL(10,4),
                ma50 DECIMAL(10,4),
                ma200 DECIMAL(10,4),
                above_ma50 BOOLEAN DEFAULT FALSE,
                above_ma200 BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_date (symbol, date),
                INDEX idx_symbol (symbol),
                INDEX idx_date (date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 52周新高新低表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS precomputed_52w_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                date DATE NOT NULL,
                close_price DECIMAL(10,4),
                high_52w DECIMAL(10,4),
                low_52w DECIMAL(10,4),
                is_new_high BOOLEAN DEFAULT FALSE,
                is_new_low BOOLEAN DEFAULT FALSE,
                days_from_high INT DEFAULT 0,
                days_from_low INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_date (symbol, date),
                INDEX idx_symbol (symbol),
                INDEX idx_date (date),
                INDEX idx_new_high (is_new_high),
                INDEX idx_new_low (is_new_low)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            conn.commit()
            self.logger.info("预计算表创建/验证完成")
            
        except Exception as e:
            self.logger.error(f"创建预计算表失败: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()
    
    def initial_ma_calculation(self, symbols: List[str] = None, days: int = 400) -> bool:
        """
        首次MA指标计算
        获取足够的历史数据计算MA200
        """
        if symbols is None:
            symbols = self.get_all_symbols()
        
        self.logger.info(f"开始首次MA计算，股票数量: {len(symbols)}")
        
        # 分批处理
        batch_size = 50
        success_count = 0
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            self.logger.info(f"处理批次 {i//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}")
            
            try:
                # 获取历史数据
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

                self.logger.info(f"处理批次 {i//batch_size + 1}: 获取 {len(batch_symbols)} 只股票的MA数据")

                price_data = download_hist_price(
                    symbols=batch_symbols,
                    interval='1d',
                    start=start_date,
                    columns=['c'],
                    threads=10
                )
                
                # 计算并保存MA指标
                for symbol, df in price_data.items():
                    if self._calculate_and_save_ma(symbol, df):
                        success_count += 1
                        
            except Exception as e:
                self.logger.error(f"批次处理失败: {e}")
                continue
        
        self.logger.info(f"首次MA计算完成，成功: {success_count}/{len(symbols)}")
        return success_count > 0
    
    def _calculate_and_save_ma(self, symbol: str, df: pd.DataFrame) -> bool:
        """计算并保存单个股票的MA指标"""
        try:
            if df.empty or len(df) < 200:
                return False
            
            # 确保索引是日期类型并排序
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            df = df.sort_index()
            
            # 计算MA
            df['ma50'] = df['close'].rolling(window=50, min_periods=50).mean()
            df['ma200'] = df['close'].rolling(window=200, min_periods=200).mean()
            
            # 判断是否在MA之上
            df['above_ma50'] = df['close'] > df['ma50']
            df['above_ma200'] = df['close'] > df['ma200']
            
            # 只保存有效数据（至少有MA50）
            valid_data = df.dropna(subset=['ma50'])
            if valid_data.empty:
                return False
            
            # 保存到数据库
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()
            
            try:
                # 准备数据
                records = []
                for date, row in valid_data.iterrows():
                    records.append((
                        symbol,
                        date.date(),
                        float(row['close']),
                        float(row['ma50']) if not pd.isna(row['ma50']) else None,
                        float(row['ma200']) if not pd.isna(row['ma200']) else None,
                        bool(row['above_ma50']) if not pd.isna(row['above_ma50']) else False,
                        bool(row['above_ma200']) if not pd.isna(row['above_ma200']) else False
                    ))
                
                # 批量插入
                cursor.executemany("""
                INSERT INTO precomputed_ma_indicators 
                (symbol, date, close_price, ma50, ma200, above_ma50, above_ma200)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                close_price = VALUES(close_price),
                ma50 = VALUES(ma50),
                ma200 = VALUES(ma200),
                above_ma50 = VALUES(above_ma50),
                above_ma200 = VALUES(above_ma200),
                updated_at = CURRENT_TIMESTAMP
                """, records)
                
                conn.commit()
                self.logger.debug(f"保存 {symbol} 的 {len(records)} 条MA记录")
                return True
                
            except Exception as e:
                self.logger.error(f"保存 {symbol} MA数据失败: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"计算 {symbol} MA指标失败: {e}")
            return False
    
    def initial_52w_calculation(self, symbols: List[str] = None, days: int = 400) -> bool:
        """
        首次52周新高新低计算
        """
        if symbols is None:
            symbols = self.get_all_symbols()
        
        self.logger.info(f"开始首次52周计算，股票数量: {len(symbols)}")
        
        # 分批处理
        batch_size = 50
        success_count = 0
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            self.logger.info(f"处理批次 {i//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}")
            
            try:
                # 获取历史数据
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

                self.logger.info(f"处理批次 {i//batch_size + 1}: 获取 {len(batch_symbols)} 只股票的52周数据")

                price_data = download_hist_price(
                    symbols=batch_symbols,
                    interval='1d',
                    start=start_date,
                    columns=['h', 'l', 'c'],
                    threads=10
                )
                
                # 计算并保存52周指标
                for symbol, df in price_data.items():
                    if self._calculate_and_save_52w(symbol, df):
                        success_count += 1
                        
            except Exception as e:
                self.logger.error(f"批次处理失败: {e}")
                continue
        
        self.logger.info(f"首次52周计算完成，成功: {success_count}/{len(symbols)}")
        return success_count > 0
    
    def _calculate_and_save_52w(self, symbol: str, df: pd.DataFrame) -> bool:
        """计算并保存单个股票的52周指标"""
        try:
            if df.empty or len(df) < 252:  # 至少需要1年数据
                return False
            
            # 确保索引是日期类型并排序
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            df = df.sort_index()
            
            # 计算52周滚动最高最低价
            df['high_52w'] = df['high'].rolling(window=252, min_periods=252).max()
            df['low_52w'] = df['low'].rolling(window=252, min_periods=252).min()
            
            # 判断是否创新高新低
            df['is_new_high'] = (df['close'] >= df['high_52w'] * 0.999)  # 允许0.1%误差
            df['is_new_low'] = (df['close'] <= df['low_52w'] * 1.001)   # 允许0.1%误差
            
            # 计算距离新高新低的天数
            df['days_from_high'] = 0
            df['days_from_low'] = 0
            
            # 只保存有效数据（至少有52周数据）
            valid_data = df.dropna(subset=['high_52w'])
            if valid_data.empty:
                return False
            
            # 保存到数据库
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()
            
            try:
                # 准备数据
                records = []
                for date, row in valid_data.iterrows():
                    records.append((
                        symbol,
                        date.date(),
                        float(row['close']),
                        float(row['high_52w']),
                        float(row['low_52w']),
                        bool(row['is_new_high']),
                        bool(row['is_new_low']),
                        0,  # days_from_high 暂时设为0
                        0   # days_from_low 暂时设为0
                    ))
                
                # 批量插入
                cursor.executemany("""
                INSERT INTO precomputed_52w_indicators 
                (symbol, date, close_price, high_52w, low_52w, is_new_high, is_new_low, days_from_high, days_from_low)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                close_price = VALUES(close_price),
                high_52w = VALUES(high_52w),
                low_52w = VALUES(low_52w),
                is_new_high = VALUES(is_new_high),
                is_new_low = VALUES(is_new_low),
                updated_at = CURRENT_TIMESTAMP
                """, records)
                
                conn.commit()
                self.logger.debug(f"保存 {symbol} 的 {len(records)} 条52周记录")
                return True
                
            except Exception as e:
                self.logger.error(f"保存 {symbol} 52周数据失败: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"计算 {symbol} 52周指标失败: {e}")
            return False
    
    def update_latest_indicators(self, symbols: List[str] = None) -> Dict[str, int]:
        """
        增量更新最新指标
        只计算最新的数据点
        """
        if symbols is None:
            symbols = self.get_all_symbols()
        
        self.logger.info(f"开始增量更新，股票数量: {len(symbols)}")
        
        results = {'ma_updated': 0, '52w_updated': 0, 'failed': 0}
        
        # 获取最新的交易日数据
        today = datetime.now().strftime('%Y-%m-%d')
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # 分批处理
        batch_size = 100
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            
            try:
                # 获取最新数据
                price_data = download_hist_price(
                    symbols=batch_symbols,
                    interval='1d',
                    start=yesterday,
                    end=today,
                    columns=['h', 'l', 'c'],
                    threads=10
                )
                
                # 更新每个股票的指标
                for symbol, df in price_data.items():
                    try:
                        if self._update_single_stock_indicators(symbol, df):
                            results['ma_updated'] += 1
                            results['52w_updated'] += 1
                        else:
                            results['failed'] += 1
                    except Exception as e:
                        self.logger.error(f"更新 {symbol} 失败: {e}")
                        results['failed'] += 1
                        
            except Exception as e:
                self.logger.error(f"批次更新失败: {e}")
                results['failed'] += len(batch_symbols)
        
        self.logger.info(f"增量更新完成: MA={results['ma_updated']}, 52W={results['52w_updated']}, 失败={results['failed']}")
        return results
    
    def _update_single_stock_indicators(self, symbol: str, df: pd.DataFrame) -> bool:
        """更新单个股票的最新指标"""
        if df.empty:
            return False
        
        try:
            # 获取最新价格
            latest_data = df.iloc[-1]
            latest_date = df.index[-1].date()
            latest_close = latest_data['close']
            latest_high = latest_data['high']
            latest_low = latest_data['low']
            
            conn = pymysql.connect(**self.db_config)
            cursor = conn.cursor()
            
            try:
                # 更新MA指标
                self._update_ma_indicators(cursor, symbol, latest_date, latest_close, latest_high, latest_low)
                
                # 更新52周指标
                self._update_52w_indicators(cursor, symbol, latest_date, latest_close, latest_high, latest_low)
                
                conn.commit()
                return True
                
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"更新 {symbol} 指标失败: {e}")
            return False
    
    def _update_ma_indicators(self, cursor, symbol: str, date, close: float, high: float, low: float):
        """更新MA指标（增量计算）"""
        # 获取最近50和200天的数据用于计算MA
        cursor.execute("""
        SELECT close_price FROM precomputed_ma_indicators 
        WHERE symbol = %s AND date <= %s 
        ORDER BY date DESC LIMIT 200
        """, (symbol, date))
        
        historical_prices = [row[0] for row in cursor.fetchall()]
        historical_prices.reverse()  # 按时间正序
        historical_prices.append(close)  # 添加最新价格
        
        # 计算MA
        ma50 = None
        ma200 = None
        
        if len(historical_prices) >= 50:
            ma50 = np.mean(historical_prices[-50:])
        
        if len(historical_prices) >= 200:
            ma200 = np.mean(historical_prices[-200:])
        
        # 判断是否在MA之上
        above_ma50 = close > ma50 if ma50 else False
        above_ma200 = close > ma200 if ma200 else False
        
        # 插入或更新
        cursor.execute("""
        INSERT INTO precomputed_ma_indicators 
        (symbol, date, close_price, ma50, ma200, above_ma50, above_ma200)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        close_price = VALUES(close_price),
        ma50 = VALUES(ma50),
        ma200 = VALUES(ma200),
        above_ma50 = VALUES(above_ma50),
        above_ma200 = VALUES(above_ma200),
        updated_at = CURRENT_TIMESTAMP
        """, (symbol, date, close, ma50, ma200, above_ma50, above_ma200))
    
    def _update_52w_indicators(self, cursor, symbol: str, date, close: float, high: float, low: float):
        """更新52周指标（增量计算）"""
        # 获取过去252天的最高最低价
        cursor.execute("""
        SELECT MAX(close_price), MIN(close_price) FROM (
            SELECT close_price FROM precomputed_52w_indicators 
            WHERE symbol = %s AND date <= %s 
            ORDER BY date DESC LIMIT 252
        ) t
        """, (symbol, date))
        
        result = cursor.fetchone()
        if not result or not result[0]:
            return
        
        # 计算52周最高最低价（包含当前价格）
        historical_high, historical_low = result
        high_52w = max(historical_high, high)
        low_52w = min(historical_low, low)
        
        # 判断是否创新高新低
        is_new_high = close >= high_52w * 0.999
        is_new_low = close <= low_52w * 1.001
        
        # 插入或更新
        cursor.execute("""
        INSERT INTO precomputed_52w_indicators 
        (symbol, date, close_price, high_52w, low_52w, is_new_high, is_new_low, days_from_high, days_from_low)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        close_price = VALUES(close_price),
        high_52w = VALUES(high_52w),
        low_52w = VALUES(low_52w),
        is_new_high = VALUES(is_new_high),
        is_new_low = VALUES(is_new_low),
        updated_at = CURRENT_TIMESTAMP
        """, (symbol, date, close, high_52w, low_52w, is_new_high, is_new_low, 0, 0))

if __name__ == "__main__":
    import sys
    
    indicator_system = PrecomputedIndicators()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--init-ma":
            print("开始首次MA计算...")
            indicator_system.initial_ma_calculation()
        elif sys.argv[1] == "--init-52w":
            print("开始首次52周计算...")
            indicator_system.initial_52w_calculation()
        elif sys.argv[1] == "--update":
            print("开始增量更新...")
            results = indicator_system.update_latest_indicators()
            print(f"更新结果: {results}")
        else:
            print("用法:")
            print("  --init-ma   : 首次MA计算")
            print("  --init-52w  : 首次52周计算")
            print("  --update    : 增量更新")
    else:
        print("请指定操作参数")
