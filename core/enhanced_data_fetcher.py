#!/usr/bin/env python3
"""
增强的数据获取器 - 支持获取缺失股票的最近价格数据
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))

from utils import download_hist_price

logger = logging.getLogger(__name__)

def download_hist_price_with_fallback(
    symbols: List[str], 
    interval: str, 
    start: str, 
    end: str = None,
    columns: List[str] = None, 
    threads: int = 10,
    max_fallback_days: int = 30,
    verbose: bool = True
) -> Dict[str, pd.DataFrame]:
    """
    增强版历史价格数据获取，支持回退到最近可用数据
    
    Args:
        symbols: 股票代码列表
        interval: 时间间隔 ('1h', '1d', etc.)
        start: 开始日期
        end: 结束日期
        columns: 需要的列
        threads: 线程数
        max_fallback_days: 最大回退天数
        verbose: 是否显示详细信息
    
    Returns:
        股票价格数据字典
    """
    
    if verbose:
        logger.info(f"🔄 获取 {len(symbols)} 只股票的 {interval} 数据，时间范围: {start} 到 {end}")
    
    # 第一次尝试：使用原始时间范围
    price_data = download_hist_price(
        symbols=symbols,
        interval=interval,
        start=start,
        end=end,
        columns=columns,
        threads=threads,
        verbose=verbose
    )
    
    initial_count = len(price_data)
    missing_symbols = set(symbols) - set(price_data.keys())
    
    if verbose:
        logger.info(f"✅ 初始获取: {initial_count}/{len(symbols)} 只股票有数据")
        if missing_symbols:
            logger.info(f"⚠️  缺失 {len(missing_symbols)} 只股票数据: {sorted(list(missing_symbols))[:5]}...")
    
    # 如果有缺失的股票，尝试获取更近期的数据
    if missing_symbols and max_fallback_days > 0:
        recovered_data = _recover_missing_stocks(
            missing_symbols, 
            interval, 
            start, 
            end, 
            columns, 
            threads, 
            max_fallback_days,
            verbose
        )
        
        # 合并恢复的数据
        price_data.update(recovered_data)
        
        if verbose:
            logger.info(f"🔧 数据恢复: 额外获取到 {len(recovered_data)} 只股票数据")
            logger.info(f"📊 最终结果: {len(price_data)}/{len(symbols)} 只股票有数据")
    
    return price_data

def _recover_missing_stocks(
    missing_symbols: set,
    interval: str,
    original_start: str,
    original_end: str,
    columns: List[str],
    threads: int,
    max_fallback_days: int,
    verbose: bool
) -> Dict[str, pd.DataFrame]:
    """
    尝试恢复缺失股票的数据
    """
    recovered_data = {}

    if not missing_symbols:
        return recovered_data

    if verbose:
        print(f"🔍 尝试恢复 {len(missing_symbols)} 只缺失股票: {sorted(list(missing_symbols))}")

    # 策略1: 扩展时间范围到更早的日期
    if verbose:
        print(f"🔍 策略1: 扩展时间范围获取缺失股票数据...")

    extended_start = (datetime.strptime(original_start, '%Y-%m-%d') - timedelta(days=max_fallback_days)).strftime('%Y-%m-%d')

    if verbose:
        print(f"   扩展时间范围: {extended_start} 到 {original_end}")

    extended_data = download_hist_price(
        symbols=list(missing_symbols),
        interval=interval,
        start=extended_start,
        end=original_end,
        columns=columns,
        threads=threads,
        verbose=False  # 避免重复的进度条
    )

    if verbose:
        print(f"   扩展获取结果: {len(extended_data)} 只股票有数据")

    # 处理扩展数据：只保留最近的有效数据点
    for symbol, df in extended_data.items():
        if df is not None and not df.empty:
            if verbose:
                print(f"   {symbol}: 找到 {len(df)} 条历史数据")
            # 获取最近的有效数据点
            recent_data = _get_most_recent_data(df, original_start, original_end, interval)
            if recent_data is not None:
                recovered_data[symbol] = recent_data
                if verbose:
                    print(f"   {symbol}: ✅ 成功恢复数据")
            else:
                if verbose:
                    print(f"   {symbol}: ❌ 无法生成有效数据点")

    still_missing = missing_symbols - set(recovered_data.keys())

    if verbose and recovered_data:
        print(f"✅ 策略1成功: 恢复了 {len(recovered_data)} 只股票")

    # 策略2: 对于仍然缺失的股票，尝试使用日度数据
    if still_missing and interval != '1d':
        if verbose:
            print(f"🔍 策略2: 使用日度数据填补剩余 {len(still_missing)} 只股票...")

        daily_data = download_hist_price(
            symbols=list(still_missing),
            interval='1d',
            start=extended_start,
            end=original_end,
            columns=columns,
            threads=threads,
            verbose=False
        )

        if verbose:
            print(f"   日度数据获取结果: {len(daily_data)} 只股票有数据")

        # 将日度数据转换为目标时间间隔的格式
        for symbol, df in daily_data.items():
            if df is not None and not df.empty:
                if verbose:
                    print(f"   {symbol}: 找到 {len(df)} 条日度数据")
                converted_data = _convert_daily_to_interval(df, interval, original_start, original_end)
                if converted_data is not None:
                    recovered_data[symbol] = converted_data
                    if verbose:
                        print(f"   {symbol}: ✅ 成功转换日度数据")

        if verbose:
            daily_recovered = len([s for s in still_missing if s in daily_data])
            if daily_recovered > 0:
                print(f"✅ 策略2成功: 额外恢复了 {daily_recovered} 只股票")

    return recovered_data

def _get_most_recent_data(df: pd.DataFrame, target_start: str, target_end: str, interval: str) -> Optional[pd.DataFrame]:
    """
    从扩展的数据中获取最近的有效数据点
    """
    if df is None or df.empty:
        return None
    
    # 获取目标时间范围内的数据
    target_start_dt = pd.to_datetime(target_start)
    target_end_dt = pd.to_datetime(target_end) if target_end else pd.Timestamp.now()
    
    # 如果目标时间范围内有数据，直接返回
    mask = (df.index >= target_start_dt) & (df.index <= target_end_dt)
    target_data = df[mask]
    
    if not target_data.empty:
        return target_data
    
    # 如果目标时间范围内没有数据，使用最近的一个数据点
    # 找到最接近目标开始时间的数据点
    recent_data = df[df.index <= target_end_dt]
    if recent_data.empty:
        return None
    
    # 获取最近的一个数据点，并复制到目标时间范围
    latest_row = recent_data.iloc[-1:].copy()
    
    # 创建一个在目标时间范围内的数据点
    if interval == '1h':
        # 对于小时数据，使用目标开始时间
        new_index = [target_start_dt]
    elif interval == '1d':
        # 对于日度数据，使用目标开始日期
        new_index = [target_start_dt.normalize()]
    else:
        # 其他情况，使用目标开始时间
        new_index = [target_start_dt]
    
    latest_row.index = new_index
    return latest_row

def _convert_daily_to_interval(df: pd.DataFrame, target_interval: str, start: str, end: str) -> Optional[pd.DataFrame]:
    """
    将日度数据转换为目标时间间隔的数据
    """
    if df is None or df.empty:
        return None
    
    # 获取最近的日度数据
    latest_daily = df.iloc[-1:].copy()
    
    # 根据目标间隔创建时间索引
    start_dt = pd.to_datetime(start)
    end_dt = pd.to_datetime(end) if end else pd.Timestamp.now()
    
    if target_interval == '1h':
        # 创建小时级别的数据点（使用最近的日度数据）
        # 简单起见，只创建一个数据点在目标时间范围的开始
        new_index = [start_dt]
    elif target_interval in ['5min', '15min', '1min']:
        # 对于分钟级数据，也只创建一个数据点
        new_index = [start_dt]
    else:
        # 其他情况
        new_index = [start_dt]
    
    # 复制数据到新的时间索引
    result = latest_daily.copy()
    result.index = new_index
    
    return result

def test_enhanced_fetcher():
    """测试增强版数据获取器"""
    # 测试SP500中缺失数据的股票
    test_symbols = ['ANSS', 'HES', 'AAPL', 'MSFT']  # 包含缺失的和正常的股票

    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')

    print("🧪 测试增强版数据获取器")
    print("=" * 50)

    # 先测试原始获取器
    print("\n📊 原始获取器结果:")
    original_data = download_hist_price(
        symbols=test_symbols,
        interval='1h',
        start=start_date,
        end=end_date,
        columns=['h', 'l', 'c', 'v'],
        threads=5,
        verbose=True
    )

    for symbol in test_symbols:
        if symbol in original_data:
            df = original_data[symbol]
            print(f"  {symbol}: ✅ {len(df)} 条数据")
        else:
            print(f"  {symbol}: ❌ 无数据")

    # 使用增强版获取器
    print(f"\n📊 增强版获取器结果:")
    enhanced_data = download_hist_price_with_fallback(
        symbols=test_symbols,
        interval='1h',
        start=start_date,
        end=end_date,
        columns=['h', 'l', 'c', 'v'],
        threads=5,
        max_fallback_days=30,
        verbose=True
    )

    print(f"\n📊 最终对比:")
    print(f"原始获取: {len(original_data)}/{len(test_symbols)} 只股票")
    print(f"增强获取: {len(enhanced_data)}/{len(test_symbols)} 只股票")
    print(f"改进效果: +{len(enhanced_data) - len(original_data)} 只股票")

    for symbol in test_symbols:
        original_has = symbol in original_data
        enhanced_has = symbol in enhanced_data

        if not original_has and enhanced_has:
            df = enhanced_data[symbol]
            print(f"  {symbol}: 🎯 成功恢复! {len(df)} 条数据")
        elif original_has and enhanced_has:
            print(f"  {symbol}: ✅ 正常获取")
        else:
            print(f"  {symbol}: ❌ 仍然缺失")

if __name__ == "__main__":
    test_enhanced_fetcher()
