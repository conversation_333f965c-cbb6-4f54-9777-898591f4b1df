import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from logging.handlers import RotatingFileHandler

import numpy as np
import pandas as pd
import pymysql
import pytz
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed

from dotenv import load_dotenv

load_dotenv()


# 配置日志记录
def setup_logging():
    """配置日志记录系统"""
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(level=logging.INFO, format=log_format)

    # 过滤第三方库的警告日志
    logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests.packages.urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests').setLevel(logging.WARNING)

    # 创建专门的logger
    logger = logging.getLogger('weekly_market_breadth_gics')
    logger.setLevel(logging.INFO)

    # 移除默认handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 文件handler
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'weekly_market_breadth_gics.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(file_handler)

    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(console_handler)

    return logger


def get_weekly_price(companies: List[str], start_date: str = None, end_date: str = None):
    """获取周度价格数据"""
    from utils import download_hist_price

    # 如果没有指定开始日期，默认获取12周的数据
    if start_date is None:
        if end_date:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            end_dt = datetime.now()
        start_dt = end_dt - timedelta(weeks=13)  # 12周前
        start_date = start_dt.strftime('%Y-%m-%d')

    company_price = download_hist_price(
        symbols=companies,  # 使用正确的参数名
        interval='1w',      # 周度数据
        start=start_date,
        end=end_date,
        columns=['h', 'l', 'c', 'v'],
        threads=10
    )
    return company_price


def calculate_weekly_breadth_metrics(current_time, market: str, stock_data: Dict) -> Dict[str, float]:
    """
    计算市场的周度广度指标
    
    :param current_time: 当前时间
    :param market: 市场/板块名称
    :param stock_data: 股票数据字典
    :return: 广度指标字典
    """
    try:
        # 获取板块股票列表
        sql = f"SELECT company FROM index_company_mapping_gics WHERE market='{market}'"
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        cursor = conn.cursor()
        cursor.execute(sql)
        companies = cursor.fetchall()
        cursor.close()
        conn.close()
        companies = [c[0] for c in companies]

        if not companies:
            # logger可能在多进程中不可用，使用print代替
            print(f"WARNING: No companies found for market: {market}")
            return {}

        # 处理股票数据
        latest_week_data = {}
        prev_week_data = {}
        
        for symbol in companies:
            if symbol in stock_data and isinstance(stock_data[symbol], pd.DataFrame):
                df = stock_data[symbol]
                if not df.empty:
                    # 确保索引是日期类型并排序
                    if not isinstance(df.index, pd.DatetimeIndex):
                        df.index = pd.to_datetime(df.index)
                    df = df.sort_index()
                    
                    if len(df) >= 1:
                        latest_week_data[symbol] = df.iloc[-1]  # 最新一周
                    if len(df) >= 2:
                        prev_week_data[symbol] = df.iloc[-2]   # 前一周

        if not latest_week_data:
            print(f"WARNING: No valid weekly data for market: {market}")
            return {}

        # 计算基础广度指标
        total_stocks = len(companies)
        valid_stocks = len(latest_week_data)
        advances = 0
        declines = 0
        unchanged = 0
        advancing_volume = 0
        declining_volume = 0

        # 计算涨跌家数和成交量广度
        for symbol in latest_week_data:
            # 修正列名：使用'close'而不是'c'，'volume'而不是'v'
            current_close = latest_week_data[symbol].get('close', 0)
            current_volume = latest_week_data[symbol].get('volume', 0)

            if symbol in prev_week_data:
                prev_close = prev_week_data[symbol].get('close', 0)
                
                if current_close > prev_close:
                    advances += 1
                    advancing_volume += current_volume
                elif current_close < prev_close:
                    declines += 1
                    declining_volume += current_volume
                else:
                    unchanged += 1
            else:
                unchanged += 1

        # 计算衍生指标
        avg_rsi = 50.0  # 周度RSI计算复杂，暂用默认值
        
        # 板块纯度
        if valid_stocks > 0:
            advance_ratio = advances / valid_stocks
            decline_ratio = declines / valid_stocks
            purity = advance_ratio**2 + decline_ratio**2
        else:
            purity = 0.5

        # 内部健康度
        if valid_stocks > 0:
            ad_ratio = advances / max(declines, 1)
            participation_rate = (advances + declines) / valid_stocks
            internal_health = min(100, (ad_ratio * 20 + participation_rate * 50 + purity * 30))
        else:
            internal_health = 50.0

        # 动量一致性
        if advances + declines > 0:
            momentum_coherence = max(advances, declines) / (advances + declines)
        else:
            momentum_coherence = 0.5

        # 组装结果
        result = {
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'advancing_volume': float(advancing_volume),
            'declining_volume': float(declining_volume),
            'new_highs_52w': 0,  # 周度暂不计算，需要从日度数据获取
            'new_lows_52w': 0,   # 周度暂不计算，需要从日度数据获取
            'above_ma50': 0,     # 周度暂不计算，需要从日度数据获取
            'above_ma200': 0,    # 周度暂不计算，需要从日度数据获取
            'avg_rsi': avg_rsi,
            'purity': float(purity),
            'internal_health': float(internal_health),
            'momentum_coherence': float(momentum_coherence),
            'divergence_type': 'none',
            'divergence_severity': 0.0
        }

        print(f"INFO: Weekly breadth for {market}: {advances}↑ {declines}↓ {unchanged}→, health: {internal_health:.1f}")
        return result

    except Exception as e:
        print(f"ERROR: Error calculating weekly breadth for {market}: {e}")
        return {}


def process_market_weekly_breadth(market_data):
    """处理单个市场的周度广度计算"""
    market, current_time = market_data

    # 在每个进程中重新设置logger
    logger = logging.getLogger('weekly_market_breadth_gics')
    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    try:
        logger.info(f"Processing weekly breadth for market: {market}")
        
        # 获取板块股票列表
        sql = f"SELECT company FROM index_company_mapping_gics WHERE market='{market}'"
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        cursor = conn.cursor()
        cursor.execute(sql)
        companies = cursor.fetchall()
        cursor.close()
        conn.close()
        companies = [c[0] for c in companies]

        if not companies:
            logger.warning(f"No companies found for market: {market}")
            return None

        # 不限制股票数量，使用所有股票
        logger.info(f"Using all {len(companies)} stocks for market: {market}")

        # 获取周度价格数据
        end_date = current_time.strftime('%Y-%m-%d')
        stock_data = get_weekly_price(companies, end_date=end_date)
        
        if not stock_data:
            logger.warning(f"No stock data retrieved for market: {market}")
            return None

        # 计算广度指标
        breadth_metrics = calculate_weekly_breadth_metrics(current_time, market, stock_data)
        
        if breadth_metrics:
            breadth_metrics['market'] = market
            breadth_metrics['recorded_at'] = current_time
            breadth_metrics['timeframe'] = '1w'
            return breadth_metrics
        else:
            return None

    except Exception as e:
        logger.error(f"Error processing market {market}: {e}")
        return None


def main():
    """主函数"""
    logger = setup_logging()
    logger.info("Starting weekly market breadth analysis...")

    # 获取当前时间
    current_time = datetime.now(tz=pytz.UTC)
    
    # 获取所有市场列表
    try:
        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT market FROM index_company_mapping_gics WHERE market IS NOT NULL")
        markets = [row[0] for row in cursor.fetchall()]
        cursor.close()
        conn.close()
        
        logger.info(f"Found {len(markets)} markets to process")
        
    except Exception as e:
        logger.error(f"Failed to get markets list: {e}")
        return

    # 并行处理所有市场
    process_count = min(multiprocessing.cpu_count(), len(markets), 8)
    logger.info(f"Using {process_count} processes for parallel computation")

    market_data_list = [(market, current_time) for market in markets]
    results = []

    with ProcessPoolExecutor(max_workers=process_count) as executor:
        future_to_market = {executor.submit(process_market_weekly_breadth, market_data): market_data[0] 
                           for market_data in market_data_list}
        
        for future in as_completed(future_to_market):
            market = future_to_market[future]
            try:
                result = future.result()
                if result:
                    results.append(result)
                    logger.info(f"Completed weekly breadth calculation for {market}")
                else:
                    logger.warning(f"No result for market {market}")
            except Exception as e:
                logger.error(f"Market {market} generated an exception: {e}")

    # 保存结果到数据库
    if results:
        try:
            conn = pymysql.Connection(
                host=os.environ['DEFAULT_DB_HOST'],
                port=int(os.environ['DEFAULT_DB_PORT']),
                user=os.environ['DEFAULT_DB_USER'],
                password=os.environ['DEFAULT_DB_PASSWORD'],
                database=os.environ['DEFAULT_DB_NAME'],
            )
            cursor = conn.cursor()

            insert_sql = """
            INSERT INTO market_breadth_metrics_gics 
            (recorded_at, market, timeframe, total_stocks, advances, declines, unchanged,
             advancing_volume, declining_volume, new_highs_52w, new_lows_52w,
             above_ma50, above_ma200, avg_rsi, purity, internal_health, 
             momentum_coherence, divergence_type, divergence_severity)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            for result in results:
                cursor.execute(insert_sql, (
                    result['recorded_at'], result['market'], result['timeframe'],
                    result['total_stocks'], result['advances'], result['declines'], result['unchanged'],
                    result['advancing_volume'], result['declining_volume'],
                    result['new_highs_52w'], result['new_lows_52w'],
                    result['above_ma50'], result['above_ma200'], result['avg_rsi'],
                    result['purity'], result['internal_health'], result['momentum_coherence'],
                    result['divergence_type'], result['divergence_severity']
                ))

            conn.commit()
            logger.info(f"Successfully saved {len(results)} weekly breadth records to database")

            # 统计信息
            df_results = pd.DataFrame(results)
            logger.info(f"Weekly breadth analysis completed for {len(results)} markets")
            logger.info(f"Average internal health: {df_results['internal_health'].mean():.1f}")
            logger.info(f"Average momentum coherence: {df_results['momentum_coherence'].mean():.3f}")

        except Exception as e:
            logger.error(f"Database insertion failed: {e}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()
    else:
        logger.error("No results to save")


if __name__ == '__main__':
    main()
