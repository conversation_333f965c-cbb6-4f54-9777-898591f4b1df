"""
LLM交互增强器 - 生成供LLM理解的完整上下文
基于优化文档要求，提供可解释的分析结果和交互式问答预设
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json
import logging

from .decision_tracker import DecisionTracker
from .market_breadth_analyzer import BreadthMetrics
from .improved_momentum_coherence import CoherenceDetails

logger = logging.getLogger(__name__)


@dataclass
class MarketContext:
    """市场上下文信息"""
    market_name: str
    timestamp: datetime
    overall_sentiment: str
    key_metrics: Dict[str, float]
    risk_factors: List[str]
    opportunities: List[str]
    market_phase: str


@dataclass
class LLMContext:
    """LLM交互上下文"""
    executive_summary: str
    market_state_explanation: str
    decision_logic_explanation: str
    risk_reward_analysis: Dict
    actionable_recommendations: List[Dict]
    interactive_qa_presets: List[Dict]
    confidence_breakdown: Dict
    supporting_evidence: List[str]
    alternative_scenarios: List[Dict]


class LLMInteractionEnhancer:
    """LLM交互增强器 - 将技术分析结果转换为LLM友好的上下文"""
    
    def __init__(self, decision_tracker: Optional[DecisionTracker] = None):
        """
        初始化LLM交互增强器
        
        Args:
            decision_tracker: 可选的决策追踪器
        """
        self.tracker = decision_tracker
        self.context_templates = self._load_context_templates()
        
    def enhance_breadth_analysis(self, breadth_metrics: BreadthMetrics, 
                                market_context: Optional[MarketContext] = None) -> LLMContext:
        """
        增强市场广度分析，生成LLM友好的上下文
        
        Args:
            breadth_metrics: 市场广度指标
            market_context: 可选的市场上下文
            
        Returns:
            LLMContext: LLM交互上下文
        """
        # 1. 生成执行摘要
        executive_summary = self._generate_executive_summary(breadth_metrics, market_context)
        
        # 2. 市场状态解释
        market_state_explanation = self._explain_market_state(breadth_metrics)
        
        # 3. 决策逻辑说明
        decision_logic_explanation = self._explain_decision_logic(breadth_metrics)
        
        # 4. 风险收益分析
        risk_reward_analysis = self._analyze_risk_reward(breadth_metrics)
        
        # 5. 可操作建议
        actionable_recommendations = self._generate_actionable_recommendations(breadth_metrics)
        
        # 6. 交互式问答预设
        interactive_qa_presets = self._generate_qa_presets(breadth_metrics)
        
        # 7. 置信度分解
        confidence_breakdown = self._breakdown_confidence(breadth_metrics)
        
        # 8. 支持证据
        supporting_evidence = self._collect_supporting_evidence(breadth_metrics)
        
        # 9. 替代场景
        alternative_scenarios = self._generate_alternative_scenarios(breadth_metrics)
        
        return LLMContext(
            executive_summary=executive_summary,
            market_state_explanation=market_state_explanation,
            decision_logic_explanation=decision_logic_explanation,
            risk_reward_analysis=risk_reward_analysis,
            actionable_recommendations=actionable_recommendations,
            interactive_qa_presets=interactive_qa_presets,
            confidence_breakdown=confidence_breakdown,
            supporting_evidence=supporting_evidence,
            alternative_scenarios=alternative_scenarios
        )
    
    def _generate_executive_summary(self, metrics: BreadthMetrics, 
                                  context: Optional[MarketContext] = None) -> str:
        """生成执行摘要"""
        sector = metrics.sector_name
        health = metrics.internal_health
        coherence = metrics.momentum_coherence or 0.5
        
        # 健康度评级
        if health > 70:
            health_grade = "强势"
        elif health > 50:
            health_grade = "中性"
        else:
            health_grade = "疲弱"
        
        # 一致性评级
        if coherence > 0.7:
            coherence_grade = "高度一致"
        elif coherence > 0.4:
            coherence_grade = "适度分化"
        else:
            coherence_grade = "严重分化"
        
        # 背离状态
        divergence_status = "无背离"
        if metrics.price_breadth_divergence:
            div_type = metrics.price_breadth_divergence['type']
            if div_type == 'positive':
                divergence_status = "正背离（价跌内强）"
            elif div_type == 'negative':
                divergence_status = "负背离（价涨内弱）"
        
        summary = f"""
📊 {sector}板块分析摘要：
• 内部健康度：{health:.1f}/100 ({health_grade})
• 动量一致性：{coherence:.2f} ({coherence_grade})
• 背离状态：{divergence_status}
• 涨跌比：{metrics.ad_ratio:.2f}
• 参与率：{metrics.participation_rate:.1%}

💡 核心观点：{self._get_core_insight(metrics)}
        """.strip()
        
        return summary
    
    def _explain_market_state(self, metrics: BreadthMetrics) -> str:
        """解释市场状态"""
        explanations = []
        
        # 健康度解释
        health = metrics.internal_health
        if health > 70:
            explanations.append(f"板块内部健康度{health:.1f}分，表明多数股票表现强势，技术面良好。")
        elif health > 50:
            explanations.append(f"板块内部健康度{health:.1f}分，表明板块整体表现中性，存在结构性机会。")
        else:
            explanations.append(f"板块内部健康度{health:.1f}分，表明板块内部疲弱，需要谨慎操作。")
        
        # 一致性解释
        if metrics.coherence_details:
            coherence_type = metrics.coherence_details.coherence_type
            interpretation = metrics.coherence_details.interpretation
            explanations.append(f"动量一致性分析显示：{interpretation}")
        
        # 背离解释
        if metrics.price_breadth_divergence:
            div = metrics.price_breadth_divergence
            explanations.append(f"检测到{div['type']}背离，严重度{div['severity']:.3f}，{div['description']}")
        
        return " ".join(explanations)
    
    def _explain_decision_logic(self, metrics: BreadthMetrics) -> str:
        """解释决策逻辑"""
        logic_steps = []
        
        # 步骤1：基础指标分析
        logic_steps.append(f"1. 基础指标：涨跌比{metrics.ad_ratio:.2f}，纯度{metrics.purity:.3f}，参与率{metrics.participation_rate:.1%}")
        
        # 步骤2：技术指标分析
        logic_steps.append(f"2. 技术面：{metrics.ma50_breadth:.1%}股票在50日均线上，{metrics.ma200_breadth:.1%}在200日均线上")
        
        # 步骤3：一致性分析
        if metrics.coherence_details:
            logic_steps.append(f"3. 一致性：{metrics.coherence_details.coherence_type}，{metrics.coherence_details.action_suggestion}")
        
        # 步骤4：综合评分
        logic_steps.append(f"4. 综合评分：内部健康度{metrics.internal_health:.1f}分，反映板块整体状况")
        
        return "\n".join(logic_steps)
    
    def _analyze_risk_reward(self, metrics: BreadthMetrics) -> Dict:
        """分析风险收益"""
        analysis = {
            "risk_level": "中等",
            "reward_potential": "中等",
            "risk_factors": [],
            "reward_drivers": [],
            "risk_score": 0.5,
            "reward_score": 0.5
        }
        
        # 风险评估
        risk_factors = []
        risk_score = 0.5
        
        if metrics.internal_health < 40:
            risk_factors.append("内部健康度低，技术面疲弱")
            risk_score += 0.2
        
        if metrics.momentum_coherence and metrics.momentum_coherence < 0.3:
            risk_factors.append("动量一致性低，板块分化严重")
            risk_score += 0.15
        
        if metrics.price_breadth_divergence and metrics.price_breadth_divergence['type'] == 'negative':
            risk_factors.append("负背离信号，价格与内部结构不匹配")
            risk_score += 0.2
        
        # 收益潜力评估
        reward_drivers = []
        reward_score = 0.5
        
        if metrics.internal_health > 70:
            reward_drivers.append("内部健康度高，技术面强势")
            reward_score += 0.2
        
        if metrics.momentum_coherence and metrics.momentum_coherence > 0.7:
            reward_drivers.append("动量一致性高，板块整体向好")
            reward_score += 0.15
        
        if metrics.price_breadth_divergence and metrics.price_breadth_divergence['type'] == 'positive':
            reward_drivers.append("正背离信号，内部结构强于价格表现")
            reward_score += 0.2
        
        # 风险等级
        if risk_score > 0.7:
            analysis["risk_level"] = "高"
        elif risk_score < 0.4:
            analysis["risk_level"] = "低"
        
        # 收益潜力等级
        if reward_score > 0.7:
            analysis["reward_potential"] = "高"
        elif reward_score < 0.4:
            analysis["reward_potential"] = "低"
        
        analysis.update({
            "risk_factors": risk_factors,
            "reward_drivers": reward_drivers,
            "risk_score": min(1.0, risk_score),
            "reward_score": min(1.0, reward_score)
        })
        
        return analysis
    
    def _generate_actionable_recommendations(self, metrics: BreadthMetrics) -> List[Dict]:
        """生成可操作建议"""
        recommendations = []
        
        # 基于健康度的建议
        if metrics.internal_health > 70:
            recommendations.append({
                "action": "积极配置",
                "reasoning": f"内部健康度{metrics.internal_health:.1f}分，板块表现强势",
                "timeframe": "中短期",
                "confidence": 0.8
            })
        elif metrics.internal_health < 40:
            recommendations.append({
                "action": "谨慎观望",
                "reasoning": f"内部健康度{metrics.internal_health:.1f}分，板块表现疲弱",
                "timeframe": "短期",
                "confidence": 0.7
            })
        
        # 基于一致性的建议
        if metrics.coherence_details:
            if metrics.coherence_details.coherence_type == "strong_consensus":
                recommendations.append({
                    "action": "板块配置",
                    "reasoning": "强共识状态，适合通过ETF或指数进行板块配置",
                    "timeframe": "中期",
                    "confidence": 0.85
                })
            elif metrics.coherence_details.coherence_type == "high_dispersion":
                recommendations.append({
                    "action": "精选个股",
                    "reasoning": "板块分化严重，需要精选强势个股",
                    "timeframe": "短期",
                    "confidence": 0.75
                })
        
        # 基于背离的建议
        if metrics.price_breadth_divergence:
            div = metrics.price_breadth_divergence
            if div['type'] == 'positive':
                recommendations.append({
                    "action": "逢低布局",
                    "reasoning": "正背离信号，内部结构强于价格表现，可考虑逢低布局",
                    "timeframe": "中期",
                    "confidence": div['confidence']
                })
            elif div['type'] == 'negative':
                recommendations.append({
                    "action": "减仓观望",
                    "reasoning": "负背离信号，价格强于内部结构，建议减仓观望",
                    "timeframe": "短期",
                    "confidence": div['confidence']
                })
        
        return recommendations
    
    def _generate_qa_presets(self, metrics: BreadthMetrics) -> List[Dict]:
        """生成交互式问答预设"""
        qa_presets = [
            {
                "question": f"{metrics.sector_name}板块现在适合投资吗？",
                "answer_template": "基于当前分析，{recommendation}。主要原因是{reasoning}。建议{timeframe}关注。",
                "context_keys": ["recommendations", "risk_reward_analysis"]
            },
            {
                "question": "这个板块的主要风险是什么？",
                "answer_template": "主要风险包括：{risk_factors}。风险等级为{risk_level}。",
                "context_keys": ["risk_reward_analysis"]
            },
            {
                "question": "为什么动量一致性这么重要？",
                "answer_template": "动量一致性反映板块内部股票的表现是否同步。当前{coherence_explanation}，这意味着{implication}。",
                "context_keys": ["coherence_details"]
            },
            {
                "question": "检测到背离意味着什么？",
                "answer_template": "背离表示价格走势与内部技术指标不一致。{divergence_explanation}，通常预示着{prediction}。",
                "context_keys": ["price_breadth_divergence"]
            }
        ]
        
        return qa_presets
