#!/usr/bin/env python3
"""
高效多时间框架市宽计算器
基于现有的预计算表，快速计算全部股票全部时间框架的市宽指标
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import pymysql
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config, get_algo_db_config

class EfficientMTFCalculator:
    """高效多时间框架市宽计算器"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.algo_config = get_algo_db_config()
        self.logger = self._setup_logger()
        
        # 支持的时间框架
        self.timeframes = ['5min', '15min', '1h', '1d', '1w', '1m']
        
        # 时间框架映射
        self.tf_map = {
            '5min': '5m',
            '15min': '15m', 
            '1h': '1h',
            '1d': '1d',
            '1w': '1w',
            '1m': '1M'
        }
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('efficient_mtf')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def calculate_all_timeframes_breadth(self, 
                                       markets: List[str] = None,
                                       days: int = 60,
                                       force_update: bool = False) -> Dict[str, Any]:
        """
        计算全部时间框架的市宽指标
        
        Args:
            markets: 市场列表，None表示所有市场
            days: 历史天数
            force_update: 是否强制更新预计算数据
        """
        if markets is None:
            markets = self._get_all_markets()
        
        self.logger.info(f"开始计算全部时间框架市宽: {len(markets)}个市场, {days}天历史")
        
        results = {
            'markets': markets,
            'timeframes': self.timeframes,
            'days': days,
            'start_time': datetime.now(),
            'success_count': 0,
            'failed_count': 0,
            'results': {}
        }
        
        # 第一步：检查并更新预计算数据
        if force_update:
            self.logger.info("强制更新预计算数据...")
            self._update_precomputed_data(days)
        
        # 第二步：并行计算各时间框架的市宽
        with ThreadPoolExecutor(max_workers=6) as executor:
            futures = []
            
            for timeframe in self.timeframes:
                future = executor.submit(
                    self._calculate_timeframe_breadth_batch,
                    timeframe, markets, days
                )
                futures.append((timeframe, future))
            
            # 收集结果
            for timeframe, future in futures:
                try:
                    tf_result = future.result()
                    results['results'][timeframe] = tf_result
                    results['success_count'] += tf_result['success_count']
                    results['failed_count'] += tf_result['failed_count']
                    
                    self.logger.info(f"✅ {timeframe}: 成功{tf_result['success_count']}, 失败{tf_result['failed_count']}")
                    
                except Exception as e:
                    self.logger.error(f"❌ {timeframe} 计算失败: {e}")
                    results['failed_count'] += len(markets)
        
        results['end_time'] = datetime.now()
        results['total_time'] = (results['end_time'] - results['start_time']).total_seconds()
        
        self.logger.info(f"全部时间框架计算完成: 总耗时{results['total_time']:.1f}秒")
        
        return results
    
    def _get_all_markets(self) -> List[str]:
        """获取所有市场"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT market FROM market_config_gics ORDER BY market")
            markets = [row[0] for row in cursor.fetchall()]
            return markets
        finally:
            conn.close()
    
    def _update_precomputed_data(self, days: int):
        """更新预计算数据"""
        # 这里可以调用现有的预计算更新逻辑
        # 或者检查数据是否足够新
        self.logger.info(f"检查预计算数据新鲜度...")
        
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 检查MA数据的最新日期
            cursor.execute("SELECT MAX(date) FROM precomputed_ma_indicators")
            ma_latest = cursor.fetchone()[0]
            
            # 检查52周数据的最新日期
            cursor.execute("SELECT MAX(date) FROM precomputed_52w_indicators")
            w52_latest = cursor.fetchone()[0]
            
            today = datetime.now().date()
            
            if ma_latest and (today - ma_latest).days <= 1:
                self.logger.info(f"✅ MA数据较新 (最新: {ma_latest})")
            else:
                self.logger.warning(f"⚠️ MA数据较旧 (最新: {ma_latest})")
            
            if w52_latest and (today - w52_latest).days <= 1:
                self.logger.info(f"✅ 52周数据较新 (最新: {w52_latest})")
            else:
                self.logger.warning(f"⚠️ 52周数据较旧 (最新: {w52_latest})")
                
        finally:
            conn.close()
    
    def _calculate_timeframe_breadth_batch(self, timeframe: str, markets: List[str], days: int) -> Dict[str, Any]:
        """批量计算单个时间框架的市宽"""
        
        result = {
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': 0,
            'markets': [],
            'total_records': 0
        }
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        for market in markets:
            try:
                market_result = self._calculate_single_market_breadth_fast(
                    market, timeframe, start_date, end_date
                )
                
                if market_result['success']:
                    result['success_count'] += 1
                    result['total_records'] += market_result['record_count']
                    result['markets'].append(market)
                else:
                    result['failed_count'] += 1
                    
            except Exception as e:
                self.logger.error(f"计算 {market} {timeframe} 失败: {e}")
                result['failed_count'] += 1
        
        return result
    
    def _calculate_single_market_breadth_fast(self, market: str, timeframe: str, 
                                            start_date, end_date) -> Dict[str, Any]:
        """快速计算单个市场的市宽 - 基于现有预计算数据"""
        
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 获取市场成分股
            cursor.execute("""
            SELECT company FROM index_company_mapping_gics WHERE market = %s
            """, (market,))
            
            symbols = [row[0] for row in cursor.fetchall()]
            if not symbols:
                return {'success': False, 'error': f'市场 {market} 没有成分股'}
            
            # 获取日期范围内的交易日
            cursor.execute("""
            SELECT DISTINCT date FROM precomputed_ma_indicators 
            WHERE date >= %s AND date <= %s
            ORDER BY date
            """, (start_date, end_date))
            
            trading_dates = [row[0] for row in cursor.fetchall()]
            if not trading_dates:
                return {'success': False, 'error': f'没有找到交易日数据'}
            
            breadth_records = []
            
            # 按日期计算市宽
            for trade_date in trading_dates:
                breadth_data = self._calculate_daily_breadth_from_precomputed(
                    market, symbols, trade_date, timeframe
                )
                
                if breadth_data:
                    breadth_records.append(breadth_data)
            
            # 保存到数据库
            if breadth_records:
                self._save_breadth_data_batch(breadth_records, timeframe)
            
            return {
                'success': True,
                'market': market,
                'timeframe': timeframe,
                'record_count': len(breadth_records),
                'date_range': f"{start_date} 到 {end_date}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
        finally:
            conn.close()
    
    def _calculate_daily_breadth_from_precomputed(self, market: str, symbols: List[str],
                                                trade_date, timeframe: str) -> Dict[str, Any]:
        """从预计算数据计算单日市宽"""

        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 获取MA数据 (基于实际表结构)
            symbol_placeholders = ','.join(['%s'] * len(symbols))
            cursor.execute(f"""
            SELECT symbol, close_price, ma50, ma200, above_ma50, above_ma200
            FROM precomputed_ma_indicators
            WHERE symbol IN ({symbol_placeholders})
            AND date = %s
            """, symbols + [trade_date])

            ma_data = cursor.fetchall()

            # 获取52周数据
            cursor.execute(f"""
            SELECT symbol, close_price, high_52w, low_52w, is_new_high, is_new_low
            FROM precomputed_52w_indicators
            WHERE symbol IN ({symbol_placeholders})
            AND date = %s
            """, symbols + [trade_date])

            w52_data = cursor.fetchall()

            if not ma_data:
                return None

            # 转换为DataFrame
            ma_df = pd.DataFrame(ma_data, columns=[
                'symbol', 'close_price', 'ma50', 'ma200', 'above_ma50', 'above_ma200'
            ])

            w52_df = pd.DataFrame(w52_data, columns=[
                'symbol', 'close_price_52w', 'high_52w', 'low_52w', 'is_new_high', 'is_new_low'
            ]) if w52_data else pd.DataFrame()

            # 合并数据
            if not w52_df.empty:
                combined_df = ma_df.merge(w52_df[['symbol', 'high_52w', 'low_52w', 'is_new_high', 'is_new_low']],
                                        on='symbol', how='left')
            else:
                combined_df = ma_df
                combined_df['is_new_high'] = False
                combined_df['is_new_low'] = False

            # 计算daily_return (需要前一日数据)
            combined_df = self._add_daily_return(combined_df, trade_date)

            # 计算市宽指标
            return self._calculate_breadth_metrics_from_df(market, trade_date, combined_df, timeframe)

        finally:
            conn.close()

    def _add_daily_return(self, df: pd.DataFrame, trade_date) -> pd.DataFrame:
        """添加日收益率计算"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 获取前一个交易日
            cursor.execute("""
            SELECT MAX(date) FROM precomputed_ma_indicators
            WHERE date < %s
            """, (trade_date,))

            prev_date = cursor.fetchone()[0]
            if not prev_date:
                # 如果没有前一日数据，设置daily_return为0
                df['daily_return'] = 0.0
                return df

            # 获取前一日收盘价
            symbols = df['symbol'].tolist()
            symbol_placeholders = ','.join(['%s'] * len(symbols))
            cursor.execute(f"""
            SELECT symbol, close_price
            FROM precomputed_ma_indicators
            WHERE symbol IN ({symbol_placeholders})
            AND date = %s
            """, symbols + [prev_date])

            prev_data = cursor.fetchall()
            prev_df = pd.DataFrame(prev_data, columns=['symbol', 'prev_close'])

            # 合并并计算收益率
            df = df.merge(prev_df, on='symbol', how='left')
            df['daily_return'] = (df['close_price'] - df['prev_close']) / df['prev_close']
            df['daily_return'] = df['daily_return'].fillna(0.0)

            # 删除临时列
            df = df.drop('prev_close', axis=1)

            return df

        finally:
            conn.close()
    
    def _calculate_breadth_metrics_from_df(self, market: str, trade_date, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """从DataFrame计算市宽指标"""
        
        # 基础统计
        total_stocks = len(df)
        advances = len(df[df['daily_return'] > 0])
        declines = len(df[df['daily_return'] < 0])
        unchanged = total_stocks - advances - declines
        
        # 成交量统计
        df['volume_value'] = df['volume'] * df['close_price']
        advancing_volume = df[df['daily_return'] > 0]['volume_value'].sum()
        declining_volume = df[df['daily_return'] < 0]['volume_value'].sum()
        total_volume = df['volume_value'].sum()
        
        # 新高新低
        new_highs = len(df[df['is_new_high'] == True])
        new_lows = len(df[df['is_new_low'] == True])
        
        # MA统计
        above_ma50 = len(df[df['above_ma50'] == True])
        above_ma200 = len(df[df['above_ma200'] == True])
        
        # RSI平均
        avg_rsi = df['rsi_14'].mean()
        
        # 收益率
        equal_weighted_return = df['daily_return'].mean()
        
        # 简化的扩展指标
        purity = abs(advances - declines) / total_stocks if total_stocks > 0 else 0
        
        # 内部健康度 (简化版)
        breadth_score = (advances / total_stocks) * 25 if total_stocks > 0 else 0
        volume_score = (advancing_volume / total_volume) * 25 if total_volume > 0 else 0
        ma_score = ((above_ma50 + above_ma200) / (2 * total_stocks)) * 50 if total_stocks > 0 else 0
        internal_health = breadth_score + volume_score + ma_score
        
        return {
            'recorded_at': trade_date,
            'market': market,
            'timeframe': timeframe,
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'advancing_volume': float(advancing_volume),
            'declining_volume': float(declining_volume),
            'total_volume': float(total_volume),
            'new_highs_52w': new_highs,
            'new_lows_52w': new_lows,
            'above_ma50': above_ma50,
            'above_ma200': above_ma200,
            'avg_rsi': float(avg_rsi) if not pd.isna(avg_rsi) else None,
            'market_cap_weighted_return': None,  # 需要市值数据
            'equal_weighted_return': float(equal_weighted_return) if not pd.isna(equal_weighted_return) else None,
            'purity': float(purity),
            'internal_health': float(internal_health),
            'momentum_coherence': None,  # 简化版暂不计算
            'divergence_type': 'none',
            'divergence_severity': 0.0,
            'coherence_details': None,
            'enhanced_divergence_details': None,
            'divergence_confidence': 0.0,
            'divergence_risk_level': 'minimal'
        }
    
    def _save_breadth_data_batch(self, breadth_records: List[Dict], timeframe: str):
        """批量保存市宽数据"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 准备数据
            records = []
            for record in breadth_records:
                records.append((
                    record['recorded_at'], record['market'], timeframe,
                    record['total_stocks'], record['advances'], record['declines'], record['unchanged'],
                    record['advancing_volume'], record['declining_volume'], record['total_volume'],
                    record['new_highs_52w'], record['new_lows_52w'],
                    record['above_ma50'], record['above_ma200'], record['avg_rsi'],
                    record['market_cap_weighted_return'], record['equal_weighted_return'],
                    record['purity'], record['internal_health'], record['momentum_coherence'],
                    record['divergence_type'], record['divergence_severity'],
                    record['divergence_confidence'], record['divergence_risk_level'],
                    None, None  # coherence_details, enhanced_divergence_details
                ))
            
            # 批量插入到现有的market_breadth_metrics_gics表
            if records:
                cursor.executemany("""
                INSERT INTO market_breadth_metrics_gics 
                (recorded_at, market, timeframe, total_stocks, advances, declines, unchanged,
                 advancing_volume, declining_volume, total_volume, new_highs_52w, new_lows_52w,
                 above_ma50, above_ma200, avg_rsi, market_cap_weighted_return, equal_weighted_return,
                 purity, internal_health, momentum_coherence, divergence_type, divergence_severity,
                 divergence_confidence, divergence_risk_level, coherence_details, enhanced_divergence_details)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                total_stocks = VALUES(total_stocks), advances = VALUES(advances), declines = VALUES(declines),
                unchanged = VALUES(unchanged), advancing_volume = VALUES(advancing_volume),
                declining_volume = VALUES(declining_volume), total_volume = VALUES(total_volume),
                new_highs_52w = VALUES(new_highs_52w), new_lows_52w = VALUES(new_lows_52w),
                above_ma50 = VALUES(above_ma50), above_ma200 = VALUES(above_ma200), avg_rsi = VALUES(avg_rsi),
                market_cap_weighted_return = VALUES(market_cap_weighted_return),
                equal_weighted_return = VALUES(equal_weighted_return), purity = VALUES(purity),
                internal_health = VALUES(internal_health), momentum_coherence = VALUES(momentum_coherence),
                divergence_type = VALUES(divergence_type), divergence_severity = VALUES(divergence_severity),
                divergence_confidence = VALUES(divergence_confidence), divergence_risk_level = VALUES(divergence_risk_level)
                """, records)
            
            conn.commit()
            self.logger.info(f"成功保存 {len(records)} 条市宽数据到 market_breadth_metrics_gics")
            
        except Exception as e:
            self.logger.error(f"保存市宽数据失败: {e}")
            conn.rollback()
        finally:
            conn.close()


# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='高效多时间框架市宽计算器')
    parser.add_argument('--calculate', action='store_true', help='计算全部时间框架市宽')
    parser.add_argument('--markets', nargs='+', help='指定市场')
    parser.add_argument('--days', type=int, default=60, help='历史天数')
    parser.add_argument('--force-update', action='store_true', help='强制更新预计算数据')
    parser.add_argument('--status', action='store_true', help='查看计算状态')
    
    args = parser.parse_args()
    
    calculator = EfficientMTFCalculator()
    
    if args.calculate:
        print("🚀 开始高效多时间框架市宽计算...")
        
        results = calculator.calculate_all_timeframes_breadth(
            markets=args.markets,
            days=args.days,
            force_update=args.force_update
        )
        
        print(f"\n✅ 计算完成!")
        print(f"   总耗时: {results['total_time']:.1f}秒")
        print(f"   成功: {results['success_count']}")
        print(f"   失败: {results['failed_count']}")
        
        for tf, tf_result in results['results'].items():
            print(f"   {tf}: 成功{tf_result['success_count']}, 失败{tf_result['failed_count']}, 记录{tf_result['total_records']}")
    
    elif args.status:
        print("📊 多时间框架市宽计算状态")
        print("=" * 50)
        # 这里可以添加状态查看逻辑
    
    else:
        print("📋 高效多时间框架市宽计算器")
        print("=" * 50)
        print()
        print("🎯 功能:")
        print("  --calculate      : 计算全部时间框架市宽")
        print("  --status         : 查看计算状态")
        print()
        print("📖 使用示例:")
        print("  # 计算全部市场全部时间框架60天市宽")
        print("  python core/efficient_mtf_calculator.py --calculate --days 60")
        print()
        print("  # 计算指定市场")
        print("  python core/efficient_mtf_calculator.py --calculate --markets SP500 NASDAQ --days 60")
        print()
        print("  # 强制更新预计算数据")
        print("  python core/efficient_mtf_calculator.py --calculate --force-update --days 60")
