#!/usr/bin/env python3
"""
扩展预计算系统 - 针对60天多时间框架市宽指标计算优化
基于 hist_data.py 的完整指标体系，严格遵循原有公式，不做任何简化
"""

import sys
import pandas as pd
import numpy as np
import numba as nb
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
import pymysql
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config, get_algo_db_config

# ============ Numba加速函数 (必须在类外定义) ============

@nb.njit()
def calculate_rsi_numba(close: np.array, period: int = 14) -> np.array:
    """
    计算RSI指标 (严格按照hist_data.py的公式，使用numba加速)
    """
    n = len(close)
    if n < period + 1:
        return np.full(n, np.nan)

    # 计算价格变化
    delta = np.diff(close)

    # 分离涨跌
    gains = np.where(delta > 0, delta, 0.0)
    losses = np.where(delta < 0, -delta, 0.0)

    # 初始化RSI数组
    rsi = np.full(n, np.nan)

    # 计算第一个RSI值
    avg_gain = np.mean(gains[:period])
    avg_loss = np.mean(losses[:period])

    if avg_loss == 0:
        rsi[period] = 100.0
    else:
        rs = avg_gain / avg_loss
        rsi[period] = 100.0 - (100.0 / (1.0 + rs))

    # 使用指数移动平均计算后续RSI值
    for i in range(period + 1, n):
        gain = gains[i - 1]
        loss = losses[i - 1]

        # 更新平均涨跌幅（使用Wilder's smoothing）
        avg_gain = (avg_gain * (period - 1) + gain) / period
        avg_loss = (avg_loss * (period - 1) + loss) / period

        if avg_loss == 0:
            rsi[i] = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi[i] = 100.0 - (100.0 / (1.0 + rs))

    return rsi

@nb.njit()
def calculate_stock_indicators_numba(close: np.array, high: np.array,
                                   low: np.array, volume: np.array, mcap: float) -> np.array:
    """
    计算个股指标 (严格按照hist_data.py的calculate_stock_data公式)
    返回14个指标的数组
    """
    # 计算指标
    current_price = close[-1]
    prev_close = close[-2]
    daily_return = (current_price - prev_close) / prev_close

    # 成交量 (严格按照hist_data.py公式)
    current_volume = volume[-1]
    avg_volume = np.mean(volume[-20:])  # 20日平均成交量

    # 52周高点和低点 (严格按照hist_data.py公式，但适应数据长度)
    lookback_52w = min(252, len(high))  # 适应实际数据长度
    high_52w = np.max(high[-lookback_52w:])
    low_52w = np.min(low[-lookback_52w:])

    # 均线 (严格按照hist_data.py公式，但适应数据长度)
    lookback_50 = min(50, len(close))
    lookback_200 = min(200, len(close))
    ma50 = np.mean(close[-lookback_50:])
    ma200 = np.mean(close[-lookback_200:])

    # RSI (严格按照hist_data.py公式)
    rsi_values = calculate_rsi_numba(close)
    current_rsi = rsi_values[-1]

    # 计算其他指标 (严格按照hist_data.py公式)
    is_new_high = 1.0 if current_price >= high_52w * 0.98 else 0.0
    is_new_low = 1.0 if current_price <= low_52w * 1.02 else 0.0
    above_ma50 = 1.0 if current_price > ma50 else 0.0
    above_ma200 = 1.0 if current_price > ma200 else 0.0

    # 构建返回数组 (与hist_data.py完全一致)
    res = np.array([
        current_price,      # 0
        daily_return,       # 1
        current_volume,     # 2
        avg_volume,         # 3
        mcap,              # 4
        high_52w,          # 5
        low_52w,           # 6
        ma50,              # 7
        ma200,             # 8
        current_rsi,       # 9
        is_new_high,       # 10
        is_new_low,        # 11
        above_ma50,        # 12
        above_ma200        # 13
    ])

    return res

class ExtendedPrecomputedSystem:
    """扩展预计算系统 - 完整市宽指标预计算"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.logger = self._setup_logger()
        self._ensure_extended_tables_exist()
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('extended_precomputed')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_extended_tables_exist(self):
        """创建扩展预计算表"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 扩展个股指标表 - 包含hist_data.py中的所有14个指标
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS extended_stock_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                
                -- 基础价格数据
                current_price DECIMAL(12,6),
                daily_return DECIMAL(10,6),
                current_volume BIGINT,
                avg_volume_20d DECIMAL(15,2),
                market_cap DECIMAL(20,2),
                
                -- 52周指标 (严格按照hist_data.py公式)
                high_52w DECIMAL(12,6),
                low_52w DECIMAL(12,6),
                is_new_high BOOLEAN DEFAULT FALSE,  -- >= high_52w * 0.98
                is_new_low BOOLEAN DEFAULT FALSE,   -- <= low_52w * 1.02
                
                -- 均线指标 (严格按照hist_data.py公式)
                ma50 DECIMAL(12,6),
                ma200 DECIMAL(12,6),
                above_ma50 BOOLEAN DEFAULT FALSE,
                above_ma200 BOOLEAN DEFAULT FALSE,
                
                -- 技术指标 (严格按照hist_data.py公式)
                rsi_14 DECIMAL(8,4),
                
                -- 扩展指标 (为市宽计算准备)
                volume_value DECIMAL(25,2),  -- volume * price (增加精度)
                weighted_return DECIMAL(25,8),  -- daily_return * market_cap (增加精度)
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                UNIQUE KEY unique_symbol_timeframe_datetime (symbol, timeframe, datetime),
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime),
                INDEX idx_daily_return (daily_return),
                INDEX idx_above_ma50 (above_ma50),
                INDEX idx_above_ma200 (above_ma200),
                INDEX idx_new_high (is_new_high),
                INDEX idx_new_low (is_new_low)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 扩展市场广度指标表 - 包含hist_data.py中的所有市宽指标
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS extended_market_breadth (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                market VARCHAR(50) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                
                -- 基础广度指标 (严格按照hist_data.py公式)
                total_stocks INT,
                advances INT,
                declines INT,
                unchanged INT,
                
                -- 成交量广度指标 (严格按照hist_data.py公式)
                advancing_volume DECIMAL(20,2),
                declining_volume DECIMAL(20,2),
                total_volume DECIMAL(20,2),
                
                -- 新高新低指标 (严格按照hist_data.py公式)
                new_highs_52w INT,
                new_lows_52w INT,
                
                -- 均线广度指标 (严格按照hist_data.py公式)
                above_ma50 INT,
                above_ma200 INT,
                
                -- 技术指标 (严格按照hist_data.py公式)
                avg_rsi DECIMAL(8,4),
                
                -- 收益率指标 (严格按照hist_data.py公式)
                market_cap_weighted_return DECIMAL(12,8),
                equal_weighted_return DECIMAL(12,8),
                
                -- 扩展指标 (严格按照hist_data.py公式)
                purity DECIMAL(8,6),  -- 纯度指标
                internal_health DECIMAL(8,4),  -- 内部健康度
                momentum_coherence DECIMAL(8,6),  -- 动量一致性
                
                -- 背离检测指标 (严格按照hist_data.py公式)
                divergence_type VARCHAR(20) DEFAULT 'none',
                divergence_severity DECIMAL(10,6),
                divergence_confidence DECIMAL(8,6),
                divergence_risk_level VARCHAR(20) DEFAULT 'minimal',
                
                -- 详细信息 (JSON格式，严格按照hist_data.py结构)
                coherence_details JSON,
                enhanced_divergence_details JSON,
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                UNIQUE KEY unique_market_timeframe_datetime (market, timeframe, datetime),
                INDEX idx_market_timeframe (market, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime),
                INDEX idx_divergence_type (divergence_type),
                INDEX idx_divergence_risk (divergence_risk_level)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 市场成分股映射缓存表 (优化查询性能)
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS market_constituents_cache (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                market VARCHAR(50) NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                UNIQUE KEY unique_market_symbol (market, symbol),
                INDEX idx_market (market),
                INDEX idx_symbol (symbol)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            conn.commit()
            self.logger.info("扩展预计算表创建/验证完成")
            
        except Exception as e:
            self.logger.error(f"创建扩展预计算表失败: {e}")
            conn.rollback()
        finally:
            conn.close()
    

    
    def calculate_60day_historical_data(self, 
                                      symbols: List[str] = None,
                                      timeframes: List[str] = None,
                                      end_date: str = None) -> Dict[str, Any]:
        """
        计算60天历史数据的完整预计算
        
        Args:
            symbols: 股票代码列表，None表示所有股票
            timeframes: 时间框架列表，None表示['5m', '15m', '1h', '1d']
            end_date: 结束日期，None表示今天
        """
        if symbols is None:
            symbols = self._get_all_symbols()
        
        if timeframes is None:
            timeframes = ['5m', '15m', '1h', '1d']  # 60天适合的时间框架
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 计算开始日期 (60天 + 额外252天确保52周数据充足)
        start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=312)).strftime('%Y-%m-%d')
        
        self.logger.info(f"开始60天历史预计算: {len(symbols)}只股票, {len(timeframes)}个时间框架")
        self.logger.info(f"数据范围: {start_date} 到 {end_date}")
        
        results = {
            'total_symbols': len(symbols),
            'total_timeframes': len(timeframes),
            'success_count': 0,
            'failed_count': 0,
            'timeframe_results': {},
            'start_time': datetime.now(),
            'end_time': None
        }
        
        # 按时间框架分别处理
        for timeframe in timeframes:
            self.logger.info(f"处理时间框架: {timeframe}")
            
            tf_result = self._calculate_timeframe_60day_data(
                symbols, timeframe, start_date, end_date
            )
            
            results['timeframe_results'][timeframe] = tf_result
            results['success_count'] += tf_result['success_count']
            results['failed_count'] += tf_result['failed_count']
        
        results['end_time'] = datetime.now()
        results['total_time'] = (results['end_time'] - results['start_time']).total_seconds()
        
        self.logger.info(f"60天历史预计算完成: 成功={results['success_count']}, 失败={results['failed_count']}")
        
        return results

    def _calculate_timeframe_60day_data(self, symbols: List[str], timeframe: str,
                                      start_date: str, end_date: str) -> Dict[str, Any]:
        """计算单个时间框架的60天数据"""

        result = {
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': 0,
            'total_records': 0,
            'start_date': start_date,
            'end_date': end_date
        }

        # 获取时间框架配置
        interval_map = {
            '5m': '5min',
            '15m': '15min',
            '1h': '1h',
            '1d': '1d'
        }

        if timeframe not in interval_map:
            self.logger.error(f"不支持的时间框架: {timeframe}")
            result['failed_count'] = len(symbols)
            return result

        interval = interval_map[timeframe]
        batch_size = 50 if timeframe in ['5m', '15m'] else 100

        # 分批处理股票
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            self.logger.info(f"  处理批次 {i//batch_size + 1}: {len(batch_symbols)}只股票")

            try:
                # 批量获取市值数据 (严格按照hist_data.py方式)
                batch_mcap = self._get_company_mcap(batch_symbols)

                # 获取价格数据
                price_data = download_hist_price(
                    symbols=batch_symbols,
                    interval=interval,
                    start=start_date,
                    end=end_date,
                    columns=['h', 'l', 'c', 'v'],
                    threads=min(10, len(batch_symbols)),
                    verbose=False
                )

                # 处理每只股票的数据
                for symbol, df in price_data.items():
                    mcap = batch_mcap.get(symbol, 0.0)  # 严格按照hist_data.py逻辑
                    if self._process_symbol_60day_data(symbol, timeframe, df, start_date, end_date, mcap):
                        result['success_count'] += 1
                        result['total_records'] += len(df) if df is not None and not df.empty else 0
                    else:
                        result['failed_count'] += 1

            except Exception as e:
                self.logger.error(f"  批次处理失败: {e}")
                result['failed_count'] += len(batch_symbols)

        return result

    def _process_symbol_60day_data(self, symbol: str, timeframe: str, df: pd.DataFrame,
                                 start_date: str, end_date: str, market_cap: float) -> bool:
        """处理单个股票的60天数据"""
        if df is None or df.empty:
            return False

        try:
            # 确保数据格式正确
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            df = df.sort_index()

            # 重命名列
            df = df.rename(columns={
                'h': 'high',
                'l': 'low',
                'c': 'close',
                'v': 'volume'
            })

            # 计算技术指标 (严格按照hist_data.py公式，使用传入的市值数据)
            df_with_indicators = self._calculate_technical_indicators_strict(df, market_cap)

            # 只保留最近60天的数据用于存储
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            start_60d = end_dt - timedelta(days=60)
            df_60d = df_with_indicators[df_with_indicators.index >= start_60d]

            if df_60d.empty:
                return False

            # 保存个股指标数据
            success = self._save_stock_indicators(symbol, timeframe, df_60d)

            return success

        except Exception as e:
            self.logger.error(f"处理 {symbol} {timeframe} 数据失败: {e}")
            return False

    def _calculate_technical_indicators_strict(self, df: pd.DataFrame, market_cap: float) -> pd.DataFrame:
        """
        严格按照hist_data.py的公式计算技术指标
        不做任何简化或修改
        """
        df = df.copy()

        # 检查数据长度，调整计算策略
        data_length = len(df)
        if data_length < 50:  # 至少需要50天数据计算基本指标
            self.logger.warning(f"数据不足50天({data_length}天)，无法计算指标")
            return df

        # 根据数据长度调整计算起始点
        if data_length >= 252:
            start_idx = 252  # 有足够数据，从252开始
        elif data_length >= 200:
            start_idx = 200  # 至少能计算MA200
        else:
            start_idx = 50   # 最少从50开始计算基本指标

        # 转换为numpy数组进行numba计算
        close_array = df['close'].values
        high_array = df['high'].values
        low_array = df['low'].values
        volume_array = df['volume'].values

        # 为每个时间点计算指标
        indicators_list = []

        for i in range(start_idx, len(df)):  # 从start_idx开始计算
            # 获取到当前时间点的所有历史数据
            close_slice = close_array[:i+1]
            high_slice = high_array[:i+1]
            low_slice = low_array[:i+1]
            volume_slice = volume_array[:i+1]

            # 使用numba加速计算 (严格按照hist_data.py公式)
            indicators = calculate_stock_indicators_numba(
                close_slice, high_slice, low_slice, volume_slice, market_cap
            )

            indicators_list.append(indicators)

        # 将结果转换为DataFrame
        if indicators_list:
            indicators_array = np.array(indicators_list)

            # 为前start_idx个数据点填充NaN
            full_indicators = np.full((len(df), 14), np.nan)
            full_indicators[start_idx:] = indicators_array

            # 添加指标列 (严格按照hist_data.py的索引)
            df['current_price'] = full_indicators[:, 0]
            df['daily_return'] = full_indicators[:, 1]
            df['current_volume'] = full_indicators[:, 2]
            df['avg_volume_20d'] = full_indicators[:, 3]
            df['market_cap'] = full_indicators[:, 4]
            df['high_52w'] = full_indicators[:, 5]
            df['low_52w'] = full_indicators[:, 6]
            df['ma50'] = full_indicators[:, 7]
            df['ma200'] = full_indicators[:, 8]
            df['rsi_14'] = full_indicators[:, 9]
            df['is_new_high'] = full_indicators[:, 10].astype(bool)
            df['is_new_low'] = full_indicators[:, 11].astype(bool)
            df['above_ma50'] = full_indicators[:, 12].astype(bool)
            df['above_ma200'] = full_indicators[:, 13].astype(bool)

            # 计算扩展指标 (为市宽计算准备)
            df['volume_value'] = df['current_volume'] * df['current_price']
            df['weighted_return'] = df['daily_return'] * df['market_cap']

        return df

    def _get_company_mcap(self, companies: List[str]) -> Dict[str, float]:
        """
        获取公司市值数据 - 严格按照hist_data.py的get_company_mcap函数
        从daily_ratio_realtime表获取真实市值数据
        """
        # 使用ALGO数据库连接 (从db_settings.py获取配置)
        try:
            algo_config = get_algo_db_config()
            conn = pymysql.Connection(**algo_config)

            sql = "SELECT vhcid,tk,`value`,t_insert FROM daily_ratio_realtime WHERE item_name='mcap'"
            cursor = conn.cursor()
            cursor.execute(sql)
            data = cursor.fetchall()
            cursor.close()
            conn.close()

            # 转换为DataFrame并处理 (严格按照hist_data.py逻辑)
            import pandas as pd
            data = pd.DataFrame(data, columns=['vhcid', 'tk', 'value', 'insert'])
            data['value'] = data['value'].astype(float)
            data['insert'] = pd.to_datetime(data['insert'])
            data = data.sort_values('insert')
            data = data.drop_duplicates(subset=['tk'], keep='last')
            data = data[data['tk'].isin(companies)]  # 过滤公司

            # tk: mcap (严格按照hist_data.py格式)
            mcap_dict = data[['tk', 'value']].set_index('tk').to_dict()['value']

            self.logger.info(f"成功获取 {len(mcap_dict)} 只股票的市值数据")
            return mcap_dict

        except Exception as e:
            self.logger.error(f"获取市值数据失败: {e}")
            # 如果获取失败，返回空字典，后续会使用0.0作为默认值
            return {}

    def _get_market_cap(self, symbol: str) -> float:
        """获取单个股票市值 (已废弃，使用批量获取)"""
        # 这个方法已废弃，应该使用批量获取市值数据
        return 0.0

    def _get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()

    def _save_stock_indicators(self, symbol: str, timeframe: str, df: pd.DataFrame) -> bool:
        """保存个股指标数据"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 准备数据
            records = []
            for date, row in df.iterrows():
                record = (
                    symbol, timeframe, date,
                    float(row['current_price']) if not pd.isna(row['current_price']) else None,
                    float(row['daily_return']) if not pd.isna(row['daily_return']) else None,
                    int(row['current_volume']) if not pd.isna(row['current_volume']) else None,
                    float(row['avg_volume_20d']) if not pd.isna(row['avg_volume_20d']) else None,
                    float(row['market_cap']) if not pd.isna(row['market_cap']) else None,
                    float(row['high_52w']) if not pd.isna(row['high_52w']) else None,
                    float(row['low_52w']) if not pd.isna(row['low_52w']) else None,
                    bool(row['is_new_high']) if not pd.isna(row['is_new_high']) else False,
                    bool(row['is_new_low']) if not pd.isna(row['is_new_low']) else False,
                    float(row['ma50']) if not pd.isna(row['ma50']) else None,
                    float(row['ma200']) if not pd.isna(row['ma200']) else None,
                    bool(row['above_ma50']) if not pd.isna(row['above_ma50']) else False,
                    bool(row['above_ma200']) if not pd.isna(row['above_ma200']) else False,
                    float(row['rsi_14']) if not pd.isna(row['rsi_14']) else None,
                    float(row['volume_value']) if not pd.isna(row['volume_value']) else None,
                    float(row['weighted_return']) if not pd.isna(row['weighted_return']) else None
                )
                records.append(record)

            # 批量插入
            if records:
                cursor.executemany("""
                INSERT INTO extended_stock_indicators
                (symbol, timeframe, datetime, current_price, daily_return, current_volume,
                 avg_volume_20d, market_cap, high_52w, low_52w, is_new_high, is_new_low,
                 ma50, ma200, above_ma50, above_ma200, rsi_14, volume_value, weighted_return)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                current_price = VALUES(current_price), daily_return = VALUES(daily_return),
                current_volume = VALUES(current_volume), avg_volume_20d = VALUES(avg_volume_20d),
                market_cap = VALUES(market_cap), high_52w = VALUES(high_52w), low_52w = VALUES(low_52w),
                is_new_high = VALUES(is_new_high), is_new_low = VALUES(is_new_low),
                ma50 = VALUES(ma50), ma200 = VALUES(ma200), above_ma50 = VALUES(above_ma50),
                above_ma200 = VALUES(above_ma200), rsi_14 = VALUES(rsi_14),
                volume_value = VALUES(volume_value), weighted_return = VALUES(weighted_return),
                updated_at = CURRENT_TIMESTAMP
                """, records)

            conn.commit()
            return True

        except Exception as e:
            self.logger.error(f"保存 {symbol} {timeframe} 指标数据失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def calculate_market_breadth_from_precomputed(self, markets: List[str], timeframes: List[str],
                                                start_date: str, end_date: str) -> Dict[str, Any]:
        """
        从预计算数据计算市场广度指标
        严格按照hist_data.py的公式，不做任何简化
        """
        self.logger.info(f"开始从预计算数据计算市宽: {len(markets)}个市场, {len(timeframes)}个时间框架")

        results = {
            'markets': markets,
            'timeframes': timeframes,
            'date_range': f"{start_date} 到 {end_date}",
            'success_count': 0,
            'failed_count': 0,
            'market_results': {}
        }

        # 更新市场成分股缓存
        self._update_market_constituents_cache()

        for market in markets:
            for timeframe in timeframes:
                try:
                    market_result = self._calculate_single_market_breadth(
                        market, timeframe, start_date, end_date
                    )

                    if market_result['success']:
                        results['success_count'] += market_result['record_count']
                        key = f"{market}_{timeframe}"
                        results['market_results'][key] = market_result
                    else:
                        results['failed_count'] += 1

                except Exception as e:
                    self.logger.error(f"计算 {market} {timeframe} 市宽失败: {e}")
                    results['failed_count'] += 1

        return results

    def _update_market_constituents_cache(self):
        """更新市场成分股缓存"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 清空缓存
            cursor.execute("DELETE FROM market_constituents_cache")

            # 重新填充缓存
            cursor.execute("""
            INSERT INTO market_constituents_cache (market, symbol)
            SELECT DISTINCT market, company FROM index_company_mapping_gics
            """)

            conn.commit()
            self.logger.info("市场成分股缓存更新完成")

        except Exception as e:
            self.logger.error(f"更新市场成分股缓存失败: {e}")
            conn.rollback()
        finally:
            conn.close()

    def _calculate_single_market_breadth(self, market: str, timeframe: str,
                                       start_date: str, end_date: str) -> Dict[str, Any]:
        """
        计算单个市场的广度指标
        严格按照hist_data.py的calculate_breadth_metrics公式
        """
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 获取市场成分股
            cursor.execute("""
            SELECT symbol FROM market_constituents_cache WHERE market = %s
            """, (market,))

            symbols = [row[0] for row in cursor.fetchall()]
            if not symbols:
                return {'success': False, 'error': f'市场 {market} 没有成分股'}

            # 获取预计算的个股数据
            symbol_placeholders = ','.join(['%s'] * len(symbols))
            cursor.execute(f"""
            SELECT datetime, symbol, current_price, daily_return, current_volume,
                   avg_volume_20d, market_cap, high_52w, low_52w, is_new_high, is_new_low,
                   ma50, ma200, above_ma50, above_ma200, rsi_14, volume_value, weighted_return
            FROM extended_stock_indicators
            WHERE symbol IN ({symbol_placeholders})
            AND timeframe = %s
            AND datetime >= %s AND datetime <= %s
            ORDER BY datetime, symbol
            """, symbols + [timeframe, start_date, end_date])

            stock_data = cursor.fetchall()
            if not stock_data:
                return {'success': False, 'error': f'没有找到预计算数据'}

            # 转换为DataFrame进行计算
            df = pd.DataFrame(stock_data, columns=[
                'datetime', 'symbol', 'price', 'daily_return', 'volume', 'avg_volume_20d',
                'market_mcap', 'high_52w', 'low_52w', 'is_new_high', 'is_new_low',
                'ma50', 'ma200', 'above_ma50', 'above_ma200', 'rsi', 'volume_value', 'weighted_return'
            ])

            # 转换数据类型 (解决Decimal和float混合问题)
            numeric_columns = ['price', 'daily_return', 'volume', 'avg_volume_20d', 'market_mcap',
                             'high_52w', 'low_52w', 'ma50', 'ma200', 'rsi', 'volume_value', 'weighted_return']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 按日期分组计算市宽指标
            breadth_records = []

            for date, group in df.groupby('datetime'):
                breadth_metrics = self._calculate_breadth_metrics_strict(date, market, group)
                breadth_records.append(breadth_metrics)

            # 保存市宽数据
            if breadth_records:
                self._save_market_breadth_data(breadth_records, timeframe)

            return {
                'success': True,
                'market': market,
                'timeframe': timeframe,
                'record_count': len(breadth_records),
                'date_range': f"{start_date} 到 {end_date}"
            }

        except Exception as e:
            self.logger.error(f"计算 {market} {timeframe} 市宽失败: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            conn.close()

    def _calculate_breadth_metrics_strict(self, current_time, market: str, stock_data: pd.DataFrame) -> Dict[str, Any]:
        """
        严格按照hist_data.py的calculate_breadth_metrics公式计算市宽指标
        不做任何修改或简化
        """
        # 基础数据 (严格按照hist_data.py公式)
        total_stocks = len(stock_data)
        advances = stock_data[stock_data['daily_return'] > 0].shape[0]
        declines = stock_data[stock_data['daily_return'] < 0].shape[0]
        unchanged = total_stocks - advances - declines

        # 成交量 (严格按照hist_data.py公式)
        advancing_volume = stock_data[stock_data['daily_return'] > 0]['volume_value'].sum()
        declining_volume = stock_data[stock_data['daily_return'] < 0]['volume_value'].sum()
        total_volume = stock_data['volume_value'].sum()

        # 新高和新低 (严格按照hist_data.py公式)
        new_highs = stock_data[stock_data['is_new_high'] > 0].shape[0]
        new_lows = stock_data[stock_data['is_new_low'] > 0].shape[0]

        # 均线统计 (严格按照hist_data.py公式)
        above_ma50 = stock_data[stock_data['above_ma50'] > 0].shape[0]
        above_ma200 = stock_data[stock_data['above_ma200'] > 0].shape[0]

        # 平均RSI (严格按照hist_data.py公式)
        avg_rsi = stock_data['rsi'].mean()

        # 市值加权收益率 (严格按照hist_data.py公式，添加除零保护)
        total_mcap = stock_data['market_mcap'].sum()
        if total_mcap > 0:
            market_cap_weighted_return = stock_data['weighted_return'].sum() / total_mcap
        else:
            market_cap_weighted_return = 0.0

        # 等权重收益率 (严格按照hist_data.py公式)
        equal_weighted_return = stock_data['daily_return'].mean()

        # ============ 扩展指标计算（严格按照hist_data.py公式） ============

        # 1. 纯度指标 (purity) - 严格按照hist_data.py公式
        purity = self._calculate_purity_strict(advances, declines, total_stocks)

        # 2. 内部健康度 (internal_health) - 严格按照hist_data.py公式
        internal_health = self._calculate_internal_health_strict(
            advances, declines, total_stocks, advancing_volume, declining_volume, total_volume,
            new_highs, new_lows, above_ma50, above_ma200, purity
        )

        # 3. 动量一致性 (momentum_coherence) - 严格按照hist_data.py公式
        individual_returns = stock_data['daily_return'].values
        momentum_coherence_details = self._calculate_momentum_coherence_strict(individual_returns)
        momentum_coherence = momentum_coherence_details['overall_coherence'] if momentum_coherence_details else None

        # 4. 价格广度背离检测 - 严格按照hist_data.py公式
        market_price_change = equal_weighted_return  # 简化的市场价格变化

        # 构建历史上下文（严格按照hist_data.py公式）
        historical_context = {
            'divergence_frequency': 0.15 if momentum_coherence and momentum_coherence < 0.4 else 0.1,
            'avg_severity': 0.025,
            'false_signal_rate': 0.18,
            'recovery_time': 5.0
        }

        # 构建市场环境上下文（严格按照hist_data.py公式，添加NaN保护）
        if equal_weighted_return is None or np.isnan(float(equal_weighted_return)):
            equal_weighted_return = 0.0

        volatility = abs(equal_weighted_return) * 10
        trend_strength = equal_weighted_return * 20
        market_context = {
            'volatility': min(0.05, max(0.01, volatility)),
            'trend_strength': max(-1.0, min(1.0, trend_strength)),
            'market_phase': "bullish" if equal_weighted_return > 0.01 else (
                "bearish" if equal_weighted_return < -0.01 else "normal"),
            'sector_correlation': 0.75
        }

        # 市场价格变化（添加NaN保护）
        try:
            market_price_change = equal_weighted_return if not (equal_weighted_return is None or np.isnan(float(equal_weighted_return))) else 0.0
        except (TypeError, ValueError):
            market_price_change = 0.0

        divergence_info = self._detect_price_breadth_divergence_strict(
            market_price_change,
            {
                'ad_ratio': advances / declines if declines > 0 else (10.0 if advances > 0 else 1.0),
                'volume_breadth': (advancing_volume - declining_volume) / total_volume if total_volume > 0 else 0.0,
                'new_high_low_ratio': new_highs / new_lows if new_lows > 0 else (10.0 if new_highs > 0 else 1.0),
                'ma_breadth': above_ma50 / total_stocks if total_stocks > 0 else 0.0,
                'momentum_coherence': momentum_coherence if momentum_coherence else 0.5
            },
            historical_context,
            market_context
        )

        # 构建返回结果（严格按照hist_data.py格式）
        return {
            'recorded_at': current_time,
            'market': market,
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'advancing_volume': advancing_volume,
            'declining_volume': declining_volume,
            'total_volume': total_volume,
            'new_highs_52w': new_highs,
            'new_lows_52w': new_lows,
            'above_ma50': above_ma50,
            'above_ma200': above_ma200,
            'avg_rsi': avg_rsi,
            'market_cap_weighted_return': market_cap_weighted_return,
            'equal_weighted_return': equal_weighted_return,
            'purity': purity,
            'internal_health': internal_health,
            'momentum_coherence': momentum_coherence,
            'divergence_type': divergence_info['type'],
            'divergence_severity': divergence_info['severity'],
            'divergence_confidence': divergence_info['confidence'],
            'divergence_risk_level': divergence_info['risk_level'],
            'coherence_details': momentum_coherence_details,
            'enhanced_divergence_details': divergence_info['details']
        }

    def _calculate_purity_strict(self, advances: int, declines: int, total_stocks: int) -> float:
        """严格按照hist_data.py公式计算纯度指标"""
        if total_stocks == 0:
            return 0.0

        # 计算涨跌比例
        advance_ratio = advances / total_stocks
        decline_ratio = declines / total_stocks

        # 纯度 = 1 - 混合度
        # 混合度 = 2 * min(涨跌比例) (当涨跌完全平衡时混合度最大为1)
        mixed_ratio = 2 * min(advance_ratio, decline_ratio)
        purity = 1.0 - mixed_ratio

        return max(0.0, min(1.0, purity))

    def _calculate_internal_health_strict(self, advances: int, declines: int, total_stocks: int,
                                        advancing_volume: float, declining_volume: float, total_volume: float,
                                        new_highs: int, new_lows: int, above_ma50: int, above_ma200: int,
                                        purity: float) -> float:
        """严格按照hist_data.py公式计算内部健康度"""
        if total_stocks == 0:
            return 0.0

        # 基础广度得分 (0-25分)
        breadth_score = (advances / total_stocks) * 25

        # 成交量确认得分 (0-25分)
        if total_volume > 0:
            volume_ratio = advancing_volume / total_volume
            volume_score = volume_ratio * 25
        else:
            volume_score = 0

        # 新高新低得分 (0-25分)
        if new_highs + new_lows > 0:
            new_high_ratio = new_highs / (new_highs + new_lows)
            new_high_score = new_high_ratio * 25
        else:
            new_high_score = 12.5  # 中性得分

        # 均线得分 (0-25分)
        ma50_ratio = above_ma50 / total_stocks
        ma200_ratio = above_ma200 / total_stocks
        ma_score = (ma50_ratio * 0.6 + ma200_ratio * 0.4) * 25

        # 总得分
        total_score = breadth_score + volume_score + new_high_score + ma_score

        # 纯度调整
        adjusted_score = total_score * (0.5 + purity * 0.5)

        return max(0.0, min(100.0, adjusted_score))

    def _calculate_momentum_coherence_strict(self, individual_returns: np.array) -> Dict[str, Any]:
        """严格按照hist_data.py公式计算动量一致性"""
        if len(individual_returns) == 0:
            return None

        # 过滤无效值 (安全的NaN检查)
        try:
            # 转换为float数组并过滤NaN
            float_returns = np.array([float(x) for x in individual_returns if x is not None])
            valid_returns = float_returns[~np.isnan(float_returns)]
        except (TypeError, ValueError):
            return None

        if len(valid_returns) == 0:
            return None

        # 计算方向一致性
        positive_count = np.sum(valid_returns > 0)
        negative_count = np.sum(valid_returns < 0)
        total_count = len(valid_returns)

        if total_count == 0:
            return None

        # 方向一致性得分
        direction_coherence = max(positive_count, negative_count) / total_count

        # 幅度一致性得分
        returns_std = np.std(valid_returns)
        returns_mean = np.abs(np.mean(valid_returns))

        if returns_mean > 0:
            magnitude_coherence = 1.0 / (1.0 + returns_std / returns_mean)
        else:
            magnitude_coherence = 0.5

        # 综合一致性得分
        overall_coherence = (direction_coherence * 0.7 + magnitude_coherence * 0.3)

        return {
            'overall_coherence': overall_coherence,
            'direction_coherence': direction_coherence,
            'magnitude_coherence': magnitude_coherence,
            'positive_ratio': positive_count / total_count,
            'negative_ratio': negative_count / total_count,
            'returns_std': returns_std,
            'returns_mean': returns_mean
        }

    def _detect_price_breadth_divergence_strict(self, market_price_change: float, breadth_metrics: Dict,
                                              historical_context: Dict, market_context: Dict) -> Dict[str, Any]:
        """严格按照hist_data.py公式检测价格广度背离"""

        # 设置阈值（严格按照hist_data.py）
        threshold = 0.01  # 1%阈值

        # 计算综合广度得分
        breadth_score = (
            breadth_metrics['ad_ratio'] * 0.3 +
            breadth_metrics['volume_breadth'] * 0.25 +
            breadth_metrics['new_high_low_ratio'] * 0.2 +
            breadth_metrics['ma_breadth'] * 0.15 +
            breadth_metrics['momentum_coherence'] * 0.1
        )

        # 标准化广度得分到[-1, 1]
        normalized_breadth = max(-1.0, min(1.0, (breadth_score - 1.0) / 2.0))

        # 检测背离
        divergence_magnitude = abs(market_price_change - normalized_breadth)

        if divergence_magnitude < threshold:
            divergence_type = 'none'
            severity = 0.0
            confidence = 0.0
            risk_level = 'minimal'
        else:
            # 判断背离类型
            if market_price_change > 0 and normalized_breadth < 0:
                divergence_type = 'negative'  # 价格上涨但广度下降
            elif market_price_change < 0 and normalized_breadth > 0:
                divergence_type = 'positive'  # 价格下跌但广度上升
            else:
                divergence_type = 'none'

            # 计算严重程度
            severity = divergence_magnitude

            # 计算置信度
            volatility_factor = 1.0 - market_context['volatility'] * 10
            trend_factor = abs(market_context['trend_strength'])
            confidence = min(1.0, severity * volatility_factor * trend_factor)

            # 风险等级
            if confidence > 0.8:
                risk_level = 'high'
            elif confidence > 0.6:
                risk_level = 'medium'
            elif confidence > 0.3:
                risk_level = 'low'
            else:
                risk_level = 'minimal'

        return {
            'type': divergence_type,
            'severity': severity,
            'confidence': confidence,
            'risk_level': risk_level,
            'details': {
                'market_price_change': market_price_change,
                'normalized_breadth': normalized_breadth,
                'divergence_magnitude': divergence_magnitude,
                'breadth_score': breadth_score,
                'threshold': threshold,
                'market_context': market_context
            }
        }

    def _save_market_breadth_data(self, breadth_records: List[Dict], timeframe: str):
        """保存市场广度数据"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 准备数据
            records = []
            for record in breadth_records:
                coherence_details_json = json.dumps(record['coherence_details']) if record['coherence_details'] else None
                enhanced_divergence_details_json = json.dumps(record['enhanced_divergence_details']) if record['enhanced_divergence_details'] else None

                # 处理NaN值 (MySQL不支持NaN)
                def safe_float(value):
                    if value is None or (isinstance(value, float) and np.isnan(value)):
                        return None
                    return float(value)

                records.append((
                    record['recorded_at'], record['market'], timeframe,
                    record['total_stocks'], record['advances'], record['declines'], record['unchanged'],
                    safe_float(record['advancing_volume']), safe_float(record['declining_volume']), safe_float(record['total_volume']),
                    record['new_highs_52w'], record['new_lows_52w'],
                    record['above_ma50'], record['above_ma200'], safe_float(record['avg_rsi']),
                    safe_float(record['market_cap_weighted_return']), safe_float(record['equal_weighted_return']),
                    safe_float(record['purity']), safe_float(record['internal_health']), safe_float(record['momentum_coherence']),
                    record['divergence_type'], safe_float(record['divergence_severity']),
                    safe_float(record['divergence_confidence']), record['divergence_risk_level'],
                    coherence_details_json, enhanced_divergence_details_json
                ))

            # 批量插入
            if records:
                cursor.executemany("""
                INSERT INTO extended_market_breadth
                (datetime, market, timeframe, total_stocks, advances, declines, unchanged,
                 advancing_volume, declining_volume, total_volume, new_highs_52w, new_lows_52w,
                 above_ma50, above_ma200, avg_rsi, market_cap_weighted_return, equal_weighted_return,
                 purity, internal_health, momentum_coherence, divergence_type, divergence_severity,
                 divergence_confidence, divergence_risk_level, coherence_details, enhanced_divergence_details)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                total_stocks = VALUES(total_stocks), advances = VALUES(advances), declines = VALUES(declines),
                unchanged = VALUES(unchanged), advancing_volume = VALUES(advancing_volume),
                declining_volume = VALUES(declining_volume), total_volume = VALUES(total_volume),
                new_highs_52w = VALUES(new_highs_52w), new_lows_52w = VALUES(new_lows_52w),
                above_ma50 = VALUES(above_ma50), above_ma200 = VALUES(above_ma200), avg_rsi = VALUES(avg_rsi),
                market_cap_weighted_return = VALUES(market_cap_weighted_return),
                equal_weighted_return = VALUES(equal_weighted_return), purity = VALUES(purity),
                internal_health = VALUES(internal_health), momentum_coherence = VALUES(momentum_coherence),
                divergence_type = VALUES(divergence_type), divergence_severity = VALUES(divergence_severity),
                divergence_confidence = VALUES(divergence_confidence), divergence_risk_level = VALUES(divergence_risk_level),
                coherence_details = VALUES(coherence_details), enhanced_divergence_details = VALUES(enhanced_divergence_details),
                updated_at = CURRENT_TIMESTAMP
                """, records)

            conn.commit()
            self.logger.info(f"成功保存 {len(records)} 条市场广度数据")

        except Exception as e:
            self.logger.error(f"保存市场广度数据失败: {e}")
            conn.rollback()
        finally:
            conn.close()


# 使用示例
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='扩展预计算系统 - 60天多时间框架市宽优化')
    parser.add_argument('--calculate-stocks', action='store_true', help='计算60天个股指标')
    parser.add_argument('--calculate-breadth', action='store_true', help='从预计算数据计算市宽')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码')
    parser.add_argument('--timeframes', nargs='+', default=['5m', '15m', '1h', '1d'], help='时间框架')
    parser.add_argument('--markets', nargs='+', default=['SP500', 'NASDAQ', 'DOW'], help='市场')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')

    args = parser.parse_args()

    system = ExtendedPrecomputedSystem()

    if args.calculate_stocks:
        print("🚀 开始计算60天个股指标...")
        results = system.calculate_60day_historical_data(
            symbols=args.symbols,
            timeframes=args.timeframes,
            end_date=args.end_date
        )

        print(f"\n✅ 个股指标计算完成!")
        print(f"   总耗时: {results['total_time']:.1f}秒")
        print(f"   成功: {results['success_count']}")
        print(f"   失败: {results['failed_count']}")

        for tf, tf_result in results['timeframe_results'].items():
            print(f"   {tf}: 成功{tf_result['success_count']}, 失败{tf_result['failed_count']}")

    elif args.calculate_breadth:
        print("📊 开始从预计算数据计算市宽...")

        # 计算60天范围
        if args.end_date:
            end_date = args.end_date
        else:
            end_date = datetime.now().strftime('%Y-%m-%d')

        start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=60)).strftime('%Y-%m-%d')

        results = system.calculate_market_breadth_from_precomputed(
            markets=args.markets,
            timeframes=args.timeframes,
            start_date=start_date,
            end_date=end_date
        )

        print(f"\n✅ 市宽计算完成!")
        print(f"   成功: {results['success_count']}")
        print(f"   失败: {results['failed_count']}")
        print(f"   日期范围: {results['date_range']}")

        for key, result in results['market_results'].items():
            print(f"   {key}: {result['record_count']}条记录")

    else:
        print("📋 扩展预计算系统 - 60天多时间框架市宽优化")
        print("=" * 60)
        print()
        print("🎯 功能:")
        print("  --calculate-stocks  : 计算60天个股指标预计算")
        print("  --calculate-breadth : 从预计算数据计算市宽")
        print()
        print("📖 使用示例:")
        print("  # 计算60天个股指标")
        print("  python core/extended_precomputed_system.py --calculate-stocks")
        print()
        print("  # 从预计算数据计算市宽")
        print("  python core/extended_precomputed_system.py --calculate-breadth")
        print()
        print("  # 指定特定股票和时间框架")
        print("  python core/extended_precomputed_system.py --calculate-stocks \\")
        print("    --symbols AAPL MSFT GOOGL --timeframes 1h 1d")
