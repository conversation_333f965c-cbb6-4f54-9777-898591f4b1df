#!/usr/bin/env python3
"""
板块数据聚合器 - 扩展市场广度计算器
基于现有的 market_breadth_integrator.py 架构，添加板块级别的数据聚合功能
支持市值加权的板块指数计算和板块轮动指标计算
"""

import os
import sys
import json
import redis
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
import multiprocessing

import numpy as np
import pandas as pd
import pymysql
import pytz
from dotenv import load_dotenv

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')

sys.path.insert(0, project_root)
sys.path.append(current_dir)
sys.path.append(os.path.join(project_root, 'data_source_tool'))

from utils.market_data_manager import MarketDataManager
from redis_data_manager import RedisDataManager
from sector_rotation_functions import (
    calculate_sector_momentum,
    calculate_relative_strength,
    calculate_sector_dispersion,
    calculate_rotation_velocity,
    calculate_sector_purity,
    calculate_rotation_intensity_index
)

# 加载环境变量
# 指向项目根目录下的.env文件
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '.env')
load_dotenv(env_path)

# Redis配置
REDIS_CONFIG = {
    'host': '************',
    'port': 6379,
    'db': 15,
    'decode_responses': True,
    'socket_timeout': 5,
    'socket_connect_timeout': 5
}

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DEFAULT_DB_HOST', 'localhost'),
    'port': int(os.getenv('DEFAULT_DB_PORT', 3306)),
    'user': os.getenv('DEFAULT_DB_USER', 'root'),
    'password': os.getenv('DEFAULT_DB_PASSWORD', ''),
    'database': os.getenv('DEFAULT_DB_NAME', 'stock_data'),
    'charset': 'utf8mb4'
}

class SectorDataAggregator:
    """
    板块数据聚合器
    
    功能：
    1. 从个股数据聚合成板块指数（市值加权）
    2. 计算板块轮动指标
    3. 存储板块数据到数据库
    4. 支持多进程并行计算
    """
    
    def __init__(self, redis_config: Dict = None, db_config: Dict = None):
        """初始化板块数据聚合器"""
        self.redis_config = redis_config or REDIS_CONFIG
        self.db_config = db_config or DB_CONFIG
        self.data_manager = MarketDataManager(db_config=self.db_config)
        self.logger = self._setup_logger()
        
        # 使用统一的Redis数据管理器
        self.redis_data_manager = RedisDataManager(self.redis_config)
        self.redis_client = self.redis_data_manager.redis_client
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('sector_data_aggregator')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _connect_redis(self) -> bool:
        """检查Redis连接状态（现在由RedisDataManager处理）"""
        return self.redis_data_manager.test_connection()
    
    def test_db_connection(self) -> bool:
        """测试数据库连接
        
        Returns:
            连接是否成功
        """
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                connection.close()
                return result[0] == 1
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_sector_stock_mapping(self) -> Dict[str, List[str]]:
        """获取板块到股票的映射关系"""
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 查询所有股票的板块分类（market字段存储板块信息）
                sql = """
                SELECT DISTINCT market, company 
                FROM index_company_mapping_gics 
                WHERE market IS NOT NULL AND market != '' 
                AND company IS NOT NULL AND company != ''
                ORDER BY market, company
                """
                cursor.execute(sql)
                results = cursor.fetchall()
                
                # 构建板块到股票的映射
                sector_mapping = {}
                for market, company in results:
                    if market not in sector_mapping:
                        sector_mapping[market] = []
                    sector_mapping[market].append(company)
                
                self.logger.info(f"[DATA] 获取到 {len(sector_mapping)} 个板块，共 {sum(len(stocks) for stocks in sector_mapping.values())} 只股票")
                return sector_mapping
                
        except Exception as e:
            self.logger.error(f"[ERROR] 获取板块股票映射失败: {e}")
            return {}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def get_stock_market_cap(self, companies: List[str]) -> Dict[str, float]:
        """
        获取股票市值数据 - 参考market_breadth_integrator.py的实现
        
        Args:
            companies: 股票代码列表
            
        Returns:
            dict: {stock: market_cap}
        """
        try:
            # 检查必要的环境变量
            required_env_vars = ['ALGO_DB_HOST', 'ALGO_DB_PORT', 'ALGO_DB_USER', 'ALGO_DB_PASSWORD', 'ALGO_DB_NAME']
            for var in required_env_vars:
                if var not in os.environ:
                    self.logger.error(f"[ERROR] 缺少环境变量: {var}")
                    return {}
            
            sql = "SELECT vhcid,tk,`value`,t_insert FROM daily_ratio_realtime WHERE item_name='mcap'"
            conn = None
            try:
                conn = pymysql.connect(
                    host=os.environ['ALGO_DB_HOST'],
                    port=int(os.environ['ALGO_DB_PORT']),
                    user=os.environ['ALGO_DB_USER'],
                    password=os.environ['ALGO_DB_PASSWORD'],
                    database=os.environ['ALGO_DB_NAME'],
                )
                cursor = conn.cursor()
                cursor.execute(sql)
                data = cursor.fetchall()
                cursor.close()
                
                if not data:
                    self.logger.warning("[WARN] 未获取到任何市值数据")
                    return {}

                data = pd.DataFrame(data, columns=['vhcid', 'tk', 'value', 'insert'])
                data['value'] = data['value'].astype(float)
                data['insert'] = pd.to_datetime(data['insert'])
                data = data.sort_values('insert')
                data = data.drop_duplicates(subset=['tk'], keep='last')
                data = data[data['tk'].isin(companies)]  # 过滤公司
                # tk: mcap
                mcap_dict = data[['tk', 'value']].set_index('tk').to_dict()['value']
                
                self.logger.info(f"[DATA] 获取到 {len(mcap_dict)} 只股票的市值数据")
                return mcap_dict
                
            finally:
                if conn:
                    conn.close()
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取股票市值失败: {e}")
            import traceback
            self.logger.debug(traceback.format_exc())
            return {}
    
    def get_redis_stock_data(self, companies: List[str]) -> Dict[str, List[Dict]]:
        """从Redis获取股票价格数据（使用统一的数据管理器）"""
        try:
            if not companies:
                return {}
            
            # 使用统一的Redis数据管理器获取数据
            # 'auto'模式会自动尝试队列数据，失败则尝试历史数据
            stock_data = self.redis_data_manager.get_stock_data_batch(companies, 'auto')
            
            self.logger.info(f"[DATA] 从Redis获取到 {len(stock_data)} 只股票的价格数据")
            return stock_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 从Redis获取股票数据失败: {e}")
            return {}
    
    @staticmethod
    def calculate_sector_index_worker(args: Tuple[str, List[str], Dict[str, List[Dict]], Dict[str, float]]) -> Optional[Dict]:
        """计算单个板块指数的工作函数（用于多进程）"""
        sector, companies, stock_data, mcap_data = args
        
        try:
            # 过滤出有数据的股票
            valid_companies = [c for c in companies if c in stock_data and c in mcap_data]
            
            if not valid_companies:
                return None
            
            # 获取所有股票的价格数据
            sector_prices = []
            sector_volumes = []
            sector_mcaps = []
            
            # 找到最短的数据长度（确保所有股票数据对齐）
            min_length = min(len(stock_data[c]) for c in valid_companies)
            
            for company in valid_companies:
                company_data = stock_data[company][-min_length:]  # 取最近的数据
                mcap = mcap_data[company]
                
                # 提取价格和成交量数据
                prices = [float(d.get('close', 0)) for d in company_data]
                volumes = [float(d.get('volume', 0)) for d in company_data]
                
                sector_prices.append(prices)
                sector_volumes.append(volumes)
                sector_mcaps.append(mcap)
            
            # 转换为numpy数组进行计算
            prices_array = np.array(sector_prices)
            volumes_array = np.array(sector_volumes)
            mcaps_array = np.array(sector_mcaps).reshape(-1, 1)
            
            # 计算市值加权的板块指数
            # 权重 = 市值 / 总市值
            total_mcap = np.sum(mcaps_array)
            weights = mcaps_array / total_mcap
            
            # 板块指数 = Σ(股票价格 × 权重)
            sector_index = np.sum(prices_array * weights, axis=0)
            
            # 板块成交量 = Σ(股票成交量 × 权重)
            sector_volume = np.sum(volumes_array * weights, axis=0)
            
            # 计算板块收益率
            sector_returns = np.diff(sector_index) / sector_index[:-1] * 100
            
            # 获取时间戳（使用最后一只股票的时间戳）
            timestamps = [d.get('timestamp') for d in stock_data[valid_companies[0]][-min_length:]]
            
            return {
                'sector': sector,
                'companies_count': len(valid_companies),
                'total_market_cap': float(total_mcap),
                'index_values': sector_index.tolist(),
                'volumes': sector_volume.tolist(),
                'returns': sector_returns.tolist(),
                'timestamps': timestamps,
                'latest_index': float(sector_index[-1]),
                'latest_volume': float(sector_volume[-1]),
                'latest_return': float(sector_returns[-1]) if len(sector_returns) > 0 else 0.0
            }
            
        except Exception as e:
            # 返回None表示计算失败
            return None
    
    def calculate_all_sector_indices(self, cpu_counts: int = 4) -> Dict[str, Dict]:
        """计算所有板块的指数（多进程）"""
        try:
            # 1. 获取板块股票映射
            sector_mapping = self.get_sector_stock_mapping()
            if not sector_mapping:
                self.logger.error("[ERROR] 未获取到板块股票映射")
                return {}
            
            # 2. 获取所有股票列表
            all_companies = list(set(sum(sector_mapping.values(), [])))
            
            # 3. 获取股票市值数据
            mcap_data = self.get_stock_market_cap(all_companies)
            
            # 4. 获取股票价格数据
            stock_data = self.get_redis_stock_data(all_companies)
            
            if not stock_data:
                self.logger.error("[ERROR] 未获取到股票价格数据")
                return {}
            
            # 5. 准备多进程任务
            tasks = [(sector, companies, stock_data, mcap_data) 
                     for sector, companies in sector_mapping.items()]
            
            # 6. 多进程计算板块指数
            max_cpu = min(cpu_counts, multiprocessing.cpu_count(), len(tasks))
            self.logger.info(f"[CALC] 使用 {max_cpu} 个进程计算 {len(tasks)} 个板块指数")
            
            try:
                with multiprocessing.Pool(max_cpu) as pool:
                    results = pool.map(SectorDataAggregator.calculate_sector_index_worker, tasks)
            except Exception as mp_error:
                self.logger.warning(f"[WARN] 多进程计算失败，回退到单进程: {mp_error}")
                results = [SectorDataAggregator.calculate_sector_index_worker(task) for task in tasks]
            
            # 7. 过滤有效结果
            sector_indices = {}
            for result in results:
                if result is not None:
                    sector_indices[result['sector']] = result
            
            self.logger.info(f"[OK] 成功计算 {len(sector_indices)} 个板块指数")
            return sector_indices
            
        except Exception as e:
            self.logger.error(f"[ERROR] 计算板块指数失败: {e}")
            return {}
    
    def save_sector_data_to_db(self, sector_indices: Dict[str, Dict], current_time: datetime) -> bool:
        """保存板块数据到数据库"""
        try:
            if not sector_indices:
                return False
            
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 创建板块数据表（如果不存在）
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS sector_indices_gics (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    recorded_at DATETIME NOT NULL,
                    sector VARCHAR(100) NOT NULL,
                    companies_count INT NOT NULL,
                    total_market_cap DECIMAL(20,2) NOT NULL,
                    index_value DECIMAL(15,6) NOT NULL,
                    volume DECIMAL(20,2) NOT NULL,
                    daily_return DECIMAL(10,6) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_recorded_sector (recorded_at, sector),
                    INDEX idx_sector (sector),
                    INDEX idx_recorded_at (recorded_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                cursor.execute(create_table_sql)
                
                # 插入板块数据
                insert_sql = """
                INSERT INTO sector_indices_gics 
                (recorded_at, sector, companies_count, total_market_cap, 
                 index_value, volume, daily_return)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                insert_data = []
                for sector_data in sector_indices.values():
                    insert_data.append((
                        current_time,
                        sector_data['sector'],
                        sector_data['companies_count'],
                        sector_data['total_market_cap'],
                        sector_data['latest_index'],
                        sector_data['latest_volume'],
                        sector_data['latest_return']
                    ))
                
                cursor.executemany(insert_sql, insert_data)
                connection.commit()
                
                self.logger.info(f"[DB] 成功保存 {len(insert_data)} 个板块数据到数据库")
                return True
                
        except Exception as e:
            self.logger.error(f"[ERROR] 保存板块数据到数据库失败: {e}")
            return False
        finally:
            if 'connection' in locals():
                connection.close()
    
    def calculate_and_save_sector_data(self) -> bool:
        """计算并保存板块数据的主函数"""
        try:
            self.logger.info("[START] 开始计算板块数据...")
            
            # 1. 计算所有板块指数
            sector_indices = self.calculate_all_sector_indices()
            
            if not sector_indices:
                self.logger.error("[ERROR] 未能计算出任何板块指数")
                return False
            
            # 2. 保存到数据库
            current_time = datetime.now(tz=pytz.UTC)
            success = self.save_sector_data_to_db(sector_indices, current_time)
            
            if success:
                self.logger.info("[OK] 板块数据计算和保存完成")
            else:
                self.logger.error("[ERROR] 板块数据保存失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"[ERROR] 计算和保存板块数据失败: {e}")
            return False


def main():
    """主函数 - 测试板块数据聚合器"""
    aggregator = SectorDataAggregator()
    success = aggregator.calculate_and_save_sector_data()
    
    if success:
        print("板块数据聚合完成")
    else:
        print("板块数据聚合失败")


if __name__ == "__main__":
    main()