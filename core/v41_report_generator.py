#!/usr/bin/env python3
"""
v41文档标准的报告生成器
完全按照多时间框架板块轮动与MarketBreadth系统_v41.py实现
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class V41ReportGenerator:
    """符合v41文档标准的报告生成器"""
    
    def generate_comprehensive_report(self, analysis) -> Dict:
        """生成符合v41标准的综合报告"""
        try:
            report = {
                'metadata': {
                    'report_type': '多时间框架板块轮动分析',
                    'timestamp': analysis.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'version': '4.1',
                    'timeframes_analyzed': len(analysis.timeframe_signals),
                    'reliability_score': analysis.signal_reliability,
                    'data_explanation': '本报告整合多个时间维度的市场数据，避免单一时间框架的噪声干扰'
                },
                
                'executive_summary': self._generate_executive_summary(analysis),
                
                'market_overview': {
                    'consensus_score': {
                        'value': analysis.consensus_score,
                        'interpretation': self._interpret_consensus(analysis.consensus_score),
                        'calculation': '基于各时间框架的阶段、强度、健康度和领涨板块的一致性'
                    },
                    'market_regime': {
                        'value': analysis.market_regime,
                        'interpretation': self._interpret_market_regime(analysis.market_regime),
                        'implications': self._get_regime_implications(analysis.market_regime)
                    },
                    'signal_reliability': {
                        'value': analysis.signal_reliability,
                        'interpretation': self._interpret_reliability(analysis.signal_reliability),
                        'factors': '一致性、市场状态、平均置信度的综合评估'
                    }
                },
                
                'timeframe_analysis': self._generate_timeframe_analysis(analysis),

                'rotation_analysis': self._generate_rotation_analysis(analysis),

                'unified_decision': {
                    'position_recommendation': {
                        'suggested': self._extract_position_percentage(analysis.unified_decision),
                        'confidence': analysis.signal_reliability,
                        'reasoning': self._explain_position_decision(analysis)
                    },
                    'sector_allocation': {
                        'recommended': self._format_recommended_sectors(analysis),
                        'avoid': self._format_avoid_sectors(analysis),
                        'allocation_summary': self._generate_allocation_summary(analysis)
                    },
                    'operation_guidance': {
                        'entry_timing': analysis.entry_strategy,
                        'exit_strategy': analysis.exit_strategy,
                        'execution': self._determine_execution_timeframe(analysis),
                        'key_points': self._generate_operation_points(analysis)
                    }
                },
                
                'risk_assessment': self._format_risk_assessment(analysis),
                
                'dynamic_weights': {
                    'weights': {tf: round(w, 3) for tf, w in analysis.dynamic_weights.items()},
                    'reasoning': analysis.weight_reasoning,
                    'explanation': '权重根据市场状态和信号质量动态调整，不是简单平均'
                },
                
                'insights_and_warnings': {
                    'key_insights': self._generate_key_insights(analysis),
                    'warnings': self._generate_warnings(analysis),
                    'monitoring_points': self._generate_monitoring_points(analysis)
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成v41报告失败: {e}")
            return self._generate_error_report(str(e))
    
    def _generate_executive_summary(self, analysis) -> Dict:
        """生成执行摘要"""
        # 确定主要结论
        decision = analysis.unified_decision
        reliability = analysis.signal_reliability
        
        if "买入" in decision:
            main_conclusion = f"建议增加仓位，当前市场呈现买入机会"
        elif "卖出" in decision:
            main_conclusion = f"建议减少仓位，当前市场存在下行风险"
        else:
            main_conclusion = f"建议维持当前仓位，等待更明确信号"
        
        return {
            'main_conclusion': main_conclusion,
            'confidence_level': f"{reliability:.1%}",
            'market_phase': analysis.market_regime,
            'primary_driver': self._identify_primary_driver(analysis),
            'action_required': self._determine_action_urgency(analysis)
        }
    
    def _generate_timeframe_analysis(self, analysis) -> Dict:
        """生成时间框架分析"""
        timeframe_data = {}
        
        for tf, signal in analysis.timeframe_signals.items():
            timeframe_data[tf] = {
                'signal': signal.signal_type,
                'strength': round(signal.strength, 3),
                'confidence': round(signal.confidence, 3),
                'rsi': round(signal.rsi, 1),
                'health': round(signal.internal_health, 1),
                'divergence': {
                    'type': signal.divergence_type,
                    'severity': round(signal.divergence_severity, 4)
                },
                'momentum_coherence': round(signal.momentum_coherence, 3),
                'recommendation': signal.recommendation,
                'supporting_factors': signal.supporting_factors,
                'risk_factors': signal.risk_factors
            }
        
        return timeframe_data

    def _generate_rotation_analysis(self, analysis) -> Dict:
        """生成板块轮动分析 - v41标准"""
        try:
            # 从分析结果中提取轮动指标
            rotation_data = {
                'unified_rii': getattr(analysis, 'unified_rii', self._calculate_unified_rii(analysis)),
                'price_dispersion': getattr(analysis, 'price_dispersion', self._calculate_price_dispersion(analysis)),
                'rank_velocity': getattr(analysis, 'rank_velocity', self._calculate_rank_velocity(analysis)),
                'volume_concentration': getattr(analysis, 'volume_concentration', self._calculate_volume_concentration(analysis)),
                'rotation_stage': getattr(analysis, 'rotation_stage', self._determine_rotation_stage(analysis)),
                'risk_level': analysis.overall_risk_level,
                'stage_probabilities': getattr(analysis, 'stage_probabilities', self._calculate_stage_probabilities(analysis)),
                'momentum_analysis': {
                    'sector_momentum': self._calculate_avg_momentum(analysis),
                    'relative_strength': self._calculate_relative_strength(analysis),
                    'momentum_coherence': self._calculate_momentum_coherence(analysis)
                },
                'optimal_weights': getattr(analysis, 'optimal_weights', self._calculate_optimal_weights(analysis))
            }

            return rotation_data

        except Exception as e:
            logger.error(f"生成轮动分析失败: {e}")
            return self._get_default_rotation_analysis()

    def _calculate_unified_rii(self, analysis) -> float:
        """计算统一轮动强度指数"""
        try:
            # 基于时间框架信号的差异程度计算RII
            if not analysis.timeframe_signals:
                return 0.0

            signals = list(analysis.timeframe_signals.values())
            if len(signals) < 2:
                return 0.0

            # 计算信号强度的标准差作为轮动强度的代理
            strengths = [s.strength for s in signals]
            mean_strength = sum(strengths) / len(strengths)
            variance = sum((s - mean_strength) ** 2 for s in strengths) / len(strengths)
            std_dev = variance ** 0.5

            # 归一化到0-1范围
            rii = min(std_dev * 2, 1.0)  # 乘以2是为了增加敏感度

            # 考虑一致性得分的影响
            consistency_factor = 1 - analysis.consensus_score
            rii = rii * 0.7 + consistency_factor * 0.3

            return round(rii, 4)

        except Exception as e:
            logger.error(f"计算RII失败: {e}")
            return 0.5  # 默认中等轮动强度

    def _calculate_price_dispersion(self, analysis) -> float:
        """计算价格离散度"""
        try:
            # 基于不同时间框架的RSI差异计算价格离散度
            if not analysis.timeframe_signals:
                return 0.0

            rsi_values = [s.rsi for s in analysis.timeframe_signals.values()]
            if len(rsi_values) < 2:
                return 0.0

            mean_rsi = sum(rsi_values) / len(rsi_values)
            variance = sum((rsi - mean_rsi) ** 2 for rsi in rsi_values) / len(rsi_values)

            # 归一化到0-1范围
            dispersion = min(variance / 400, 1.0)  # RSI方差最大约400

            return round(dispersion, 4)

        except Exception as e:
            logger.error(f"计算价格离散度失败: {e}")
            return 0.3  # 默认中等离散度

    def _calculate_rank_velocity(self, analysis) -> float:
        """计算排名变化速度"""
        try:
            # 基于信号强度的变化计算排名变化速度
            if not analysis.timeframe_signals:
                return 0.0

            # 简化计算：基于信号冲突数量
            conflict_ratio = len(analysis.conflicting_signals) / max(len(analysis.timeframe_signals), 1)
            velocity = min(conflict_ratio * 2, 1.0)

            return round(velocity, 4)

        except Exception as e:
            logger.error(f"计算排名变化速度失败: {e}")
            return 0.2  # 默认低变化速度

    def _calculate_volume_concentration(self, analysis) -> float:
        """计算成交量集中度"""
        try:
            # 基于信号可靠性计算成交量集中度的代理
            reliability = analysis.signal_reliability

            # 高可靠性通常意味着更集中的成交量
            concentration = reliability * 0.8 + 0.2  # 基础值0.2

            return round(min(concentration, 1.0), 4)

        except Exception as e:
            logger.error(f"计算成交量集中度失败: {e}")
            return 0.5  # 默认中等集中度

    def _determine_rotation_stage(self, analysis) -> str:
        """确定轮动阶段"""
        try:
            rii = self._calculate_unified_rii(analysis)
            reliability = analysis.signal_reliability
            consensus = analysis.consensus_score

            # 基于RII和一致性确定阶段
            if rii < 0.2 and consensus > 0.8:
                return 'stable'  # 稳定期
            elif rii < 0.4 and consensus > 0.6:
                return 'convergence'  # 收敛期
            elif rii < 0.6 and reliability > 0.6:
                return 'startup'  # 启动期
            elif rii < 0.8:
                return 'acceleration'  # 加速期
            else:
                return 'chaos'  # 混乱期

        except Exception as e:
            logger.error(f"确定轮动阶段失败: {e}")
            return 'unknown'

    def _calculate_stage_probabilities(self, analysis) -> Dict[str, float]:
        """计算各阶段概率"""
        try:
            rii = self._calculate_unified_rii(analysis)
            consensus = analysis.consensus_score

            # 基于RII和一致性计算各阶段概率
            probabilities = {
                'stable': max(0, (0.8 - rii) * consensus),
                'convergence': max(0, (0.6 - abs(rii - 0.3)) * (1 - abs(consensus - 0.7))),
                'startup': max(0, (0.8 - abs(rii - 0.5)) * (1 - abs(consensus - 0.6))),
                'acceleration': max(0, rii * (1 - consensus)),
                'chaos': max(0, rii * (1 - consensus) * 1.2)
            }

            # 归一化概率
            total = sum(probabilities.values())
            if total > 0:
                probabilities = {k: round(v / total, 3) for k, v in probabilities.items()}

            return probabilities

        except Exception as e:
            logger.error(f"计算阶段概率失败: {e}")
            return {'stable': 0.2, 'convergence': 0.2, 'startup': 0.2, 'acceleration': 0.2, 'chaos': 0.2}

    def _calculate_momentum_coherence(self, analysis) -> float:
        """计算动量一致性"""
        try:
            if not analysis.timeframe_signals:
                return 0.0

            # 基于各时间框架信号的一致性
            return analysis.consensus_score

        except Exception as e:
            logger.error(f"计算动量一致性失败: {e}")
            return 0.5

    def _calculate_relative_strength(self, analysis) -> float:
        """计算相对强度"""
        try:
            # 基于平均信号强度
            return self._calculate_avg_momentum(analysis)

        except Exception as e:
            logger.error(f"计算相对强度失败: {e}")
            return 0.5

    def _calculate_optimal_weights(self, analysis) -> Dict[str, float]:
        """计算最优权重"""
        try:
            # 基于动态权重
            if hasattr(analysis, 'dynamic_weights') and analysis.dynamic_weights:
                return analysis.dynamic_weights

            # 默认等权重
            timeframes = list(analysis.timeframe_signals.keys())
            equal_weight = 1.0 / len(timeframes) if timeframes else 0.0

            return {tf: round(equal_weight, 3) for tf in timeframes}

        except Exception as e:
            logger.error(f"计算最优权重失败: {e}")
            return {}

    def _get_default_rotation_analysis(self) -> Dict:
        """获取默认轮动分析"""
        return {
            'unified_rii': 0.5,
            'price_dispersion': 0.3,
            'rank_velocity': 0.2,
            'volume_concentration': 0.5,
            'rotation_stage': 'unknown',
            'risk_level': 'medium',
            'stage_probabilities': {'stable': 0.2, 'convergence': 0.2, 'startup': 0.2, 'acceleration': 0.2, 'chaos': 0.2},
            'momentum_analysis': {
                'sector_momentum': 0.5,
                'relative_strength': 0.5,
                'momentum_coherence': 0.5
            },
            'optimal_weights': {}
        }

    def _format_recommended_sectors(self, analysis) -> List[Dict]:
        """格式化推荐板块 - v41标准"""
        # 由于当前实现是单板块分析，这里模拟多板块格式
        recommended = []
        
        if "买入" in analysis.unified_decision:
            recommended.append({
                'sector': analysis.sector_name,
                'recommended_weight': self._extract_position_percentage(analysis.unified_decision),
                'health_score': round(self._calculate_avg_health(analysis), 1),
                'momentum': round(self._calculate_avg_momentum(analysis), 3),
                'composite_score': round(analysis.signal_reliability, 3),
                'recommendation': analysis.unified_decision,
                'rationale': f"综合得分{analysis.signal_reliability:.2f}，多时间框架一致性{analysis.consensus_score:.2f}"
            })
        
        return recommended
    
    def _format_avoid_sectors(self, analysis) -> List[Dict]:
        """格式化避免板块 - v41标准"""
        avoid_sectors = []
        
        # 基于风险等级判断是否应该避免
        if analysis.overall_risk_level in ['high', 'extreme']:
            risk_emoji = {
                'extreme': '🔴',
                'high': '🟠',
                'medium': '🟡',
                'low': '⚪'
            }
            
            avoid_sectors.append({
                'sector': analysis.sector_name,
                'risk_level': f"{risk_emoji.get(analysis.overall_risk_level, '')} {analysis.overall_risk_level}",
                'score': round(analysis.signal_reliability, 3),
                'health': round(self._calculate_avg_health(analysis), 1),
                'momentum': round(self._calculate_avg_momentum(analysis), 3),
                'avoid_reason': '高风险等级',
                'reasons': analysis.risk_factors,
                'recommendation': f"避免配置{analysis.sector_name}板块",
                'summary': f"{analysis.sector_name}板块当前风险等级为{analysis.overall_risk_level}，建议避免配置"
            })
        
        return avoid_sectors
    
    def _generate_allocation_summary(self, analysis) -> Dict:
        """生成配置摘要 - v41标准"""
        recommended = self._format_recommended_sectors(analysis)
        avoid = self._format_avoid_sectors(analysis)
        
        return {
            'total_recommended_sectors': len(recommended),
            'total_avoid_sectors': len(avoid),
            'recommended_allocation': sum(self._extract_percentage_value(s['recommended_weight']) for s in recommended if s['recommended_weight']),
            'risk_budget': f"{100 - len(avoid) * 10}%",  # 简化计算
            'diversification_score': 0.8 if len(recommended) > 1 else 0.5,
            'allocation_rationale': self._generate_allocation_rationale(analysis)
        }
    
    def _format_risk_assessment(self, analysis) -> Dict:
        """格式化风险评估 - v41标准"""
        risk_levels = {
            'low': '低风险',
            'medium': '中等风险',
            'high': '高风险',
            'extreme': '极高风险'
        }
        
        formatted = {
            'overall_risk': {
                'level': analysis.overall_risk_level,
                'description': risk_levels.get(analysis.overall_risk_level, '未知风险'),
                'factors': analysis.risk_factors
            },
            'specific_risks': {}
        }
        
        # 基于分析结果推断具体风险
        if analysis.consensus_score < 0.5:
            formatted['specific_risks']['信号一致性风险'] = {
                'level': 'medium',
                'description': '各时间框架信号不一致'
            }
        
        if analysis.signal_reliability < 0.6:
            formatted['specific_risks']['信号可靠性风险'] = {
                'level': 'medium', 
                'description': '信号可靠性较低'
            }
        
        return formatted
    
    def _generate_key_insights(self, analysis) -> List[str]:
        """生成关键洞察 - v41标准"""
        insights = []
        
        # 基于一致性得分
        if analysis.consensus_score > 0.8:
            insights.append(f"各时间框架高度一致({analysis.consensus_score:.1%})，信号可靠性强")
        elif analysis.consensus_score < 0.5:
            insights.append(f"时间框架间存在分歧({analysis.consensus_score:.1%})，需谨慎操作")
        
        # 基于市场状态
        if analysis.market_regime == 'trending_up':
            insights.append("市场处于上升趋势，适合趋势跟踪策略")
        elif analysis.market_regime == 'ranging':
            insights.append("市场处于震荡状态，适合区间操作")
        
        # 基于风险等级
        if analysis.overall_risk_level == 'low':
            insights.append("当前风险可控，可适当增加仓位")
        elif analysis.overall_risk_level == 'high':
            insights.append("风险较高，建议降低仓位或观望")
        
        return insights
    
    def _generate_warnings(self, analysis) -> List[str]:
        """生成警告信息 - v41标准"""
        warnings = []
        
        # 基于冲突信号
        if analysis.conflicting_signals:
            warnings.append(f"检测到{len(analysis.conflicting_signals)}个冲突信号，增加操作难度")
        
        # 基于风险因素
        if analysis.risk_factors:
            for risk in analysis.risk_factors[:2]:  # 只显示前2个最重要的
                warnings.append(f"风险提示: {risk}")
        
        # 基于可靠性
        if analysis.signal_reliability < 0.5:
            warnings.append("信号可靠性较低，建议等待更明确的信号")
        
        return warnings
    
    def _generate_monitoring_points(self, analysis) -> List[Dict]:
        """生成监控要点 - v41标准"""
        points = []
        
        # 基于时间框架信号
        for tf, signal in analysis.timeframe_signals.items():
            if signal.strength > 0.7:
                points.append({
                    'timeframe': tf,
                    'metric': 'signal_strength',
                    'current_value': signal.strength,
                    'threshold': 0.7,
                    'description': f"{tf}时间框架信号强度较高，需持续关注"
                })
        
        # 基于风险等级
        if analysis.overall_risk_level in ['high', 'extreme']:
            points.append({
                'timeframe': 'all',
                'metric': 'risk_level',
                'current_value': analysis.overall_risk_level,
                'threshold': 'medium',
                'description': "整体风险等级较高，需密切监控市场变化"
            })
        
        return points
    
    # 辅助方法
    def _interpret_consensus(self, score: float) -> str:
        if score > 0.8:
            return "高度一致"
        elif score > 0.6:
            return "基本一致"
        elif score > 0.4:
            return "部分一致"
        else:
            return "存在分歧"
    
    def _interpret_market_regime(self, regime: str) -> str:
        regime_map = {
            'trending_up': '上升趋势',
            'trending_down': '下降趋势',
            'ranging': '震荡整理',
            'reversal_up': '向上转折',
            'reversal_down': '向下转折',
            'volatile': '高度波动',
            'unknown': '状态不明'
        }
        return regime_map.get(regime, regime)
    
    def _get_regime_implications(self, regime: str) -> str:
        implications = {
            'trending_up': '适合趋势跟踪，逢低买入',
            'trending_down': '适合空头策略，逢高卖出',
            'ranging': '适合区间操作，高抛低吸',
            'reversal_up': '可能的底部，谨慎建仓',
            'reversal_down': '可能的顶部，及时减仓',
            'volatile': '降低仓位，等待明确方向',
            'unknown': '保持观望，等待更多信息'
        }
        return implications.get(regime, '需要进一步观察')
    
    def _interpret_reliability(self, reliability: float) -> str:
        if reliability > 0.8:
            return "高可靠性"
        elif reliability > 0.6:
            return "中等可靠性"
        elif reliability > 0.4:
            return "较低可靠性"
        else:
            return "低可靠性"
    
    def _extract_position_percentage(self, decision: str) -> str:
        """从决策中提取仓位百分比"""
        if "强烈买入" in decision:
            return "60-80%"
        elif "买入" in decision:
            return "40-60%"
        elif "谨慎买入" in decision:
            return "20-40%"
        elif "强烈卖出" in decision:
            return "0-20%"
        elif "卖出" in decision:
            return "20-40%"
        elif "谨慎卖出" in decision:
            return "40-60%"
        else:
            return "40-60%"  # 持有
    
    def _calculate_avg_health(self, analysis) -> float:
        """计算平均健康度"""
        if not analysis.timeframe_signals:
            return 50.0
        
        healths = [signal.internal_health for signal in analysis.timeframe_signals.values()]
        return sum(healths) / len(healths)
    
    def _calculate_avg_momentum(self, analysis) -> float:
        """计算平均动量"""
        if not analysis.timeframe_signals:
            return 0.0
        
        # 使用strength作为动量的代理
        strengths = [signal.strength for signal in analysis.timeframe_signals.values()]
        return sum(strengths) / len(strengths)
    
    def _identify_primary_driver(self, analysis) -> str:
        """识别主要驱动因素"""
        if analysis.consensus_score > 0.8:
            return "多时间框架一致性"
        elif analysis.overall_risk_level == 'low':
            return "低风险环境"
        elif len(analysis.conflicting_signals) > 0:
            return "信号冲突"
        else:
            return "市场状态变化"
    
    def _determine_action_urgency(self, analysis) -> str:
        """确定行动紧急性"""
        if analysis.overall_risk_level == 'extreme':
            return "立即行动"
        elif "强烈" in analysis.unified_decision:
            return "尽快行动"
        elif analysis.signal_reliability > 0.8:
            return "适时行动"
        else:
            return "观察等待"
    
    def _determine_execution_timeframe(self, analysis) -> str:
        """确定执行时间框架"""
        if analysis.overall_risk_level in ['high', 'extreme']:
            return "短期内(1-3天)"
        elif analysis.signal_reliability > 0.7:
            return "中期内(1-2周)"
        else:
            return "长期内(1个月)"
    
    def _generate_operation_points(self, analysis) -> List[str]:
        """生成操作要点"""
        points = []
        
        points.append(f"主要策略: {analysis.entry_strategy}")
        points.append(f"风险控制: {analysis.exit_strategy}")
        points.append(f"仓位管理: {analysis.position_sizing}")
        
        if analysis.overall_risk_level == 'high':
            points.append("当前风险较高，优先保护本金")
        
        return points
    
    def _generate_allocation_rationale(self, analysis) -> str:
        """生成配置理由"""
        return f"基于{analysis.consensus_score:.1%}的时间框架一致性和{analysis.signal_reliability:.1%}的信号可靠性"

    def _explain_position_decision(self, analysis) -> str:
        """解释仓位决策的原因"""
        reasons = []

        # 基于一致性得分
        if analysis.consensus_score > 0.8:
            reasons.append("各时间框架高度一致")
        elif analysis.consensus_score < 0.5:
            reasons.append("时间框架存在分歧，降低仓位")

        # 基于信号可靠性
        if analysis.signal_reliability > 0.8:
            reasons.append("信号可靠性高")
        elif analysis.signal_reliability < 0.5:
            reasons.append("信号可靠性较低，谨慎操作")

        # 基于风险等级
        if analysis.overall_risk_level == 'low':
            reasons.append("风险可控")
        elif analysis.overall_risk_level in ['high', 'extreme']:
            reasons.append("风险较高，需要控制仓位")

        # 基于市场状态
        if analysis.market_regime == 'trending_up':
            reasons.append("市场上升趋势支持")
        elif analysis.market_regime == 'ranging':
            reasons.append("震荡市场，适度配置")

        return "; ".join(reasons) if reasons else "基于综合分析结果"

    def _extract_percentage_value(self, percentage_str: str) -> float:
        """从百分比字符串中提取数值"""
        try:
            # 处理 "60-80%" 格式，取中间值
            if '-' in percentage_str:
                parts = percentage_str.replace('%', '').split('-')
                if len(parts) == 2:
                    return (float(parts[0]) + float(parts[1])) / 2

            # 处理 "60%" 格式
            return float(percentage_str.rstrip('%'))
        except:
            return 50.0  # 默认值
    
    def _generate_error_report(self, error_msg: str) -> Dict:
        """生成错误报告"""
        return {
            'metadata': {
                'report_type': '错误报告',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'version': '4.1',
                'error': error_msg
            },
            'executive_summary': {
                'main_conclusion': '分析过程中出现错误',
                'confidence_level': '0%',
                'action_required': '检查数据和系统状态'
            }
        }
