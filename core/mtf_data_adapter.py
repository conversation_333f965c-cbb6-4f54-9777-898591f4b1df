#!/usr/bin/env python3
"""
MTF数据适配器 - 将现有MySQL/Redis数据转换为MTF系统需要的格式
"""

import sys
import os
import json
import redis
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
import pymysql
from dotenv import load_dotenv

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

# Redis配置（与price_data_collector保持一致）
REDIS_CONFIG = {
    'host': '************',
    'port': 6379,
    'db': 15,
    'decode_responses': True,
    'socket_timeout': 5,
    'socket_connect_timeout': 5
}

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DEFAULT_DB_HOST', 'localhost'),
    'port': int(os.getenv('DEFAULT_DB_PORT', 3306)),
    'user': os.getenv('DEFAULT_DB_USER', 'root'),
    'password': os.getenv('DEFAULT_DB_PASSWORD', ''),
    'database': os.getenv('DEFAULT_DB_NAME', 'stock_data'),
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MTF系统数据结构
class SectorBreadthData:
    """板块广度数据结构"""
    def __init__(self, sector_name: str, db_record: Dict):
        """
        从数据库记录初始化广度数据
        :param sector_name: 板块名称
        :param db_record: 来自 market_breadth_metrics_gics 表的记录
        """
        self.sector_name = sector_name
        self.timestamp = db_record.get('recorded_at', datetime.now())

        # 基础数据（直接从数据库读取）
        self.advances = int(db_record.get('advances', 0))
        self.declines = int(db_record.get('declines', 0))
        self.unchanged = int(db_record.get('unchanged', 0))
        self.total_stocks = int(db_record.get('total_stocks', 0))
        self.advancing_volume = float(db_record.get('advancing_volume', 0))
        self.declining_volume = float(db_record.get('declining_volume', 0))
        self.new_highs_52w = int(db_record.get('new_highs_52w', 0))
        self.new_lows_52w = int(db_record.get('new_lows_52w', 0))
        self.above_ma50 = int(db_record.get('above_ma50', 0))
        self.above_ma200 = int(db_record.get('above_ma200', 0))
        self.avg_rsi = float(db_record.get('avg_rsi', 50.0))

        # 计算指标（直接从数据库读取，避免重复计算）
        self.ad_ratio = self.advances / self.declines if self.declines > 0 else (10.0 if self.advances > 0 else 1.0)
        self.purity = float(db_record.get('purity', 0.5))
        self.volume_breadth = (self.advancing_volume - self.declining_volume) / (self.advancing_volume + self.declining_volume) if (self.advancing_volume + self.declining_volume) > 0 else 0.0
        self.nh_nl_ratio = self.new_highs_52w / self.new_lows_52w if self.new_lows_52w > 0 else (10.0 if self.new_highs_52w > 0 else 1.0)
        self.ma50_breadth = self.above_ma50 / self.total_stocks if self.total_stocks > 0 else 0.5
        self.ma200_breadth = self.above_ma200 / self.total_stocks if self.total_stocks > 0 else 0.5
        self.participation_rate = 1 - (self.unchanged / self.total_stocks) if self.total_stocks > 0 else 0.5

        # 高级指标（直接从数据库读取，由 hist_data.py 计算）
        self.internal_health = float(db_record.get('internal_health', 50.0))
        self.momentum_coherence = float(db_record.get('momentum_coherence', 0.5)) if db_record.get('momentum_coherence') is not None else 0.5

        # 背离信息（直接从数据库读取）
        divergence_type = db_record.get('divergence_type', 'none')
        divergence_severity = float(db_record.get('divergence_severity', 0.0))

        if divergence_type != 'none':
            self.price_breadth_divergence = {
                'type': divergence_type,
                'severity': divergence_severity,
                'message': f'检测到{divergence_type}背离，严重程度: {divergence_severity:.4f}'
            }
        else:
            self.price_breadth_divergence = None

        # 一致性详细信息（基于数据库数据估算）
        self.coherence_details = {
            'overall_coherence': self.momentum_coherence,
            'coherence_type': 'strong_consensus' if self.momentum_coherence > 0.8 else (
                'high_dispersion' if self.momentum_coherence < 0.3 else 'mixed'
            ),
            'interpretation': '基于数据库数据的估算'
        }

        # 个股收益率（MTF分析中不需要，设为空列表）
        self.individual_returns = []


class MTFDataAdapter:
    """MTF数据适配器 - 第一阶段：从现有数据源获取并转换数据"""
    
    def __init__(self):
        """初始化适配器"""
        self.redis_client = redis.Redis(**REDIS_CONFIG)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 测试连接
        try:
            self.redis_client.ping()
            self.logger.info("[OK] Redis连接成功")
        except Exception as e:
            self.logger.error(f"[ERROR] Redis连接失败: {e}")
            raise
    
    def get_mtf_data_for_single_timeframe(self, days: int = 30, use_real_data: bool = False) -> Dict:
        """
        获取单一时间框架（日度）的MTF数据
        
        :param days: 获取天数
        :param use_real_data: 是否使用真实数据（True）还是模拟数据（False）
        :return: MTF标准格式数据
        """
        try:
            self.logger.info(f"[MTF] 开始获取 {days} 天的MTF数据... (真实数据: {use_real_data})")
            
            if use_real_data:
                # 使用真实数据
                prices_df, volumes_df = self._get_real_prices_and_volumes_from_db(days)
                sectors_breadth = self._get_real_sectors_breadth_data()
            else:
                # 使用模拟数据（第一阶段）
                prices_df, volumes_df = self._get_prices_and_volumes_from_redis(days)
                sectors_breadth = self._get_sectors_breadth_data()
            
            # 组装MTF数据
            mtf_data = {
                'prices': prices_df,
                'volumes': volumes_df,
                'sectors_breadth': sectors_breadth
            }
            
            self.logger.info(f"[MTF] 成功获取MTF数据 - 板块数: {len(prices_df.columns) if not prices_df.empty else 0}")
            return mtf_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取MTF数据失败: {e}")
            return {'prices': pd.DataFrame(), 'volumes': pd.DataFrame(), 'sectors_breadth': {}}
    
    def _get_prices_and_volumes_from_redis(self, days: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """从Redis获取价格和成交量数据"""
        try:
            # 1. 获取所有股票队列
            pattern = "stock_queue:*"
            keys = self.redis_client.keys(pattern)
            
            if not keys:
                self.logger.warning("[WARN] Redis中未找到股票队列")
                return pd.DataFrame(), pd.DataFrame()
            
            # 2. 获取板块映射（简化版：使用前10只股票作为示例板块）
            sector_mapping = self._get_sector_mapping_from_stocks(keys[:10])
            
            # 3. 聚合板块数据
            sector_prices = {}
            sector_volumes = {}
            
            for sector, stock_keys in sector_mapping.items():
                sector_price_data = []
                sector_volume_data = []
                
                for stock_key in stock_keys:
                    stock_data = self._get_stock_data_from_redis(stock_key, days)
                    if stock_data:
                        sector_price_data.append(stock_data['prices'])
                        sector_volume_data.append(stock_data['volumes'])
                
                if sector_price_data:
                    # 计算板块平均价格（简化版）
                    sector_prices[sector] = self._calculate_sector_average(sector_price_data)
                    sector_volumes[sector] = self._calculate_sector_sum(sector_volume_data)
            
            # 4. 转换为DataFrame
            prices_df = pd.DataFrame(sector_prices)
            volumes_df = pd.DataFrame(sector_volumes)
            
            self.logger.info(f"[DATA] 获取 {len(sector_prices)} 个板块的价格数据")
            return prices_df, volumes_df
            
        except Exception as e:
            self.logger.error(f"[ERROR] 从Redis获取数据失败: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _get_real_prices_and_volumes_from_db(self, days: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """从数据库获取真实的板块价格和成交量数据"""
        try:
            # 1. 获取真实的板块映射
            sector_mapping = self._get_real_sector_mapping()
            
            if not sector_mapping:
                self.logger.warning("[WARN] 未获取到真实板块映射，回退到Redis数据")
                return self._get_prices_and_volumes_from_redis(days)
            
            # 2. 获取板块历史价格数据
            sector_prices = {}
            sector_volumes = {}
            
            # 使用数据库中的sector_indices_gics表
            connection = pymysql.connect(**DB_CONFIG)
            try:
                with connection.cursor() as cursor:
                    # 获取最近days天的板块数据
                    query = """
                    SELECT recorded_at, sector, index_value, volume 
                    FROM sector_indices_gics 
                    WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                    ORDER BY recorded_at ASC, sector ASC
                    """
                    cursor.execute(query, (days,))
                    results = cursor.fetchall()
                    
                    # 组织数据
                    for recorded_at, sector, index_value, volume in results:
                        if sector not in sector_prices:
                            sector_prices[sector] = []
                            sector_volumes[sector] = []
                        
                        sector_prices[sector].append({
                            'date': recorded_at,
                            'value': float(index_value)
                        })
                        sector_volumes[sector].append({
                            'date': recorded_at,
                            'value': float(volume)
                        })
            
            finally:
                connection.close()
            
            # 3. 转换为DataFrame格式
            prices_df_data = {}
            volumes_df_data = {}
            
            for sector in sector_prices:
                if sector_prices[sector]:
                    # 转换为时间序列
                    dates = [item['date'] for item in sector_prices[sector]]
                    prices = [item['value'] for item in sector_prices[sector]]
                    volumes = [item['value'] for item in sector_volumes[sector]]
                    
                    prices_df_data[sector] = pd.Series(prices, index=pd.to_datetime(dates))
                    volumes_df_data[sector] = pd.Series(volumes, index=pd.to_datetime(dates))
            
            prices_df = pd.DataFrame(prices_df_data)
            volumes_df = pd.DataFrame(volumes_df_data)
            
            self.logger.info(f"[REAL_DATA] 获取 {len(prices_df.columns)} 个真实板块的历史数据")
            return prices_df, volumes_df
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取真实板块数据失败: {e}")
            self.logger.warning("[FALLBACK] 回退到Redis模拟数据")
            return self._get_prices_and_volumes_from_redis(days)
    
    def _get_real_sector_mapping(self) -> Dict[str, List[str]]:
        """获取真实的GICS板块映射"""
        try:
            connection = pymysql.connect(**DB_CONFIG)
            with connection.cursor() as cursor:
                # 查询所有股票的板块分类
                sql = """
                SELECT DISTINCT market, company 
                FROM index_company_mapping_gics 
                WHERE market IS NOT NULL AND market != '' 
                AND company IS NOT NULL AND company != ''
                ORDER BY market, company
                """
                cursor.execute(sql)
                results = cursor.fetchall()
                
                # 构建板块到股票的映射
                sector_mapping = {}
                for market, company in results:
                    if market not in sector_mapping:
                        sector_mapping[market] = []
                    sector_mapping[market].append(company)
                
                # 统计唯一股票数量（去重）
                unique_stocks = set()
                for stocks in sector_mapping.values():
                    unique_stocks.update(stocks)
                
                total_with_duplicates = sum(len(stocks) for stocks in sector_mapping.values())
                
                self.logger.info(f"[REAL_MAPPING] 获取 {len(sector_mapping)} 个真实板块，"
                               f"共 {len(unique_stocks)} 只唯一股票 "
                               f"(原统计 {total_with_duplicates} 只，去重 {total_with_duplicates - len(unique_stocks)} 只)")
                return sector_mapping
                
        except Exception as e:
            self.logger.error(f"[ERROR] 获取真实板块映射失败: {e}")
            return {}
        finally:
            if 'connection' in locals():
                connection.close()
    
    def _get_sector_mapping_from_stocks(self, stock_keys: List[str]) -> Dict[str, List[str]]:
        """从股票创建简化的板块映射"""
        # 简化版：每3只股票组成一个板块
        sector_mapping = {}
        sector_count = 0
        
        for i in range(0, len(stock_keys), 3):
            sector_name = f"Sector_{sector_count + 1}"
            sector_stocks = stock_keys[i:i+3]
            sector_mapping[sector_name] = sector_stocks
            sector_count += 1
        
        return sector_mapping
    
    def _get_stock_data_from_redis(self, stock_key: str, days: int) -> Optional[Dict]:
        """从Redis获取单只股票数据"""
        try:
            data_raw = self.redis_client.get(stock_key)
            if not data_raw:
                return None
            
            stock_data = json.loads(data_raw)
            price_data = stock_data.get('data', [])
            
            if not price_data:
                return None
            
            # 只取最近days天的数据
            recent_data = price_data[-days:] if len(price_data) > days else price_data
            
            prices = []
            volumes = []
            dates = []
            
            for item in recent_data:
                try:
                    dates.append(pd.to_datetime(item['date']))
                    prices.append(float(item['close']))
                    volumes.append(float(item['volume']))
                except (KeyError, ValueError, TypeError):
                    continue
            
            if dates:
                return {
                    'dates': dates,
                    'prices': pd.Series(prices, index=dates),
                    'volumes': pd.Series(volumes, index=dates)
                }
            
            return None
            
        except Exception as e:
            self.logger.debug(f"[DEBUG] 获取股票数据失败 {stock_key}: {e}")
            return None
    
    def _calculate_sector_average(self, price_series_list: List[pd.Series]) -> pd.Series:
        """计算板块平均价格"""
        if not price_series_list:
            return pd.Series()
        
        # 对齐时间序列并计算平均值
        combined_df = pd.concat(price_series_list, axis=1)
        return combined_df.mean(axis=1)
    
    def _calculate_sector_sum(self, volume_series_list: List[pd.Series]) -> pd.Series:
        """计算板块成交量总和"""
        if not volume_series_list:
            return pd.Series()
        
        # 对齐时间序列并计算总和
        combined_df = pd.concat(volume_series_list, axis=1)
        return combined_df.sum(axis=1)
    
    def _get_sectors_breadth_data(self) -> Dict[str, SectorBreadthData]:
        """获取板块广度数据（第一阶段：模拟数据）"""
        try:
            # 第一阶段：创建模拟的广度数据
            sectors_breadth = {}
            current_time = datetime.now()
            
            # 创建几个示例板块的广度数据
            for i in range(1, 4):  # 3个板块
                sector_name = f"Sector_{i}"
                
                # 模拟广度指标
                total_stocks = 50
                advances = np.random.randint(20, 35)
                declines = np.random.randint(10, 25)
                unchanged = total_stocks - advances - declines
                
                breadth_data = SectorBreadthData(
                    sector_name=sector_name,
                    timestamp=current_time,
                    advances=advances,
                    declines=declines,
                    unchanged=unchanged,
                    total_stocks=total_stocks,
                    advancing_volume=np.random.uniform(1000000, 5000000),
                    declining_volume=np.random.uniform(800000, 3000000),
                    new_highs_52w=np.random.randint(2, 8),
                    new_lows_52w=np.random.randint(0, 5),
                    above_ma50=np.random.randint(25, 40),
                    above_ma200=np.random.randint(20, 35),
                    avg_rsi=np.random.uniform(45, 65),
                    individual_returns=np.random.normal(0, 0.02, 20).tolist()
                )
                
                sectors_breadth[sector_name] = breadth_data
            
            self.logger.info(f"[BREADTH] 创建 {len(sectors_breadth)} 个板块的广度数据（模拟）")
            return sectors_breadth
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取广度数据失败: {e}")
            return {}
    
    def _get_real_sectors_breadth_data(self) -> Dict[str, SectorBreadthData]:
        """获取真实的板块广度数据（直接从数据库读取，避免重复计算）"""
        try:
            connection = pymysql.connect(**DB_CONFIG)
            sectors_breadth = {}

            with connection.cursor() as cursor:
                # 获取最新的市场广度数据（包含所有计算好的指标）
                query = """
                SELECT market, recorded_at, total_stocks, advances, declines, unchanged,
                       advancing_volume, declining_volume, new_highs_52w, new_lows_52w,
                       above_ma50, above_ma200, avg_rsi,
                       purity, internal_health, momentum_coherence,
                       divergence_type, divergence_severity
                FROM market_breadth_metrics_gics
                WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                ORDER BY market, recorded_at DESC
                """
                cursor.execute(query)
                results = cursor.fetchall()

                # 按市场分组，取最新数据
                market_data = {}
                for row in results:
                    market = row[0]
                    if market not in market_data:
                        market_data[market] = row

                # 转换为SectorBreadthData格式（使用简化版构造函数）
                for market, data in market_data.items():
                    # 构建数据库记录字典
                    db_record = {
                        'recorded_at': data[1],
                        'total_stocks': data[2],
                        'advances': data[3],
                        'declines': data[4],
                        'unchanged': data[5],
                        'advancing_volume': data[6],
                        'declining_volume': data[7],
                        'new_highs_52w': data[8],
                        'new_lows_52w': data[9],
                        'above_ma50': data[10],
                        'above_ma200': data[11],
                        'avg_rsi': data[12],
                        'purity': data[13],
                        'internal_health': data[14],
                        'momentum_coherence': data[15],
                        'divergence_type': data[16],
                        'divergence_severity': data[17]
                    }

                    # 使用简化版构造函数（直接从数据库读取）
                    breadth_data = SectorBreadthData(market, db_record)
                    sectors_breadth[market] = breadth_data
            
            self.logger.info(f"[REAL_BREADTH] 获取 {len(sectors_breadth)} 个板块的真实广度数据")
            return sectors_breadth
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取真实广度数据失败: {e}")
            self.logger.warning("[FALLBACK] 回退到模拟广度数据")
            return self._get_sectors_breadth_data()
        finally:
            if 'connection' in locals():
                connection.close()

    def validate_mtf_data(self, mtf_data: Dict) -> bool:
        """验证MTF数据格式"""
        try:
            required_keys = ['prices', 'volumes', 'sectors_breadth']
            
            for key in required_keys:
                if key not in mtf_data:
                    self.logger.error(f"[ERROR] MTF数据缺少必需字段: {key}")
                    return False
            
            prices_df = mtf_data['prices']
            volumes_df = mtf_data['volumes']
            
            if prices_df.empty or volumes_df.empty:
                self.logger.warning("[WARN] 价格或成交量数据为空")
                return False
            
            if len(mtf_data['sectors_breadth']) == 0:
                self.logger.warning("[WARN] 广度数据为空")
                return False
            
            self.logger.info("[OK] MTF数据格式验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] MTF数据验证失败: {e}")
            return False


if __name__ == "__main__":
    """测试MTFDataAdapter"""
    try:
        adapter = MTFDataAdapter()
        
        # 测试获取MTF数据
        mtf_data = adapter.get_mtf_data_for_single_timeframe(days=30)
        
        # 验证数据
        is_valid = adapter.validate_mtf_data(mtf_data)
        
        if is_valid:
            print("✅ MTFDataAdapter测试成功")
            print(f"📊 价格数据形状: {mtf_data['prices'].shape}")
            print(f"📊 成交量数据形状: {mtf_data['volumes'].shape}")
            print(f"📊 广度数据板块数: {len(mtf_data['sectors_breadth'])}")
        else:
            print("❌ MTFDataAdapter测试失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}") 