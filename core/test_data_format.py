#!/usr/bin/env python3
"""
测试数据格式
"""

import os
import sys
from datetime import datetime, timedelta

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
config_path = os.path.join(parent_dir, 'config')
data_source_path = os.path.join(parent_dir, 'data_source_tool')

if config_path not in sys.path:
    sys.path.insert(0, config_path)
if data_source_path not in sys.path:
    sys.path.insert(0, data_source_path)

from market_data_manager import download_hist_price

# 测试下载一只股票的数据
end_date = datetime.now()
start_date = end_date - timedelta(days=30)

print("测试下载AAPL的数据...")
data = download_hist_price(
    ['AAPL'],
    interval='1d',
    start=start_date.strftime('%Y-%m-%d'),
    end=end_date.strftime('%Y-%m-%d'),
    columns=['h', 'l', 'c', 'v'],
    threads=1
)

if data and 'AAPL' in data:
    df = data['AAPL']
    print(f"数据类型: {type(df)}")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print(f"索引类型: {type(df.index)}")
    print(f"索引名称: {df.index.name}")
    print("前5行数据:")
    print(df.head())
    print("\n索引前5个值:")
    print(df.index[:5])
else:
    print("没有获取到数据")
