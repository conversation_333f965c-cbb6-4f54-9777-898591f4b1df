
"""
市场广度分析器 - 负责板块内部结构分析
基于优化文档要求，集成改进的动量一致性计算和数据验证
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging

from .improved_momentum_coherence import ImprovedMomentumCoherence, CoherenceDetails

logger = logging.getLogger(__name__)


@dataclass
class SectorBreadthData:
    """板块广度数据结构"""
    sector_name: str
    timestamp: datetime
    total_stocks: int
    advances: int
    declines: int
    unchanged: int
    advancing_volume: float
    declining_volume: float
    total_volume: float
    new_highs_52w: int
    new_lows_52w: int
    above_ma50: int
    above_ma200: int
    avg_rsi: float
    individual_returns: Optional[List[float]] = None


@dataclass
class BreadthMetrics:
    """广度指标结果"""
    sector_name: str
    timestamp: datetime
    ad_ratio: float
    purity: float
    volume_breadth: float
    nh_nl_ratio: float
    ma50_breadth: float
    ma200_breadth: float
    internal_health: float
    participation_rate: float
    momentum_coherence: Optional[float] = None
    coherence_details: Optional[CoherenceDetails] = None
    price_breadth_divergence: Optional[Dict] = None


class MarketBreadthAnalyzer:
    """市场广度分析器 - 负责板块内部结构分析"""

    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化市场广度分析器

        Args:
            decision_tracker: 可选的决策追踪器
        """
        self.min_stocks_threshold = 10  # 最小股票数量阈值
        self.coherence_calculator = ImprovedMomentumCoherence(decision_tracker)
        self.decision_tracker = decision_tracker

    def analyze_sector_breadth(self, sector_data: SectorBreadthData,
                             price_change: Optional[float] = None) -> BreadthMetrics:
        """
        分析单个板块的内部广度

        Args:
            sector_data: 板块广度数据
            price_change: 可选的价格变化率，用于背离检测

        Returns:
            BreadthMetrics: 广度指标结果
        """
        # 开始决策追踪
        if self.decision_tracker:
            self.decision_tracker.start_decision('sector_breadth_analysis', {
                'sector_name': sector_data.sector_name,
                'total_stocks': sector_data.total_stocks,
                'advances': sector_data.advances,
                'declines': sector_data.declines,
                'price_change': price_change
            })

        # 数据验证
        is_valid, errors = self._validate_sector_data(sector_data)
        if not is_valid:
            if self.decision_tracker:
                self.decision_tracker.add_step(
                    'data_validation_failed',
                    {'errors': errors},
                    f'数据验证失败: {"; ".join(errors)}',
                    confidence=0.0
                )
            logger.warning(f"板块 {sector_data.sector_name} 数据验证失败: {errors}")
        else:
            if self.decision_tracker:
                self.decision_tracker.add_step(
                    'data_validation_passed',
                    {'valid': True},
                    '数据验证通过，所有字段符合要求',
                    confidence=0.95
                )

        # 基础广度指标计算
        ad_ratio = self._calculate_ad_ratio(sector_data.advances, sector_data.declines)
        purity = self._calculate_purity(sector_data)
        volume_breadth = self._calculate_volume_breadth(sector_data)
        nh_nl_ratio = self._calculate_nh_nl_ratio(sector_data)

        # 均线广度
        ma50_breadth = (sector_data.above_ma50 / sector_data.total_stocks
                       if sector_data.total_stocks > 0 else 0.5)
        ma200_breadth = (sector_data.above_ma200 / sector_data.total_stocks
                        if sector_data.total_stocks > 0 else 0.5)

        # 参与率
        participation_rate = (1 - (sector_data.unchanged / sector_data.total_stocks)
                            if sector_data.total_stocks > 0 else 0.5)

        # 动量一致性（使用改进算法）
        momentum_coherence = None
        coherence_details = None
        if sector_data.individual_returns:
            momentum_coherence, coherence_details = self.coherence_calculator.calculate_coherence(
                sector_data.individual_returns
            )

        # 内部健康度评分（考虑一致性）
        internal_health = self._calculate_internal_health(
            ad_ratio, purity, volume_breadth, nh_nl_ratio,
            ma50_breadth, ma200_breadth, participation_rate,
            momentum_coherence, coherence_details
        )

        # 背离检测（如果提供了价格变化）
        price_breadth_divergence = None
        if price_change is not None:
            price_breadth_divergence = self._detect_price_breadth_divergence(
                price_change, sector_data, ad_ratio, volume_breadth,
                nh_nl_ratio, ma50_breadth, ma200_breadth, momentum_coherence
            )

        # 构建结果
        result = BreadthMetrics(
            sector_name=sector_data.sector_name,
            timestamp=sector_data.timestamp,
            ad_ratio=ad_ratio,
            purity=purity,
            volume_breadth=volume_breadth,
            nh_nl_ratio=nh_nl_ratio,
            ma50_breadth=ma50_breadth,
            ma200_breadth=ma200_breadth,
            internal_health=internal_health,
            participation_rate=participation_rate,
            momentum_coherence=momentum_coherence,
            coherence_details=coherence_details,
            price_breadth_divergence=price_breadth_divergence
        )

        # 完成决策记录
        if self.decision_tracker:
            self.decision_tracker.complete_decision({
                'internal_health': internal_health,
                'momentum_coherence': momentum_coherence,
                'coherence_type': coherence_details.coherence_type if coherence_details else None,
                'divergence_detected': price_breadth_divergence is not None
            }, internal_health / 100.0)  # 将健康度转换为0-1范围作为置信度

        return result

    def _validate_sector_data(self, data: SectorBreadthData) -> Tuple[bool, List[str]]:
        """
        验证数据完整性和合理性（OPT-T1.3）

        Args:
            data: 板块广度数据

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误列表)
        """
        errors = []

        # 完整性检查
        if data.total_stocks < self.min_stocks_threshold:
            errors.append(f"股票数量过少: {data.total_stocks}")

        # 逻辑一致性检查
        if data.advances + data.declines + data.unchanged != data.total_stocks:
            errors.append(f"涨跌停家数不匹配总数: {data.advances}+{data.declines}+{data.unchanged}≠{data.total_stocks}")

        # 范围合理性检查
        if not 0 <= data.avg_rsi <= 100:
            errors.append(f"RSI超出范围: {data.avg_rsi}")

        if data.above_ma50 > data.total_stocks or data.above_ma200 > data.total_stocks:
            errors.append("均线统计数据异常")

        if data.total_volume < 0 or data.advancing_volume < 0 or data.declining_volume < 0:
            errors.append("成交量数据异常")

        # 异常值检测
        if data.individual_returns:
            extreme_returns = [r for r in data.individual_returns if abs(r) > 0.5]  # 超过50%的收益率
            if len(extreme_returns) > len(data.individual_returns) * 0.1:  # 超过10%的股票有极端收益率
                errors.append(f"发现{len(extreme_returns)}个极端收益率值")

        return len(errors) == 0, errors

    def _calculate_ad_ratio(self, advances: int, declines: int) -> float:
        """计算涨跌比率"""
        if declines == 0:
            return 10.0 if advances > 0 else 1.0
        return min(advances / declines, 10.0)

    def _calculate_purity(self, data: SectorBreadthData) -> float:
        """计算纯度指标"""
        if data.total_stocks == 0:
            return 0.5

        advance_ratio = data.advances / data.total_stocks
        decline_ratio = data.declines / data.total_stocks
        purity = advance_ratio**2 + decline_ratio**2
        return max(0, min(1, purity))

    def _calculate_volume_breadth(self, data: SectorBreadthData) -> float:
        """计算成交量广度"""
        if data.total_volume == 0:
            return 0.0
        return (data.advancing_volume - data.declining_volume) / data.total_volume

    def _calculate_nh_nl_ratio(self, data: SectorBreadthData) -> float:
        """计算新高新低比率"""
        if data.new_lows_52w == 0:
            return 10.0 if data.new_highs_52w > 0 else 1.0
        return min(data.new_highs_52w / data.new_lows_52w, 10.0)

    def _calculate_internal_health(self, ad_ratio: float, purity: float,
                                 volume_breadth: float, nh_nl_ratio: float,
                                 ma50_breadth: float, ma200_breadth: float,
                                 participation_rate: float,
                                 momentum_coherence: Optional[float],
                                 coherence_details: Optional[CoherenceDetails]) -> float:
        """
        计算内部健康度（改进版，考虑一致性）

        Returns:
            float: 内部健康度评分 [0, 100]
        """
        # 基础评分
        scores = {
            'ad_score': min(ad_ratio * 20, 100),
            'purity_score': purity * 100,
            'volume_score': (volume_breadth + 1) * 50,
            'nh_nl_score': min(nh_nl_ratio * 15, 100),
            'ma_score': (ma50_breadth * 0.6 + ma200_breadth * 0.4) * 100,
            'participation_score': participation_rate * 100
        }

        # 权重分配
        weights = {
            'ad_score': 0.20,
            'purity_score': 0.15,
            'volume_score': 0.15,
            'nh_nl_score': 0.10,
            'ma_score': 0.25,
            'participation_score': 0.15
        }

        # 基础健康度
        base_health = sum(scores[k] * weights[k] for k in scores)

        # 一致性调整
        if momentum_coherence is not None and coherence_details is not None:
            coherence_type = coherence_details.coherence_type

            if coherence_type == 'strong_consensus':
                health_bonus = 5
                adjustment_reason = "强共识加分"
            elif coherence_type == 'high_dispersion':
                health_bonus = -10
                adjustment_reason = "高度分化减分"
            elif coherence_type == 'directional_divergence':
                health_bonus = -3
                adjustment_reason = "幅度分化轻微减分"
            elif momentum_coherence > 0.7:
                health_bonus = 3
                adjustment_reason = "高一致性加分"
            elif momentum_coherence < 0.3:
                health_bonus = -5
                adjustment_reason = "低一致性减分"
            else:
                health_bonus = 0
                adjustment_reason = "一致性正常"

            adjusted_health = base_health + health_bonus

            # 记录调整
            if self.decision_tracker:
                self.decision_tracker.add_step(
                    'health_adjustment',
                    {
                        'base_health': base_health,
                        'coherence_bonus': health_bonus,
                        'final_health': adjusted_health
                    },
                    adjustment_reason,
                    confidence=0.85
                )
        else:
            adjusted_health = base_health

        return round(max(0, min(100, adjusted_health)), 2)

    def _detect_price_breadth_divergence(self, price_change: float,
                                       sector_data: SectorBreadthData,
                                       ad_ratio: float, volume_breadth: float,
                                       nh_nl_ratio: float, ma50_breadth: float,
                                       ma200_breadth: float,
                                       momentum_coherence: Optional[float]) -> Optional[Dict]:
        """
        检测价格广度背离

        Args:
            price_change: 价格变化率
            sector_data: 板块数据
            其他参数: 已计算的广度指标

        Returns:
            Optional[Dict]: 背离信息，如果没有背离则返回None
        """
        try:
            # 导入背离检测器（避免循环导入）
            from .enhanced_divergence_detector import EnhancedDivergenceDetector

            detector = EnhancedDivergenceDetector(self.decision_tracker)

            # 构建广度指标字典
            breadth_dict = {
                'sector_name': sector_data.sector_name,
                'ad_ratio': ad_ratio,
                'volume_breadth': volume_breadth,
                'nh_nl_ratio': nh_nl_ratio,
                'ma50_breadth': ma50_breadth,
                'ma200_breadth': ma200_breadth,
                'avg_rsi': sector_data.avg_rsi,
                'momentum_coherence': momentum_coherence
            }

            # 执行背离检测
            divergence_result = detector.detect_divergence(price_change, breadth_dict)

            if divergence_result.divergence_type.value != 'none':
                return {
                    'type': divergence_result.divergence_type.value,
                    'severity': divergence_result.enhanced_severity,
                    'confidence': divergence_result.confidence,
                    'risk_level': divergence_result.risk_level,
                    'description': divergence_result.description,
                    'recommendation': divergence_result.recommendation,
                    'supporting_evidence': divergence_result.supporting_evidence,
                    'adjustments': divergence_result.adjustments
                }

            return None

        except Exception as e:
            logger.warning(f"背离检测失败: {e}")
            return None

    def calculate_market_breadth_summary(self,
                                       sector_breadth_list: List[BreadthMetrics]) -> Dict:
        """
        计算市场整体广度摘要（增强版）

        Args:
            sector_breadth_list: 各板块广度指标列表

        Returns:
            Dict: 市场整体广度摘要
        """
        if not sector_breadth_list:
            return {}

        # 基础统计
        health_scores = [b.internal_health for b in sector_breadth_list]
        avg_internal_health = np.mean(health_scores)

        # 板块分类
        strong_sectors = [(b.sector_name, b.internal_health)
                         for b in sector_breadth_list if b.internal_health > 70]
        neutral_sectors = [(b.sector_name, b.internal_health)
                          for b in sector_breadth_list if 40 <= b.internal_health <= 70]
        weak_sectors = [(b.sector_name, b.internal_health)
                       for b in sector_breadth_list if b.internal_health < 40]

        # 一致性分析
        coherence_scores = []
        coherence_types = {}
        for b in sector_breadth_list:
            if b.coherence_details:
                coherence_scores.append(b.coherence_details.overall_coherence)
                coh_type = b.coherence_details.coherence_type
                coherence_types[coh_type] = coherence_types.get(coh_type, 0) + 1

        avg_coherence = np.mean(coherence_scores) if coherence_scores else 0.5

        # 背离统计
        divergence_count = sum(1 for b in sector_breadth_list
                             if b.price_breadth_divergence is not None)
        high_risk_divergences = [
            b.sector_name for b in sector_breadth_list
            if b.price_breadth_divergence and
            b.price_breadth_divergence['risk_level'] in ['high', 'extreme']
        ]

        # 市场结构诊断
        market_diagnosis = self._diagnose_market_structure(
            avg_internal_health, len(strong_sectors), len(weak_sectors),
            avg_coherence, divergence_count, len(sector_breadth_list)
        )

        return {
            'market_internal_health': round(avg_internal_health, 2),
            'health_distribution': {
                'strong_sectors': strong_sectors,
                'neutral_sectors': neutral_sectors,
                'weak_sectors': weak_sectors
            },
            'sector_counts': {
                'strong': len(strong_sectors),
                'neutral': len(neutral_sectors),
                'weak': len(weak_sectors),
                'total': len(sector_breadth_list)
            },
            'coherence_analysis': {
                'average_coherence': round(avg_coherence, 3),
                'coherence_distribution': coherence_types,
                'interpretation': self._interpret_market_coherence(avg_coherence, coherence_types)
            },
            'divergence_analysis': {
                'total_divergences': divergence_count,
                'high_risk_sectors': high_risk_divergences,
                'divergence_rate': round(divergence_count / len(sector_breadth_list), 2)
            },
            'market_diagnosis': market_diagnosis,
            'breadth_dispersion': round(np.std(health_scores), 2),
            'market_participation': round(
                np.mean([b.participation_rate for b in sector_breadth_list]), 3
            )
        }

    def _diagnose_market_structure(self, avg_health: float, strong_count: int,
                                 weak_count: int, avg_coherence: float,
                                 divergence_count: int, total_sectors: int) -> Dict:
        """诊断市场结构"""
        diagnosis = {
            'overall_condition': '',
            'key_observations': [],
            'risk_factors': [],
            'opportunities': [],
            'recommended_stance': ''
        }

        # 整体状况判断
        if avg_health > 70 and strong_count > total_sectors * 0.6:
            diagnosis['overall_condition'] = '市场结构健康，多数板块表现强势'
            diagnosis['recommended_stance'] = '积极参与，可适度提高仓位'
        elif avg_health < 40 or weak_count > total_sectors * 0.5:
            diagnosis['overall_condition'] = '市场结构疲弱，风险较高'
            diagnosis['recommended_stance'] = '谨慎防守，降低仓位'
        else:
            diagnosis['overall_condition'] = '市场结构分化，存在结构性机会'
            diagnosis['recommended_stance'] = '精选个股，控制总仓位'

        # 关键观察
        if avg_coherence > 0.7:
            diagnosis['key_observations'].append('板块内部一致性高，适合板块操作')
        elif avg_coherence < 0.4:
            diagnosis['key_observations'].append('板块内部分化严重，需要精选个股')

        if divergence_count > total_sectors * 0.3:
            diagnosis['risk_factors'].append('多个板块出现背离，需要警惕')

        if strong_count > 0 and weak_count > 0:
            diagnosis['opportunities'].append('市场分化提供轮动机会')

        return diagnosis

    def _interpret_market_coherence(self, avg_coherence: float,
                                  type_distribution: Dict) -> str:
        """解释市场整体一致性"""
        if avg_coherence > 0.7:
            return "市场整体一致性高，板块内部结构良好，适合指数和板块操作"
        elif avg_coherence > 0.5:
            high_dispersion = type_distribution.get('high_dispersion', 0)
            if high_dispersion > 2:
                return f"市场一致性适中，但有{high_dispersion}个板块严重分化，需要区别对待"
            return "市场一致性适中，部分板块存在分化，建议精选"
        else:
            return "市场一致性低，多数板块内部分化严重，不宜进行板块配置"
