#!/usr/bin/env python3
"""
小时数据板块轮动分析修正方案
解决52周新高新低和移动平均线指标为0的问题
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import logging

class HourlyRotationFix:
    """小时数据板块轮动分析修正器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_enhanced_sector_health_score(self, breadth_data, timeframe: str = '1h') -> float:
        """
        计算增强版板块健康度分数
        针对小时数据优化权重分配
        """
        try:
            total_stocks = breadth_data.total_stocks
            if total_stocks == 0:
                return 50.0
            
            # 基础指标计算
            ad_score = (breadth_data.advances / total_stocks) * 100
            
            # 成交量广度得分
            if breadth_data.total_volume and breadth_data.total_volume > 0:
                volume_score = (breadth_data.advancing_volume / breadth_data.total_volume) * 100
            else:
                volume_score = 50.0
            
            # RSI得分 (转换为0-100分制)
            rsi_score = breadth_data.avg_rsi if breadth_data.avg_rsi else 50.0
            
            # 根据时间框架调整计算方式
            if timeframe in ['5m', '15m', '1h']:
                # 短期时间框架：重新分配权重，去除不可用指标
                health_score = (
                    ad_score * 0.5 +        # 涨跌广度 (50%)
                    volume_score * 0.3 +    # 成交量广度 (30%)  
                    rsi_score * 0.2         # RSI (20%)
                )
                
                self.logger.debug(f"短期健康度计算: AD={ad_score:.1f}, Vol={volume_score:.1f}, RSI={rsi_score:.1f}")
                
            else:
                # 长期时间框架：使用完整指标
                # 新高新低得分
                if hasattr(breadth_data, 'new_highs_52w') and hasattr(breadth_data, 'new_lows_52w'):
                    nh_nl_score = ((breadth_data.new_highs_52w - breadth_data.new_lows_52w) / total_stocks + 1) * 50
                else:
                    nh_nl_score = 50.0
                
                # 均线广度得分
                if hasattr(breadth_data, 'above_ma50') and hasattr(breadth_data, 'above_ma200'):
                    ma50_breadth = breadth_data.above_ma50 / total_stocks
                    ma200_breadth = breadth_data.above_ma200 / total_stocks
                    ma_score = (ma50_breadth * 0.6 + ma200_breadth * 0.4) * 100
                else:
                    ma_score = 50.0
                
                # 原始权重分配
                health_score = (
                    ad_score * 0.3 +        # 涨跌广度 (30%)
                    volume_score * 0.2 +    # 成交量广度 (20%)
                    nh_nl_score * 0.2 +     # 新高新低 (20%)
                    ma_score * 0.3          # 均线广度 (30%)
                )
                
                self.logger.debug(f"长期健康度计算: AD={ad_score:.1f}, Vol={volume_score:.1f}, NH_NL={nh_nl_score:.1f}, MA={ma_score:.1f}")
            
            return round(health_score, 2)
            
        except Exception as e:
            self.logger.error(f"计算增强版健康度失败: {e}")
            return 50.0
    
    def get_mixed_breadth_data(self, hourly_breadth, sector: str):
        """
        获取混合广度数据：小时数据 + 最新日度数据补充
        """
        try:
            # 这里可以添加获取最新日度数据的逻辑
            # 暂时返回原始数据
            return hourly_breadth
            
        except Exception as e:
            self.logger.error(f"获取混合广度数据失败: {e}")
            return hourly_breadth
    
    def calculate_timeframe_specific_rii(self, 
                                       price_dispersion: float, 
                                       rank_velocity: float,
                                       volume_concentration: float, 
                                       breadth_metrics: Dict, 
                                       timeframe: str = '1h') -> float:
        """
        计算时间框架特定的轮动强度指数
        """
        try:
            # 价格维度RII（保持不变）
            price_rii = 0.5 * price_dispersion + 0.3 * rank_velocity + 0.2 * volume_concentration
            
            # 广度维度调整因子（使用增强版健康度）
            if breadth_metrics:
                # 使用增强版健康度计算
                health_scores = []
                for sector, breadth_data in breadth_metrics.items():
                    enhanced_health = self.calculate_enhanced_sector_health_score(breadth_data, timeframe)
                    health_scores.append(enhanced_health)
                
                if health_scores:
                    breadth_dispersion = np.std(health_scores) / 100
                    avg_health = np.mean(health_scores)
                    health_risk = (100 - avg_health) / 100
                    breadth_factor = 0.6 * breadth_dispersion + 0.4 * health_risk
                else:
                    breadth_factor = 0.5
            else:
                breadth_factor = 0.5
            
            # 统一RII
            unified_rii = 0.6 * price_rii + 0.4 * breadth_factor
            
            self.logger.debug(f"时间框架特定RII: price_rii={price_rii:.3f}, breadth_factor={breadth_factor:.3f}, unified_rii={unified_rii:.3f}")
            
            return unified_rii
            
        except Exception as e:
            self.logger.error(f"计算时间框架特定RII失败: {e}")
            return 0.5
    
    def identify_rotation_stage_enhanced(self, 
                                       unified_rii: float, 
                                       rank_velocity: float, 
                                       breadth_metrics: Dict,
                                       timeframe: str = '1h') -> tuple:
        """
        增强版轮动阶段识别
        """
        try:
            # 计算增强版平均健康度
            if breadth_metrics:
                health_scores = []
                for sector, breadth_data in breadth_metrics.items():
                    enhanced_health = self.calculate_enhanced_sector_health_score(breadth_data, timeframe)
                    health_scores.append(enhanced_health)
                avg_health = np.mean(health_scores) if health_scores else 50.0
            else:
                avg_health = 50.0
            
            # 阶段判定逻辑（针对时间框架调整阈值）
            if timeframe in ['5m', '15m', '1h']:
                # 短期时间框架：降低阈值
                if avg_health > 65 and unified_rii < 0.3:
                    stage = "expansion"
                    probs = {"expansion": 0.7, "rotation": 0.2, "contraction": 0.1}
                elif avg_health > 45 and 0.3 <= unified_rii <= 0.7:
                    stage = "rotation"  
                    probs = {"expansion": 0.2, "rotation": 0.6, "contraction": 0.2}
                else:
                    stage = "contraction"
                    probs = {"expansion": 0.1, "rotation": 0.2, "contraction": 0.7}
            else:
                # 长期时间框架：使用原始阈值
                if avg_health > 70 and unified_rii < 0.3:
                    stage = "expansion"
                    probs = {"expansion": 0.8, "rotation": 0.15, "contraction": 0.05}
                elif avg_health > 50 and 0.3 <= unified_rii <= 0.7:
                    stage = "rotation"
                    probs = {"expansion": 0.15, "rotation": 0.7, "contraction": 0.15}
                else:
                    stage = "contraction"
                    probs = {"expansion": 0.05, "rotation": 0.15, "contraction": 0.8}
            
            self.logger.info(f"轮动阶段识别: {stage} (健康度: {avg_health:.1f}, RII: {unified_rii:.3f})")
            
            return stage, probs
            
        except Exception as e:
            self.logger.error(f"轮动阶段识别失败: {e}")
            return "unknown", {"expansion": 0.33, "rotation": 0.33, "contraction": 0.34}
    
    def assess_risk_level_enhanced(self, 
                                 unified_rii: float, 
                                 stage: str, 
                                 breadth_metrics: Dict,
                                 timeframe: str = '1h') -> str:
        """
        增强版风险等级评估
        """
        try:
            # 计算增强版平均健康度
            if breadth_metrics:
                health_scores = []
                for sector, breadth_data in breadth_metrics.items():
                    enhanced_health = self.calculate_enhanced_sector_health_score(breadth_data, timeframe)
                    health_scores.append(enhanced_health)
                avg_health = np.mean(health_scores) if health_scores else 50.0
            else:
                avg_health = 50.0
            
            # 风险等级判定（针对时间框架调整）
            if timeframe in ['5m', '15m', '1h']:
                # 短期时间框架：调整阈值
                if avg_health < 25 or unified_rii > 0.8:
                    risk_level = "high"
                elif avg_health < 55 or unified_rii > 0.5:
                    risk_level = "medium"
                else:
                    risk_level = "low"
            else:
                # 长期时间框架：原始阈值
                if avg_health < 30 or unified_rii > 0.8:
                    risk_level = "high"
                elif avg_health < 60 or unified_rii > 0.5:
                    risk_level = "medium"
                else:
                    risk_level = "low"
            
            self.logger.info(f"风险等级评估: {risk_level} (健康度: {avg_health:.1f}, RII: {unified_rii:.3f})")
            
            return risk_level
            
        except Exception as e:
            self.logger.error(f"风险等级评估失败: {e}")
            return "medium"

# 使用示例
def apply_hourly_rotation_fix():
    """应用小时数据轮动分析修正"""
    fix = HourlyRotationFix()
    
    # 在现有的轮动分析代码中替换相关函数
    print("小时数据板块轮动分析修正器已初始化")
    print("建议在MTF分析和板块轮动调度器中使用增强版函数")
    
    return fix

if __name__ == "__main__":
    fix = apply_hourly_rotation_fix()
    print("修正器测试完成")
