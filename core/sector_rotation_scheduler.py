#!/usr/bin/env python3
"""
板块轮动计算调度器
复用现有的多进程计算框架和信号监听机制
实现板块轮动指标的实时计算和监控
"""

import os
import sys
import json
import redis
import time
import threading
import logging
import signal
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple
import multiprocessing

import numpy as np
import pandas as pd
import pymysql
import pytz
from dotenv import load_dotenv

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
config_dir = os.path.join(project_root, 'config')

# 确保配置目录在路径中
sys.path.insert(0, config_dir)
sys.path.append(current_dir)
sys.path.append(os.path.join(project_root, 'data_source_tool'))
sys.path.append(project_root)

# 直接从配置目录导入
from db_settings import get_default_db_config, get_breadth_redis_config
from sector_settings import (
    get_data_config, get_calculation_config, get_signal_config,
    get_logging_config, get_mtf_config, get_performance_config,
    get_run_mode_config
)
from sector_data_aggregator import SectorDataAggregator
from sector_rotation_functions import (
    calculate_sector_momentum,
    calculate_relative_strength,
    calculate_sector_dispersion,
    calculate_rotation_velocity,
    calculate_sector_purity,
    calculate_rotation_intensity_index,
    rank_sectors
)
# 🆕 MTF集成导入
from mtf_analyzer_service import MTFAnalyzerService

# 广度数据类
class BreadthMetrics:
    """板块广度指标数据类"""

    def __init__(self, sector: str, breadth_df: pd.DataFrame):
        self.sector = sector
        self.data = breadth_df

        # 如果数据为空，创建默认值
        if breadth_df.empty:
            self._create_default_metrics()
        else:
            self._extract_latest_metrics()

    def _create_default_metrics(self):
        """创建默认的广度指标（当数据为空时）"""
        self.total_stocks = 0
        self.advances = 0
        self.declines = 0
        self.unchanged = 0
        self.new_highs_52w = 0
        self.new_lows_52w = 0
        self.above_ma50 = 0
        self.above_ma200 = 0
        self.advancing_volume = 0.0
        self.declining_volume = 0.0
        self.total_volume = 0.0
        self.avg_rsi = 50.0
        self.market_cap_weighted_return = 0.0
        self.equal_weighted_return = 0.0
        self.purity = 0.5  # 默认中性
        self.internal_health = 50.0  # 默认中等健康度
        self.momentum_coherence = 0.5  # 默认中等一致性
        self.divergence_type = 'normal'
        self.divergence_severity = 0.0
        # 兼容性：保持原有的 companies_count 字段
        self.companies_count = 0

    def _extract_latest_metrics(self):
        """从数据中提取最新的广度指标"""
        latest = self.data.iloc[-1]  # 获取最新一行数据

        self.total_stocks = int(latest.get('total_stocks', 0))
        self.advances = int(latest.get('advances', 0))
        self.declines = int(latest.get('declines', 0))
        self.unchanged = int(latest.get('unchanged', 0))
        self.new_highs_52w = int(latest.get('new_highs_52w', 0))
        self.new_lows_52w = int(latest.get('new_lows_52w', 0))
        self.above_ma50 = int(latest.get('above_ma50', 0))
        self.above_ma200 = int(latest.get('above_ma200', 0))
        self.advancing_volume = float(latest.get('advancing_volume', 0))
        self.declining_volume = float(latest.get('declining_volume', 0))
        self.total_volume = float(latest.get('total_volume', 0))
        self.avg_rsi = float(latest.get('avg_rsi', 50.0))
        self.market_cap_weighted_return = float(latest.get('market_cap_weighted_return', 0.0))
        self.equal_weighted_return = float(latest.get('equal_weighted_return', 0.0))
        self.purity = float(latest.get('purity', 0.5))
        self.internal_health = float(latest.get('internal_health', 50.0))
        self.momentum_coherence = float(latest.get('momentum_coherence', 0.5))
        self.divergence_type = str(latest.get('divergence_type', 'normal'))
        self.divergence_severity = float(latest.get('divergence_severity', 0.0))

        # 一致性详细信息（按文档v4.1标准）
        self.coherence_details = {
            'overall_coherence': self.momentum_coherence,
            'coherence_type': 'estimated'  # 基于数据库数据的估算
        }

        # 兼容性：保持原有的 companies_count 字段
        self.companies_count = self.total_stocks

    def get_advance_decline_ratio(self) -> float:
        """获取涨跌比"""
        if self.declines == 0:
            return float('inf') if self.advances > 0 else 0.0
        return self.advances / self.declines

    def get_new_highs_lows_ratio(self) -> float:
        """获取新高新低比"""
        if self.new_lows_52w == 0:
            return float('inf') if self.new_highs_52w > 0 else 0.0
        return self.new_highs_52w / self.new_lows_52w

    def get_ma_breadth_ratio(self) -> float:
        """获取均线广度比（MA50上方/MA200上方）"""
        if self.above_ma200 == 0:
            return float('inf') if self.above_ma50 > 0 else 0.0
        return self.above_ma50 / self.above_ma200

    def get_volume_breadth_ratio(self) -> float:
        """获取成交量广度比"""
        if self.declining_volume == 0:
            return float('inf') if self.advancing_volume > 0 else 0.0
        return self.advancing_volume / self.declining_volume

    def __repr__(self):
        return f"BreadthMetrics(sector={self.sector}, health={self.internal_health:.1f}, purity={self.purity:.3f})"

# 加载环境变量
# 指向项目根目录下的.env文件
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '.env')
load_dotenv(env_path)

# 使用统一的数据库配置文件
# Redis配置（板块轮动专用，支持长时间监听）
def get_sector_redis_config():
    config = get_breadth_redis_config()
    config['socket_timeout'] = None  # 无限等待，避免监听信号时超时
    return config

# 数据库配置
def get_sector_db_config():
    return get_default_db_config()

class SectorRotationScheduler:
    """
    板块轮动计算调度器

    功能：
    1. 监听市场数据更新信号
    2. 触发板块数据聚合
    3. 计算板块轮动指标
    4. 保存轮动指标到数据库
    5. 发送轮动计算完成信号
    """

    def __init__(self, redis_config: Dict = None, db_config: Dict = None):
        """初始化板块轮动调度器"""
        # 加载配置
        self.data_config = get_data_config()
        self.calc_config = get_calculation_config()
        self.signal_config = get_signal_config()
        self.log_config = get_logging_config()
        self.mtf_config = get_mtf_config()
        self.perf_config = get_performance_config()
        self.run_config = get_run_mode_config()

        # 数据库和Redis配置
        self.redis_config = redis_config or get_sector_redis_config()
        self.db_config = db_config or get_sector_db_config()
        self.redis_client = None
        self.logger = self._setup_logger()
        self.sector_aggregator = SectorDataAggregator(self.redis_config, self.db_config)

        # 🆕 MTF分析器初始化
        if self.mtf_config.ENABLE_MTF_ANALYSIS:
            try:
                self.mtf_analyzer = MTFAnalyzerService()
                self.logger.info("[INIT] MTF分析器初始化成功")
            except Exception as e:
                self.logger.warning(f"[WARN] MTF分析器初始化失败: {e}")
                self.mtf_analyzer = None
        else:
            self.mtf_analyzer = None
            self.logger.info("[INIT] MTF分析器已禁用")

        # 信号队列配置（使用配置文件）
        self.signal_queue = self.signal_config.INPUT_SIGNAL_QUEUE
        self.rotation_signal_queue = self.signal_config.OUTPUT_SIGNAL_QUEUE

        # 停止标志
        self.stop_flag = threading.Event()

        # 连接Redis
        self._connect_redis()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.log_config.LOGGER_NAME)
        logger.setLevel(getattr(logging, self.log_config.LOG_LEVEL))

        # 避免重复添加handler
        if not logger.handlers:
            # 控制台handler
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(self.log_config.LOG_FORMAT)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

            # 文件handler（如果启用）
            if self.log_config.ENABLE_FILE_LOGGING:
                try:
                    os.makedirs(os.path.dirname(self.log_config.LOG_FILE_PATH), exist_ok=True)
                    file_handler = logging.FileHandler(self.log_config.LOG_FILE_PATH)
                    file_handler.setFormatter(formatter)
                    logger.addHandler(file_handler)
                except Exception as e:
                    print(f"警告：无法创建日志文件 {self.log_config.LOG_FILE_PATH}: {e}")

        return logger
    
    def _connect_redis(self) -> bool:
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(**self.redis_config)
            self.redis_client.ping()
            self.logger.info("[OK] Redis连接成功")
            return True
        except Exception as e:
            self.logger.error(f"[ERROR] Redis连接失败: {e}")
            return False
    
    def get_historical_sector_data(self, days: int = None) -> Dict[str, pd.DataFrame]:
        """获取历史板块数据（优先数据库，不足时从Redis实时计算）"""
        if days is None:
            days = self.data_config.HISTORICAL_DAYS

        connection = None
        try:
            connection = pymysql.connect(**self.db_config)

            # 计算查询时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 查询历史板块数据（使用配置的表名）
            sql = f"""
            SELECT recorded_at, sector, index_value, volume, daily_return, total_market_cap
            FROM {self.data_config.SECTOR_INDICES_TABLE}
            WHERE recorded_at >= %s AND recorded_at <= %s
            ORDER BY sector, recorded_at
            """

            df = pd.read_sql(sql, connection, params=[start_date, end_date])

            # 检查数据充足性（使用配置的阈值）
            min_expected_records = days * self.data_config.DATA_SUFFICIENCY_RATIO
            if df.empty or len(df) < min_expected_records:
                self.logger.warning(f"[WARN] 数据库中板块数据不足（{len(df)}条，期望>{min_expected_records:.0f}条），尝试从Redis实时计算")
                return self._calculate_realtime_sector_data(days)

            # 按板块分组
            sector_data = {}
            for sector in df['sector'].unique():
                sector_df = df[df['sector'] == sector].copy()
                sector_df = sector_df.sort_values('recorded_at')

                # 检查单个板块数据是否充足（使用配置的阈值）
                min_sector_records = days * self.data_config.SINGLE_SECTOR_DATA_RATIO
                if len(sector_df) < min_sector_records:
                    self.logger.warning(f"[WARN] 板块 {sector} 数据不足（{len(sector_df)}条，期望>{min_sector_records:.0f}条）")

                sector_data[sector] = sector_df

            return sector_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取历史板块数据失败: {e}")
            # 如果数据库查询失败，尝试从Redis实时计算
            self.logger.info(f"[FALLBACK] 尝试从Redis实时计算板块数据")
            return self._calculate_realtime_sector_data(days)
        finally:
            if connection:
                try:
                    connection.close()
                except:
                    pass

    def get_historical_breadth_data(self, days: int = None) -> Dict[str, pd.DataFrame]:
        """获取历史广度数据"""
        if days is None:
            days = self.data_config.HISTORICAL_DAYS

        connection = None
        try:
            connection = pymysql.connect(**self.db_config)

            # 计算查询时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 查询历史广度数据（使用实际的字段名）
            sql = """
            SELECT recorded_at, market,
                   total_stocks, advances, declines, unchanged,
                   new_highs_52w, new_lows_52w,
                   above_ma50, above_ma200,
                   advancing_volume, declining_volume, total_volume,
                   avg_rsi, market_cap_weighted_return, equal_weighted_return,
                   purity, internal_health, momentum_coherence,
                   divergence_type, divergence_severity
            FROM market_breadth_metrics_gics
            WHERE recorded_at >= %s AND recorded_at <= %s
            ORDER BY market, recorded_at
            """

            df = pd.read_sql(sql, connection, params=[start_date, end_date])

            if df.empty:
                self.logger.warning(f"[WARN] 数据库中广度数据为空")
                return {}

            # 按板块分组（使用 market 字段）
            breadth_data = {}
            for market in df['market'].unique():
                market_df = df[df['market'] == market].copy()
                market_df = market_df.sort_values('recorded_at')
                # 使用 market 作为 sector 的键
                breadth_data[market] = market_df

            self.logger.info(f"[INFO] 获取到 {len(breadth_data)} 个板块的广度数据，共 {len(df)} 条记录")
            return breadth_data

        except Exception as e:
            self.logger.error(f"[ERROR] 获取历史广度数据失败: {e}")
            return {}
        finally:
            if connection:
                try:
                    connection.close()
                except:
                    pass

    @staticmethod
    def calculate_rotation_metrics_worker_with_breadth(args: Tuple[str, pd.DataFrame, Dict[str, pd.DataFrame], 'BreadthMetrics']) -> Optional[Dict]:
        """计算单个板块轮动指标的工作函数（包含广度数据，用于多进程）"""
        sector, sector_df, all_sector_data, breadth_metrics = args

        try:
            # 使用配置的最小数据点要求
            min_data_points = 3  # 从配置中获取，但这里需要静态访问
            if len(sector_df) < min_data_points:
                return None

            # 准备数据 - 转换为DataFrame格式以符合文档标准
            sector_df_sorted = sector_df.sort_values('recorded_at')

            # 创建价格DataFrame（所有板块的价格数据）
            all_prices_data = {}
            all_volumes_data = {}

            # 获取所有板块的价格和成交量数据
            for s, s_df in all_sector_data.items():
                s_df_sorted = s_df.sort_values('recorded_at')
                # 使用时间戳作为索引，确保数据对齐
                all_prices_data[s] = s_df_sorted.set_index('recorded_at')['index_value']
                all_volumes_data[s] = s_df_sorted.set_index('recorded_at')['volume']

            # 创建对齐的DataFrame
            prices_df = pd.DataFrame(all_prices_data)
            volumes_df = pd.DataFrame(all_volumes_data)

            # 填充缺失值
            prices_df = prices_df.fillna(method='ffill').fillna(method='bfill')
            volumes_df = volumes_df.fillna(method='ffill').fillna(method='bfill')

            # 确保有足够的数据
            if len(prices_df) < min_data_points:
                return None
            
            # 按照文档标准计算轮动指标

            # 1. 计算动量（按文档标准：prices.pct_change(lookback_short) * 100）
            lookback_short = 5
            momentum_df = prices_df.pct_change(lookback_short) * 100

            # 2. 计算相对强度（按文档标准）
            market_momentum = momentum_df.mean(axis=1)
            relative_strength_df = momentum_df.div(market_momentum, axis=0)

            # 处理特殊值
            relative_strength_df = relative_strength_df.replace([np.inf, -np.inf], np.nan)
            mask = abs(market_momentum) < 0.01
            for col in relative_strength_df.columns:
                relative_strength_df.loc[mask, col] = 1.0
            relative_strength_df = relative_strength_df.fillna(1.0)

            # 3. 计算板块特定的价格离散度
            sector_price_dispersion = SectorRotationScheduler._calculate_sector_price_dispersion(
                sector, prices_df, relative_strength_df
            )

            # 4. 计算板块特定的排名变化速度
            sector_rank_velocity = SectorRotationScheduler._calculate_sector_rank_velocity(
                sector, momentum_df
            )

            # 5. 计算板块特定的成交量集中度
            sector_volume_concentration = SectorRotationScheduler._calculate_sector_volume_concentration(
                sector, volumes_df
            )

            # 6. 计算统一RII（使用板块特定的值）
            unified_rii = SectorRotationScheduler._calculate_unified_rii_standard(
                sector_price_dispersion, sector_rank_velocity, sector_volume_concentration, {sector: breadth_metrics}
            )

            # 获取当前板块的指标值
            current_momentum = momentum_df[sector].iloc[-1] if sector in momentum_df.columns else 0.0
            current_relative_strength = relative_strength_df[sector].iloc[-1] if sector in relative_strength_df.columns else 1.0

            # 基础指标
            metrics = {
                'sector': sector,
                'data_points': len(sector_df),
                'latest_price': float(prices_df[sector].iloc[-1]),
                'latest_volume': float(volumes_df[sector].iloc[-1]),
                'latest_return': float(sector_df_sorted['daily_return'].iloc[-1]),
                'latest_timestamp': sector_df_sorted['recorded_at'].iloc[-1],

                # 按文档标准计算的轮动指标
                'momentum_5d': float(current_momentum),
                'momentum_10d': float(prices_df[sector].pct_change(10).iloc[-1] * 100 if len(prices_df) > 10 and sector in prices_df.columns else 0.0),
                'momentum_20d': float(prices_df[sector].pct_change(20).iloc[-1] * 100 if len(prices_df) > 20 and sector in prices_df.columns else 0.0),
                'relative_strength': float(current_relative_strength),
                'rotation_velocity': float(sector_rank_velocity),  # 使用数据库字段名
                'rotation_intensity_index': float(unified_rii),
                # 文档标准的板块特定指标
                'price_dispersion': float(sector_price_dispersion),
                'rank_velocity': float(sector_rank_velocity),
                'volume_concentration': float(sector_volume_concentration),

                # 广度数据（保持原有格式）
                'breadth_total_stocks': breadth_metrics.total_stocks,
                'breadth_advances': breadth_metrics.advances,
                'breadth_declines': breadth_metrics.declines,
                'breadth_unchanged': breadth_metrics.unchanged,
                'breadth_new_highs_52w': breadth_metrics.new_highs_52w,
                'breadth_new_lows_52w': breadth_metrics.new_lows_52w,
                'breadth_above_ma50': breadth_metrics.above_ma50,
                'breadth_above_ma200': breadth_metrics.above_ma200,
                'breadth_advancing_volume': breadth_metrics.advancing_volume,
                'breadth_declining_volume': breadth_metrics.declining_volume,
                'breadth_total_volume': breadth_metrics.total_volume,
                'breadth_avg_rsi': breadth_metrics.avg_rsi,
                'breadth_market_cap_weighted_return': breadth_metrics.market_cap_weighted_return,
                'breadth_equal_weighted_return': breadth_metrics.equal_weighted_return,
                'breadth_purity': breadth_metrics.purity,
                'breadth_internal_health': breadth_metrics.internal_health,
                'breadth_momentum_coherence': breadth_metrics.momentum_coherence,
                'breadth_divergence_type': breadth_metrics.divergence_type,
                'breadth_divergence_severity': breadth_metrics.divergence_severity,
                'breadth_companies_count': breadth_metrics.companies_count,
                # 广度比率
                'breadth_ad_ratio': breadth_metrics.get_advance_decline_ratio(),
                'breadth_hl_ratio': breadth_metrics.get_new_highs_lows_ratio(),
                'breadth_ma_ratio': breadth_metrics.get_ma_breadth_ratio(),
                'breadth_volume_ratio': breadth_metrics.get_volume_breadth_ratio()
            }

            # 计算其他传统指标（保持兼容性）
            try:
                # 计算简单移动平均线
                sector_prices = prices_df[sector].values
                sector_returns = sector_df_sorted['daily_return'].values

                if len(sector_prices) >= 5:
                    sma_5 = np.mean(sector_prices[-5:])
                    metrics['sma_5'] = float(sma_5)
                    metrics['price_vs_sma5'] = float((sector_prices[-1] / sma_5 - 1) * 100)
                else:
                    metrics['sma_5'] = float(sector_prices[-1])
                    metrics['price_vs_sma5'] = 0.0

                if len(sector_prices) >= 10:
                    sma_10 = np.mean(sector_prices[-10:])
                    metrics['sma_10'] = float(sma_10)
                    metrics['price_vs_sma10'] = float((sector_prices[-1] / sma_10 - 1) * 100)
                else:
                    metrics['sma_10'] = float(sector_prices[-1])
                    metrics['price_vs_sma10'] = 0.0

                if len(sector_prices) >= 20:
                    sma_20 = np.mean(sector_prices[-20:])
                    metrics['sma_20'] = float(sma_20)
                    metrics['price_vs_sma20'] = float((sector_prices[-1] / sma_20 - 1) * 100)
                else:
                    metrics['sma_20'] = float(sector_prices[-1])
                    metrics['price_vs_sma20'] = 0.0

                # 计算板块纯度（数据库需要的字段）
                if len(sector_returns) > 0:
                    positive_returns = np.sum(sector_returns > 0)
                    negative_returns = np.sum(sector_returns < 0)
                    total_returns = len(sector_returns)
                    if total_returns > 0:
                        pos_ratio = positive_returns / total_returns
                        neg_ratio = negative_returns / total_returns
                        sector_purity = pos_ratio**2 + neg_ratio**2
                        metrics['sector_purity'] = float(max(0, min(1, sector_purity)))
                    else:
                        metrics['sector_purity'] = 0.5
                else:
                    metrics['sector_purity'] = 0.5

            except Exception as e:
                # 默认值
                sector_price = float(prices_df[sector].iloc[-1]) if sector in prices_df.columns else 0.0
                metrics.update({
                    'sma_5': sector_price,
                    'sma_10': sector_price,
                    'sma_20': sector_price,
                    'price_vs_sma5': 0.0,
                    'price_vs_sma10': 0.0,
                    'price_vs_sma20': 0.0,
                    'sector_purity': 0.5
                })

            return metrics
            
            # 3. 计算轮动速度
            try:
                # 修复：计算轮动速度，适配numpy数组
                def calculate_rotation_velocity_fixed(prices_array, volumes_array):
                    if len(prices_array) < 3:
                        return 0.0
                    # 计算价格变化率
                    price_changes = np.abs(np.diff(prices_array[-3:]))
                    # 计算成交量变化率
                    volume_changes = np.abs(np.diff(volumes_array[-3:]))
                    # 综合轮动速度
                    velocity = np.mean(price_changes) / np.mean(prices_array[-3:]) * 100
                    if len(volume_changes) > 0 and np.mean(volumes_array[-3:]) > 0:
                        volume_velocity = np.mean(volume_changes) / np.mean(volumes_array[-3:]) * 100
                        velocity = (velocity + volume_velocity) / 2
                    return velocity
                
                rotation_velocity = calculate_rotation_velocity_fixed(prices, volumes)
                metrics['rotation_velocity'] = float(rotation_velocity)
            except Exception:
                metrics['rotation_velocity'] = 0.0
            
            # 4. 计算板块纯度（基于收益率的一致性）
            try:
                # 修复：计算板块纯度，适配numpy数组
                def calculate_sector_purity_fixed(returns_array):
                    if len(returns_array) == 0:
                        return 0.5
                    # 统计正负收益
                    positive_returns = np.sum(returns_array > 0)
                    negative_returns = np.sum(returns_array < 0)
                    total_returns = len(returns_array)
                    if total_returns == 0:
                        return 0.5
                    # 计算纯度
                    pos_ratio = positive_returns / total_returns
                    neg_ratio = negative_returns / total_returns
                    purity = pos_ratio**2 + neg_ratio**2
                    return max(0, min(1, purity))
                
                sector_purity = calculate_sector_purity_fixed(returns)
                metrics['sector_purity'] = float(sector_purity)
            except Exception:
                metrics['sector_purity'] = 0.5
            
            # 5. 计算轮动强度指数（RII）
            try:
                # 修复：计算轮动强度指数，适配numpy数组
                def calculate_rotation_intensity_index_fixed(prices_array, volumes_array):
                    if len(prices_array) < 6 or len(volumes_array) < 6:
                        return 0.0
                    # 计算价格离散度（修复数组长度不匹配问题）
                    recent_prices = prices_array[-6:]
                    price_returns = np.diff(recent_prices) / recent_prices[:-1] * 100
                    price_dispersion = np.std(price_returns) if len(price_returns) > 0 else 0.0
                    # 计算轮动速度
                    velocity = calculate_rotation_velocity_fixed(prices_array, volumes_array)
                    # 计算成交量集中度
                    recent_volumes = volumes_array[-5:]
                    volume_concentration = np.std(recent_volumes) / np.mean(recent_volumes) if np.mean(recent_volumes) > 0 else 0.0
                    # 综合指数
                    rii = 0.4 * price_dispersion + 0.4 * velocity + 0.2 * volume_concentration
                    return rii
                
                rii = calculate_rotation_intensity_index_fixed(prices, volumes)
                metrics['rotation_intensity_index'] = float(rii)
            except Exception:
                metrics['rotation_intensity_index'] = 0.0
            
            # 6. 计算技术指标
            try:
                # 简单移动平均
                sma_5 = np.mean(prices[-5:]) if len(prices) >= 5 else prices[-1]
                sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else prices[-1]
                sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else prices[-1]
                
                # 价格相对于移动平均的位置
                price_vs_sma5 = (prices[-1] - sma_5) / sma_5 * 100
                price_vs_sma10 = (prices[-1] - sma_10) / sma_10 * 100
                price_vs_sma20 = (prices[-1] - sma_20) / sma_20 * 100
                
                metrics.update({
                    'sma_5': float(sma_5),
                    'sma_10': float(sma_10),
                    'sma_20': float(sma_20),
                    'price_vs_sma5': float(price_vs_sma5),
                    'price_vs_sma10': float(price_vs_sma10),
                    'price_vs_sma20': float(price_vs_sma20)
                })
            except Exception:
                metrics.update({
                    'sma_5': float(prices[-1]),
                    'sma_10': float(prices[-1]),
                    'sma_20': float(prices[-1]),
                    'price_vs_sma5': 0.0,
                    'price_vs_sma10': 0.0,
                    'price_vs_sma20': 0.0
                })
            
            return metrics
            
        except Exception as e:
            # 打印错误信息用于调试
            print(f"[DEBUG] 计算板块 {sector} 轮动指标失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回None表示计算失败
            return None
    
    def calculate_all_rotation_metrics(self, cpu_counts: int = None) -> Dict[str, Dict]:
        """计算所有板块的轮动指标（多进程）"""
        try:
            # 1. 获取历史板块数据（使用配置的天数）
            sector_data = self.get_historical_sector_data()

            if not sector_data:
                self.logger.error("[ERROR] 未获取到历史板块数据")
                return {}

            # 2. 获取历史广度数据
            breadth_data = self.get_historical_breadth_data()
            self.logger.info(f"[INFO] 获取到 {len(breadth_data)} 个板块的广度数据")

            # 3. 创建广度指标对象
            breadth_metrics = {}
            for sector in sector_data.keys():
                sector_breadth_df = breadth_data.get(sector, pd.DataFrame())
                breadth_metrics[sector] = BreadthMetrics(sector, sector_breadth_df)

            # 4. 准备多进程任务（包含广度数据）
            tasks = [(sector, sector_df, sector_data, breadth_metrics[sector])
                     for sector, sector_df in sector_data.items()]

            # 3. 多进程计算轮动指标（使用配置的CPU数量）
            if cpu_counts is None:
                cpu_counts = self.calc_config.DEFAULT_CPU_COUNTS

            max_cpu_by_config = int(multiprocessing.cpu_count() * self.calc_config.MAX_CPU_USAGE_RATIO)
            max_cpu = min(cpu_counts, max_cpu_by_config, len(tasks))

            # 根据配置决定是否使用多进程
            if self.perf_config.ENABLE_MULTIPROCESSING and len(tasks) > 1:
                try:
                    with multiprocessing.Pool(max_cpu) as pool:
                        results = pool.map(SectorRotationScheduler.calculate_rotation_metrics_worker_with_breadth, tasks)
                except Exception as mp_error:
                    if self.perf_config.FALLBACK_TO_SINGLE_PROCESS:
                        self.logger.warning(f"[WARN] 多进程计算失败，回退到单进程: {mp_error}")
                        results = [SectorRotationScheduler.calculate_rotation_metrics_worker_with_breadth(task) for task in tasks]
                    else:
                        raise mp_error
            else:
                # 单进程计算
                results = [SectorRotationScheduler.calculate_rotation_metrics_worker_with_breadth(task) for task in tasks]
            
            # 4. 过滤有效结果
            rotation_metrics = {}
            for result in results:
                if result is not None:
                    rotation_metrics[result['sector']] = result
            
            # 5. 获取MTF分析结果中的全市场指标
            mtf_result = None
            try:
                # 执行MTF分析获取全市场指标
                mtf_result = self.mtf_analyzer.run_mtf_analysis(days=self.data_config.HISTORICAL_DAYS, use_real_data=True)
                self.logger.info(f"[MTF] 获取到全市场轮动指标")
            except Exception as e:
                self.logger.warning(f"[WARN] 获取MTF分析结果失败: {e}")

            # 6. 计算板块排名并添加全市场指标
            if rotation_metrics:
                try:
                    # 计算综合得分并排名
                    sector_scores = {}
                    weights = self.calc_config.COMPOSITE_SCORE_WEIGHTS
                    for sector, metrics in rotation_metrics.items():
                        score = (
                            metrics.get('momentum_20d', 0) * weights['momentum_20d'] +
                            metrics.get('relative_strength', 0) * weights['relative_strength'] +
                            metrics.get('rotation_intensity_index', 0) * weights['rotation_intensity_index']
                        )
                        sector_scores[sector] = score

                    # 排名
                    ranked_sectors = rank_sectors(sector_scores)

                    # 从MTF结果获取全市场指标和避免板块列表
                    avoid_sectors = set()
                    if mtf_result and mtf_result.avoid_sectors:
                        avoid_sectors = {sector['sector'] for sector in mtf_result.avoid_sectors}

                    # 添加排名信息和全市场指标
                    for rank, (sector, score) in enumerate(ranked_sectors, 1):
                        if sector in rotation_metrics:
                            # 获取该板块的最优权重
                            optimal_weight = 0.0
                            if mtf_result and mtf_result.optimal_weights:
                                optimal_weight = mtf_result.optimal_weights.get(sector, 0.0)

                            rotation_metrics[sector].update({
                                'rank': rank,
                                'composite_score': float(score),
                                'breadth_factor': breadth_metrics[sector].internal_health / 100 if sector in breadth_metrics else 0.5,
                                'combined_score': rotation_metrics[sector].get('relative_strength', 1.0) * (breadth_metrics[sector].internal_health / 100 if sector in breadth_metrics else 0.5),

                                # 从MTF分析获取的全市场指标
                                'rotation_stage': mtf_result.rotation_stage if mtf_result else 'unknown',
                                'stage_probabilities': json.dumps(mtf_result.stage_probabilities, ensure_ascii=False) if mtf_result else '{}',
                                'risk_level': mtf_result.risk_level if mtf_result else 'medium',
                                'optimal_weights': json.dumps({sector: optimal_weight}, ensure_ascii=False),
                                'avoid_sector': sector in avoid_sectors,
                                'analysis_timestamp': datetime.now(tz=pytz.UTC)
                            })

                    self.logger.info(f"[SUCCESS] 成功计算 {len(rotation_metrics)} 个板块的完整轮动分析")
                    if mtf_result:
                        self.logger.info(f"[ANALYSIS] 轮动阶段: {mtf_result.rotation_stage}, 风险等级: {mtf_result.risk_level}")
                        self.logger.info(f"[ANALYSIS] 避免板块数量: {len(avoid_sectors)}")

                except Exception as e:
                    self.logger.warning(f"[WARN] 板块分析失败: {e}")
            
            return rotation_metrics
            
        except Exception as e:
            self.logger.error(f"[ERROR] 计算板块轮动指标失败: {e}")
            return {}
    
    def save_rotation_metrics_to_db(self, rotation_metrics: Dict[str, Dict], current_time: datetime) -> bool:
        """保存板块轮动指标到数据库"""
        try:
            if not rotation_metrics:
                return False
            
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 创建板块轮动指标表（如果不存在，使用配置的表名）
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.data_config.ROTATION_METRICS_TABLE} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    recorded_at DATETIME NOT NULL,
                    sector VARCHAR(100) NOT NULL,
                    data_points INT NOT NULL,
                    latest_price DECIMAL(15,6) NOT NULL,
                    latest_volume DECIMAL(20,2) NOT NULL,
                    latest_return DECIMAL(10,6) NOT NULL,
                    momentum_5d DECIMAL(10,6) NOT NULL,
                    momentum_10d DECIMAL(10,6) NOT NULL,
                    momentum_20d DECIMAL(10,6) NOT NULL,
                    relative_strength DECIMAL(10,6) NOT NULL,
                    rotation_velocity DECIMAL(10,6) NOT NULL,
                    sector_purity DECIMAL(10,6) NOT NULL,
                    rotation_intensity_index DECIMAL(10,6) NOT NULL,
                    sma_5 DECIMAL(15,6) NOT NULL,
                    sma_10 DECIMAL(15,6) NOT NULL,
                    sma_20 DECIMAL(15,6) NOT NULL,
                    price_vs_sma5 DECIMAL(10,6) NOT NULL,
                    price_vs_sma10 DECIMAL(10,6) NOT NULL,
                    price_vs_sma20 DECIMAL(10,6) NOT NULL,
                    sector_rank INT DEFAULT NULL,
                    composite_score DECIMAL(10,6) DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_recorded_sector (recorded_at, sector),
                    INDEX idx_sector (sector),
                    INDEX idx_recorded_at (recorded_at),
                    INDEX idx_rank (sector_rank)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                cursor.execute(create_table_sql)
                
                # 插入轮动指标数据（包含全市场指标的板块分配）
                insert_sql = f"""
                INSERT INTO {self.data_config.ROTATION_METRICS_TABLE}
                (recorded_at, sector, data_points, latest_price, latest_volume, latest_return,
                 momentum_5d, momentum_10d, momentum_20d, relative_strength, rotation_velocity,
                 sector_purity, rotation_intensity_index, sma_5, sma_10, sma_20,
                 price_vs_sma5, price_vs_sma10, price_vs_sma20, sector_rank, composite_score,
                 rotation_stage, stage_probabilities, risk_level, optimal_weights,
                 price_dispersion, rank_velocity, volume_concentration, avoid_sector,
                 analysis_timestamp, breadth_factor, combined_score)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                insert_data = []
                for metrics in rotation_metrics.values():
                    insert_data.append((
                        current_time,
                        metrics['sector'],
                        metrics['data_points'],
                        metrics['latest_price'],
                        metrics['latest_volume'],
                        metrics['latest_return'],
                        metrics['momentum_5d'],
                        metrics['momentum_10d'],
                        metrics['momentum_20d'],
                        metrics['relative_strength'],
                        metrics['rotation_velocity'],
                        metrics['sector_purity'],
                        metrics['rotation_intensity_index'],
                        metrics['sma_5'],
                        metrics['sma_10'],
                        metrics['sma_20'],
                        metrics['price_vs_sma5'],
                        metrics['price_vs_sma10'],
                        metrics['price_vs_sma20'],
                        metrics.get('rank'),
                        metrics.get('composite_score'),
                        # 全市场指标（从MTF分析获取）
                        metrics.get('rotation_stage'),
                        metrics.get('stage_probabilities'),
                        metrics.get('risk_level'),
                        metrics.get('optimal_weights'),
                        # 板块特定指标
                        metrics.get('price_dispersion'),
                        metrics.get('rank_velocity'),
                        metrics.get('volume_concentration'),
                        metrics.get('avoid_sector', False),
                        metrics.get('analysis_timestamp'),
                        metrics.get('breadth_factor'),
                        metrics.get('combined_score')
                    ))
                
                cursor.executemany(insert_sql, insert_data)
                connection.commit()
                
                return True
                
        except Exception as e:
            self.logger.error(f"[ERROR] 保存板块轮动指标到数据库失败: {e}")
            return False
        finally:
            if 'connection' in locals():
                connection.close()
    
    def send_rotation_signal(self, signal_type: str = "rotation_calculated") -> bool:
        """发送板块轮动计算完成信号"""
        try:
            if not self.redis_client:
                return False
            
            signal_data = {
                'type': signal_type,
                'timestamp': datetime.now(tz=pytz.UTC).isoformat(),
                'source': 'sector_rotation_scheduler'
            }
            
            self.redis_client.lpush(self.rotation_signal_queue, json.dumps(signal_data))
            self.logger.info(f"[SIGNAL] 发送轮动计算信号: {signal_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] 发送轮动信号失败: {e}")
            return False
    
    def calculate_rotation_metrics(self) -> bool:
        """计算板块轮动指标的主函数"""
        try:
            self.logger.info("[START] 开始计算板块轮动指标...")
            
            # 1. 先触发板块数据聚合
            self.logger.info("[STEP 1] 触发板块数据聚合...")
            aggregation_success = self.sector_aggregator.calculate_and_save_sector_data()
            
            if not aggregation_success:
                self.logger.warning("[WARN] 板块数据聚合失败，继续使用历史数据计算轮动指标")
            
            # 2. 计算轮动指标
            self.logger.info("[STEP 2] 计算板块轮动指标...")
            rotation_metrics = self.calculate_all_rotation_metrics()
            
            if not rotation_metrics:
                self.logger.error("[ERROR] 未能计算出任何板块轮动指标")
                return False
            
            # 3. 保存到数据库
            self.logger.info("[STEP 3] 保存轮动指标到数据库...")
            current_time = datetime.now(tz=pytz.UTC)
            save_success = self.save_rotation_metrics_to_db(rotation_metrics, current_time)
            
            if not save_success:
                self.logger.error("[ERROR] 轮动指标保存失败")
                return False
            
            # 4. 发送完成信号
            self.send_rotation_signal("rotation_calculated")
            
            self.logger.info("[OK] 板块轮动指标计算完成")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] 计算板块轮动指标失败: {e}")
            return False
    
    def _calculate_realtime_sector_data(self, days: int = 30) -> Dict[str, pd.DataFrame]:
        """从Redis实时计算板块数据"""
        try:
            pass  # 静默处理
            
            # 使用板块数据聚合器进行实时计算
            sector_indices = self.sector_aggregator.calculate_all_sector_indices()
            
            if not sector_indices:
                self.logger.error(f"[ERROR] 实时计算板块数据失败")
                return {}
            
            # 转换为DataFrame格式
            sector_data = {}
            current_time = datetime.now()
            
            for sector, data in sector_indices.items():
                # 创建单行DataFrame（当前时刻的数据）
                df_data = {
                    'recorded_at': [current_time],
                    'sector': [sector],
                    'index_value': [data.get('index_value', 0)],
                    'volume': [data.get('volume', 0)],
                    'daily_return': [0.0],  # 实时计算时无法获得日收益率
                    'total_market_cap': [data.get('total_market_cap', 0)]
                }
                
                sector_df = pd.DataFrame(df_data)
                sector_data[sector] = sector_df
            
            return sector_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 实时计算板块数据失败: {e}")
            return {}
    
    def listen_for_signals(self):
        """监听市场数据更新信号（增强版）"""
        self.logger.info("[LISTEN] 开始监听市场数据更新信号...")
        
        # 支持的信号类型（使用配置）
        signal_types = self.signal_config.SUPPORTED_SIGNAL_TYPES
        
        while not self.stop_flag.is_set():
            try:
                if not self.redis_client:
                    self.logger.error("[ERROR] Redis连接丢失，尝试重连...")
                    if not self._connect_redis():
                        if self.stop_flag.wait(self.signal_config.REDIS_RECONNECT_WAIT):
                            break
                        continue

                # 监听信号队列（使用配置的超时时间）
                signal_data = self.redis_client.brpop(self.signal_queue, timeout=self.signal_config.SIGNAL_LISTEN_TIMEOUT)
                
                if signal_data is None:
                    # 超时，继续循环检查停止标志
                    continue
                
                if signal_data:
                    try:
                        signal_info = json.loads(signal_data[1])
                        signal_type = signal_info.get('type', 'unknown')
                        signal_timestamp = datetime.fromisoformat(signal_info['timestamp'].replace('Z', '+00:00'))
                        
                        # 检查信号是否在有效期内（使用配置的有效期）
                        time_diff = datetime.now(tz=pytz.UTC) - signal_timestamp
                        if time_diff.total_seconds() <= self.signal_config.SIGNAL_VALIDITY_SECONDS:
                            signal_desc = signal_types.get(signal_type, f'未知信号({signal_type})')
                            self.logger.info(f"[SIGNAL] 收到信号: {signal_desc}，开始计算板块轮动指标")
                            
                            # 在新线程中执行计算，避免阻塞信号监听
                            calculation_thread = threading.Thread(
                                target=self._handle_signal_calculation,
                                args=(signal_info,),
                                daemon=True
                            )
                            calculation_thread.start()
                        else:
                            pass  # 静默忽略过期信号
                            
                    except Exception as parse_error:
                        self.logger.error(f"[ERROR] 解析信号数据失败: {parse_error}")
                
            except Exception as e:
                self.logger.error(f"[ERROR] 监听信号失败: {e}")
                if self.stop_flag.wait(5):  # 等待5秒或直到停止标志被设置
                    break
        
        self.logger.info("[STOP] 信号监听已停止")
    
    def _handle_signal_calculation(self, signal_info: Dict):
        """处理信号触发的计算"""
        try:
            signal_type = signal_info.get('type', 'unknown')
            
            # 根据信号类型决定计算策略
            if signal_type == 'market_breadth_calculated':
                # 市场广度计算完成，执行完整的板块轮动计算
                self.logger.info("[CALC] 执行完整板块轮动计算")
                success = self.calculate_rotation_metrics()
                
            elif signal_type == 'sector_data_updated':
                # 板块数据更新，可以执行快速计算
                self.logger.info("[CALC] 执行快速板块轮动计算")
                success = self.calculate_rotation_metrics()
                
            elif signal_type == 'manual_trigger':
                # 手动触发，执行完整计算
                self.logger.info("[CALC] 手动触发板块轮动计算")
                success = self.calculate_rotation_metrics()
                
            else:
                self.logger.warning(f"[WARN] 未知信号类型: {signal_type}")
                success = False
            
            if success:
                self.logger.info(f"[SUCCESS] 板块轮动计算完成")
                
                # 🆕 在板块轮动计算成功后执行MTF分析
                self._run_mtf_analysis_if_available()
            else:
                self.logger.error(f"[ERROR] 板块轮动计算失败")
                
        except Exception as e:
            self.logger.error(f"[ERROR] 处理信号计算失败: {e}")
    
    def _run_mtf_analysis_if_available(self):
        """如果MTF分析器可用则执行MTF分析"""
        try:
            if self.mtf_analyzer is None:
                return
            
            self.logger.info("[MTF] 开始执行MTF分析...")
            
            # 执行MTF分析（使用配置的参数）
            mtf_days = self.mtf_config.MTF_ANALYSIS_DAYS
            use_real_data_first = self.mtf_config.MTF_USE_REAL_DATA_FIRST

            try:
                mtf_result = self.mtf_analyzer.run_mtf_analysis(days=mtf_days, use_real_data=use_real_data_first)
                data_type = "真实数据" if use_real_data_first else "模拟数据"
                self.logger.info(f"[MTF] 使用{data_type}执行MTF分析")
            except Exception as real_data_error:
                if use_real_data_first:
                    self.logger.warning(f"[MTF] 真实数据分析失败，回退到模拟数据: {real_data_error}")
                    mtf_result = self.mtf_analyzer.run_mtf_analysis(days=mtf_days, use_real_data=False)
                else:
                    raise real_data_error
            
            if mtf_result and mtf_result.consensus_score > 0:
                self.logger.info("[MTF] MTF分析完成")
                self._log_mtf_summary(mtf_result)
                
                # 保存MTF结果到数据库（根据配置决定是否保存）
                if self.mtf_config.MTF_SAVE_TO_DATABASE:
                    try:
                        if self.mtf_analyzer.save_mtf_results_to_db(mtf_result):
                            self.logger.info("[MTF] MTF结果已保存到数据库")
                        else:
                            self.logger.warning("[MTF] MTF结果保存失败")
                    except Exception as save_error:
                        self.logger.warning(f"[MTF] 数据库保存异常: {save_error}")
                else:
                    self.logger.info("[MTF] MTF结果保存已禁用")
                
            else:
                self.logger.warning("[MTF] MTF分析返回空结果")
                
        except Exception as e:
            self.logger.error(f"[ERROR] MTF分析执行失败: {e}")
    
    def _log_mtf_summary(self, mtf_result):
        """记录MTF分析摘要"""
        try:
            self.logger.info("=" * 50)
            self.logger.info("🔍 MTF分析摘要")
            self.logger.info("=" * 50)
            self.logger.info(f"📊 市场状态: {mtf_result.market_regime}")
            self.logger.info(f"📊 建议仓位: {mtf_result.suggested_position}%")
            self.logger.info(f"📊 信号可靠性: {mtf_result.signal_reliability:.1%}")
            self.logger.info(f"📊 一致性得分: {mtf_result.consensus_score:.3f}")
            self.logger.info(f"🎯 操作策略: {mtf_result.operation_strategy}")
            
            if mtf_result.top_sectors:
                self.logger.info(f"🔥 推荐板块:")
                for sector in mtf_result.top_sectors:
                    self.logger.info(f"   • {sector['sector']}: {sector['weight']} - {sector['reason']}")
            
            if mtf_result.warnings:
                for warning in mtf_result.warnings:
                    self.logger.warning(f"⚠️  {warning}")
            
            self.logger.info("=" * 50)
            
        except Exception as e:
            self.logger.error(f"[ERROR] 记录MTF摘要失败: {e}")

    @staticmethod
    def _calculate_rank_velocity_standard(momentum_df: pd.DataFrame) -> float:
        """按文档标准计算排名变化速度"""
        if len(momentum_df) < 2:
            return 0.0

        # 最近N天的排名
        lookback = min(5, len(momentum_df))
        recent_ranks = pd.DataFrame(index=momentum_df.index[-lookback:],
                                  columns=momentum_df.columns)

        for idx in recent_ranks.index:
            recent_ranks.loc[idx] = momentum_df.loc[idx].rank(ascending=False)

        # 计算排名变化
        rank_changes = recent_ranks.diff().abs().mean(axis=1)
        avg_velocity = rank_changes.mean() / len(momentum_df.columns)

        return avg_velocity

    @staticmethod
    def _calculate_volume_concentration_hhi(volumes_df: pd.DataFrame) -> float:
        """按文档标准计算成交量集中度（HHI指数）"""
        latest_volumes = volumes_df.iloc[-1]
        total_volume = latest_volumes.sum()

        if total_volume == 0:
            return 0.1

        market_shares = latest_volumes / total_volume
        hhi = (market_shares ** 2).sum()

        return hhi

    @staticmethod
    def _calculate_unified_rii_standard(price_dispersion: float, rank_velocity: float,
                                      volume_concentration: float,
                                      breadth_metrics: Dict[str, 'BreadthMetrics']) -> float:
        """按文档标准计算统一的轮动强度指数"""
        # 价格维度RII（60%权重）
        price_rii = 0.5 * price_dispersion + 0.3 * rank_velocity + 0.2 * volume_concentration

        # 广度维度调整因子（40%权重）
        if breadth_metrics:
            # 计算广度健康度的标准差
            health_scores = [b.internal_health for b in breadth_metrics.values()]
            breadth_dispersion = np.std(health_scores) / 100

            # 计算平均内部健康度的反向指标
            avg_health = np.mean(health_scores)
            health_risk = (100 - avg_health) / 100

            breadth_factor = 0.6 * breadth_dispersion + 0.4 * health_risk
        else:
            breadth_factor = 0.5

        # 统一RII
        unified_rii = 0.6 * price_rii + 0.4 * breadth_factor

        return unified_rii

    @staticmethod
    def _calculate_sector_price_dispersion(sector: str, prices_df: pd.DataFrame,
                                         relative_strength_df: pd.DataFrame) -> float:
        """计算单个板块的价格离散度"""
        try:
            if sector not in prices_df.columns or len(prices_df) < 10:
                return 0.0

            # 计算该板块最近10天的价格变化率
            sector_prices = prices_df[sector].dropna()
            if len(sector_prices) < 10:
                return 0.0

            # 计算价格变化率的标准差作为离散度
            price_returns = sector_prices.pct_change().dropna()
            if len(price_returns) < 5:
                return 0.0

            dispersion = price_returns.std() * 100  # 转换为百分比
            return float(dispersion)

        except Exception:
            return 0.0

    @staticmethod
    def _calculate_sector_rank_velocity(sector: str, momentum_df: pd.DataFrame) -> float:
        """计算单个板块的排名变化速度"""
        try:
            if sector not in momentum_df.columns or len(momentum_df) < 5:
                return 0.0

            # 计算最近5天该板块的排名变化
            lookback = min(5, len(momentum_df))
            recent_data = momentum_df.tail(lookback)

            sector_ranks = []
            for idx in recent_data.index:
                daily_momentum = recent_data.loc[idx]
                sector_rank = daily_momentum.rank(ascending=False)[sector]
                sector_ranks.append(sector_rank)

            if len(sector_ranks) < 2:
                return 0.0

            # 计算排名变化的平均绝对值
            rank_changes = [abs(sector_ranks[i] - sector_ranks[i-1])
                          for i in range(1, len(sector_ranks))]

            avg_velocity = np.mean(rank_changes) / len(momentum_df.columns)
            return float(avg_velocity)

        except Exception:
            return 0.0

    @staticmethod
    def _calculate_sector_volume_concentration(sector: str, volumes_df: pd.DataFrame) -> float:
        """计算单个板块的成交量集中度"""
        try:
            if sector not in volumes_df.columns or len(volumes_df) < 5:
                return 0.0

            # 计算该板块最近5天的成交量变异系数
            sector_volumes = volumes_df[sector].dropna().tail(5)
            if len(sector_volumes) < 3:
                return 0.0

            mean_volume = sector_volumes.mean()
            if mean_volume == 0:
                return 0.0

            # 变异系数作为集中度指标
            cv = sector_volumes.std() / mean_volume
            return float(cv)

        except Exception:
            return 0.0

    @staticmethod
    def _identify_rotation_stage(unified_rii: float, rank_velocity: float,
                               breadth_metrics: Dict[str, 'BreadthMetrics']) -> tuple:
        """按文档标准识别轮动阶段"""
        # 获取平均内部健康度
        if breadth_metrics:
            avg_health = np.mean([b.internal_health for b in breadth_metrics.values()])
        else:
            avg_health = 70

        # 阶段概率计算
        probs = {
            '稳定期': 0,
            '启动期': 0,
            '加速期': 0,
            '混乱期': 0,
            '收敛期': 0
        }

        # 基于规则的概率分配（按文档标准）
        if unified_rii < 0.3 and avg_health > 70:
            probs['稳定期'] = 0.8
            probs['收敛期'] = 0.2
        elif 0.3 <= unified_rii < 0.6 and rank_velocity > 0.1:
            probs['启动期'] = 0.6
            probs['稳定期'] = 0.2
            probs['加速期'] = 0.2
        elif 0.6 <= unified_rii < 0.8 and avg_health > 50:
            probs['加速期'] = 0.7
            probs['启动期'] = 0.2
            probs['混乱期'] = 0.1
        elif unified_rii >= 0.8 or (unified_rii > 0.6 and avg_health < 40):
            probs['混乱期'] = 0.8
            probs['加速期'] = 0.2
        else:
            probs['收敛期'] = 0.5
            probs['稳定期'] = 0.3
            probs['启动期'] = 0.2

        # 选择最高概率的阶段
        stage = max(probs.keys(), key=lambda k: probs[k])

        return stage, probs

    @staticmethod
    def _assess_risk_level(unified_rii: float, stage: str,
                         breadth_metrics: Dict[str, 'BreadthMetrics']) -> str:
        """按文档标准评估风险等级"""
        # 基础风险评估
        if unified_rii < 0.4:
            base_risk = 'low'
        elif unified_rii < 0.7:
            base_risk = 'medium'
        else:
            base_risk = 'high'

        # 阶段调整
        if stage == '混乱期':
            base_risk = 'high'
        elif stage == '稳定期' and base_risk == 'high':
            base_risk = 'medium'

        # 广度调整
        if breadth_metrics:
            weak_sectors = sum(1 for b in breadth_metrics.values() if b.internal_health < 40)
            if weak_sectors > len(breadth_metrics) * 0.5:
                if base_risk == 'low':
                    base_risk = 'medium'
                elif base_risk == 'medium':
                    base_risk = 'high'

        return base_risk

    @staticmethod
    def _calculate_optimal_weights(all_relative_strength: Dict[str, float], stage: str,
                                 breadth_metrics: Dict[str, 'BreadthMetrics'],
                                 risk_level: str) -> Dict[str, float]:
        """按文档标准计算最优权重"""
        sectors = list(all_relative_strength.keys())
        weights = {sector: 0.0 for sector in sectors}

        # 获取内部健康度
        health_scores = {}
        for sector in sectors:
            if sector in breadth_metrics:
                health_scores[sector] = breadth_metrics[sector].internal_health
            else:
                health_scores[sector] = 50

        # 综合得分 = 相对强度 × 内部健康度
        combined_scores = {}
        for sector in sectors:
            rs_score = all_relative_strength[sector]
            health_score = health_scores[sector] / 100
            combined_scores[sector] = rs_score * (0.5 + 0.5 * health_score)

        # 只给正分且足够高的板块分配权重
        positive_sectors = {k: v for k, v in combined_scores.items() if v > 0.05}

        if len(positive_sectors) == 0:
            # 如果没有合格板块，全部现金
            return weights

        # 按得分排序
        sorted_sectors = sorted(positive_sectors.items(), key=lambda x: x[1], reverse=True)

        if stage == '稳定期' and risk_level != 'high':
            # 可以集中配置
            if len(sorted_sectors) >= 3:
                weights[sorted_sectors[0][0]] = 0.4
                weights[sorted_sectors[1][0]] = 0.3
                weights[sorted_sectors[2][0]] = 0.2
                remaining = 0.1
                other_sectors = sorted_sectors[3:]
                if other_sectors:
                    weight_per_other = remaining / len(other_sectors)
                    for sector, _ in other_sectors:
                        weights[sector] = weight_per_other
            else:
                # 按比例分配
                total_score = sum(positive_sectors.values())
                for sector, score in positive_sectors.items():
                    weights[sector] = score / total_score

        elif stage in ['混乱期', '加速期'] or risk_level == 'high':
            # 必须分散配置
            max_weight = 0.15 if stage == '混乱期' else 0.2

            # 按得分比例分配
            total_score = sum(positive_sectors.values())
            for sector, score in positive_sectors.items():
                raw_weight = score / total_score
                weights[sector] = min(raw_weight, max_weight)

            # 重新归一化
            total_weight = sum(weights.values())
            if total_weight > 0:
                for sector in weights:
                    weights[sector] = weights[sector] / total_weight

        else:
            # 温和配置
            total_score = sum(positive_sectors.values())
            for sector, score in positive_sectors.items():
                weights[sector] = score / total_score

        # 确保权重和为1或0
        total_weight = sum(weights.values())
        if total_weight > 0:
            for sector in weights:
                weights[sector] = weights[sector] / total_weight

        return weights

    @staticmethod
    def _identify_avoid_sectors(breadth_metrics: Dict[str, 'BreadthMetrics'],
                              all_relative_strength: Dict[str, float]) -> set:
        """识别应该避免的板块"""
        avoid_sectors = set()

        for sector, breadth in breadth_metrics.items():
            # 内部健康度过低
            if breadth.internal_health < 30:
                avoid_sectors.add(sector)

            # 相对强度持续为负且健康度不佳
            if (sector in all_relative_strength and
                all_relative_strength[sector] < -0.1 and
                breadth.internal_health < 50):
                avoid_sectors.add(sector)

            # 广度指标极度恶化
            if (breadth.get_advance_decline_ratio() < 0.3 and
                breadth.internal_health < 40):
                avoid_sectors.add(sector)

        return avoid_sectors


def signal_handler(signum, frame, scheduler):
    """信号处理函数"""
    print("\n接收到中断信号，正在停止程序...")
    scheduler.stop_flag.set()

def main():
    """主函数 - 启动板块轮动调度器"""
    scheduler = SectorRotationScheduler()

    # 设置信号处理
    signal.signal(signal.SIGINT, lambda signum, frame: signal_handler(signum, frame, scheduler))
    signal.signal(signal.SIGTERM, lambda signum, frame: signal_handler(signum, frame, scheduler))

    # 可以选择直接计算一次或者启动监听模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        # 单次计算模式
        days = scheduler.data_config.HISTORICAL_DAYS
        print(f"开始计算最近{days}天的板块轮动指标...")
        success = scheduler.calculate_rotation_metrics()
        if success:
            print("板块轮动指标计算完成")
        else:
            print("板块轮动指标计算失败")
    elif len(sys.argv) > 1 and sys.argv[1] == "--standalone":
        # 独立运行模式 - 不依赖Redis信号
        days = scheduler.data_config.HISTORICAL_DAYS
        print(f"独立模式：开始计算最近{days}天的板块轮动指标...")
        try:
            # 直接计算轮动指标，不等待信号
            rotation_metrics = scheduler.calculate_all_rotation_metrics()
            if rotation_metrics:
                current_time = datetime.now(tz=pytz.UTC)
                save_success = scheduler.save_rotation_metrics_to_db(rotation_metrics, current_time)
                if save_success:
                    print(f"成功计算并保存了 {len(rotation_metrics)} 个板块的轮动指标")
                    # 打印前N个板块的排名（使用配置的数量）
                    top_count = scheduler.run_config.STANDALONE_TOP_SECTORS_COUNT
                    sorted_sectors = sorted(rotation_metrics.items(),
                                           key=lambda x: x[1].get('rank', 999))
                    print(f"\n板块排名前{top_count}：")
                    for i, (sector, metrics) in enumerate(sorted_sectors[:top_count], 1):
                        score = metrics.get('composite_score', 0)
                        print(f"{i}. {sector}: {score:.4f}")

                    # 根据配置决定是否执行MTF分析
                    if scheduler.run_config.STANDALONE_ENABLE_MTF and scheduler.mtf_analyzer:
                        print("\n执行MTF分析...")
                        scheduler._run_mtf_analysis_if_available()
                else:
                    print("计算成功但保存失败")
            else:
                print("未能计算出板块轮动指标")
        except Exception as e:
            print(f"独立计算失败: {e}")
    elif len(sys.argv) > 1 and sys.argv[1] == "--config":
        # 显示配置信息
        from sector_settings import print_current_config
        print_current_config()
    else:
        # 监听模式
        print("监听模式：等待Redis信号触发计算...")
        print("提示：如果想要独立运行，请使用 --standalone 参数")
        print("提示：如果想要查看配置，请使用 --config 参数")
        try:
            scheduler.listen_for_signals()
        except KeyboardInterrupt:
            print("\n板块轮动调度器已停止")
        except Exception as e:
            print(f"程序运行出错: {e}")
        finally:
            scheduler.stop_flag.set()
            print("程序结束")


if __name__ == "__main__":
    main()