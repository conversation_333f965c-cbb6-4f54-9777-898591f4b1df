"""
改进的动量一致性计算模块
基于优化文档要求，修复均值接近0时的计算bug，提供7种细分的一致性类型
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class CoherenceDetails:
    """动量一致性详细信息"""
    overall_coherence: float
    direction_coherence: float
    magnitude_coherence: float
    coherence_type: str
    dominant_direction: str
    interpretation: str
    action_suggestion: str
    statistics: Dict
    calculation_method: Dict


class ImprovedMomentumCoherence:
    """改进版动量一致性计算器"""
    
    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化动量一致性计算器
        
        Args:
            decision_tracker: 可选的决策追踪器，用于记录计算过程
        """
        self.direction_threshold = 0.001  # 方向判定阈值
        self.cv_scale = 1.0  # 变异系数缩放因子
        self.tracker = decision_tracker
        
    def calculate_coherence(self, individual_returns: List[float]) -> <PERSON><PERSON>[float, CoherenceDetails]:
        """
        计算板块动量一致性（改进版）

        Args:
            individual_returns: 个股收益率列表

        Returns:
            Tuple[float, CoherenceDetails]: (overall_coherence, details)
        """
        if self.tracker:
            self.tracker.start_decision('momentum_coherence_analysis', {
                'return_count': len(individual_returns) if individual_returns else 0,
                'sample_returns': individual_returns[:5] if individual_returns else []
            })

            self.tracker.add_step(
                'coherence_calculation_start',
                {'return_count': len(individual_returns) if individual_returns else 0},
                '开始计算板块内部动量一致性'
            )
        
        # 数据验证
        if not individual_returns or len(individual_returns) < 2:
            return self._create_insufficient_data_result(individual_returns)
        
        returns_array = np.array(individual_returns)
        
        # 1. 计算方向一致性
        direction_coherence, dominant_direction, direction_stats = self._calculate_direction_coherence(returns_array)
        
        # 2. 计算幅度一致性
        magnitude_coherence, magnitude_method = self._calculate_magnitude_coherence(returns_array)
        
        # 3. 计算综合一致性（动态权重）
        overall_coherence, weight_reason = self._calculate_overall_coherence(
            direction_coherence, magnitude_coherence
        )
        
        # 4. 判断一致性类型和生成解释
        coherence_type, interpretation, action_suggestion = self._classify_coherence(
            direction_coherence, magnitude_coherence, np.mean(returns_array),
            direction_stats['positive_count'], direction_stats['negative_count'], 
            len(returns_array)
        )
        
        # 5. 记录决策过程
        if self.tracker:
            self.tracker.add_step(
                'coherence_analysis',
                {
                    'direction': direction_coherence,
                    'magnitude': magnitude_coherence,
                    'overall': overall_coherence
                },
                f'{weight_reason}，使用{magnitude_method}方法计算幅度一致性',
                confidence=0.9 if len(returns_array) > 20 else 0.7
            )
        
        # 6. 构建详细结果
        details = CoherenceDetails(
            overall_coherence=round(overall_coherence, 3),
            direction_coherence=round(direction_coherence, 3),
            magnitude_coherence=round(magnitude_coherence, 3),
            coherence_type=coherence_type,
            dominant_direction=dominant_direction,
            interpretation=interpretation,
            action_suggestion=action_suggestion,
            statistics={
                'positive_ratio': round(direction_stats['positive_count'] / len(returns_array), 3),
                'negative_ratio': round(direction_stats['negative_count'] / len(returns_array), 3),
                'neutral_ratio': round(direction_stats['neutral_count'] / len(returns_array), 3),
                'mean_return': round(np.mean(returns_array) * 100, 2),  # 转换为百分比
                'std_return': round(np.std(returns_array) * 100, 2),
                'sample_size': len(returns_array)
            },
            calculation_method={
                'magnitude': magnitude_method,
                'weights': self._get_dynamic_weights(direction_coherence),
                'weight_reason': weight_reason
            }
        )

        # 7. 完成决策记录
        if self.tracker:
            self.tracker.complete_decision({
                'overall_coherence': overall_coherence,
                'coherence_type': coherence_type,
                'interpretation': interpretation,
                'action_suggestion': action_suggestion
            }, overall_coherence)

        return overall_coherence, details
    
    def _calculate_direction_coherence(self, returns_array: np.ndarray) -> Tuple[float, str, Dict]:
        """计算方向一致性"""
        positive_count = np.sum(returns_array > self.direction_threshold)
        negative_count = np.sum(returns_array < -self.direction_threshold)
        neutral_count = len(returns_array) - positive_count - negative_count
        total_count = len(returns_array)
        
        direction_coherence = max(positive_count, negative_count) / total_count
        dominant_direction = 'bullish' if positive_count > negative_count else 'bearish'
        
        direction_stats = {
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count,
            'total_count': total_count
        }
        
        return direction_coherence, dominant_direction, direction_stats
    
    def _calculate_magnitude_coherence(self, returns_array: np.ndarray) -> Tuple[float, str]:
        """计算幅度一致性（修复均值接近0的bug）"""
        mean_return = np.mean(returns_array)
        std_return = np.std(returns_array)
        
        if abs(mean_return) < 0.0001:
            # 使用四分位距方法处理小均值情况
            q75, q25 = np.percentile(returns_array, [75, 25])
            iqr = q75 - q25
            median_abs = np.median(np.abs(returns_array))
            
            if median_abs > 0:
                magnitude_coherence = 1 / (1 + iqr / median_abs)
            else:
                magnitude_coherence = 0.3  # 默认低一致性
                
            magnitude_method = 'iqr_based'
        else:
            # 使用改进的变异系数法
            cv = std_return / abs(mean_return)
            # 使用tanh函数获得更平滑的映射
            magnitude_coherence = 0.5 * (1 + np.tanh(self.cv_scale * (1 - cv)))
            magnitude_coherence = np.clip(magnitude_coherence, 0, 1)
            magnitude_method = 'cv_based'
        
        return magnitude_coherence, magnitude_method
    
    def _calculate_overall_coherence(self, direction_coherence: float, 
                                   magnitude_coherence: float) -> Tuple[float, str]:
        """计算综合一致性（动态权重）"""
        weights = self._get_dynamic_weights(direction_coherence)
        
        overall_coherence = (weights['direction'] * direction_coherence + 
                           weights['magnitude'] * magnitude_coherence)
        
        return overall_coherence, weights['reason']
    
    def _get_dynamic_weights(self, direction_coherence: float) -> Dict:
        """获取动态权重"""
        if direction_coherence > 0.8:
            return {
                'direction': 0.4,
                'magnitude': 0.6,
                'reason': '方向高度一致，重视幅度'
            }
        elif direction_coherence < 0.6:
            return {
                'direction': 0.7,
                'magnitude': 0.3,
                'reason': '方向分歧，优先考虑方向'
            }
        else:
            return {
                'direction': 0.5,
                'magnitude': 0.5,
                'reason': '均衡权重'
            }
    
    def _classify_coherence(self, dir_coh: float, mag_coh: float, 
                          mean_return: float, pos_count: int, 
                          neg_count: int, total_count: int) -> Tuple[str, str, str]:
        """分类一致性类型并生成解释（7种类型）"""
        
        # 判断类型
        if dir_coh > 0.8 and mag_coh > 0.7:
            coh_type = "strong_consensus"
            interpretation = f"板块内{dir_coh*100:.0f}%个股同向，且涨跌幅度相近(一致性{mag_coh:.2f})"
            action = "适合板块整体配置，可以通过ETF或龙头股参与"
            
        elif dir_coh > 0.8 and mag_coh < 0.4:
            coh_type = "directional_divergence"
            interpretation = f"方向一致但强弱分化严重，{pos_count}只上涨但涨幅差异大"
            action = "不宜板块配置，需精选强势个股"
            
        elif dir_coh < 0.4:
            coh_type = "high_dispersion"
            interpretation = f"板块严重分化，仅{max(pos_count, neg_count)/total_count*100:.0f}%个股同向"
            action = "避免板块操作，个股机会需要深度研究"
            
        elif dir_coh < 0.6 and mag_coh > 0.6:
            coh_type = "balanced_market"
            interpretation = f"多空平衡({pos_count}涨{neg_count}跌)，个股表现温和"
            action = "板块整体机会不大，可寻找结构性机会"
            
        elif mean_return > 0.01 and dir_coh > 0.7:
            coh_type = "bullish_trend"
            interpretation = f"板块偏多趋势，{dir_coh*100:.0f}%个股上涨，平均涨幅{mean_return*100:.1f}%"
            action = "可以跟随趋势，但注意涨幅分化情况"
            
        elif mean_return < -0.01 and dir_coh > 0.7:
            coh_type = "bearish_trend"
            interpretation = f"板块偏空趋势，{dir_coh*100:.0f}%个股下跌，平均跌幅{abs(mean_return)*100:.1f}%"
            action = "谨慎观望，等待企稳信号"
            
        else:
            coh_type = "moderate_coherence"
            interpretation = "板块表现分化但不极端，存在个股机会"
            action = "适度参与，均衡配置"
        
        return coh_type, interpretation, action
    
    def _create_insufficient_data_result(self, individual_returns: List[float]) -> Tuple[float, CoherenceDetails]:
        """创建数据不足时的结果"""
        details = CoherenceDetails(
            overall_coherence=0.5,
            direction_coherence=0.5,
            magnitude_coherence=0.5,
            coherence_type='insufficient_data',
            dominant_direction='neutral',
            interpretation='数据不足，无法评估一致性',
            action_suggestion='等待更多数据',
            statistics={
                'sample_size': len(individual_returns) if individual_returns else 0,
                'positive_ratio': 0.0,
                'negative_ratio': 0.0,
                'neutral_ratio': 0.0,
                'mean_return': 0.0,
                'std_return': 0.0
            },
            calculation_method={
                'magnitude': 'insufficient_data',
                'weights': {'direction': 0.5, 'magnitude': 0.5},
                'weight_reason': '数据不足，使用默认权重'
            }
        )
        
        return 0.5, details


def calculate_improved_momentum_coherence_simple(individual_returns: List[float]) -> Dict[str, float]:
    """
    简化版改进动量一致性计算（兼容现有代码）
    
    Args:
        individual_returns: 个股收益率列表
        
    Returns:
        Dict: 包含一致性指标的字典
    """
    calculator = ImprovedMomentumCoherence()
    overall_coherence, details = calculator.calculate_coherence(individual_returns)
    
    return {
        'overall_coherence': details.overall_coherence,
        'direction_coherence': details.direction_coherence,
        'magnitude_coherence': details.magnitude_coherence,
        'coherence_type': details.coherence_type,
        'mean_return': details.statistics['mean_return'] / 100,  # 转回小数
        'std_return': details.statistics['std_return'] / 100
    }
