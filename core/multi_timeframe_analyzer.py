#!/usr/bin/env python3
"""
多时间框架综合分析引擎
基于v41文档实现跨时间框架的智能决策融合
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import logging
import json

logger = logging.getLogger(__name__)

# ========================================
# 数据结构定义
# ========================================

class TimeFrame(Enum):
    """时间框架枚举"""
    M5 = "5m"
    M15 = "15m"
    H1 = "1h"
    H4 = "4h"
    D1 = "1d"
    W1 = "1w"
    MN1 = "1M"


class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    REVERSAL_UP = "reversal_up"
    REVERSAL_DOWN = "reversal_down"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"


class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_STRONG = "very_strong"
    STRONG = "strong"
    MODERATE = "moderate"
    WEAK = "weak"
    VERY_WEAK = "very_weak"


@dataclass
class TimeFrameSignal:
    """单个时间框架的信号"""
    timeframe: str
    signal_type: str  # buy, sell, hold
    strength: float  # 0-1
    confidence: float  # 0-1
    rsi: float
    internal_health: float
    divergence_type: str
    divergence_severity: float
    momentum_coherence: float
    recommendation: str
    supporting_factors: List[str]
    risk_factors: List[str]


@dataclass
class MultiTimeframeAnalysis:
    """多时间框架综合分析结果"""
    sector_name: str
    analysis_timestamp: datetime
    
    # 各时间框架信号
    timeframe_signals: Dict[str, TimeFrameSignal]
    
    # 综合分析结果
    unified_decision: str  # buy, sell, hold
    consensus_score: float  # 0-1, 各时间框架一致性
    signal_reliability: float  # 0-1, 信号可靠性
    market_regime: str  # 市场状态
    
    # 动态权重
    dynamic_weights: Dict[str, float]
    weight_reasoning: str
    
    # 冲突分析
    conflicting_signals: List[str]
    conflict_resolution: str
    
    # 风险评估
    overall_risk_level: str
    risk_factors: List[str]
    
    # 操作建议
    entry_strategy: str
    exit_strategy: str
    position_sizing: str
    
    # 详细分析
    detailed_analysis: Dict[str, Any]


# ========================================
# 多时间框架分析引擎
# ========================================

class MultiTimeframeAnalyzer:
    """多时间框架综合分析引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 默认时间框架权重
        self.default_weights = {
            "5m": 0.1,
            "15m": 0.15,
            "1h": 0.25,
            "4h": 0.3,
            "1d": 0.2
        }
        
        # 信号强度阈值
        self.signal_thresholds = {
            "very_strong": 0.8,
            "strong": 0.6,
            "moderate": 0.4,
            "weak": 0.2
        }
        
    def analyze_all_timeframes(self, sector: str,
                             timeframes: List[str] = None) -> MultiTimeframeAnalysis:
        """
        综合分析所有时间框架
        
        参数:
            sector: 板块名称
            timeframes: 要分析的时间框架列表
            
        返回:
            MultiTimeframeAnalysis: 综合分析结果
        """
        if timeframes is None:
            timeframes = ["5m", "1h", "1d"]  # 默认分析这三个时间框架
            
        try:
            # 1. 获取各时间框架数据
            timeframe_data = self._fetch_timeframe_data(sector, timeframes)

            # 1.1 检查数据可用性
            if not timeframe_data:
                self.logger.warning(f"没有找到任何时间框架的数据: {sector}, {timeframes}")
                # 返回空分析结果
                return self._create_empty_analysis(sector, timeframes)

            # 1.2 警告缺失的时间框架
            missing_timeframes = set(timeframes) - set(timeframe_data.keys())
            if missing_timeframes:
                self.logger.warning(f"缺失时间框架数据: {missing_timeframes}")

            # 2. 生成各时间框架信号
            timeframe_signals = self._generate_timeframe_signals(timeframe_data)
            
            # 3. 计算一致性得分
            consensus_score = self._calculate_consensus_score(timeframe_signals)
            
            # 4. 识别市场状态
            market_regime = self._determine_market_regime(timeframe_signals)
            
            # 5. 计算动态权重
            dynamic_weights, weight_reasoning = self._calculate_dynamic_weights(
                timeframe_signals, market_regime
            )
            
            # 6. 检测冲突信号
            conflicting_signals, conflict_resolution = self._detect_and_resolve_conflicts(
                timeframe_signals
            )
            
            # 7. 生成统一决策
            unified_decision = self._generate_unified_decision(
                timeframe_signals, dynamic_weights, consensus_score
            )
            
            # 8. 计算信号可靠性
            signal_reliability = self._calculate_signal_reliability(
                consensus_score, len(timeframe_signals), market_regime
            )
            
            # 9. 风险评估
            overall_risk_level, risk_factors = self._assess_overall_risk(
                timeframe_signals, market_regime, conflicting_signals
            )
            
            # 10. 生成操作策略
            entry_strategy, exit_strategy, position_sizing = self._generate_trading_strategies(
                unified_decision, signal_reliability, overall_risk_level, market_regime
            )
            
            # 11. 详细分析
            detailed_analysis = self._generate_detailed_analysis(
                timeframe_signals, consensus_score, market_regime
            )
            
            return MultiTimeframeAnalysis(
                sector_name=sector,
                analysis_timestamp=datetime.now(),
                timeframe_signals=timeframe_signals,
                unified_decision=unified_decision,
                consensus_score=consensus_score,
                signal_reliability=signal_reliability,
                market_regime=market_regime,
                dynamic_weights=dynamic_weights,
                weight_reasoning=weight_reasoning,
                conflicting_signals=conflicting_signals,
                conflict_resolution=conflict_resolution,
                overall_risk_level=overall_risk_level,
                risk_factors=risk_factors,
                entry_strategy=entry_strategy,
                exit_strategy=exit_strategy,
                position_sizing=position_sizing,
                detailed_analysis=detailed_analysis
            )
            
        except Exception as e:
            self.logger.error(f"多时间框架分析失败 {sector}: {e}")
            raise

    def _create_empty_analysis(self, sector: str, timeframes: List[str]) -> MultiTimeframeAnalysis:
        """创建空的分析结果（当没有数据时）"""
        return MultiTimeframeAnalysis(
            sector_name=sector,
            analysis_timestamp=datetime.now(),
            timeframe_signals={},
            unified_decision="数据不足，无法分析",
            consensus_score=0.0,
            signal_reliability=0.0,
            market_regime="unknown",
            dynamic_weights={tf: 1.0/len(timeframes) for tf in timeframes},
            weight_reasoning="无数据，使用平均权重",
            conflicting_signals=[],
            conflict_resolution="无数据，无冲突",
            overall_risk_level="unknown",
            risk_factors=[f"缺少{len(timeframes)}个时间框架的数据"],
            entry_strategy="等待数据可用",
            exit_strategy="等待数据可用",
            position_sizing="0%",
            detailed_analysis={
                "data_availability": {
                    "requested_timeframes": timeframes,
                    "available_timeframes": [],
                    "missing_timeframes": timeframes,
                    "data_coverage": "0%"
                }
            }
        )
    
    def _fetch_timeframe_data(self, sector: str, timeframes: List[str]) -> Dict[str, Dict]:
        """获取各时间框架的数据"""
        try:
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))
            
            import pymysql
            from db_settings import get_default_db_config
            
            config = get_default_db_config()
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            timeframe_data = {}
            
            for tf in timeframes:
                # 获取市场广度数据 (只查询存在的字段)
                cursor.execute("""
                SELECT market, timeframe, advances, declines, unchanged, total_stocks,
                       avg_rsi, internal_health, momentum_coherence, recorded_at
                FROM market_breadth_metrics_gics
                WHERE market = %s AND timeframe = %s
                ORDER BY recorded_at DESC
                LIMIT 1
                """, (sector, tf))
                
                breadth_data = cursor.fetchone()
                
                if breadth_data:
                    timeframe_data[tf] = {
                        'breadth': {
                            'market': breadth_data[0],
                            'timeframe': breadth_data[1],
                            'advances': breadth_data[2],
                            'declines': breadth_data[3],
                            'unchanged': breadth_data[4],
                            'total_stocks': breadth_data[5],
                            'avg_rsi': float(breadth_data[6]) if breadth_data[6] is not None else 50.0,
                            'internal_health': float(breadth_data[7]) if breadth_data[7] is not None else 50.0,
                            'momentum_coherence': float(breadth_data[8]) if breadth_data[8] is not None else 0.5,
                            'recorded_at': breadth_data[9]
                        }
                    }
                    
                    # 获取轮动数据（如果存在）
                    cursor.execute("""
                    SELECT rotation_stage, rotation_intensity_index, relative_strength,
                           momentum_20d, risk_level, composite_score
                    FROM sector_rotation_metrics_gics
                    WHERE sector = %s AND timeframe = %s
                    ORDER BY recorded_at DESC
                    LIMIT 1
                    """, (sector, tf))

                    rotation_data = cursor.fetchone()
                    if rotation_data:
                        timeframe_data[tf]['rotation'] = {
                            'rotation_stage': rotation_data[0],
                            'rotation_intensity_index': float(rotation_data[1]) if rotation_data[1] is not None else 0.0,
                            'relative_strength': float(rotation_data[2]) if rotation_data[2] is not None else 0.0,
                            'momentum_20d': float(rotation_data[3]) if rotation_data[3] is not None else 0.0,
                            'risk_level': rotation_data[4],
                            'composite_score': float(rotation_data[5]) if rotation_data[5] is not None else 0.0
                        }
            
            conn.close()
            return timeframe_data
            
        except Exception as e:
            self.logger.error(f"获取时间框架数据失败: {e}")
            return {}
    
    def _generate_timeframe_signals(self, timeframe_data: Dict[str, Dict]) -> Dict[str, TimeFrameSignal]:
        """生成各时间框架的信号"""
        signals = {}
        
        for tf, data in timeframe_data.items():
            if 'breadth' not in data:
                continue
                
            breadth = data['breadth']
            rotation = data.get('rotation', {})
            
            # 生成信号
            signal_type, strength, confidence = self._analyze_single_timeframe(breadth, rotation)
            
            # 提取支撑因素和风险因素
            supporting_factors, risk_factors = self._extract_factors(breadth, rotation)
            
            # 生成建议
            recommendation = self._generate_timeframe_recommendation(
                signal_type, strength, confidence, 'medium'  # 默认风险等级
            )
            
            signals[tf] = TimeFrameSignal(
                timeframe=tf,
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                rsi=breadth['avg_rsi'],
                internal_health=breadth['internal_health'],
                divergence_type='none',  # 默认值，因为数据库中没有这个字段
                divergence_severity=0.0,  # 默认值
                momentum_coherence=breadth['momentum_coherence'],
                recommendation=recommendation,
                supporting_factors=supporting_factors,
                risk_factors=risk_factors
            )
        
        return signals

    def _analyze_single_timeframe(self, breadth: Dict, rotation: Dict) -> Tuple[str, float, float]:
        """分析单个时间框架的信号"""
        # 基础信号判断
        rsi = breadth['avg_rsi']
        health = breadth['internal_health']

        # 信号类型判断 (简化版，不依赖背离数据)
        if rsi < 30 and health > 45:
            signal_type = 'buy'
            base_strength = 0.6
        elif rsi > 70 and health < 55:
            signal_type = 'sell'
            base_strength = 0.6
        elif health > 60:
            signal_type = 'buy'
            base_strength = 0.4
        elif health < 40:
            signal_type = 'sell'
            base_strength = 0.4
        else:
            signal_type = 'hold'
            base_strength = 0.3

        # 强度调整
        strength = base_strength

        # RSI极值调整
        if rsi < 20:
            strength *= 1.2
        elif rsi > 80:
            strength *= 1.2
        elif 45 <= rsi <= 55:
            strength *= 0.8

        # 健康度调整
        if health > 70:
            strength *= 1.1
        elif health < 30:
            strength *= 1.1

        # 轮动数据调整
        if rotation:
            momentum = rotation.get('momentum_20d', 0)
            if abs(momentum) > 0.05:
                strength *= 1.1

        # 限制范围
        strength = max(0.1, min(1.0, strength))

        # 置信度计算
        confidence = breadth.get('divergence_confidence', 0.5)
        if confidence == 0:
            confidence = 0.5  # 默认置信度

        # 基于数据质量调整置信度
        total_stocks = breadth.get('total_stocks', 0)
        if total_stocks > 100:
            confidence *= 1.1
        elif total_stocks < 50:
            confidence *= 0.9

        confidence = max(0.1, min(1.0, confidence))

        return signal_type, strength, confidence

    def _extract_factors(self, breadth: Dict, rotation: Dict) -> Tuple[List[str], List[str]]:
        """提取支撑因素和风险因素"""
        supporting_factors = []
        risk_factors = []

        # RSI因素
        rsi = breadth['avg_rsi']
        if rsi < 30:
            supporting_factors.append(f"RSI超卖({rsi:.1f})")
        elif rsi > 70:
            risk_factors.append(f"RSI超买({rsi:.1f})")

        # 健康度因素
        health = breadth['internal_health']
        if health > 60:
            supporting_factors.append(f"内部健康度良好({health:.1f})")
        elif health < 40:
            risk_factors.append(f"内部健康度较差({health:.1f})")

        # 背离因素 (暂时跳过，因为数据库中没有背离数据)
        # 可以在未来添加背离检测逻辑

        # 一致性因素
        coherence = breadth['momentum_coherence']
        if coherence > 0.7:
            supporting_factors.append(f"动量一致性高({coherence:.2f})")
        elif coherence < 0.3:
            risk_factors.append(f"动量一致性低({coherence:.2f})")

        # 轮动因素
        if rotation:
            momentum = rotation.get('momentum_20d', 0)
            if momentum > 0.03:
                supporting_factors.append(f"20日动量强劲({momentum:.1%})")
            elif momentum < -0.03:
                risk_factors.append(f"20日动量疲弱({momentum:.1%})")

        return supporting_factors, risk_factors

    def _generate_timeframe_recommendation(self, signal_type: str, strength: float,
                                         confidence: float, risk_level: str) -> str:
        """生成时间框架建议"""
        if signal_type == 'buy':
            if strength > 0.7 and confidence > 0.7:
                return "强烈建议买入"
            elif strength > 0.5:
                return "建议买入"
            else:
                return "可考虑买入"
        elif signal_type == 'sell':
            if strength > 0.7 and confidence > 0.7:
                return "强烈建议卖出"
            elif strength > 0.5:
                return "建议卖出"
            else:
                return "可考虑卖出"
        else:
            return "建议持有观望"

    def _calculate_consensus_score(self, timeframe_signals: Dict[str, TimeFrameSignal]) -> float:
        """计算各时间框架一致性得分"""
        if len(timeframe_signals) < 2:
            return 1.0

        # 统计各信号类型的数量
        signal_counts = {'buy': 0, 'sell': 0, 'hold': 0}
        total_weight = 0

        for tf, signal in timeframe_signals.items():
            weight = self.default_weights.get(tf, 0.2)
            signal_counts[signal.signal_type] += weight * signal.strength
            total_weight += weight

        # 计算主导信号的比例
        if total_weight > 0:
            max_signal_weight = max(signal_counts.values())
            consensus_score = max_signal_weight / total_weight
        else:
            consensus_score = 0.5

        return min(1.0, consensus_score)

    def _determine_market_regime(self, timeframe_signals: Dict[str, TimeFrameSignal]) -> str:
        """确定市场状态"""
        if not timeframe_signals:
            return MarketRegime.UNKNOWN.value

        # 分析各时间框架的信号模式
        short_term_signals = []  # 5m, 15m
        medium_term_signals = []  # 1h, 4h
        long_term_signals = []   # 1d, 1w

        for tf, signal in timeframe_signals.items():
            if tf in ['5m', '15m']:
                short_term_signals.append(signal)
            elif tf in ['1h', '4h']:
                medium_term_signals.append(signal)
            else:
                long_term_signals.append(signal)

        # 判断趋势方向
        def get_trend_direction(signals):
            if not signals:
                return 'neutral'
            buy_strength = sum(s.strength for s in signals if s.signal_type == 'buy')
            sell_strength = sum(s.strength for s in signals if s.signal_type == 'sell')

            if buy_strength > sell_strength * 1.5:
                return 'up'
            elif sell_strength > buy_strength * 1.5:
                return 'down'
            else:
                return 'neutral'

        short_trend = get_trend_direction(short_term_signals)
        medium_trend = get_trend_direction(medium_term_signals)
        long_trend = get_trend_direction(long_term_signals)

        # 市场状态判断
        if long_trend == 'up' and medium_trend == 'up':
            if short_trend == 'down':
                return MarketRegime.TRENDING_UP.value  # 长期上涨中的短期回调
            else:
                return MarketRegime.TRENDING_UP.value
        elif long_trend == 'down' and medium_trend == 'down':
            if short_trend == 'up':
                return MarketRegime.TRENDING_DOWN.value  # 长期下跌中的短期反弹
            else:
                return MarketRegime.TRENDING_DOWN.value
        elif long_trend == 'down' and medium_trend == 'up' and short_trend == 'up':
            return MarketRegime.REVERSAL_UP.value
        elif long_trend == 'up' and medium_trend == 'down' and short_trend == 'down':
            return MarketRegime.REVERSAL_DOWN.value
        elif long_trend == 'neutral' and medium_trend == 'neutral':
            return MarketRegime.RANGING.value
        else:
            return MarketRegime.VOLATILE.value

    def _calculate_dynamic_weights(self, timeframe_signals: Dict[str, TimeFrameSignal],
                                 market_regime: str) -> Tuple[Dict[str, float], str]:
        """计算动态权重"""
        weights = self.default_weights.copy()
        reasoning = ""

        # 根据市场状态调整权重
        if market_regime == MarketRegime.TRENDING_UP.value or market_regime == MarketRegime.TRENDING_DOWN.value:
            # 趋势市场：长期权重增加
            weights["1d"] = weights.get("1d", 0.2) * 1.5
            weights["1h"] = weights.get("1h", 0.25) * 1.2
            weights["5m"] = weights.get("5m", 0.1) * 0.7
            reasoning = "趋势市场，增加长期时间框架权重"

        elif market_regime == MarketRegime.RANGING.value:
            # 震荡市场：短期权重增加
            weights["5m"] = weights.get("5m", 0.1) * 2.0
            weights["15m"] = weights.get("15m", 0.15) * 1.5
            weights["1d"] = weights.get("1d", 0.2) * 0.7
            reasoning = "震荡市场，增加短期时间框架权重"

        elif market_regime == MarketRegime.REVERSAL_UP.value or market_regime == MarketRegime.REVERSAL_DOWN.value:
            # 转折市场：中期权重增加
            weights["1h"] = weights.get("1h", 0.25) * 1.8
            weights["4h"] = weights.get("4h", 0.3) * 1.5
            weights["5m"] = weights.get("5m", 0.1) * 0.8
            reasoning = "市场转折期，增加中期时间框架权重"

        elif market_regime == MarketRegime.VOLATILE.value:
            # 波动市场：平均分配权重
            total_timeframes = len(timeframe_signals)
            equal_weight = 1.0 / total_timeframes
            for tf in timeframe_signals.keys():
                weights[tf] = equal_weight
            reasoning = "高波动市场，平均分配各时间框架权重"

        # 根据信号质量调整权重
        for tf, signal in timeframe_signals.items():
            if signal.confidence > 0.8:
                weights[tf] = weights.get(tf, 0.2) * 1.2
            elif signal.confidence < 0.4:
                weights[tf] = weights.get(tf, 0.2) * 0.8

        # 标准化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {tf: w/total_weight for tf, w in weights.items()}

        return weights, reasoning

    def _detect_and_resolve_conflicts(self, timeframe_signals: Dict[str, TimeFrameSignal]) -> Tuple[List[str], str]:
        """检测和解决冲突信号"""
        conflicts = []

        # 检测信号冲突
        signal_types = [signal.signal_type for signal in timeframe_signals.values()]
        unique_signals = set(signal_types)

        if len(unique_signals) > 1:
            # 存在冲突，分析具体冲突
            for tf1, signal1 in timeframe_signals.items():
                for tf2, signal2 in timeframe_signals.items():
                    if tf1 < tf2 and signal1.signal_type != signal2.signal_type:
                        if signal1.signal_type != 'hold' and signal2.signal_type != 'hold':
                            conflicts.append(f"{tf1}({signal1.signal_type}) vs {tf2}({signal2.signal_type})")

        # 冲突解决策略
        if not conflicts:
            resolution = "各时间框架信号一致，无冲突"
        else:
            # 按权重和置信度解决冲突
            weighted_scores = {}
            for tf, signal in timeframe_signals.items():
                weight = self.default_weights.get(tf, 0.2)
                score = weight * signal.strength * signal.confidence

                if signal.signal_type not in weighted_scores:
                    weighted_scores[signal.signal_type] = 0
                weighted_scores[signal.signal_type] += score

            dominant_signal = max(weighted_scores.items(), key=lambda x: x[1])
            resolution = f"存在冲突信号，以{dominant_signal[0]}为主导(权重得分: {dominant_signal[1]:.2f})"

        return conflicts, resolution

    def _generate_unified_decision(self, timeframe_signals: Dict[str, TimeFrameSignal],
                                 dynamic_weights: Dict[str, float],
                                 consensus_score: float) -> str:
        """生成统一决策"""
        # 加权投票
        weighted_scores = {'buy': 0, 'sell': 0, 'hold': 0}

        for tf, signal in timeframe_signals.items():
            weight = dynamic_weights.get(tf, 0.2)
            score = weight * signal.strength * signal.confidence
            weighted_scores[signal.signal_type] += score

        # 确定主导信号
        dominant_signal = max(weighted_scores.items(), key=lambda x: x[1])
        dominant_type = dominant_signal[0]
        dominant_score = dominant_signal[1]

        # 根据一致性调整决策强度
        if consensus_score > 0.8:
            if dominant_type == 'buy':
                return "强烈买入"
            elif dominant_type == 'sell':
                return "强烈卖出"
            else:
                return "坚定持有"
        elif consensus_score > 0.6:
            if dominant_type == 'buy':
                return "买入"
            elif dominant_type == 'sell':
                return "卖出"
            else:
                return "持有"
        else:
            # 低一致性，保守决策
            if dominant_score > 0.3:
                if dominant_type == 'buy':
                    return "谨慎买入"
                elif dominant_type == 'sell':
                    return "谨慎卖出"
                else:
                    return "观望"
            else:
                return "观望"

    def _calculate_signal_reliability(self, consensus_score: float,
                                    signal_count: int, market_regime: str) -> float:
        """计算信号可靠性"""
        base_reliability = consensus_score

        # 信号数量调整
        if signal_count >= 3:
            base_reliability *= 1.1
        elif signal_count < 2:
            base_reliability *= 0.8

        # 市场状态调整
        if market_regime in [MarketRegime.TRENDING_UP.value, MarketRegime.TRENDING_DOWN.value]:
            base_reliability *= 1.1  # 趋势市场信号更可靠
        elif market_regime == MarketRegime.VOLATILE.value:
            base_reliability *= 0.8  # 波动市场信号可靠性降低

        return min(1.0, base_reliability)

    def _assess_overall_risk(self, timeframe_signals: Dict[str, TimeFrameSignal],
                           market_regime: str, conflicting_signals: List[str]) -> Tuple[str, List[str]]:
        """评估整体风险"""
        risk_factors = []
        risk_score = 0.0

        # 冲突信号风险
        if conflicting_signals:
            risk_score += 0.2
            risk_factors.append(f"存在{len(conflicting_signals)}个冲突信号")

        # 市场状态风险
        if market_regime == MarketRegime.VOLATILE.value:
            risk_score += 0.3
            risk_factors.append("市场高度波动")
        elif market_regime in [MarketRegime.REVERSAL_UP.value, MarketRegime.REVERSAL_DOWN.value]:
            risk_score += 0.2
            risk_factors.append("市场处于转折期")

        # 信号质量风险
        low_confidence_count = sum(1 for signal in timeframe_signals.values() if signal.confidence < 0.5)
        if low_confidence_count > 0:
            risk_score += 0.1 * low_confidence_count
            risk_factors.append(f"{low_confidence_count}个时间框架信号置信度较低")

        # 极端RSI风险
        extreme_rsi_count = sum(1 for signal in timeframe_signals.values()
                               if signal.rsi < 20 or signal.rsi > 80)
        if extreme_rsi_count > 0:
            risk_score += 0.1
            risk_factors.append("存在极端RSI读数")

        # 确定风险等级
        if risk_score >= 0.6:
            risk_level = "extreme"
        elif risk_score >= 0.4:
            risk_level = "high"
        elif risk_score >= 0.2:
            risk_level = "medium"
        else:
            risk_level = "low"

        return risk_level, risk_factors

    def _generate_trading_strategies(self, unified_decision: str, signal_reliability: float,
                                   overall_risk_level: str, market_regime: str) -> Tuple[str, str, str]:
        """生成交易策略"""
        # 入场策略
        if "买入" in unified_decision:
            if signal_reliability > 0.8 and overall_risk_level in ["low", "medium"]:
                entry_strategy = "立即建仓，分批买入"
            elif signal_reliability > 0.6:
                entry_strategy = "逢低分批建仓"
            else:
                entry_strategy = "小仓位试探，等待确认"
        elif "卖出" in unified_decision:
            if signal_reliability > 0.8:
                entry_strategy = "立即减仓或清仓"
            elif signal_reliability > 0.6:
                entry_strategy = "逢高分批减仓"
            else:
                entry_strategy = "部分减仓，观察后续"
        else:
            entry_strategy = "保持观望，等待明确信号"

        # 出场策略
        if market_regime == MarketRegime.TRENDING_UP.value:
            exit_strategy = "趋势跟踪止损，回撤10-15%止损"
        elif market_regime == MarketRegime.TRENDING_DOWN.value:
            exit_strategy = "快速止损，反弹5-8%止盈"
        elif market_regime == MarketRegime.RANGING.value:
            exit_strategy = "区间操作，上沿减仓下沿加仓"
        elif market_regime in [MarketRegime.REVERSAL_UP.value, MarketRegime.REVERSAL_DOWN.value]:
            exit_strategy = "密切关注转折确认，设置较紧止损"
        else:
            exit_strategy = "设置5-8%止损，见好就收"

        # 仓位管理
        if overall_risk_level == "low" and signal_reliability > 0.8:
            position_sizing = "可使用60-80%仓位"
        elif overall_risk_level == "medium" or signal_reliability > 0.6:
            position_sizing = "建议30-50%仓位"
        elif overall_risk_level == "high":
            position_sizing = "轻仓操作，10-20%仓位"
        else:
            position_sizing = "极轻仓或空仓观望"

        return entry_strategy, exit_strategy, position_sizing

    def _generate_detailed_analysis(self, timeframe_signals: Dict[str, TimeFrameSignal],
                                  consensus_score: float, market_regime: str) -> Dict[str, Any]:
        """生成详细分析"""
        analysis = {
            "signal_summary": {},
            "strength_analysis": {},
            "confidence_analysis": {},
            "rsi_analysis": {},
            "health_analysis": {},
            "divergence_analysis": {},
            "market_regime_details": {},
            "recommendation_summary": {}
        }

        # 信号汇总
        signal_counts = {'buy': 0, 'sell': 0, 'hold': 0}
        for signal in timeframe_signals.values():
            signal_counts[signal.signal_type] += 1
        analysis["signal_summary"] = signal_counts

        # 强度分析
        strengths = [signal.strength for signal in timeframe_signals.values()]
        if strengths:
            analysis["strength_analysis"] = {
                "average": np.mean(strengths),
                "max": np.max(strengths),
                "min": np.min(strengths),
                "std": np.std(strengths)
            }
        else:
            analysis["strength_analysis"] = {
                "average": 0.0,
                "max": 0.0,
                "min": 0.0,
                "std": 0.0
            }

        # 置信度分析
        confidences = [signal.confidence for signal in timeframe_signals.values()]
        if confidences:
            analysis["confidence_analysis"] = {
                "average": np.mean(confidences),
                "max": np.max(confidences),
                "min": np.min(confidences),
                "high_confidence_count": sum(1 for c in confidences if c > 0.7)
            }
        else:
            analysis["confidence_analysis"] = {
                "average": 0.0,
                "max": 0.0,
                "min": 0.0,
                "high_confidence_count": 0
            }

        # RSI分析
        rsi_values = [signal.rsi for signal in timeframe_signals.values()]
        if rsi_values:
            analysis["rsi_analysis"] = {
                "average": np.mean(rsi_values),
                "oversold_count": sum(1 for rsi in rsi_values if rsi < 30),
                "overbought_count": sum(1 for rsi in rsi_values if rsi > 70),
                "neutral_count": sum(1 for rsi in rsi_values if 30 <= rsi <= 70)
            }
        else:
            analysis["rsi_analysis"] = {
                "average": 50.0,
                "oversold_count": 0,
                "overbought_count": 0,
                "neutral_count": 0
            }

        # 健康度分析
        health_values = [signal.internal_health for signal in timeframe_signals.values()]
        if health_values:
            analysis["health_analysis"] = {
                "average": np.mean(health_values),
                "healthy_count": sum(1 for h in health_values if h > 60),
                "unhealthy_count": sum(1 for h in health_values if h < 40)
            }
        else:
            analysis["health_analysis"] = {
                "average": 50.0,
                "healthy_count": 0,
                "unhealthy_count": 0
            }

        # 背离分析 (简化版)
        divergence_types = [signal.divergence_type for signal in timeframe_signals.values()]
        analysis["divergence_analysis"] = {
            "positive_count": divergence_types.count('positive'),
            "negative_count": divergence_types.count('negative'),
            "none_count": divergence_types.count('none')
        }

        # 市场状态详情
        analysis["market_regime_details"] = {
            "regime": market_regime,
            "consensus_score": consensus_score,
            "regime_description": self._get_regime_description(market_regime)
        }

        # 建议汇总
        recommendations = [signal.recommendation for signal in timeframe_signals.values()]
        analysis["recommendation_summary"] = {
            "timeframe_recommendations": {tf: signal.recommendation
                                        for tf, signal in timeframe_signals.items()},
            "strong_buy_count": sum(1 for r in recommendations if "强烈" in r and "买" in r),
            "strong_sell_count": sum(1 for r in recommendations if "强烈" in r and "卖" in r)
        }

        return analysis

    def _get_regime_description(self, market_regime: str) -> str:
        """获取市场状态描述"""
        descriptions = {
            MarketRegime.TRENDING_UP.value: "市场处于上升趋势，适合趋势跟踪策略",
            MarketRegime.TRENDING_DOWN.value: "市场处于下降趋势，注意风险控制",
            MarketRegime.RANGING.value: "市场处于震荡区间，适合高抛低吸",
            MarketRegime.REVERSAL_UP.value: "市场可能正在转向上涨，谨慎乐观",
            MarketRegime.REVERSAL_DOWN.value: "市场可能正在转向下跌，提高警惕",
            MarketRegime.VOLATILE.value: "市场高度波动，建议降低仓位",
            MarketRegime.UNKNOWN.value: "市场状态不明，建议观望"
        }
        return descriptions.get(market_regime, "市场状态待确认")


# ========================================
# 便捷使用接口
# ========================================

def analyze_sector_mtf(sector: str, timeframes: List[str] = None) -> MultiTimeframeAnalysis:
    """
    便捷的多时间框架分析接口

    参数:
        sector: 板块名称
        timeframes: 时间框架列表，默认为 ['5m', '1h', '1d']

    返回:
        MultiTimeframeAnalysis: 综合分析结果
    """
    analyzer = MultiTimeframeAnalyzer()
    return analyzer.analyze_all_timeframes(sector, timeframes)


def quick_analysis_demo():
    """快速分析演示"""
    print("🚀 多时间框架综合分析演示")
    print("=" * 60)

    # 分析几个主要板块
    sectors = ["Technology", "Financials", "Health Care"]

    for sector in sectors:
        try:
            print(f"\n📊 分析板块: {sector}")
            analysis = analyze_sector_mtf(sector)

            print(f"  统一决策: {analysis.unified_decision}")
            print(f"  一致性得分: {analysis.consensus_score:.2f}")
            print(f"  信号可靠性: {analysis.signal_reliability:.2f}")
            print(f"  市场状态: {analysis.market_regime}")
            print(f"  风险等级: {analysis.overall_risk_level}")
            print(f"  入场策略: {analysis.entry_strategy}")

        except Exception as e:
            print(f"  ❌ 分析失败: {e}")


# ========================================
# v41文档标准接口
# ========================================

def analyze_market_mtf(
    sector: str = "Information Technology",
    timeframes: List[str] = None,
    output_format: str = 'full'  # 'full', 'summary', 'signals'
) -> Dict:
    """
    多时间框架市场分析的便捷接口 - 符合v41文档标准

    参数:
        sector: 板块名称
        timeframes: 时间框架列表
        output_format: 输出格式

    返回:
        分析报告（人类和LLM可读）
    """
    if timeframes is None:
        timeframes = ['5min', '1h', '1d']  # v41标准时间框架

    # 标准化时间框架为v41格式
    timeframes = normalize_v41_timeframes(timeframes)

    # 验证时间框架
    if not validate_v41_timeframes(timeframes):
        logger.warning(f"时间框架不符合v41标准: {timeframes}")

    try:
        # 执行分析
        analyzer = MultiTimeframeAnalyzer()
        analysis = analyzer.analyze_all_timeframes(sector, timeframes)

        # 生成v41标准报告
        from v41_report_generator import V41ReportGenerator
        report_generator = V41ReportGenerator()
        full_report = report_generator.generate_comprehensive_report(analysis)

        # 根据输出格式返回
        if output_format == 'summary':
            return _extract_summary(full_report)
        elif output_format == 'signals':
            return _extract_signals(full_report)
        else:
            return full_report

    except Exception as e:
        logger.error(f"v41分析失败: {e}")
        return {
            'error': str(e),
            'metadata': {
                'report_type': '错误报告',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'version': '4.1'
            }
        }


def _extract_summary(full_report: Dict) -> Dict:
    """提取摘要信息 - v41标准"""
    return {
        'timestamp': full_report['metadata']['timestamp'],
        'reliability': full_report['metadata']['reliability_score'],
        'market_regime': full_report['market_overview']['market_regime']['value'],
        'consensus': full_report['market_overview']['consensus_score']['value'],
        'decision': full_report['unified_decision']['position_recommendation']['suggested'],
        'key_insight': full_report['insights_and_warnings']['key_insights'][0] if full_report['insights_and_warnings']['key_insights'] else "无特殊洞察",
        'main_risk': full_report['insights_and_warnings']['warnings'][0] if full_report['insights_and_warnings']['warnings'] else "风险可控"
    }


def _extract_signals(full_report: Dict) -> Dict:
    """提取信号信息 - v41标准"""
    unified_decision = full_report['unified_decision']

    return {
        'action': _determine_action_from_decision(unified_decision['position_recommendation']['suggested']),
        'position': unified_decision['position_recommendation']['suggested'],
        'confidence': unified_decision['position_recommendation']['confidence'],
        'buy_sectors': [s['sector'] for s in unified_decision['sector_allocation']['recommended']],
        'avoid_sectors': [s['sector'] for s in unified_decision['sector_allocation']['avoid']],
        'entry_timing': unified_decision['operation_guidance']['entry_timing'],
        'exit_strategy': unified_decision['operation_guidance']['exit_strategy'],
        'key_points': unified_decision['operation_guidance']['key_points'],
        'risk_level': full_report['risk_assessment']['overall_risk']['level'],
        'monitoring_required': len(full_report['insights_and_warnings']['monitoring_points']) > 0
    }


def _determine_action_from_decision(position: str) -> str:
    """从仓位建议确定行动"""
    if any(x in position for x in ['60-80%', '80-100%']):
        return 'strong_buy'
    elif any(x in position for x in ['40-60%']):
        return 'buy' if '60%' in position else 'hold'
    elif any(x in position for x in ['20-40%']):
        return 'light_buy' if '40%' in position else 'light_sell'
    elif any(x in position for x in ['0-20%']):
        return 'sell'
    else:
        return 'hold'


def normalize_v41_timeframes(timeframes: List[str]) -> List[str]:
    """
    标准化时间框架为v41文档标准格式

    参数:
        timeframes: 输入的时间框架列表

    返回:
        标准化后的时间框架列表
    """
    # 数据库实际时间框架映射
    db_mapping = {
        # 输入格式 -> 数据库实际格式
        '5m': '5m',      # 数据库中是5m，不是5min
        '15m': '15m',    # 数据库中是15m，不是15min
        '1h': '1h',
        '1d': '1d',
        '1w': '1w',      # 数据库中是1w，不是5d
        '1M': '1M',      # 数据库中是1M，不是20d

        # 已经是数据库格式的保持不变
        '5min': '5m',    # v41格式转换为数据库格式
        '15min': '15m',  # v41格式转换为数据库格式
        '5d': '1w',      # v41格式转换为数据库格式
        '20d': '1M'      # v41格式转换为数据库格式
    }

    normalized = []
    for tf in timeframes:
        if tf in db_mapping:
            normalized.append(db_mapping[tf])
        else:
            # 如果不在映射中，保持原样（可能已经是正确格式）
            normalized.append(tf)

    return normalized


def get_v41_timeframe_description(timeframe: str) -> str:
    """获取v41时间框架的中文描述"""
    descriptions = {
        "5min": "5分钟",
        "15min": "15分钟",
        "1h": "小时",
        "1d": "日度",
        "5d": "周度",
        "20d": "月度"
    }
    return descriptions.get(timeframe, timeframe)


def validate_v41_timeframes(timeframes: List[str]) -> bool:
    """验证时间框架是否符合数据库实际格式"""
    valid_timeframes = {"5m", "15m", "1h", "1d", "1w", "1M"}  # 数据库实际格式
    return all(tf in valid_timeframes for tf in timeframes)


if __name__ == "__main__":
    quick_analysis_demo()
