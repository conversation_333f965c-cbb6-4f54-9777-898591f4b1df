#!/usr/bin/env python3
"""
Redis数据管理器
统一管理Redis连接和数据操作

作者：Financial Master
日期：2025-01-22
"""

import json
import redis
import logging
from typing import Dict, List, Optional, Any

class RedisDataManager:
    """
    Redis数据管理器
    
    功能：
    1. 管理Redis连接
    2. 提供统一的数据获取接口
    3. 支持队列数据和历史数据获取
    """
    
    def __init__(self, redis_config: Dict):
        """初始化Redis数据管理器"""
        self.redis_config = redis_config
        self.redis_client = None
        self.logger = self._setup_logger()
        self._connect()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('redis_data_manager')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _connect(self) -> bool:
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(**self.redis_config)
            self.redis_client.ping()
            self.logger.info(f"[OK] Redis连接成功: {self.redis_config['host']}:{self.redis_config['port']}/db{self.redis_config['db']}")
            return True
        except Exception as e:
            self.logger.error(f"[ERROR] Redis连接失败: {e}")
            return False
    
    def test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            if self.redis_client:
                self.redis_client.ping()
                return True
            return False
        except Exception as e:
            self.logger.error(f"[ERROR] Redis连接测试失败: {e}")
            return False
    
    def get_stock_queue_data(self, company: str) -> Optional[List[Dict]]:
        """获取单个股票的队列数据"""
        try:
            redis_key = f"stock_queue:{company}"
            queue_data = self.redis_client.get(redis_key)
            
            if queue_data:
                queue_info = json.loads(queue_data)
                return queue_info.get('data', [])
            return None
            
        except Exception as e:
            self.logger.debug(f"[DEBUG] 获取股票 {company} 队列数据失败: {e}")
            return None
    
    def get_stock_hist_data(self, company: str) -> Optional[List[Dict]]:
        """获取单个股票的历史数据"""
        try:
            redis_key = f"stock_hist:{company}"
            hist_data = self.redis_client.get(redis_key)
            
            if hist_data:
                return json.loads(hist_data)
            return None
            
        except Exception as e:
            self.logger.debug(f"[DEBUG] 获取股票 {company} 历史数据失败: {e}")
            return None
    
    def get_stock_data_batch(self, companies: List[str], mode: str = 'queue') -> Dict[str, List[Dict]]:
        """
        批量获取股票数据
        
        :param companies: 股票代码列表
        :param mode: 数据模式 ('queue', 'hist', 'auto')
        :return: 股票数据字典
        """
        try:
            stock_data = {}
            successful_count = 0
            
            for company in companies:
                try:
                    data = None
                    
                    if mode == 'queue':
                        data = self.get_stock_queue_data(company)
                    elif mode == 'hist':
                        data = self.get_stock_hist_data(company)
                    elif mode == 'auto':
                        # 优先尝试队列数据，失败则尝试历史数据
                        data = self.get_stock_queue_data(company)
                        if not data:
                            data = self.get_stock_hist_data(company)
                    
                    if data:
                        stock_data[company] = data
                        successful_count += 1
                        
                except Exception as e:
                    # 静默处理单个股票的数据获取失败
                    continue
            
            self.logger.info(f"[DATA] 成功获取 {successful_count}/{len(companies)} 只股票的数据 (模式: {mode})")
            return stock_data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 批量获取股票数据失败: {e}")
            return {}
    
    def set_data(self, key: str, data: Any, expire: int = None) -> bool:
        """设置Redis数据"""
        try:
            if isinstance(data, (dict, list)):
                data = json.dumps(data)
            
            result = self.redis_client.set(key, data)
            
            if expire:
                self.redis_client.expire(key, expire)
            
            return result
            
        except Exception as e:
            self.logger.error(f"[ERROR] 设置Redis数据失败 {key}: {e}")
            return False
    
    def get_data(self, key: str, as_json: bool = True) -> Any:
        """获取Redis数据"""
        try:
            data = self.redis_client.get(key)
            
            if data and as_json:
                return json.loads(data)
            
            return data
            
        except Exception as e:
            self.logger.error(f"[ERROR] 获取Redis数据失败 {key}: {e}")
            return None
    
    def delete_data(self, key: str) -> bool:
        """删除Redis数据"""
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            self.logger.error(f"[ERROR] 删除Redis数据失败 {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            self.logger.error(f"[ERROR] 检查Redis键存在性失败 {key}: {e}")
            return False
    
    def get_keys(self, pattern: str) -> List[str]:
        """获取匹配模式的所有键"""
        try:
            return [key.decode() if isinstance(key, bytes) else key 
                   for key in self.redis_client.keys(pattern)]
        except Exception as e:
            self.logger.error(f"[ERROR] 获取Redis键列表失败 {pattern}: {e}")
            return []