#!/usr/bin/env python3
"""
市场广度计算集成器
支持历史数据、实时数据、混合模式三种计算模式
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import argparse

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
config_path = os.path.join(parent_dir, 'config')

if config_path not in sys.path:
    sys.path.insert(0, config_path)

from market_breadth_config import (
    MarketBreadthConfig, 
    CalculationMode,
    HISTORICAL_ONLY_CONFIG,
    REALTIME_ONLY_CONFIG,
    FULL_CONFIG
)

# 导入hist_data模块的函数
from hist_data import (
    setup_logging,
    get_companies,
    get_company_mcap,
    get_price,
    calculate_stock_data_for_date,
    get_markets_from_config
)


class MarketBreadthIntegrator:
    """市场广度计算集成器"""
    
    def __init__(self, config: MarketBreadthConfig):
        """
        初始化集成器
        
        :param config: 配置对象
        """
        self.config = config
        self.logger = setup_logging()
        
        # 根据配置设置日志级别
        logging.getLogger().setLevel(getattr(logging, config.processing.log_level))
        
        self.logger.info(f"市场广度计算器初始化完成，模式: {config.mode.value}")
    
    def run_historical_calculation(self) -> bool:
        """
        运行历史数据计算
        
        :return: 是否成功
        """
        try:
            self.logger.info("开始历史数据计算...")
            
            # 获取市场列表
            markets = get_markets_from_config()
            self.logger.info(f"获取到 {len(markets)} 个市场分组")
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config.historical.data_range_days)
            
            self.logger.info(f"计算最近 {self.config.historical.days} 天的市场广度指标")
            self.logger.info(f"数据范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            
            # 获取公司和市值数据
            companies = get_companies(markets)
            company_mcap = get_company_mcap(companies)
            self.logger.info(f"获取到 {len(companies)} 只股票，{len(company_mcap)} 只有市值数据")
            
            # 下载历史价格数据
            self.logger.info("正在下载历史价格数据...")
            company_price_data = get_price(
                companies,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if not company_price_data:
                self.logger.error("无法获取价格数据")
                return False
            
            self.logger.info(f"成功下载 {len(company_price_data)} 只股票的历史数据")
            
            # 生成工作日期列表
            work_dates = self._generate_work_dates(end_date, self.config.historical.days)
            self.logger.info(f"将处理 {len(work_dates)} 个交易日")
            
            # 执行计算
            success_count = self._calculate_breadth_multiprocess(
                work_dates, company_price_data, company_mcap, markets
            )
            
            # 统计结果
            total_expected = len(work_dates) * len(markets)
            success_rate = (success_count / total_expected * 100) if total_expected > 0 else 0
            
            self.logger.info(f"历史数据计算完成: {success_count}/{total_expected} ({success_rate:.1f}%)")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"历史数据计算失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_realtime_calculation(self) -> bool:
        """
        运行实时数据计算
        
        :return: 是否成功
        """
        try:
            self.logger.info("开始实时数据计算...")
            
            # TODO: 实现实时数据计算逻辑
            # 这里可以调用原有的实时计算函数
            self.logger.info("实时数据计算功能待实现")
            return True
            
        except Exception as e:
            self.logger.error(f"实时数据计算失败: {e}")
            return False
    
    def run(self) -> bool:
        """
        根据配置模式运行计算
        
        :return: 是否成功
        """
        success = True
        
        if self.config.mode in [CalculationMode.HISTORICAL, CalculationMode.BOTH]:
            historical_success = self.run_historical_calculation()
            success = success and historical_success
        
        if self.config.mode in [CalculationMode.REALTIME, CalculationMode.BOTH]:
            realtime_success = self.run_realtime_calculation()
            success = success and realtime_success
        
        return success
    
    def _generate_work_dates(self, end_date: datetime, days: int) -> List[datetime]:
        """
        生成工作日期列表
        
        :param end_date: 结束日期
        :param days: 天数
        :return: 工作日期列表
        """
        work_dates = []
        current_date = end_date
        days_count = 0

        while days_count < days:
            if current_date.weekday() < 5:  # 工作日
                work_dates.append(current_date)
                days_count += 1
            current_date -= timedelta(days=1)

        work_dates.reverse()  # 按时间顺序排列
        return work_dates

    def _calculate_breadth_multiprocess(self, work_dates: List[datetime],
                                      company_price_data: Dict[str, any],
                                      company_mcap: Dict[str, float],
                                      markets: List[str]) -> int:
        """使用多进程计算广度指标"""
        success_count = 0

        for date_obj in work_dates:
            try:
                date_str = date_obj.strftime('%Y-%m-%d')

                # 为每个日期计算股票数据
                stock_data = calculate_stock_data_for_date(
                    company_price_data,
                    company_mcap,
                    date_str
                )

                if stock_data.empty:
                    self.logger.warning(f"日期 {date_str} 没有股票数据")
                    continue

                # 为每个市场计算广度指标
                for market in markets:
                    try:
                        market_stock_data = self._filter_market_stocks(stock_data, market)
                        if market_stock_data.empty or len(market_stock_data) < 10:
                            continue

                        breadth_metrics = self._calculate_market_breadth_metrics(market_stock_data, date_str)
                        if breadth_metrics:
                            self._save_breadth_metrics_to_db(market, date_str, breadth_metrics)
                            success_count += 1
                    except Exception as e:
                        self.logger.error(f"处理市场 {market} 日期 {date_str} 时出错: {e}")
                        continue

            except Exception as e:
                self.logger.error(f"处理日期 {date_str} 时出错: {e}")

        return success_count

    def _filter_market_stocks(self, stock_data: any, market: str) -> any:
        """筛选特定市场的股票数据"""
        import pymysql

        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )

        sql = f"SELECT DISTINCT company FROM index_company_mapping_gics WHERE market='{market}'"
        cursor = conn.cursor()
        cursor.execute(sql)
        companies = cursor.fetchall()
        cursor.close()
        conn.close()

        companies = [c[0] for c in companies]
        return stock_data[stock_data['symbol'].isin(companies)].copy()

    def _calculate_market_breadth_metrics(self, market_stock_data: any, date_str: str) -> Dict[str, float]:
        """计算市场广度指标"""
        if market_stock_data.empty:
            return {}

        # 基础数据
        total_stocks = len(market_stock_data)
        advances = market_stock_data[market_stock_data['daily_return'] > 0].shape[0]
        declines = market_stock_data[market_stock_data['daily_return'] < 0].shape[0]
        unchanged = total_stocks - advances - declines

        # 成交量
        market_stock_data['volume_value'] = market_stock_data['volume'] * market_stock_data['price']
        advancing_volume = market_stock_data[market_stock_data['daily_return'] > 0]['volume_value'].sum()
        declining_volume = market_stock_data[market_stock_data['daily_return'] < 0]['volume_value'].sum()
        total_volume = market_stock_data['volume_value'].sum()

        # 新高和新低
        new_highs = market_stock_data[market_stock_data['is_new_high'] > 0].shape[0]
        new_lows = market_stock_data[market_stock_data['is_new_low'] > 0].shape[0]

        # 均线统计
        above_ma50 = market_stock_data[market_stock_data['above_ma50'] > 0].shape[0]
        above_ma200 = market_stock_data[market_stock_data['above_ma200'] > 0].shape[0]

        # 平均RSI
        avg_rsi = market_stock_data['rsi'].mean()

        # 市值加权收益率
        market_stock_data['weighted_return'] = market_stock_data['daily_return'] * market_stock_data['market_mcap']
        market_cap_weighted_return = market_stock_data['weighted_return'].sum() / market_stock_data['market_mcap'].sum()

        # 等权重收益率
        equal_weighted_return = market_stock_data['daily_return'].mean()

        return {
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'advancing_volume': float(advancing_volume),
            'declining_volume': float(declining_volume),
            'total_volume': float(total_volume),
            'new_highs_52w': new_highs,
            'new_lows_52w': new_lows,
            'above_ma50': above_ma50,
            'above_ma200': above_ma200,
            'avg_rsi': float(avg_rsi),
            'market_cap_weighted_return': float(market_cap_weighted_return),
            'equal_weighted_return': float(equal_weighted_return),
        }

    def _save_breadth_metrics_to_db(self, market: str, date_str: str, metrics: Dict[str, float]):
        """保存广度指标到数据库"""
        import pymysql

        conn = pymysql.Connection(
            host=os.environ['DEFAULT_DB_HOST'],
            port=int(os.environ['DEFAULT_DB_PORT']),
            user=os.environ['DEFAULT_DB_USER'],
            password=os.environ['DEFAULT_DB_PASSWORD'],
            database=os.environ['DEFAULT_DB_NAME'],
        )

        # 插入数据
        sql = """
        INSERT INTO market_breadth_gics (
            recorded_at, market, total_stocks, advances, declines, unchanged,
            advancing_volume, declining_volume, total_volume, new_highs_52w, new_lows_52w,
            above_ma50, above_ma200, avg_rsi, market_cap_weighted_return, equal_weighted_return
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        cursor = conn.cursor()
        cursor.execute(sql, (
            date_str, market, metrics['total_stocks'], metrics['advances'], metrics['declines'],
            metrics['unchanged'], metrics['advancing_volume'], metrics['declining_volume'],
            metrics['total_volume'], metrics['new_highs_52w'], metrics['new_lows_52w'],
            metrics['above_ma50'], metrics['above_ma200'], metrics['avg_rsi'],
            metrics['market_cap_weighted_return'], metrics['equal_weighted_return']
        ))
        conn.commit()
        cursor.close()
        conn.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='市场广度计算集成器')
    parser.add_argument(
        '--mode', 
        choices=['historical', 'realtime', 'both'],
        default='historical',
        help='计算模式: historical(历史), realtime(实时), both(两者)'
    )
    parser.add_argument(
        '--days',
        type=int,
        default=90,
        help='历史数据计算天数 (默认: 90)'
    )
    parser.add_argument(
        '--threads',
        type=int,
        default=10,
        help='下载线程数 (默认: 10)'
    )
    
    args = parser.parse_args()
    
    # 根据参数选择配置
    if args.mode == 'historical':
        config = HISTORICAL_ONLY_CONFIG
    elif args.mode == 'realtime':
        config = REALTIME_ONLY_CONFIG
    else:
        config = FULL_CONFIG
    
    # 应用命令行参数
    config.historical.days = args.days
    config.historical.download_threads = args.threads
    
    # 创建并运行集成器
    integrator = MarketBreadthIntegrator(config)
    success = integrator.run()
    
    if success:
        print(f"✅ 市场广度计算完成 (模式: {args.mode})")
        exit(0)
    else:
        print(f"❌ 市场广度计算失败 (模式: {args.mode})")
        exit(1)


if __name__ == "__main__":
    main()
