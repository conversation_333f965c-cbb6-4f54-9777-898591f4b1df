"""
决策追踪器 - 记录完整的决策过程和关键节点
基于优化文档要求，提供可回溯的决策分析
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class DecisionStep:
    """决策步骤"""
    name: str
    result: Any
    reasoning: str
    confidence: float
    timestamp: datetime
    metadata: Dict = field(default_factory=dict)


@dataclass
class ConfidenceFactor:
    """置信度影响因素"""
    value: float
    impact: str
    weight: float = 1.0


class DecisionTracker:
    """记录决策过程，提供完整的决策路径"""
    
    def __init__(self):
        """初始化决策追踪器"""
        self.decisions = []
        self.current_context = {}
        self.step_counter = 0
        
    def start_decision(self, decision_type: str, inputs: Dict):
        """
        开始一个新的决策过程
        
        Args:
            decision_type: 决策类型（如'coherence_analysis', 'divergence_detection'）
            inputs: 输入数据
        """
        self.current_context = {
            'type': decision_type,
            'timestamp': datetime.now(),
            'inputs': self._serialize_inputs(inputs),
            'steps': [],
            'outputs': {},
            'confidence_factors': {},
            'overall_confidence': 0.0,
            'duration': 0.0
        }
        self.step_counter = 0
        
        logger.debug(f"开始决策过程: {decision_type}")
        
    def add_step(self, step_name: str, result: Any, 
                 reasoning: str, confidence: float = 1.0, **metadata):
        """
        添加决策步骤
        
        Args:
            step_name: 步骤名称
            result: 步骤结果
            reasoning: 推理过程
            confidence: 置信度 [0, 1]
            **metadata: 额外的元数据
        """
        if not self.current_context:
            logger.warning("尚未开始决策过程，无法添加步骤")
            return
            
        self.step_counter += 1
        
        step = DecisionStep(
            name=step_name,
            result=self._serialize_result(result),
            reasoning=reasoning,
            confidence=max(0.0, min(1.0, confidence)),
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        self.current_context['steps'].append(step)
        
        logger.debug(f"添加决策步骤 {self.step_counter}: {step_name} (置信度: {confidence:.2f})")
        
    def add_confidence_factor(self, factor: str, value: float, impact: str, weight: float = 1.0):
        """
        添加置信度影响因素
        
        Args:
            factor: 因素名称
            value: 因素值
            impact: 影响描述
            weight: 权重
        """
        if not self.current_context:
            logger.warning("尚未开始决策过程，无法添加置信度因素")
            return
            
        self.current_context['confidence_factors'][factor] = ConfidenceFactor(
            value=value,
            impact=impact,
            weight=weight
        )
        
    def complete_decision(self, final_output: Dict, overall_confidence: float):
        """
        完成决策记录
        
        Args:
            final_output: 最终输出
            overall_confidence: 总体置信度
        """
        if not self.current_context:
            logger.warning("尚未开始决策过程，无法完成决策")
            return None
            
        self.current_context['outputs'] = self._serialize_result(final_output)
        self.current_context['overall_confidence'] = max(0.0, min(1.0, overall_confidence))
        self.current_context['duration'] = (
            datetime.now() - self.current_context['timestamp']
        ).total_seconds()
        
        # 计算步骤统计
        steps = self.current_context['steps']
        if steps:
            avg_step_confidence = sum(s.confidence for s in steps) / len(steps)
            self.current_context['step_statistics'] = {
                'total_steps': len(steps),
                'avg_confidence': round(avg_step_confidence, 3),
                'min_confidence': min(s.confidence for s in steps),
                'max_confidence': max(s.confidence for s in steps)
            }
        
        # 保存到历史记录
        decision_record = self.current_context.copy()
        self.decisions.append(decision_record)
        
        logger.info(f"完成决策: {decision_record['type']} "
                   f"(总置信度: {overall_confidence:.2f}, "
                   f"耗时: {decision_record['duration']:.3f}s)")
        
        return decision_record
        
    def get_decision_summary(self) -> Dict:
        """
        获取决策摘要，供LLM理解
        
        Returns:
            Dict: 决策摘要
        """
        if not self.current_context:
            return {}
            
        return {
            'decision_type': self.current_context['type'],
            'key_inputs': self._summarize_inputs(self.current_context['inputs']),
            'decision_path': [
                {
                    'step': s.name,
                    'result': self._summarize_result(s.result),
                    'reasoning': s.reasoning,
                    'confidence': s.confidence
                }
                for s in self.current_context['steps'][-5:]  # 最近5步
            ],
            'confidence_breakdown': {
                factor: {
                    'value': cf.value,
                    'impact': cf.impact,
                    'weight': cf.weight
                }
                for factor, cf in self.current_context['confidence_factors'].items()
            },
            'final_confidence': self.current_context.get('overall_confidence', 0),
            'step_count': len(self.current_context['steps'])
        }
        
    def get_full_decision_history(self) -> List[Dict]:
        """获取完整的决策历史"""
        return [self._serialize_decision_record(record) for record in self.decisions]
        
    def get_recent_decisions(self, count: int = 5) -> List[Dict]:
        """获取最近的决策记录"""
        return [self._serialize_decision_record(record) 
                for record in self.decisions[-count:]]
        
    def export_decision_log(self, filepath: str):
        """导出决策日志到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.get_full_decision_history(), f, 
                         ensure_ascii=False, indent=2, default=str)
            logger.info(f"决策日志已导出到: {filepath}")
        except Exception as e:
            logger.error(f"导出决策日志失败: {e}")
            
    def _serialize_inputs(self, inputs: Dict) -> Dict:
        """序列化输入数据"""
        return self._serialize_result(inputs)
        
    def _serialize_result(self, result: Any) -> Any:
        """序列化结果数据"""
        if isinstance(result, (int, float, str, bool, type(None))):
            return result
        elif isinstance(result, dict):
            return {k: self._serialize_result(v) for k, v in result.items()}
        elif isinstance(result, (list, tuple)):
            return [self._serialize_result(item) for item in result]
        elif hasattr(result, '__dict__'):
            return self._serialize_result(result.__dict__)
        else:
            return str(result)
            
    def _summarize_inputs(self, inputs: Dict) -> Dict:
        """简化输入数据用于展示"""
        summary = {}
        for key, value in inputs.items():
            if isinstance(value, (int, float, str, bool)):
                summary[key] = value
            elif isinstance(value, (list, dict)):
                summary[key] = f"{type(value).__name__}[{len(value)}]"
            else:
                summary[key] = str(type(value).__name__)
        return summary
        
    def _summarize_result(self, result: Any) -> Any:
        """简化结果用于展示"""
        if isinstance(result, (int, float, str, bool, type(None))):
            return result
        elif isinstance(result, dict):
            if len(result) <= 3:
                return {k: self._summarize_result(v) for k, v in result.items()}
            else:
                return f"Dict[{len(result)} keys]"
        elif isinstance(result, (list, tuple)):
            if len(result) <= 5:
                return [self._summarize_result(item) for item in result]
            else:
                return f"List[{len(result)} items]"
        else:
            return str(type(result).__name__)
            
    def _serialize_decision_record(self, record: Dict) -> Dict:
        """序列化决策记录"""
        serialized = record.copy()
        
        # 序列化步骤
        if 'steps' in serialized:
            serialized['steps'] = [
                {
                    'name': step.name,
                    'result': step.result,
                    'reasoning': step.reasoning,
                    'confidence': step.confidence,
                    'timestamp': step.timestamp.isoformat(),
                    'metadata': step.metadata
                }
                for step in serialized['steps']
            ]
            
        # 序列化置信度因素
        if 'confidence_factors' in serialized:
            serialized['confidence_factors'] = {
                factor: {
                    'value': cf.value,
                    'impact': cf.impact,
                    'weight': cf.weight
                }
                for factor, cf in serialized['confidence_factors'].items()
            }
            
        # 序列化时间戳
        if 'timestamp' in serialized:
            serialized['timestamp'] = serialized['timestamp'].isoformat()
            
        return serialized


# 全局决策追踪器实例（可选）
_global_tracker = None

def get_global_tracker() -> DecisionTracker:
    """获取全局决策追踪器实例"""
    global _global_tracker
    if _global_tracker is None:
        _global_tracker = DecisionTracker()
    return _global_tracker

def reset_global_tracker():
    """重置全局决策追踪器"""
    global _global_tracker
    _global_tracker = DecisionTracker()
