#!/usr/bin/env python3
"""
MTF分析服务 - 简化版，集成MTF分析引擎
第一阶段：基于单一时间框架（日度）实现MTF分析流程

作者：Financial Master  
日期：2025-01-25
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.mtf_data_adapter import MTFDataAdapter, SectorBreadthData

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 简化版MTF分析结果数据结构
class MTFAnalysisResult:
    """MTF分析结果"""
    def __init__(self):
        self.timestamp = datetime.now()
        self.market_regime = "normal_market"
        self.consensus_score = 0.0
        self.signal_reliability = 0.0
        self.suggested_position = 50
        self.top_sectors = []
        self.avoid_sectors = []
        self.key_insights = []
        self.warnings = []
        self.operation_strategy = ""

        # 全市场轮动指标（按文档标准）
        self.price_dispersion = 0.0
        self.rank_velocity = 0.0
        self.volume_concentration = 0.0
        self.unified_rii = 0.0
        self.rotation_stage = "unknown"
        self.stage_probabilities = {}
        self.risk_level = "medium"
        self.breadth_factor = 0.5
        self.optimal_weights = {}


class MTFAnalyzerService:
    """MTF分析服务 - 第一阶段简化版"""
    
    def __init__(self):
        """初始化MTF分析服务"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.data_adapter = MTFDataAdapter()
        
        # 简化版配置
        self.lookback_short = 5
        self.lookback_medium = 20
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.8
        }
    
    def run_mtf_analysis(self, days: int = 30, use_real_data: bool = False) -> MTFAnalysisResult:
        """
        执行MTF分析的主入口方法
        
        :param days: 分析时间窗口（天数）
        :param use_real_data: 是否使用真实数据
        :return: MTF分析结果
        """
        try:
            data_type = "真实数据" if use_real_data else "模拟数据"
            self.logger.info(f"[MTF-ANALYSIS] 开始执行MTF分析（{days}天{data_type}）...")
            
            # 1. 获取MTF数据
            mtf_data = self.data_adapter.get_mtf_data_for_single_timeframe(days, use_real_data)
            
            if not self.data_adapter.validate_mtf_data(mtf_data):
                self.logger.error("[ERROR] MTF数据验证失败")
                return self._create_empty_result()
            
            # 2. 执行各模块分析
            breadth_analysis = self._analyze_market_breadth(mtf_data)
            rotation_analysis = self._analyze_sector_rotation(mtf_data)
            
            # 3. 综合分析结果
            unified_result = self._generate_unified_decision(breadth_analysis, rotation_analysis)
            
            # 4. 生成最终结果
            result = self._create_analysis_result(unified_result, breadth_analysis, rotation_analysis)
            
            self.logger.info("[MTF-ANALYSIS] MTF分析完成")
            self._log_analysis_summary(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"[ERROR] MTF分析失败: {e}")
            return self._create_empty_result()
    
    def save_mtf_results_to_db(self, result: MTFAnalysisResult) -> bool:
        """保存MTF分析结果到数据库"""
        try:
            import pymysql
            import os
            
            # 数据库配置
            db_config = {
                'host': os.getenv('DEFAULT_DB_HOST', 'localhost'),
                'port': int(os.getenv('DEFAULT_DB_PORT', 3306)),
                'user': os.getenv('DEFAULT_DB_USER', 'root'),
                'password': os.getenv('DEFAULT_DB_PASSWORD', ''),
                'database': os.getenv('DEFAULT_DB_NAME', 'stock_data'),
                'charset': 'utf8mb4'
            }
            
            connection = pymysql.connect(**db_config)
            with connection.cursor() as cursor:
                # 创建MTF分析结果表（如果不存在）
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS mtf_analysis_results (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    recorded_at DATETIME NOT NULL,
                    market_regime VARCHAR(50) NOT NULL,
                    consensus_score DECIMAL(5,3) NOT NULL,
                    signal_reliability DECIMAL(5,3) NOT NULL,
                    suggested_position INT NOT NULL,
                    operation_strategy VARCHAR(200),
                    top_sectors_json TEXT,
                    avoid_sectors_json TEXT,
                    key_insights_json TEXT,
                    warnings_json TEXT,
                    data_source VARCHAR(20) DEFAULT 'real',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    -- 轮动指标字段（Web端需要的关键指标）
                    price_dispersion DECIMAL(8,4) DEFAULT 0.0 COMMENT '价格离散度',
                    rank_velocity DECIMAL(8,4) DEFAULT 0.0 COMMENT '排名变化速度',
                    volume_concentration DECIMAL(8,4) DEFAULT 0.0 COMMENT '成交量集中度',
                    unified_rii DECIMAL(8,4) DEFAULT 0.0 COMMENT '统一轮动强度指数',
                    rotation_stage VARCHAR(50) DEFAULT 'unknown' COMMENT '轮动阶段',
                    stage_probabilities TEXT COMMENT '阶段概率JSON',
                    risk_level VARCHAR(20) DEFAULT 'medium' COMMENT '风险等级',
                    breadth_factor DECIMAL(8,4) DEFAULT 0.5 COMMENT '广度因子',
                    optimal_weights TEXT COMMENT '最优权重JSON',
                    INDEX idx_recorded_at (recorded_at),
                    INDEX idx_market_regime (market_regime),
                    INDEX idx_data_source (data_source),
                    INDEX idx_unified_rii (unified_rii),
                    INDEX idx_rotation_stage (rotation_stage),
                    INDEX idx_risk_level (risk_level)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """
                cursor.execute(create_table_sql)

                # 添加缺失的轮动指标字段（如果表已存在但缺少字段）
                alter_table_sqls = [
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS price_dispersion DECIMAL(8,4) DEFAULT 0.0 COMMENT '价格离散度'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS rank_velocity DECIMAL(8,4) DEFAULT 0.0 COMMENT '排名变化速度'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS volume_concentration DECIMAL(8,4) DEFAULT 0.0 COMMENT '成交量集中度'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS unified_rii DECIMAL(8,4) DEFAULT 0.0 COMMENT '统一轮动强度指数'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS rotation_stage VARCHAR(50) DEFAULT 'unknown' COMMENT '轮动阶段'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS stage_probabilities TEXT COMMENT '阶段概率JSON'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) DEFAULT 'medium' COMMENT '风险等级'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS breadth_factor DECIMAL(8,4) DEFAULT 0.5 COMMENT '广度因子'",
                    "ALTER TABLE mtf_analysis_results ADD COLUMN IF NOT EXISTS optimal_weights TEXT COMMENT '最优权重JSON'"
                ]

                for alter_sql in alter_table_sqls:
                    try:
                        cursor.execute(alter_sql)
                    except Exception as e:
                        # 忽略字段已存在的错误
                        if "Duplicate column name" not in str(e):
                            self.logger.warning(f"ALTER TABLE警告: {e}")

                # 准备数据
                import json
                top_sectors_json = json.dumps(result.top_sectors, ensure_ascii=False)
                avoid_sectors_json = json.dumps(result.avoid_sectors, ensure_ascii=False)
                key_insights_json = json.dumps(result.key_insights, ensure_ascii=False)
                warnings_json = json.dumps(result.warnings, ensure_ascii=False)
                
                # 准备轮动指标JSON数据
                stage_probabilities_json = json.dumps(result.stage_probabilities, ensure_ascii=False)
                optimal_weights_json = json.dumps(result.optimal_weights, ensure_ascii=False)

                # 插入MTF分析结果（包含轮动指标）
                insert_sql = """
                INSERT INTO mtf_analysis_results
                (recorded_at, market_regime, consensus_score, signal_reliability,
                 suggested_position, operation_strategy, top_sectors_json, avoid_sectors_json,
                 key_insights_json, warnings_json, data_source,
                 price_dispersion, rank_velocity, volume_concentration, unified_rii,
                 rotation_stage, stage_probabilities, risk_level, breadth_factor, optimal_weights)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                cursor.execute(insert_sql, (
                    result.timestamp,
                    result.market_regime,
                    result.consensus_score,
                    result.signal_reliability,
                    result.suggested_position,
                    result.operation_strategy,
                    top_sectors_json,
                    avoid_sectors_json,
                    key_insights_json,
                    warnings_json,
                    'real',  # 标记为真实数据分析
                    # 轮动指标
                    result.price_dispersion,
                    result.rank_velocity,
                    result.volume_concentration,
                    result.unified_rii,
                    result.rotation_stage,
                    stage_probabilities_json,
                    result.risk_level,
                    result.breadth_factor,
                    optimal_weights_json
                ))
                
                connection.commit()
                self.logger.info(f"[DB] MTF分析结果已保存到数据库")
                return True
                
        except Exception as e:
            self.logger.error(f"[ERROR] 保存MTF结果到数据库失败: {e}")
            return False
        finally:
            if 'connection' in locals():
                connection.close()
    
    def _analyze_market_breadth(self, mtf_data: Dict) -> Dict:
        """分析市场广度"""
        try:
            sectors_breadth = mtf_data.get('sectors_breadth', {})
            
            if not sectors_breadth:
                return {'market_health': 50, 'healthy_sectors': 0, 'weak_sectors': 0}
            
            # 计算整体市场健康度
            health_scores = []
            healthy_count = 0
            weak_count = 0
            
            for sector_name, breadth_data in sectors_breadth.items():
                # 计算板块健康度
                sector_health = self._calculate_sector_health(breadth_data)
                health_scores.append(sector_health)
                
                if sector_health > 70:
                    healthy_count += 1
                elif sector_health < 40:
                    weak_count += 1
            
            market_health = np.mean(health_scores) if health_scores else 50
            
            result = {
                'market_health': round(market_health, 2),
                'healthy_sectors': healthy_count,
                'weak_sectors': weak_count,
                'total_sectors': len(sectors_breadth),
                'breadth_dispersion': np.std(health_scores) if len(health_scores) > 1 else 0
            }
            
            self.logger.info(f"[BREADTH] 市场健康度: {market_health:.1f}, 健康板块: {healthy_count}, 弱势板块: {weak_count}")
            return result
            
        except Exception as e:
            self.logger.error(f"[ERROR] 市场广度分析失败: {e}")
            return {'market_health': 50, 'healthy_sectors': 0, 'weak_sectors': 0}
    
    def _calculate_sector_health(self, breadth_data: SectorBreadthData) -> float:
        """计算板块健康度"""
        try:
            # 涨跌比得分
            ad_ratio = breadth_data.advances / max(breadth_data.declines, 1)
            ad_score = min(ad_ratio * 20, 100)
            
            # 成交量广度得分
            total_volume = breadth_data.advancing_volume + breadth_data.declining_volume
            volume_breadth = (breadth_data.advancing_volume - breadth_data.declining_volume) / max(total_volume, 1)
            volume_score = (volume_breadth + 1) * 50
            
            # 新高新低比得分
            nh_nl_ratio = breadth_data.new_highs_52w / max(breadth_data.new_lows_52w, 1)
            nh_nl_score = min(nh_nl_ratio * 15, 100)
            
            # 均线广度得分
            ma50_breadth = breadth_data.above_ma50 / breadth_data.total_stocks
            ma200_breadth = breadth_data.above_ma200 / breadth_data.total_stocks
            ma_score = (ma50_breadth * 0.6 + ma200_breadth * 0.4) * 100
            
            # 综合健康度
            health_score = (ad_score * 0.3 + volume_score * 0.2 + 
                          nh_nl_score * 0.2 + ma_score * 0.3)
            
            return round(health_score, 2)
            
        except Exception as e:
            self.logger.error(f"[ERROR] 计算板块健康度失败: {e}")
            return 50.0
    
    def _analyze_sector_rotation(self, mtf_data: Dict) -> Dict:
        """分析板块轮动（按文档标准计算全市场指标）"""
        try:
            prices_df = mtf_data.get('prices', pd.DataFrame())
            volumes_df = mtf_data.get('volumes', pd.DataFrame())
            breadth_data = mtf_data.get('breadth_data', {})

            if prices_df.empty:
                return self._create_empty_rotation_result()

            # 1. 计算动量（按文档标准）
            momentum = self._calculate_momentum(prices_df)

            # 2. 计算相对强度（按文档标准）
            relative_strength = self._calculate_relative_strength(momentum)

            # 3. 计算全市场级别的轮动指标（按文档标准）
            price_dispersion = self._calculate_market_price_dispersion(relative_strength)
            rank_velocity = self._calculate_market_rank_velocity(momentum)
            volume_concentration = self._calculate_market_volume_concentration(volumes_df)

            # 4. 计算统一RII（按文档标准）
            breadth_metrics = self._convert_breadth_data_to_metrics(breadth_data)
            unified_rii = self._calculate_unified_rii_standard(
                price_dispersion, rank_velocity, volume_concentration, breadth_metrics
            )

            # 5. 识别轮动阶段（按文档标准）
            rotation_stage, stage_probabilities = self._identify_rotation_stage_standard(
                unified_rii, rank_velocity, breadth_metrics
            )

            # 6. 评估风险等级（按文档标准）
            risk_level = self._assess_risk_level_standard(
                unified_rii, rotation_stage, breadth_metrics
            )

            # 7. 计算广度调整因子
            breadth_factor = self._calculate_breadth_factor(breadth_metrics)

            # 8. 计算最优权重（按文档标准）
            relative_strength_dict = relative_strength.iloc[-1].to_dict() if not relative_strength.empty else {}
            optimal_weights = self._calculate_optimal_weights_standard(
                relative_strength_dict, rotation_stage, breadth_metrics, risk_level
            )

            # 找出表现最好的板块
            top_sector = relative_strength.iloc[-1].idxmax() if not relative_strength.empty else None

            result = {
                # 传统指标（保持兼容性）
                'rotation_stage': rotation_stage,
                'rotation_intensity': round(unified_rii, 3),
                'top_sector': top_sector,
                'sector_momentum': momentum.iloc[-1].to_dict() if not momentum.empty else {},
                'relative_strength': relative_strength.iloc[-1].to_dict() if not relative_strength.empty else {},

                # 文档标准的全市场指标
                'price_dispersion': round(price_dispersion, 6),
                'rank_velocity': round(rank_velocity, 6),
                'volume_concentration': round(volume_concentration, 6),
                'unified_rii': round(unified_rii, 6),
                'stage_probabilities': stage_probabilities,
                'risk_level': risk_level,
                'breadth_factor': round(breadth_factor, 6),
                'optimal_weights': optimal_weights
            }

            self.logger.info(f"[ROTATION] 轮动阶段: {rotation_stage}, 强度: {unified_rii:.3f}, 领涨板块: {top_sector}")
            return result

        except Exception as e:
            self.logger.error(f"[ERROR] 板块轮动分析失败: {e}")
            return self._create_empty_rotation_result()

    def _create_empty_rotation_result(self) -> Dict:
        """创建空的轮动分析结果"""
        return {
            'rotation_stage': 'unknown',
            'rotation_intensity': 0.0,
            'top_sector': None,
            'sector_momentum': {},
            'relative_strength': {},
            'price_dispersion': 0.0,
            'rank_velocity': 0.0,
            'volume_concentration': 0.0,
            'unified_rii': 0.0,
            'stage_probabilities': {},
            'risk_level': 'medium',
            'breadth_factor': 0.5,
            'optimal_weights': {}
        }

    def _calculate_market_price_dispersion(self, relative_strength: pd.DataFrame) -> float:
        """计算全市场价格离散度（按文档标准）"""
        try:
            if relative_strength.empty:
                return 0.0
            # 文档标准：相对强度的标准差
            return float(relative_strength.iloc[-1].std())
        except Exception:
            return 0.0

    def _calculate_market_rank_velocity(self, momentum: pd.DataFrame) -> float:
        """计算全市场排名变化速度（按文档标准）"""
        try:
            if len(momentum) < 2:
                return 0.0

            # 最近N天的排名
            lookback = min(5, len(momentum))
            recent_ranks = pd.DataFrame(index=momentum.index[-lookback:],
                                      columns=momentum.columns)

            for idx in recent_ranks.index:
                recent_ranks.loc[idx] = momentum.loc[idx].rank(ascending=False)

            # 计算排名变化
            rank_changes = recent_ranks.diff().abs().mean(axis=1)
            avg_velocity = rank_changes.mean() / len(momentum.columns)

            return float(avg_velocity)
        except Exception:
            return 0.0

    def _calculate_market_volume_concentration(self, volumes: pd.DataFrame) -> float:
        """计算全市场成交量集中度（按文档标准：HHI指数）"""
        try:
            if volumes.empty:
                return 0.1

            latest_volumes = volumes.iloc[-1]
            total_volume = latest_volumes.sum()

            if total_volume == 0:
                return 0.1

            market_shares = latest_volumes / total_volume
            hhi = (market_shares ** 2).sum()

            return float(hhi)
        except Exception:
            return 0.1

    def _calculate_momentum(self, prices_df: pd.DataFrame) -> pd.DataFrame:
        """计算动量"""
        try:
            return prices_df.pct_change(self.lookback_short) * 100
        except Exception:
            return pd.DataFrame()
    
    def _calculate_relative_strength(self, momentum: pd.DataFrame) -> pd.DataFrame:
        """计算相对强度"""
        try:
            if momentum.empty:
                return pd.DataFrame()
            
            market_momentum = momentum.mean(axis=1)
            relative_strength = momentum.div(market_momentum, axis=0)
            
            # 处理特殊值
            relative_strength = relative_strength.replace([np.inf, -np.inf], np.nan)
            mask = abs(market_momentum) < 0.01
            for col in relative_strength.columns:
                relative_strength.loc[mask, col] = 1.0
            
            return relative_strength.fillna(1.0)
            
        except Exception:
            return pd.DataFrame()

    def _calculate_unified_rii_standard(self, price_dispersion: float, rank_velocity: float,
                                      volume_concentration: float, breadth_metrics: Dict) -> float:
        """按文档标准计算统一的轮动强度指数"""
        try:
            # 价格维度RII（60%权重）
            price_rii = 0.5 * price_dispersion + 0.3 * rank_velocity + 0.2 * volume_concentration

            # 广度维度调整因子（40%权重）
            breadth_factor = self._calculate_breadth_factor(breadth_metrics)

            # 统一RII
            unified_rii = 0.6 * price_rii + 0.4 * breadth_factor

            return float(unified_rii)
        except Exception:
            return 0.0

    def _calculate_breadth_factor(self, breadth_metrics: Dict) -> float:
        """计算广度调整因子"""
        try:
            if not breadth_metrics:
                return 0.5

            # 计算广度健康度的标准差
            health_scores = [data.get('internal_health', 50) for data in breadth_metrics.values()]
            if not health_scores:
                return 0.5

            breadth_dispersion = np.std(health_scores) / 100

            # 计算平均内部健康度的反向指标
            avg_health = np.mean(health_scores)
            health_risk = (100 - avg_health) / 100

            breadth_factor = 0.6 * breadth_dispersion + 0.4 * health_risk
            return float(breadth_factor)
        except Exception:
            return 0.5

    def _identify_rotation_stage_standard(self, rii: float, velocity: float, breadth_metrics: Dict) -> tuple:
        """按文档标准识别轮动阶段"""
        try:
            # 获取平均内部健康度
            if breadth_metrics:
                health_scores = [data.get('internal_health', 50) for data in breadth_metrics.values()]
                avg_health = np.mean(health_scores) if health_scores else 70
            else:
                avg_health = 70

            # 阶段概率计算
            probs = {
                '稳定期': 0,
                '启动期': 0,
                '加速期': 0,
                '混乱期': 0,
                '收敛期': 0
            }

            # 基于规则的概率分配（按文档标准）
            if rii < 0.3 and avg_health > 70:
                probs['稳定期'] = 0.8
                probs['收敛期'] = 0.2
            elif 0.3 <= rii < 0.6 and velocity > 0.1:
                probs['启动期'] = 0.6
                probs['稳定期'] = 0.2
                probs['加速期'] = 0.2
            elif 0.6 <= rii < 0.8 and avg_health > 50:
                probs['加速期'] = 0.7
                probs['启动期'] = 0.2
                probs['混乱期'] = 0.1
            elif rii >= 0.8 or (rii > 0.6 and avg_health < 40):
                probs['混乱期'] = 0.8
                probs['加速期'] = 0.2
            else:
                probs['收敛期'] = 0.5
                probs['稳定期'] = 0.3
                probs['启动期'] = 0.2

            # 选择最高概率的阶段
            stage = max(probs.keys(), key=lambda k: probs[k])

            return stage, probs
        except Exception:
            return '未知', {}

    def _assess_risk_level_standard(self, rii: float, stage: str, breadth_metrics: Dict) -> str:
        """按文档标准评估风险等级"""
        try:
            # 基础风险评估
            if rii < 0.4:
                base_risk = 'low'
            elif rii < 0.7:
                base_risk = 'medium'
            else:
                base_risk = 'high'

            # 阶段调整
            if stage == '混乱期':
                base_risk = 'high'
            elif stage == '稳定期' and base_risk == 'high':
                base_risk = 'medium'

            # 广度调整
            if breadth_metrics:
                health_scores = [data.get('internal_health', 50) for data in breadth_metrics.values()]
                if health_scores:
                    weak_sectors = sum(1 for h in health_scores if h < 40)
                    if weak_sectors > len(health_scores) * 0.5:
                        if base_risk == 'low':
                            base_risk = 'medium'
                        elif base_risk == 'medium':
                            base_risk = 'high'

            return base_risk
        except Exception:
            return 'medium'

    def _calculate_optimal_weights_standard(self, relative_strength: Dict, stage: str,
                                          breadth_metrics: Dict, risk_level: str) -> Dict:
        """按文档标准计算最优权重"""
        try:
            if not relative_strength:
                return {}

            sectors = list(relative_strength.keys())
            weights = {sector: 0.0 for sector in sectors}

            # 获取内部健康度
            health_scores = {}
            for sector in sectors:
                if sector in breadth_metrics:
                    health_scores[sector] = breadth_metrics[sector].get('internal_health', 50)
                else:
                    health_scores[sector] = 50

            # 综合得分 = 相对强度 × 内部健康度
            combined_scores = {}
            for sector in sectors:
                rs_score = relative_strength[sector]
                health_score = health_scores[sector] / 100
                combined_scores[sector] = rs_score * (0.5 + 0.5 * health_score)

            # 筛选正分且足够高的板块
            positive_sectors = {k: v for k, v in combined_scores.items() if v > 0.05}

            if not positive_sectors:
                return weights  # 全部现金

            # 根据阶段和风险等级分配权重
            if stage == '稳定期' and risk_level != 'high':
                # 可以集中配置
                sorted_sectors = sorted(positive_sectors.items(), key=lambda x: x[1], reverse=True)
                if len(sorted_sectors) >= 3:
                    weights[sorted_sectors[0][0]] = 0.4
                    weights[sorted_sectors[1][0]] = 0.3
                    weights[sorted_sectors[2][0]] = 0.2
                    remaining = 0.1
                    other_sectors = sorted_sectors[3:]
                    if other_sectors:
                        weight_per_other = remaining / len(other_sectors)
                        for sector, _ in other_sectors:
                            weights[sector] = weight_per_other
                else:
                    # 按比例分配
                    total_score = sum(positive_sectors.values())
                    for sector, score in positive_sectors.items():
                        weights[sector] = score / total_score

            elif stage in ['混乱期', '加速期'] or risk_level == 'high':
                # 必须分散配置
                max_weight = 0.15 if stage == '混乱期' else 0.2

                # 按得分比例分配
                total_score = sum(positive_sectors.values())
                for sector, score in positive_sectors.items():
                    raw_weight = score / total_score
                    weights[sector] = min(raw_weight, max_weight)

                # 重新归一化
                total_weight = sum(weights.values())
                if total_weight > 0:
                    for sector in weights:
                        weights[sector] = weights[sector] / total_weight

            else:
                # 温和配置
                total_score = sum(positive_sectors.values())
                for sector, score in positive_sectors.items():
                    weights[sector] = score / total_score

            # 确保权重和为1或0
            total_weight = sum(weights.values())
            if total_weight > 0:
                for sector in weights:
                    weights[sector] = weights[sector] / total_weight

            return weights

        except Exception:
            return {sector: 0.0 for sector in relative_strength.keys()}

    def _convert_breadth_data_to_metrics(self, breadth_data: Dict) -> Dict:
        """将广度数据转换为指标格式"""
        try:
            metrics = {}
            for sector, data in breadth_data.items():
                if isinstance(data, dict):
                    metrics[sector] = data
                elif hasattr(data, 'internal_health'):
                    metrics[sector] = {'internal_health': data.internal_health}
                else:
                    metrics[sector] = {'internal_health': 50}
            return metrics
        except Exception:
            return {}

    def _calculate_rotation_intensity(self, relative_strength: pd.DataFrame) -> float:
        """计算轮动强度"""
        try:
            if relative_strength.empty:
                return 0.0
            
            return relative_strength.iloc[-1].std()
            
        except Exception:
            return 0.0
    
    def _identify_rotation_stage(self, intensity: float) -> str:
        """识别轮动阶段"""
        if intensity < self.risk_thresholds['low']:
            return '稳定期'
        elif intensity < self.risk_thresholds['medium']:
            return '启动期'
        elif intensity < self.risk_thresholds['high']:
            return '加速期'
        else:
            return '混乱期'
    
    def _generate_unified_decision(self, breadth_analysis: Dict, rotation_analysis: Dict) -> Dict:
        """生成综合决策"""
        try:
            market_health = breadth_analysis.get('market_health', 50)
            rotation_intensity = rotation_analysis.get('rotation_intensity', 0.0)
            rotation_stage = rotation_analysis.get('rotation_stage', 'unknown')
            
            # 计算建议仓位
            base_position = 80
            
            # 根据市场健康度调整
            if market_health < 40:
                base_position -= 30
            elif market_health < 60:
                base_position -= 15
            
            # 根据轮动阶段调整
            stage_adjustments = {
                '稳定期': 0,
                '启动期': -10,
                '加速期': -20,
                '混乱期': -40
            }
            base_position += stage_adjustments.get(rotation_stage, -10)
            
            suggested_position = max(20, min(90, base_position))
            
            # 计算一致性得分（简化版）
            consensus_score = 0.8 if market_health > 60 and rotation_intensity < 0.5 else 0.4
            
            # 计算信号可靠性
            signal_reliability = consensus_score * (market_health / 100)
            
            # 确定市场状态
            if rotation_intensity < 0.3 and market_health > 70:
                market_regime = "trending_stable"
            elif rotation_intensity > 0.7:
                market_regime = "high_rotation"
            else:
                market_regime = "normal_market"
            
            # 生成操作策略
            operation_strategy = self._generate_operation_strategy(market_regime, rotation_stage)
            
            return {
                'suggested_position': suggested_position,
                'consensus_score': round(consensus_score, 3),
                'signal_reliability': round(signal_reliability, 3),
                'market_regime': market_regime,
                'operation_strategy': operation_strategy
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 生成综合决策失败: {e}")
            return {
                'suggested_position': 50,
                'consensus_score': 0.5,
                'signal_reliability': 0.5,
                'market_regime': 'unknown',
                'operation_strategy': '观望等待'
            }
    
    def _generate_operation_strategy(self, market_regime: str, rotation_stage: str) -> str:
        """生成操作策略"""
        strategies = {
            ("trending_stable", "稳定期"): "趋势跟踪，中线持有",
            ("trending_stable", "启动期"): "逐步建仓，把握主线",
            ("high_rotation", "加速期"): "快进快出，严格止损",
            ("high_rotation", "混乱期"): "降低频率，等待明朗",
            ("normal_market", "稳定期"): "均衡配置，稳健操作"
        }
        
        return strategies.get((market_regime, rotation_stage), "谨慎观望，灵活应对")
    
    def _create_analysis_result(self, unified_decision: Dict, breadth_analysis: Dict, rotation_analysis: Dict) -> MTFAnalysisResult:
        """创建分析结果对象"""
        result = MTFAnalysisResult()
        
        # 基本信息
        result.market_regime = unified_decision.get('market_regime', 'unknown')
        result.consensus_score = unified_decision.get('consensus_score', 0.5)
        result.signal_reliability = unified_decision.get('signal_reliability', 0.5)
        result.suggested_position = unified_decision.get('suggested_position', 50)
        result.operation_strategy = unified_decision.get('operation_strategy', '观望等待')

        # 全市场轮动指标（从rotation_analysis中获取）
        result.price_dispersion = rotation_analysis.get('price_dispersion', 0.0)
        result.rank_velocity = rotation_analysis.get('rank_velocity', 0.0)
        result.volume_concentration = rotation_analysis.get('volume_concentration', 0.0)
        result.unified_rii = rotation_analysis.get('unified_rii', 0.0)
        result.rotation_stage = rotation_analysis.get('rotation_stage', 'unknown')
        result.stage_probabilities = rotation_analysis.get('stage_probabilities', {})
        result.risk_level = rotation_analysis.get('risk_level', 'medium')
        result.breadth_factor = rotation_analysis.get('breadth_factor', 0.5)
        result.optimal_weights = rotation_analysis.get('optimal_weights', {})
        
        # 获取MTF数据进行详细分析
        mtf_data = self.data_adapter.get_mtf_data_for_single_timeframe(use_real_data=True)
        
        # 板块推荐（增强版）
        result.top_sectors = self._generate_top_sectors(mtf_data, rotation_analysis)
        
        # 避免板块（新增）
        result.avoid_sectors = self._generate_avoid_sectors(mtf_data, breadth_analysis, rotation_analysis)
        
        # 关键洞察
        market_health = breadth_analysis.get('market_health', 50)
        rotation_stage = rotation_analysis.get('rotation_stage', 'unknown')
        
        result.key_insights = [
            f"市场处于{rotation_stage}，健康度{market_health:.1f}",
            f"建议仓位{result.suggested_position}%，信号可靠性{result.signal_reliability:.1%}",
            f"操作策略：{result.operation_strategy}"
        ]
        
        # 增强的风险警告系统
        result.warnings = self._generate_enhanced_warnings(breadth_analysis, rotation_analysis, result)
        
        return result
    
    def _generate_top_sectors(self, mtf_data: Dict, rotation_analysis: Dict) -> List[Dict]:
        """生成推荐板块列表"""
        top_sectors = []
        
        # 获取板块数据
        sectors_breadth = mtf_data.get('sectors_breadth', {})
        if not sectors_breadth:
            return top_sectors
        
        # 计算每个板块的综合得分
        sector_scores = {}
        
        for sector_name, breadth_data in sectors_breadth.items():
            # 健康度得分 (0-1)
            health_score = breadth_data.internal_health / 100
            
            # 模拟相对强度（实际应从价格数据计算）
            relative_strength = 1.0 + (breadth_data.internal_health - 50) / 100
            
            # 综合得分
            combined_score = relative_strength * (0.6 + 0.4 * health_score)
            sector_scores[sector_name] = {
                'score': combined_score,
                'health': breadth_data.internal_health,
                'relative_strength': relative_strength
            }
        
        # 排序并选择前3个
        sorted_sectors = sorted(sector_scores.items(), key=lambda x: x[1]['score'], reverse=True)
        
        for i, (sector, data) in enumerate(sorted_sectors[:3]):
            if data['score'] > 0.05:  # 只推荐得分足够高的板块
                weight = max(10, min(40, 30 - i * 5))  # 递减权重
                top_sectors.append({
                    'sector': sector,
                    'weight': f'{weight}%',
                    'reason': self._generate_sector_reason(data['score'], data['health'])
                })
        
        return top_sectors
    
    def _generate_avoid_sectors(self, mtf_data: Dict, breadth_analysis: Dict, rotation_analysis: Dict) -> List[Dict]:
        """按文档标准生成应避免的板块列表"""
        avoid_sectors = []

        # 获取板块数据
        sectors_breadth = mtf_data.get('sectors_breadth', {})
        if not sectors_breadth:
            return avoid_sectors

        # 获取最优权重和相对强度
        optimal_weights = rotation_analysis.get('optimal_weights', {})
        relative_strength = rotation_analysis.get('relative_strength', {})

        for sector_name, breadth_data in sectors_breadth.items():
            avoid_reasons = []

            # 1. 计算综合得分（按文档标准）
            weight_score = optimal_weights.get(sector_name, 0)
            health_score = breadth_data.internal_health / 100
            comprehensive_score = weight_score * (0.6 + 0.4 * health_score)

            # 2. 综合得分检查
            if comprehensive_score < 0:
                avoid_reasons.append("综合得分为负")
            elif comprehensive_score < 0.05:
                avoid_reasons.append("表现疲弱")

            # 3. 内部健康度检查
            health = breadth_data.internal_health
            if health < 40:
                avoid_reasons.append(f"内部健康度偏低({health:.1f})")

            # 4. 相对强度检查
            rs = relative_strength.get(sector_name, 1.0)
            if rs < 0.8:
                avoid_reasons.append(f"相对强度弱({rs:.2f})")

            # 5. 广度指标检查
            if breadth_data.ad_ratio < 0.8:
                avoid_reasons.append(f"涨跌比低({breadth_data.ad_ratio:.2f})")

            if breadth_data.volume_breadth < -0.3:
                avoid_reasons.append("成交量广度为负")

            # 6. 技术指标检查
            if breadth_data.ma200_breadth < 0.3:
                avoid_reasons.append("大部分股票跌破200日均线")

            if breadth_data.nh_nl_ratio < 0.5:
                avoid_reasons.append("新低多于新高")

            # 7. 动量检查（模拟）
            momentum = (health - 50) * 0.5  # 基于健康度模拟动量
            if momentum < -5:
                avoid_reasons.append(f"下跌动量{momentum:.1f}%")

            # 8. 判断是否需要避免
            if avoid_reasons:
                risk_level = self._assess_sector_risk_standard(comprehensive_score, avoid_reasons)
                avoid_sectors.append({
                    'sector': sector_name,
                    'score': round(comprehensive_score, 3),
                    'health': round(health, 1),
                    'momentum': round(momentum, 2),
                    'relative_strength': round(rs, 2),
                    'reasons': avoid_reasons,
                    'risk_level': risk_level,
                    'recommendation': self._generate_avoid_recommendation(risk_level)
                })

        # 按综合得分排序（得分越低风险越高）
        avoid_sectors.sort(key=lambda x: x['score'])
        return avoid_sectors[:5]

    def _assess_sector_risk_standard(self, score: float, reasons: List[str]) -> str:
        """按文档标准评估板块风险等级"""
        try:
            # 基于得分和理由数量评估风险
            if score < -0.1 or len(reasons) >= 4:
                return 'extreme'
            elif score < 0 or len(reasons) >= 3:
                return 'high'
            elif score < 0.05 or len(reasons) >= 2:
                return 'medium'
            else:
                return 'low'
        except Exception:
            return 'medium'

    def _generate_sector_reason(self, score: float, health: float) -> str:
        """生成板块推荐理由"""
        if score > 1.5 and health > 80:
            return "综合实力强劲，重点配置"
        elif score > 1.2 and health > 70:
            return "相对强度领先"
        elif score > 1.0 and health > 60:
            return "表现稳健，可以配置"
        else:
            return "谨慎参与"
    
    def _assess_sector_risk_level(self, risk_factors: int, health: float) -> str:
        """评估板块风险等级"""
        if risk_factors >= 4 or health < 30:
            return "extreme"
        elif risk_factors >= 3 or health < 40:
            return "high"
        elif risk_factors >= 2 or health < 60:
            return "medium"
        else:
            return "low"
    
    def _generate_avoid_recommendation(self, risk_level: str) -> str:
        """生成避免建议"""
        recommendations = {
            'extreme': "立即清仓，风险极高",
            'high': "尽快卖出，不宜持有",
            'medium': "逐步减仓，谨慎观望",
            'low': "暂不建议买入"
        }
        return recommendations.get(risk_level, "谨慎观望")
    
    def _generate_enhanced_warnings(self, breadth_analysis: Dict, rotation_analysis: Dict, result: MTFAnalysisResult) -> List[str]:
        """生成增强的风险警告"""
        warnings = []
        
        market_health = breadth_analysis.get('market_health', 50)
        rotation_intensity = rotation_analysis.get('rotation_intensity', 0)
        rotation_stage = rotation_analysis.get('rotation_stage', 'unknown')
        
        # 1. 市场健康度警告
        if market_health < 30:
            warnings.append("⚠️ 市场健康度极低，强烈建议减仓避险")
        elif market_health < 40:
            warnings.append("⚠️ 市场健康度较低，建议降低仓位")
        elif market_health < 50:
            warnings.append("⚠️ 市场健康度一般，需要谨慎操作")
        
        # 2. 轮动强度警告
        if rotation_intensity > 0.9:
            warnings.append("⚠️ 板块轮动极其剧烈，市场可能失控")
        elif rotation_intensity > 0.8:
            warnings.append("⚠️ 板块轮动剧烈，注意风险控制")
        elif rotation_intensity > 0.6:
            warnings.append("⚠️ 板块轮动加速，需要灵活应对")
        
        # 3. 轮动阶段警告
        if rotation_stage == '混乱期':
            warnings.append("⚠️ 市场处于混乱期，避免激进操作")
        elif rotation_stage == '加速期':
            warnings.append("⚠️ 轮动加速期，注意及时止损")
        
        # 4. 仓位警告
        if result.suggested_position < 30:
            warnings.append("⚠️ 建议仓位较低，市场风险较高")
        
        # 5. 信号可靠性警告
        if result.signal_reliability < 0.3:
            warnings.append("⚠️ 信号可靠性低，决策需要额外确认")
        
        # 6. 避免板块过多警告
        if len(result.avoid_sectors) >= 3:
            warnings.append("⚠️ 多个板块存在风险，需要重点防范")
        
        # 7. 特殊情况警告
        if result.consensus_score < 0.3:
            warnings.append("⚠️ 市场信号混乱，不建议大幅调仓")
        
        return warnings
    
    def _create_empty_result(self) -> MTFAnalysisResult:
        """创建空的分析结果"""
        result = MTFAnalysisResult()
        result.key_insights = ["数据不足，无法进行有效分析"]
        result.warnings = ["⚠️ 请检查数据源连接"]
        return result
    
    def _log_analysis_summary(self, result: MTFAnalysisResult):
        """记录分析摘要到日志"""
        self.logger.info("=" * 60)
        self.logger.info("MTF分析结果摘要")
        self.logger.info("=" * 60)
        self.logger.info(f"分析时间: {result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"市场状态: {result.market_regime}")
        self.logger.info(f"一致性得分: {result.consensus_score:.3f}")
        self.logger.info(f"信号可靠性: {result.signal_reliability:.1%}")
        self.logger.info(f"建议仓位: {result.suggested_position}%")
        self.logger.info(f"操作策略: {result.operation_strategy}")
        
        if result.top_sectors:
            self.logger.info("推荐板块:")
            for sector in result.top_sectors:
                self.logger.info(f"  - {sector['sector']}: {sector['weight']} ({sector['reason']})")
        
        if result.key_insights:
            self.logger.info("关键洞察:")
            for insight in result.key_insights:
                self.logger.info(f"  • {insight}")
        
        if result.warnings:
            self.logger.info("风险警告:")
            for warning in result.warnings:
                self.logger.info(f"  {warning}")
        
        self.logger.info("=" * 60)


if __name__ == "__main__":
    """测试MTFAnalyzerService"""
    try:
        analyzer = MTFAnalyzerService()
        
        # 执行MTF分析
        result = analyzer.run_mtf_analysis(days=30)
        
        if result.consensus_score > 0:
            print("✅ MTFAnalyzerService测试成功")
            print(f"📊 市场状态: {result.market_regime}")
            print(f"📊 建议仓位: {result.suggested_position}%")
            print(f"📊 信号可靠性: {result.signal_reliability:.1%}")
        else:
            print("❌ MTFAnalyzerService测试失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}") 