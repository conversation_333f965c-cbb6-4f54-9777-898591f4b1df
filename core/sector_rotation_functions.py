#!/usr/bin/env python3
"""
板块轮动计算函数库
提供各种板块轮动指标的计算函数
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional

def calculate_sector_momentum(prices: np.ndarray, period: int = 20) -> float:
    """
    计算板块动量
    
    :param prices: 价格数组
    :param period: 计算周期
    :return: 动量值（百分比）
    """
    try:
        if len(prices) < period + 1:
            return 0.0
        
        current_price = prices[-1]
        past_price = prices[-(period + 1)]
        
        if past_price == 0:
            return 0.0
        
        momentum = (current_price / past_price - 1) * 100
        return float(momentum)
        
    except Exception:
        return 0.0

def calculate_relative_strength(sector_prices: np.ndarray, market_prices: np.ndarray) -> float:
    """
    计算相对强度（相对于市场）
    
    :param sector_prices: 板块价格数组
    :param market_prices: 市场价格数组
    :return: 相对强度值
    """
    try:
        if len(sector_prices) < 2 or len(market_prices) < 2:
            return 1.0
        
        # 计算收益率
        sector_return = (sector_prices[-1] / sector_prices[-2] - 1) * 100
        market_return = (market_prices[-1] / market_prices[-2] - 1) * 100
        
        if abs(market_return) < 0.01:  # 避免除零
            return 1.0 + sector_return / 100
        
        relative_strength = sector_return / market_return
        return float(relative_strength) if not np.isnan(relative_strength) else 1.0
        
    except Exception:
        return 1.0

def calculate_sector_dispersion(sector_returns: Dict[str, float]) -> float:
    """
    计算板块离散度
    
    :param sector_returns: 各板块收益率字典
    :return: 离散度值
    """
    try:
        if not sector_returns:
            return 0.0
        
        returns = list(sector_returns.values())
        if len(returns) < 2:
            return 0.0
        
        dispersion = np.std(returns)
        return float(dispersion)
        
    except Exception:
        return 0.0

def calculate_rotation_velocity(prices: np.ndarray, volumes: np.ndarray) -> float:
    """
    计算轮动速度
    
    :param prices: 价格数组
    :param volumes: 成交量数组
    :return: 轮动速度值
    """
    try:
        if len(prices) < 3 or len(volumes) < 3:
            return 0.0
        
        # 计算价格变化率
        recent_prices = prices[-3:]
        price_changes = np.abs(np.diff(recent_prices))
        
        # 计算成交量变化率
        recent_volumes = volumes[-3:]
        volume_changes = np.abs(np.diff(recent_volumes))
        
        # 综合轮动速度
        price_velocity = np.mean(price_changes) / np.mean(recent_prices) * 100
        
        if len(volume_changes) > 0 and np.mean(recent_volumes) > 0:
            volume_velocity = np.mean(volume_changes) / np.mean(recent_volumes) * 100
            velocity = (price_velocity + volume_velocity) / 2
        else:
            velocity = price_velocity
        
        return float(velocity)
        
    except Exception:
        return 0.0

def calculate_sector_purity(returns: np.ndarray) -> float:
    """
    计算板块纯度（基于收益率的一致性）
    
    :param returns: 收益率数组
    :return: 纯度值（0-1）
    """
    try:
        if len(returns) == 0:
            return 0.5
        
        # 统计正负收益
        positive_returns = np.sum(returns > 0)
        negative_returns = np.sum(returns < 0)
        total_returns = len(returns)
        
        if total_returns == 0:
            return 0.5
        
        # 计算纯度
        pos_ratio = positive_returns / total_returns
        neg_ratio = negative_returns / total_returns
        purity = pos_ratio**2 + neg_ratio**2
        
        return max(0, min(1, float(purity)))
        
    except Exception:
        return 0.5

def calculate_rotation_intensity_index(prices: np.ndarray, volumes: np.ndarray) -> float:
    """
    计算轮动强度指数（RII）
    
    :param prices: 价格数组
    :param volumes: 成交量数组
    :return: 轮动强度指数
    """
    try:
        if len(prices) < 6 or len(volumes) < 6:
            return 0.0
        
        # 计算价格离散度
        recent_prices = prices[-6:]
        price_returns = np.diff(recent_prices) / recent_prices[:-1] * 100
        price_dispersion = np.std(price_returns) if len(price_returns) > 0 else 0.0
        
        # 计算轮动速度
        velocity = calculate_rotation_velocity(prices, volumes)
        
        # 计算成交量集中度
        recent_volumes = volumes[-5:]
        volume_concentration = (np.std(recent_volumes) / np.mean(recent_volumes) 
                              if np.mean(recent_volumes) > 0 else 0.0)
        
        # 综合指数
        rii = 0.4 * price_dispersion + 0.4 * velocity + 0.2 * volume_concentration
        return float(rii)
        
    except Exception:
        return 0.0

def rank_sectors(sector_scores: Dict[str, float]) -> List[Tuple[str, float]]:
    """
    对板块进行排名
    
    :param sector_scores: 板块得分字典
    :return: 排名后的板块列表 [(sector, score), ...]
    """
    try:
        if not sector_scores:
            return []
        
        # 按得分从高到低排序
        ranked = sorted(sector_scores.items(), key=lambda x: x[1], reverse=True)
        return ranked
        
    except Exception:
        return []

def calculate_sector_correlation(sector1_returns: np.ndarray, sector2_returns: np.ndarray) -> float:
    """
    计算两个板块之间的相关性
    
    :param sector1_returns: 板块1收益率数组
    :param sector2_returns: 板块2收益率数组
    :return: 相关系数
    """
    try:
        if len(sector1_returns) < 2 or len(sector2_returns) < 2:
            return 0.0
        
        # 确保数组长度一致
        min_len = min(len(sector1_returns), len(sector2_returns))
        returns1 = sector1_returns[-min_len:]
        returns2 = sector2_returns[-min_len:]
        
        correlation = np.corrcoef(returns1, returns2)[0, 1]
        return float(correlation) if not np.isnan(correlation) else 0.0
        
    except Exception:
        return 0.0

def calculate_sector_beta(sector_returns: np.ndarray, market_returns: np.ndarray) -> float:
    """
    计算板块相对于市场的Beta值
    
    :param sector_returns: 板块收益率数组
    :param market_returns: 市场收益率数组
    :return: Beta值
    """
    try:
        if len(sector_returns) < 2 or len(market_returns) < 2:
            return 1.0
        
        # 确保数组长度一致
        min_len = min(len(sector_returns), len(market_returns))
        sector_ret = sector_returns[-min_len:]
        market_ret = market_returns[-min_len:]
        
        # 计算协方差和方差
        covariance = np.cov(sector_ret, market_ret)[0, 1]
        market_variance = np.var(market_ret)
        
        if market_variance == 0:
            return 1.0
        
        beta = covariance / market_variance
        return float(beta) if not np.isnan(beta) else 1.0
        
    except Exception:
        return 1.0

def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
    """
    计算夏普比率
    
    :param returns: 收益率数组
    :param risk_free_rate: 无风险利率（年化）
    :return: 夏普比率
    """
    try:
        if len(returns) < 2:
            return 0.0
        
        # 计算超额收益
        daily_risk_free = risk_free_rate / 252  # 转换为日收益率
        excess_returns = returns - daily_risk_free
        
        # 计算夏普比率
        if np.std(excess_returns) == 0:
            return 0.0
        
        sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
        return float(sharpe) if not np.isnan(sharpe) else 0.0
        
    except Exception:
        return 0.0

def calculate_max_drawdown(prices: np.ndarray) -> float:
    """
    计算最大回撤
    
    :param prices: 价格数组
    :return: 最大回撤（百分比）
    """
    try:
        if len(prices) < 2:
            return 0.0
        
        # 计算累计最高价
        peak = np.maximum.accumulate(prices)
        
        # 计算回撤
        drawdown = (prices - peak) / peak * 100
        
        # 返回最大回撤（负值）
        max_dd = np.min(drawdown)
        return float(max_dd)
        
    except Exception:
        return 0.0

def calculate_volatility(returns: np.ndarray, annualized: bool = True) -> float:
    """
    计算波动率
    
    :param returns: 收益率数组
    :param annualized: 是否年化
    :return: 波动率
    """
    try:
        if len(returns) < 2:
            return 0.0
        
        volatility = np.std(returns)
        
        if annualized:
            volatility *= np.sqrt(252)  # 年化
        
        return float(volatility)
        
    except Exception:
        return 0.0