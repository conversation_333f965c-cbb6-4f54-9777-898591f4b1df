#!/usr/bin/env python3
"""
超高速多时间框架系统
设计理念：先批量下载，再并行计算
充分利用64核心64G内存
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import pymysql
import json
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import multiprocessing as mp
import time

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config
from mtf_historical_config import get_mtf_config

class UltraFastMTFSystem:
    """超高速多时间框架系统"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.logger = self._setup_logger()
        
        # 系统配置
        self.max_workers = 48  # 最大并行数
        self.batch_size = 48   # 每批处理48只股票
        self.download_threads = 32  # 下载线程数
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('ultra_fast_mtf')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def calculate_all_markets_ultra_fast(self, timeframes: List[str], history_months: int = 18):
        """
        超高速计算全市场多时间框架
        
        策略：
        1. 批量下载所有股票数据
        2. 48核心并行计算技术指标
        3. 实时保存结果
        """
        
        start_time = datetime.now()
        self.logger.info(f"🚀 开始超高速多时间框架计算")
        self.logger.info(f"   时间框架: {timeframes}")
        self.logger.info(f"   历史月数: {history_months}")
        self.logger.info(f"   并行核心: {self.max_workers}")
        
        # 第一步：获取所有股票列表
        symbols = self._get_all_symbols()
        self.logger.info(f"   股票总数: {len(symbols):,}")
        
        # 第二步：按时间框架逐个处理（避免内存爆炸）
        total_success = 0
        total_failed = 0
        
        for timeframe in timeframes:
            tf_start = datetime.now()
            self.logger.info(f"\n📊 开始处理 {timeframe} 时间框架...")
            
            success, failed = self._process_timeframe_ultra_fast(
                timeframe, symbols, history_months
            )
            
            total_success += success
            total_failed += failed
            
            tf_time = (datetime.now() - tf_start).total_seconds()
            self.logger.info(f"✅ {timeframe} 完成: 成功{success:,}, 失败{failed}, 耗时{tf_time:.1f}秒")
        
        total_time = (datetime.now() - start_time).total_seconds()
        self.logger.info(f"\n🎉 全部计算完成!")
        self.logger.info(f"   总耗时: {total_time/60:.1f}分钟")
        self.logger.info(f"   总成功: {total_success:,}")
        self.logger.info(f"   总失败: {total_failed}")
        
        return {
            'success_count': total_success,
            'failed_count': total_failed,
            'total_time_minutes': total_time / 60,
            'timeframes': timeframes
        }
    
    def _process_timeframe_ultra_fast(self, timeframe: str, symbols: List[str], history_months: int):
        """超高速处理单个时间框架"""
        
        # 分批处理，每批48只股票
        batches = [symbols[i:i + self.batch_size] for i in range(0, len(symbols), self.batch_size)]
        
        self.logger.info(f"   分为 {len(batches)} 批，每批 {self.batch_size} 只股票")
        
        success_count = 0
        failed_count = 0
        
        for batch_idx, symbol_batch in enumerate(batches):
            batch_start = datetime.now()
            
            self.logger.info(f"   处理第 {batch_idx + 1}/{len(batches)} 批: {len(symbol_batch)} 只股票")
            
            # 第一步：批量下载这批股票的数据
            price_data = self._download_batch_data(symbol_batch, timeframe, history_months)
            
            if not price_data:
                self.logger.warning(f"   第 {batch_idx + 1} 批数据下载失败")
                failed_count += len(symbol_batch)
                continue
            
            # 第二步：并行计算技术指标
            batch_success, batch_failed = self._calculate_batch_parallel(
                price_data, timeframe, symbol_batch
            )
            
            success_count += batch_success
            failed_count += batch_failed
            
            batch_time = (datetime.now() - batch_start).total_seconds()
            self.logger.info(f"   第 {batch_idx + 1} 批完成: 成功{batch_success}, 失败{batch_failed}, 耗时{batch_time:.1f}秒")
            
            # 每10批显示一次总进度
            if (batch_idx + 1) % 10 == 0:
                progress = (batch_idx + 1) / len(batches) * 100
                self.logger.info(f"   📈 {timeframe} 进度: {progress:.1f}% ({batch_idx + 1}/{len(batches)})")
        
        return success_count, failed_count
    
    def _download_batch_data(self, symbols: List[str], timeframe: str, history_months: int) -> Dict:
        """批量下载一批股票的数据"""
        
        try:
            # 计算时间范围
            end_date = datetime.now()
            min_days_needed = 252 * 1.4 + 200  # 确保足够计算52周指标
            actual_days_needed = max(history_months * 30, min_days_needed)
            start_date = end_date - timedelta(days=actual_days_needed)
            
            # 获取时间框架配置
            tf_config = get_mtf_config(timeframe)
            interval = tf_config['interval']
            
            # 批量下载（使用高并发）
            price_data = download_hist_price(
                symbols=symbols,
                interval=interval,
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                columns=['h', 'l', 'c', 'v'],
                threads=self.download_threads,  # 高并发下载
                verbose=False
            )
            
            return price_data
            
        except Exception as e:
            self.logger.error(f"批量下载失败: {e}")
            return {}
    
    def _calculate_batch_parallel(self, price_data: Dict, timeframe: str, symbols: List[str]):
        """并行计算一批股票的技术指标"""
        
        # 准备并行任务
        tasks = []
        for symbol in symbols:
            if symbol in price_data and price_data[symbol] is not None:
                tasks.append((symbol, price_data[symbol], timeframe))
        
        if not tasks:
            return 0, len(symbols)
        
        # 并行计算（使用进程池）
        success_count = 0
        failed_count = 0
        
        with ProcessPoolExecutor(max_workers=min(self.max_workers, len(tasks))) as executor:
            # 提交所有任务
            futures = [executor.submit(process_single_symbol, task) for task in tasks]
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result['success']:
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    self.logger.error(f"并行计算任务失败: {e}")
                    failed_count += 1
        
        # 统计未处理的股票
        unprocessed = len(symbols) - len(tasks)
        failed_count += unprocessed
        
        return success_count, failed_count
    
    def _get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics ORDER BY company")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()


def process_single_symbol(task_data):
    """
    处理单个股票的多进程工作函数
    
    Args:
        task_data: (symbol, df, timeframe)
    """
    symbol, df, timeframe = task_data
    
    try:
        # 重命名列
        df = df.rename(columns={'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'})
        df = df.sort_index()
        
        # 计算技术指标
        df = calculate_technical_indicators(df, timeframe)
        
        # 保存到数据库
        success = save_symbol_data(symbol, timeframe, df)
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': success,
            'records': len(df)
        }
        
    except Exception as e:
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': False,
            'error': str(e)
        }


def calculate_technical_indicators(df, timeframe):
    """计算技术指标（优化版）"""
    
    # 基础移动平均
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma10'] = df['close'].rolling(10).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma200'] = df['close'].rolling(200).mean()
    
    # 布尔值
    df['above_ma5'] = df['close'] > df['ma5']
    df['above_ma10'] = df['close'] > df['ma10']
    df['above_ma20'] = df['close'] > df['ma20']
    df['above_ma50'] = df['close'] > df['ma50']
    df['above_ma200'] = df['close'] > df['ma200']
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi_14'] = 100 - (100 / (1 + rs))
    
    # 52周指标（按hist_data.py的逻辑）
    if timeframe in ['1d', '1w', '1M'] and len(df) >= 20:
        window_map = {'1d': 252, '1w': 52, '1M': 12}
        window = min(len(df), window_map.get(timeframe, 252))
        
        df['high_52w'] = df['high'].rolling(window=window, min_periods=20).max()
        df['low_52w'] = df['low'].rolling(window=window, min_periods=20).min()
        df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
        df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
        df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])
    
    # 20日指标
    df['high_20d'] = df['high'].rolling(20).max()
    df['low_20d'] = df['low'].rolling(20).min()
    df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
    df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
    df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])
    
    return df


def save_symbol_data(symbol: str, timeframe: str, df) -> bool:
    """保存股票数据（优化版）"""
    
    from db_settings import get_default_db_config
    
    # 60天存储配置
    storage_config = {
        '5m': 60 * 24 * 12,   # 17280条
        '15m': 60 * 24 * 4,   # 5760条
        '1h': 60 * 24,        # 1440条
        '1d': 60,             # 60条
        '1w': 12,             # 12条
        '1M': 3               # 3条
    }
    
    recent_records = storage_config.get(timeframe, 60)
    df_recent = df.tail(recent_records)
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 准备数据
        ma_records = []
        w52_records = []
        
        for date, row in df_recent.iterrows():
            # MA记录
            ma_record = (
                symbol, timeframe, date,
                float(row['close']) if not pd.isna(row['close']) else None,
                float(row['high']) if not pd.isna(row['high']) else None,
                float(row['low']) if not pd.isna(row['low']) else None,
                int(row['volume']) if not pd.isna(row['volume']) else None,
                float(row['ma5']) if not pd.isna(row['ma5']) else None,
                float(row['ma10']) if not pd.isna(row['ma10']) else None,
                float(row['ma20']) if not pd.isna(row['ma20']) else None,
                float(row['ma50']) if not pd.isna(row['ma50']) else None,
                float(row['ma200']) if not pd.isna(row['ma200']) else None,
                bool(row['above_ma5']) if not pd.isna(row['above_ma5']) else False,
                bool(row['above_ma10']) if not pd.isna(row['above_ma10']) else False,
                bool(row['above_ma20']) if not pd.isna(row['above_ma20']) else False,
                bool(row['above_ma50']) if not pd.isna(row['above_ma50']) else False,
                bool(row['above_ma200']) if not pd.isna(row['above_ma200']) else False,
                float(row['rsi_14']) if not pd.isna(row['rsi_14']) else None
            )
            ma_records.append(ma_record)
            
            # 52周记录
            w52_record = (
                symbol, timeframe, date,
                float(row.get('high_52w', 0)) if not pd.isna(row.get('high_52w', np.nan)) else None,
                float(row.get('low_52w', 0)) if not pd.isna(row.get('low_52w', np.nan)) else None,
                float(row.get('high_20d', 0)) if not pd.isna(row.get('high_20d', np.nan)) else None,
                float(row.get('low_20d', 0)) if not pd.isna(row.get('low_20d', np.nan)) else None,
                bool(row.get('is_new_high_52w', False)),
                bool(row.get('is_new_low_52w', False)),
                bool(row.get('is_new_high_20d', False)),
                bool(row.get('is_new_low_20d', False)),
                0, 0, 0, 0,  # days_from 字段
                float(row.get('price_position_52w', 0.5)) if not pd.isna(row.get('price_position_52w', np.nan)) else 0.5,
                float(row.get('price_position_20d', 0.5)) if not pd.isna(row.get('price_position_20d', np.nan)) else 0.5
            )
            w52_records.append(w52_record)
        
        # 批量插入
        if ma_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_ma_indicators 
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE close_price = VALUES(close_price)
            """, ma_records)
        
        if w52_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_52w_indicators 
            (symbol, timeframe, datetime, high_52w, low_52w, high_20d, low_20d,
             is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
             days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
             price_position_52w, price_position_20d)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE high_52w = VALUES(high_52w)
            """, w52_records)
        
        conn.commit()
        return True
        
    except Exception as e:
        conn.rollback()
        return False
    finally:
        conn.close()


# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='超高速多时间框架系统')
    parser.add_argument('--timeframes', nargs='+', 
                       default=['5m', '15m', '1h', '1d', '1w', '1M'],
                       help='时间框架列表')
    parser.add_argument('--months', type=int, default=18, help='历史月数')
    
    args = parser.parse_args()
    
    system = UltraFastMTFSystem()
    
    print("🚀 超高速多时间框架系统")
    print("=" * 50)
    print(f"时间框架: {args.timeframes}")
    print(f"历史月数: {args.months}")
    print(f"并行核心: 48")
    print()
    
    results = system.calculate_all_markets_ultra_fast(
        timeframes=args.timeframes,
        history_months=args.months
    )
    
    print(f"\n🎉 计算完成!")
    print(f"成功: {results['success_count']:,}")
    print(f"失败: {results['failed_count']:,}")
    print(f"耗时: {results['total_time_minutes']:.1f}分钟")
