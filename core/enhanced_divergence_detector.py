#!/usr/bin/env python3
"""
增强版背离检测器 - 基于enhanced-divergence-algorithm.md文档实现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import logging

logger = logging.getLogger(__name__)

# ========================================
# 数据结构定义
# ========================================

class DivergenceType(Enum):
    """背离类型枚举"""
    NEGATIVE = "negative"  # 负背离（价涨内弱）
    POSITIVE = "positive"  # 正背离（价跌内强）
    VOLUME = "volume"      # 成交量背离
    COMPLEX = "complex"    # 复合背离
    NONE = "none"         # 无背离


@dataclass
class DivergenceResult:
    """背离检测结果"""
    sector: str
    divergence_type: DivergenceType
    base_severity: float
    enhanced_severity: float
    adjustments: Dict[str, float]
    confidence: float
    risk_level: str
    description: str
    recommendation: str
    supporting_evidence: List[str]


@dataclass
class MarketContext:
    """市场环境上下文"""
    volatility: float = 0.02          # 市场波动率
    trend_strength: float = 0.5       # 趋势强度
    market_phase: str = "normal"      # 市场阶段
    sector_correlation: float = 0.7   # 板块相关性
    

@dataclass
class HistoricalContext:
    """历史数据上下文"""
    divergence_frequency: float = 0.1  # 历史背离频率
    avg_severity: float = 0.02         # 平均严重度
    false_signal_rate: float = 0.2     # 误报率
    recovery_time: float = 5.0         # 平均恢复时间（天）


# ========================================
# 增强版背离检测算法
# ========================================

class EnhancedDivergenceDetector:
    """增强版背离检测器"""

    def __init__(self, decision_tracker: Optional[object] = None):
        """
        初始化增强版背离检测器

        Args:
            decision_tracker: 可选的决策追踪器
        """
        # 基础阈值
        self.price_threshold = 0.01  # 1%价格变化阈值
        self.ad_neutral = 1.0       # AD比率中性值
        self.tracker = decision_tracker

        # 调整系数
        self.adjustment_factors = {
            'volume_divergence': 1.2,      # 成交量背离
            'nh_nl_confirmation': 1.3,     # 新高新低确认
            'ma_divergence': 1.15,         # 均线背离
            'rsi_divergence': 1.1,         # RSI背离
            'coherence_penalty': 0.8,      # 低一致性惩罚
            'frequent_divergence': 0.8,    # 频繁背离折扣
            'high_volatility': 0.9,        # 高波动率折扣
            'trend_against': 1.25,         # 逆势背离加重
            'correlation_factor': 1.1      # 高相关性加重
        }
        
    def detect_divergence(self, 
                         price_change: float,
                         breadth_metrics: Dict,
                         historical_context: Optional[HistoricalContext] = None,
                         market_context: Optional[MarketContext] = None) -> DivergenceResult:
        """
        综合背离检测
        
        参数:
            price_change: 价格变化率
            breadth_metrics: 广度指标字典
            historical_context: 历史上下文
            market_context: 市场环境
            
        返回:
            DivergenceResult: 背离检测结果
        """
        # 默认上下文
        if historical_context is None:
            historical_context = HistoricalContext()
        if market_context is None:
            market_context = MarketContext()
            
        # 1. 基础背离检测
        div_type, base_severity = self._detect_basic_divergence(
            price_change, breadth_metrics
        )
        
        if div_type == DivergenceType.NONE:
            return self._create_no_divergence_result(
                breadth_metrics.get('market', 'Unknown')
            )
        
        # 2. 多维度验证和调整
        adjustments, evidences = self._calculate_adjustments(
            price_change, breadth_metrics, div_type,
            historical_context, market_context
        )
        
        # 3. 计算增强严重度
        total_adjustment = self._calculate_total_adjustment(adjustments)
        enhanced_severity = base_severity * total_adjustment
        
        # 4. 计算置信度
        confidence = self._calculate_confidence(
            adjustments, historical_context, len(evidences)
        )
        
        # 5. 确定风险等级
        risk_level = self._determine_risk_level(enhanced_severity, confidence)
        
        # 6. 生成描述和建议
        description = self._generate_description(
            div_type, price_change, breadth_metrics, adjustments
        )
        recommendation = self._generate_recommendation(
            div_type, risk_level, enhanced_severity, confidence
        )
        
        return DivergenceResult(
            sector=breadth_metrics.get('market', 'Unknown'),
            divergence_type=div_type,
            base_severity=round(base_severity, 4),
            enhanced_severity=round(enhanced_severity, 4),
            adjustments=adjustments,
            confidence=round(confidence, 3),
            risk_level=risk_level,
            description=description,
            recommendation=recommendation,
            supporting_evidence=evidences
        )
    
    def _detect_basic_divergence(self, price_change: float,
                                breadth_metrics: Dict) -> Tuple[DivergenceType, float]:
        """检测基础背离类型和严重度"""
        # 获取AD比率（优先使用直接提供的ad_ratio）
        ad_ratio = breadth_metrics.get('ad_ratio')

        if ad_ratio is None:
            # 如果没有直接提供ad_ratio，则从advances和declines计算
            advances = breadth_metrics.get('advances', 0)
            declines = breadth_metrics.get('declines', 0)
            total_stocks = breadth_metrics.get('total_stocks', advances + declines)

            if total_stocks > 0:
                ad_ratio = advances / total_stocks
            else:
                ad_ratio = 0.5  # 默认中性值

        # 将ad_ratio转换为涨跌比例（0-1范围）
        if ad_ratio > 1:  # 如果是涨跌比（如2.17），转换为涨跌比例
            total_ratio = ad_ratio + 1  # 涨跌比 + 1 = 总比例
            advance_ratio = ad_ratio / total_ratio  # 上涨比例
        else:
            advance_ratio = ad_ratio  # 已经是比例
        
        # 负背离：价格上涨但内部结构弱
        if price_change > self.price_threshold and advance_ratio < 0.5:
            severity = abs(price_change * (0.5 - advance_ratio))
            return DivergenceType.NEGATIVE, severity

        # 正背离：价格下跌但内部结构强
        elif price_change < -self.price_threshold and advance_ratio > 0.5:
            severity = abs(price_change * (advance_ratio - 0.5))
            return DivergenceType.POSITIVE, severity
            
        # 成交量背离
        elif self._check_volume_divergence(price_change, breadth_metrics):
            severity = abs(price_change * 0.5)  # 成交量背离基础权重较低
            return DivergenceType.VOLUME, severity
            
        return DivergenceType.NONE, 0.0
    
    def _check_volume_divergence(self, price_change: float, 
                               breadth_metrics: Dict) -> bool:
        """检查是否存在成交量背离"""
        # 这里可以根据实际的成交量数据来判断
        # 暂时使用简化逻辑
        return False
    
    def _calculate_adjustments(self, price_change: float, breadth_metrics: Dict,
                             div_type: DivergenceType,
                             hist_ctx: HistoricalContext,
                             mkt_ctx: MarketContext) -> Tuple[Dict[str, float], List[str]]:
        """计算多维度调整系数"""
        adjustments = {}
        evidences = []
        
        # 1. 新高新低比率确认
        new_highs = breadth_metrics.get('new_highs_52w', 0)
        new_lows = breadth_metrics.get('new_lows_52w', 0)
        if new_highs + new_lows > 0:
            nh_nl_ratio = new_highs / (new_highs + new_lows)
        else:
            nh_nl_ratio = 0.5
            
        if div_type == DivergenceType.NEGATIVE and nh_nl_ratio < 0.3:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新低多于新高(NH/NL={nh_nl_ratio:.2f})")
        elif div_type == DivergenceType.POSITIVE and nh_nl_ratio > 0.7:
            adjustments['nh_nl_confirmation'] = self.adjustment_factors['nh_nl_confirmation']
            evidences.append(f"新高多于新低(NH/NL={nh_nl_ratio:.2f})")
        
        # 2. 均线位置背离
        ma50_pct = breadth_metrics.get('above_ma50', 0) / breadth_metrics.get('total_stocks', 1)
        ma200_pct = breadth_metrics.get('above_ma200', 0) / breadth_metrics.get('total_stocks', 1)
        ma_health = (ma50_pct + ma200_pct) / 2
        
        if div_type == DivergenceType.NEGATIVE and ma_health < 0.4:
            adjustments['ma_divergence'] = self.adjustment_factors['ma_divergence']
            evidences.append(f"均线支撑弱(MA健康度={ma_health:.2f})")
        elif div_type == DivergenceType.POSITIVE and ma_health > 0.6:
            adjustments['ma_divergence'] = self.adjustment_factors['ma_divergence']
            evidences.append(f"均线支撑强(MA健康度={ma_health:.2f})")
        
        # 3. RSI背离
        avg_rsi = breadth_metrics.get('avg_rsi', 50)
        if div_type == DivergenceType.NEGATIVE and avg_rsi > 70:
            adjustments['rsi_divergence'] = self.adjustment_factors['rsi_divergence']
            evidences.append(f"RSI超买但价格仍涨(RSI={avg_rsi:.1f})")
        elif div_type == DivergenceType.POSITIVE and avg_rsi < 30:
            adjustments['rsi_divergence'] = self.adjustment_factors['rsi_divergence']
            evidences.append(f"RSI超卖但价格仍跌(RSI={avg_rsi:.1f})")
        
        # 4. 动量一致性惩罚
        coherence = breadth_metrics.get('momentum_coherence', 0.5)
        if coherence < 0.3:
            adjustments['coherence_penalty'] = self.adjustment_factors['coherence_penalty']
            evidences.append(f"板块内部高度分化(一致性={coherence:.2f})")
        
        # 5. 市场环境调整
        if mkt_ctx.volatility > 0.03:
            adjustments['high_volatility'] = self.adjustment_factors['high_volatility']
            evidences.append(f"市场高波动(波动率={mkt_ctx.volatility:.1%})")
        
        return adjustments, evidences

    def _calculate_total_adjustment(self, adjustments: Dict[str, float]) -> float:
        """计算总调整系数"""
        if not adjustments:
            return 1.0

        # 乘积方式组合所有调整
        total = 1.0
        for factor in adjustments.values():
            total *= factor

        # 限制最大调整幅度
        return max(0.5, min(3.0, total))

    def _calculate_confidence(self, adjustments: Dict[str, float],
                            hist_ctx: HistoricalContext,
                            evidence_count: int) -> float:
        """计算背离信号的置信度"""
        # 基础置信度
        base_confidence = 0.5

        # 证据数量加分
        evidence_bonus = min(0.3, evidence_count * 0.05)

        # 历史准确率加分
        accuracy_bonus = max(0, (1 - hist_ctx.false_signal_rate) * 0.2)

        # 调整因子一致性加分
        adjustment_values = list(adjustments.values())
        if adjustment_values:
            # 如果大多数因子都指向同一方向
            confirming_factors = sum(1 for v in adjustment_values if v > 1.0)
            consistency_bonus = (confirming_factors / len(adjustment_values)) * 0.2
        else:
            consistency_bonus = 0

        confidence = base_confidence + evidence_bonus + accuracy_bonus + consistency_bonus

        return min(0.95, confidence)  # 最高95%置信度

    def _determine_risk_level(self, severity: float, confidence: float) -> str:
        """确定风险等级"""
        # 综合风险分 = 严重度 × 置信度
        risk_score = severity * confidence

        if risk_score >= 0.05:
            return "extreme"
        elif risk_score >= 0.03:
            return "high"
        elif risk_score >= 0.015:
            return "medium"
        elif risk_score >= 0.008:
            return "low"
        else:
            return "minimal"

    def _generate_description(self, div_type: DivergenceType,
                            price_change: float,
                            breadth_metrics: Dict,
                            adjustments: Dict) -> str:
        """生成背离描述"""
        advances = breadth_metrics.get('advances', 0)
        total_stocks = breadth_metrics.get('total_stocks', 1)
        ad_ratio = advances / total_stocks if total_stocks > 0 else 0.5
        market = breadth_metrics.get('market', 'Unknown')

        if div_type == DivergenceType.NEGATIVE:
            desc = f"{market}价格上涨{price_change:.1%}，但仅{ad_ratio*100:.0f}%个股上涨"
        elif div_type == DivergenceType.POSITIVE:
            desc = f"{market}价格下跌{abs(price_change):.1%}，但{ad_ratio*100:.0f}%个股在上涨"
        elif div_type == DivergenceType.VOLUME:
            desc = f"{market}价格与成交量流向背离"
        else:
            desc = f"{market}出现复合背离"

        # 添加关键调整因素
        key_factors = [k for k, v in adjustments.items() if v > 1.1]
        if key_factors:
            desc += f"，{len(key_factors)}个因素确认背离"

        return desc

    def _generate_recommendation(self, div_type: DivergenceType,
                               risk_level: str,
                               severity: float,
                               confidence: float) -> str:
        """生成操作建议"""
        recommendations = {
            DivergenceType.NEGATIVE: {
                'extreme': "立即减仓或清仓，上涨缺乏支撑",
                'high': "减仓50%以上，密切关注",
                'medium': "逢高减仓30%，设置止损",
                'low': "谨慎持有，不追高",
                'minimal': "继续观察"
            },
            DivergenceType.POSITIVE: {
                'extreme': "积极建仓，下跌即将结束",
                'high': "分批买入，把握反弹机会",
                'medium': "小仓位试探，等待确认",
                'low': "关注但不急于行动",
                'minimal': "继续观察"
            },
            DivergenceType.VOLUME: {
                'extreme': "警惕资金异动，控制风险",
                'high': "降低仓位，观察资金流向",
                'medium': "谨慎操作，缩短持有期",
                'low': "正常持有，关注变化",
                'minimal': "可以忽略"
            }
        }

        base_rec = recommendations.get(div_type, {}).get(risk_level, "评估后操作")

        # 添加置信度说明
        if confidence < 0.6:
            base_rec += "（信号可靠性一般）"
        elif confidence > 0.8:
            base_rec += "（信号高度可靠）"

        return base_rec

    def _create_no_divergence_result(self, market: str) -> DivergenceResult:
        """创建无背离的结果"""
        return DivergenceResult(
            sector=market,
            divergence_type=DivergenceType.NONE,
            base_severity=0.0,
            enhanced_severity=0.0,
            adjustments={},
            confidence=0.0,
            risk_level="none",
            description=f"{market}价格与内部结构一致",
            recommendation="正常操作",
            supporting_evidence=[]
        )
