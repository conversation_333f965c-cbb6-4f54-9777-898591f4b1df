#!/usr/bin/env python3
"""
高性能多时间框架预计算系统 - 64核心64G优化版
支持多进程并行计算，充分利用服务器资源
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
import pymysql
import json
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import psutil
import time

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config, get_algo_db_config
from high_performance_mtf_config import (
    HIGH_PERFORMANCE_MTF_CONFIG,
    HIGH_PERFORMANCE_STRATEGIES,
    HIGH_PERFORMANCE_DB_CONFIG,
    HIGH_PERFORMANCE_PLANS,
    get_high_performance_config,
    get_optimal_process_count,
    get_optimal_batch_size,
    get_system_resource_status
)

class HighPerformanceMTFSystem:
    """高性能多时间框架预计算系统"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.algo_config = get_algo_db_config()
        self.logger = self._setup_logger()
        
        # 系统资源状态
        self.system_status = get_system_resource_status()
        self.logger.info(f"系统资源: {self.system_status['cpu_count']}核心, "
                        f"{self.system_status['memory_total_gb']:.1f}GB内存, "
                        f"推荐最大进程数: {self.system_status['recommended_max_processes']}")
        
        # 确保多时间框架表存在
        self._ensure_mtf_tables_exist()
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('high_performance_mtf')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_mtf_tables_exist(self):
        """确保多时间框架表存在"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 创建多时间框架MA指标表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_ma_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                close_price DECIMAL(12,6),
                high_price DECIMAL(12,6),
                low_price DECIMAL(12,6),
                volume BIGINT,
                ma5 DECIMAL(12,6),
                ma10 DECIMAL(12,6),
                ma20 DECIMAL(12,6),
                ma50 DECIMAL(12,6),
                ma200 DECIMAL(12,6),
                above_ma5 BOOLEAN DEFAULT FALSE,
                above_ma10 BOOLEAN DEFAULT FALSE,
                above_ma20 BOOLEAN DEFAULT FALSE,
                above_ma50 BOOLEAN DEFAULT FALSE,
                above_ma200 BOOLEAN DEFAULT FALSE,
                rsi_14 DECIMAL(8,4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe_datetime (symbol, timeframe, datetime),
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 创建多时间框架52周指标表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_52w_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                high_52w DECIMAL(12,6),
                low_52w DECIMAL(12,6),
                high_20d DECIMAL(12,6),
                low_20d DECIMAL(12,6),
                is_new_high_52w BOOLEAN DEFAULT FALSE,
                is_new_low_52w BOOLEAN DEFAULT FALSE,
                is_new_high_20d BOOLEAN DEFAULT FALSE,
                is_new_low_20d BOOLEAN DEFAULT FALSE,
                days_from_high_52w INT,
                days_from_low_52w INT,
                days_from_high_20d INT,
                days_from_low_20d INT,
                price_position_52w DECIMAL(8,6),
                price_position_20d DECIMAL(8,6),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe_datetime (symbol, timeframe, datetime),
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 创建计算状态表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_status (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                last_calculated_datetime DATETIME,
                total_records INT DEFAULT 0,
                ma_complete BOOLEAN DEFAULT FALSE,
                w52_complete BOOLEAN DEFAULT FALSE,
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe (timeframe),
                INDEX idx_last_update (last_update)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            conn.commit()
            self.logger.info("多时间框架预计算表创建/验证完成")
            
        except Exception as e:
            self.logger.error(f"创建多时间框架表失败: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def calculate_all_timeframes_ultra_fast(self, 
                                          plan_name: str = 'ultra_fast',
                                          symbols: List[str] = None,
                                          force_recalculate: bool = False) -> Dict[str, Any]:
        """
        超高速计算全部时间框架
        
        Args:
            plan_name: 计算方案名称
            symbols: 股票列表，None表示所有股票
            force_recalculate: 是否强制重新计算
        """
        start_time = datetime.now()
        
        # 获取计算方案
        plan = HIGH_PERFORMANCE_PLANS.get(plan_name, HIGH_PERFORMANCE_PLANS['ultra_fast'])
        timeframes = plan['timeframes']
        history_months = plan['history_months']
        max_processes = min(plan['max_processes'], self.system_status['recommended_max_processes'])
        
        self.logger.info(f"🚀 开始{plan['name']}计算")
        self.logger.info(f"   时间框架: {timeframes}")
        self.logger.info(f"   历史月数: {history_months}")
        self.logger.info(f"   最大进程数: {max_processes}")
        
        # 获取股票列表
        if symbols is None:
            symbols = self._get_all_symbols()
        
        total_symbols = len(symbols)
        self.logger.info(f"   股票总数: {total_symbols:,}")
        
        # 估算计算时间
        from high_performance_mtf_config import estimate_computation_time
        time_estimate = estimate_computation_time(plan_name, total_symbols)
        self.logger.info(f"   预计耗时: {time_estimate['estimated_minutes']}分钟")
        
        results = {
            'plan_name': plan_name,
            'timeframes': timeframes,
            'total_symbols': total_symbols,
            'history_months': history_months,
            'max_processes': max_processes,
            'start_time': start_time,
            'timeframe_results': {},
            'total_success': 0,
            'total_failed': 0,
            'total_records': 0
        }
        
        # 多进程并行计算各时间框架
        with ProcessPoolExecutor(max_workers=len(timeframes)) as executor:
            futures = {}
            
            for timeframe in timeframes:
                future = executor.submit(
                    self._calculate_timeframe_multiprocess,
                    timeframe, symbols, history_months, max_processes // len(timeframes), force_recalculate
                )
                futures[timeframe] = future
            
            # 收集结果
            for timeframe, future in futures.items():
                try:
                    tf_result = future.result()
                    results['timeframe_results'][timeframe] = tf_result
                    results['total_success'] += tf_result['success_count']
                    results['total_failed'] += tf_result['failed_count']
                    results['total_records'] += tf_result['total_records']
                    
                    self.logger.info(f"✅ {timeframe}: 成功{tf_result['success_count']:,}, "
                                   f"失败{tf_result['failed_count']}, "
                                   f"记录{tf_result['total_records']:,}")
                    
                except Exception as e:
                    self.logger.error(f"❌ {timeframe} 计算失败: {e}")
                    results['timeframe_results'][timeframe] = {
                        'success_count': 0,
                        'failed_count': total_symbols,
                        'total_records': 0,
                        'error': str(e)
                    }
                    results['total_failed'] += total_symbols
        
        results['end_time'] = datetime.now()
        results['total_time_seconds'] = (results['end_time'] - results['start_time']).total_seconds()
        results['total_time_minutes'] = results['total_time_seconds'] / 60
        
        self.logger.info(f"🎉 {plan['name']}计算完成!")
        self.logger.info(f"   总耗时: {results['total_time_minutes']:.1f}分钟")
        self.logger.info(f"   总成功: {results['total_success']:,}")
        self.logger.info(f"   总失败: {results['total_failed']:,}")
        self.logger.info(f"   总记录: {results['total_records']:,}")
        
        return results

    def _calculate_timeframe_multiprocess(self, timeframe: str, symbols: List[str],
                                        history_months: int, max_processes: int,
                                        force_recalculate: bool) -> Dict[str, Any]:
        """多进程计算单个时间框架"""

        self.logger.info(f"开始计算 {timeframe} 时间框架: {len(symbols):,}只股票")

        # 获取配置
        tf_config = get_high_performance_config(timeframe)
        optimal_batch_size = get_optimal_batch_size(timeframe, self.system_status['memory_available_gb'])
        optimal_processes = min(max_processes, get_optimal_process_count(timeframe, len(symbols)))

        self.logger.info(f"  批次大小: {optimal_batch_size}, 进程数: {optimal_processes}")

        # 分批处理
        symbol_batches = [symbols[i:i + optimal_batch_size]
                         for i in range(0, len(symbols), optimal_batch_size)]

        # 准备多进程参数
        worker_args = [
            (timeframe, batch, history_months, i)
            for i, batch in enumerate(symbol_batches)
        ]

        result = {
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': 0,
            'total_records': 0,
            'batches_processed': 0,
            'total_batches': len(symbol_batches)
        }

        # 多进程执行
        with ProcessPoolExecutor(max_workers=optimal_processes) as executor:
            futures = [executor.submit(calculate_symbol_batch_worker, args) for args in worker_args]

            for future in as_completed(futures):
                try:
                    batch_result = future.result()
                    result['success_count'] += batch_result['success_count']
                    result['failed_count'] += batch_result['failed_count']
                    result['total_records'] += batch_result['total_records']
                    result['batches_processed'] += 1

                    if result['batches_processed'] % 10 == 0:
                        self.logger.info(f"  {timeframe}: 已完成 {result['batches_processed']}/{result['total_batches']} 批次")

                except Exception as e:
                    self.logger.error(f"  {timeframe}: 批次处理失败 - {e}")
                    result['failed_count'] += optimal_batch_size

        self.logger.info(f"完成 {timeframe}: 成功{result['success_count']:,}, "
                        f"失败{result['failed_count']}, 记录{result['total_records']:,}")

        return result

    def _get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics ORDER BY company")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()
    
    def get_calculation_status(self) -> Dict[str, Any]:
        """获取计算状态"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 获取各时间框架的统计
            cursor.execute("""
            SELECT timeframe, 
                   COUNT(DISTINCT symbol) as symbols,
                   COUNT(*) as total_records,
                   MAX(last_calculated_datetime) as latest_date
            FROM mtf_precomputed_status 
            GROUP BY timeframe
            ORDER BY timeframe
            """)
            
            status_data = cursor.fetchall()
            
            # 获取总体统计
            cursor.execute("SELECT COUNT(*) FROM mtf_precomputed_ma_indicators")
            ma_total = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM mtf_precomputed_52w_indicators")
            w52_total = cursor.fetchone()[0]
            
            return {
                'system_resources': self.system_status,
                'total_ma_records': ma_total,
                'total_52w_records': w52_total,
                'timeframe_status': [
                    {
                        'timeframe': tf,
                        'symbols': symbols,
                        'records': records,
                        'latest_date': latest_date
                    }
                    for tf, symbols, records, latest_date in status_data
                ],
                'last_updated': datetime.now()
            }
            
        finally:
            conn.close()


def calculate_symbol_batch_worker(args):
    """多进程工作函数 - 计算股票批次"""
    timeframe, symbol_batch, history_months, worker_id = args
    
    # 设置进程内日志
    import logging
    logger = logging.getLogger(f'worker_{worker_id}')
    
    try:
        # 重新连接数据库 (每个进程独立连接)
        from db_settings import get_default_db_config
        db_config = get_default_db_config()
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=history_months * 30 + 400)  # 额外400天确保52周数据
        
        # 获取时间框架配置
        tf_config = get_high_performance_config(timeframe)
        
        success_count = 0
        failed_count = 0
        total_records = 0
        
        # 下载价格数据
        try:
            if timeframe == '1M':
                # 月度数据特殊处理
                price_data = download_hist_price(
                    symbols=symbol_batch,
                    interval='1d',
                    start=start_date.strftime('%Y-%m-%d'),
                    end=end_date.strftime('%Y-%m-%d'),
                    columns=['h', 'l', 'c', 'v'],
                    threads=min(16, len(symbol_batch)),
                    verbose=False
                )
                # 这里需要添加月度聚合逻辑
            else:
                price_data = download_hist_price(
                    symbols=symbol_batch,
                    interval=tf_config['interval'],
                    start=start_date.strftime('%Y-%m-%d'),
                    end=end_date.strftime('%Y-%m-%d'),
                    columns=['h', 'l', 'c', 'v'],
                    threads=min(16, len(symbol_batch)),
                    verbose=False
                )
            
            # 处理每只股票
            for symbol, df in price_data.items():
                if df is not None and not df.empty:
                    try:
                        # 计算技术指标并保存
                        if _process_symbol_data(symbol, timeframe, df, db_config):
                            success_count += 1
                            total_records += len(df)
                        else:
                            failed_count += 1
                    except Exception as e:
                        logger.error(f"处理 {symbol} 失败: {e}")
                        failed_count += 1
                else:
                    failed_count += 1
                    
        except Exception as e:
            logger.error(f"Worker {worker_id} 处理批次失败: {e}")
            failed_count += len(symbol_batch)
        
        return {
            'worker_id': worker_id,
            'timeframe': timeframe,
            'success_count': success_count,
            'failed_count': failed_count,
            'total_records': total_records,
            'symbols_processed': len(symbol_batch)
        }
        
    except Exception as e:
        logger.error(f"Worker {worker_id} 异常: {e}")
        return {
            'worker_id': worker_id,
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': len(symbol_batch),
            'total_records': 0,
            'error': str(e)
        }


def _process_symbol_data(symbol: str, timeframe: str, df, db_config) -> bool:
    """处理单个股票数据 - 计算指标并保存"""
    try:
        # 确保数据格式正确
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)
        df = df.sort_index()

        # 重命名列
        df = df.rename(columns={
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        })

        # 计算技术指标
        df = _calculate_technical_indicators(df)

        # 保存到数据库
        return _save_symbol_data(symbol, timeframe, df, db_config)

    except Exception as e:
        print(f"处理 {symbol} {timeframe} 失败: {e}")
        return False


def _calculate_technical_indicators(df):
    """计算技术指标"""
    # 计算移动平均线
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma10'] = df['close'].rolling(10).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma200'] = df['close'].rolling(200).mean()

    # 计算上方MA的布尔值
    df['above_ma5'] = df['close'] > df['ma5']
    df['above_ma10'] = df['close'] > df['ma10']
    df['above_ma20'] = df['close'] > df['ma20']
    df['above_ma50'] = df['close'] > df['ma50']
    df['above_ma200'] = df['close'] > df['ma200']

    # 计算RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi_14'] = 100 - (100 / (1 + rs))

    # 计算52周高低点
    df['high_52w'] = df['high'].rolling(252).max()
    df['low_52w'] = df['low'].rolling(252).min()
    df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.98
    df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.02

    # 计算20日高低点
    df['high_20d'] = df['high'].rolling(20).max()
    df['low_20d'] = df['low'].rolling(20).min()
    df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.98
    df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.02

    return df


def _save_symbol_data(symbol: str, timeframe: str, df, db_config) -> bool:
    """保存股票数据到数据库"""
    conn = pymysql.connect(**db_config)
    cursor = conn.cursor()

    try:
        # 准备MA指标数据
        ma_records = []
        w52_records = []

        for date, row in df.iterrows():
            # MA指标记录
            ma_record = (
                symbol, timeframe, date,
                float(row['close']) if not pd.isna(row['close']) else None,
                float(row['high']) if not pd.isna(row['high']) else None,
                float(row['low']) if not pd.isna(row['low']) else None,
                int(row['volume']) if not pd.isna(row['volume']) else None,
                float(row['ma5']) if not pd.isna(row['ma5']) else None,
                float(row['ma10']) if not pd.isna(row['ma10']) else None,
                float(row['ma20']) if not pd.isna(row['ma20']) else None,
                float(row['ma50']) if not pd.isna(row['ma50']) else None,
                float(row['ma200']) if not pd.isna(row['ma200']) else None,
                bool(row['above_ma5']) if not pd.isna(row['above_ma5']) else False,
                bool(row['above_ma10']) if not pd.isna(row['above_ma10']) else False,
                bool(row['above_ma20']) if not pd.isna(row['above_ma20']) else False,
                bool(row['above_ma50']) if not pd.isna(row['above_ma50']) else False,
                bool(row['above_ma200']) if not pd.isna(row['above_ma200']) else False,
                float(row['rsi_14']) if not pd.isna(row['rsi_14']) else None
            )
            ma_records.append(ma_record)

            # 52周指标记录
            w52_record = (
                symbol, timeframe, date,
                float(row['high_52w']) if not pd.isna(row['high_52w']) else None,
                float(row['low_52w']) if not pd.isna(row['low_52w']) else None,
                float(row['high_20d']) if not pd.isna(row['high_20d']) else None,
                float(row['low_20d']) if not pd.isna(row['low_20d']) else None,
                bool(row['is_new_high_52w']) if not pd.isna(row['is_new_high_52w']) else False,
                bool(row['is_new_low_52w']) if not pd.isna(row['is_new_low_52w']) else False,
                bool(row['is_new_high_20d']) if not pd.isna(row['is_new_high_20d']) else False,
                bool(row['is_new_low_20d']) if not pd.isna(row['is_new_low_20d']) else False,
                0, 0, 0, 0,  # days_from_high/low 暂时设为0
                0.5, 0.5     # price_position 暂时设为0.5
            )
            w52_records.append(w52_record)

        # 批量插入MA数据
        if ma_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_ma_indicators
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            close_price = VALUES(close_price), updated_at = CURRENT_TIMESTAMP
            """, ma_records)

        # 批量插入52周数据
        if w52_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_52w_indicators
            (symbol, timeframe, datetime, high_52w, low_52w, high_20d, low_20d,
             is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
             days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
             price_position_52w, price_position_20d)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            high_52w = VALUES(high_52w), updated_at = CURRENT_TIMESTAMP
            """, w52_records)

        # 更新状态表 (兼容现有表结构)
        cursor.execute("""
        INSERT INTO mtf_precomputed_status (symbol, timeframe, last_calculated_datetime, total_records, ma_complete, w52_complete)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        last_calculated_datetime = VALUES(last_calculated_datetime),
        total_records = VALUES(total_records),
        ma_complete = VALUES(ma_complete),
        w52_complete = VALUES(w52_complete)
        """, (symbol, timeframe, df_recent.index[-1], len(df_recent), True, True))

        conn.commit()
        return True

    except Exception as e:
        print(f"保存 {symbol} {timeframe} 数据失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()


# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='高性能多时间框架预计算系统')
    parser.add_argument('--plan', choices=['ultra_fast', 'full_power', 'maximum_performance'], 
                       default='ultra_fast', help='计算方案')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码')
    parser.add_argument('--force', action='store_true', help='强制重新计算')
    parser.add_argument('--status', action='store_true', help='查看计算状态')
    
    args = parser.parse_args()
    
    system = HighPerformanceMTFSystem()
    
    if args.status:
        print("📊 高性能多时间框架计算状态")
        print("=" * 60)
        status = system.get_calculation_status()
        
        print(f"系统资源:")
        print(f"  CPU: {status['system_resources']['cpu_count']}核心")
        print(f"  内存: {status['system_resources']['memory_total_gb']:.1f}GB")
        print(f"  推荐进程数: {status['system_resources']['recommended_max_processes']}")
        
        print(f"\n数据统计:")
        print(f"  MA指标记录: {status['total_ma_records']:,}")
        print(f"  52周指标记录: {status['total_52w_records']:,}")
        
        print(f"\n各时间框架状态:")
        for tf_status in status['timeframe_status']:
            print(f"  {tf_status['timeframe']}: {tf_status['symbols']}只股票, "
                  f"{tf_status['records']:,}条记录, 最新: {tf_status['latest_date']}")
    
    else:
        print(f"🚀 开始高性能多时间框架计算 - 方案: {args.plan}")
        
        results = system.calculate_all_timeframes_ultra_fast(
            plan_name=args.plan,
            symbols=args.symbols,
            force_recalculate=args.force
        )
        
        print(f"\n✅ 计算完成!")
        print(f"   方案: {results['plan_name']}")
        print(f"   总耗时: {results['total_time_minutes']:.1f}分钟")
        print(f"   股票数: {results['total_symbols']:,}")
        print(f"   成功: {results['total_success']:,}")
        print(f"   失败: {results['total_failed']:,}")
        print(f"   记录: {results['total_records']:,}")
        
        for tf, tf_result in results['timeframe_results'].items():
            print(f"   {tf}: 成功{tf_result['success_count']:,}, "
                  f"记录{tf_result['total_records']:,}")
