#!/usr/bin/env python3
"""
月度数据聚合器
从日线数据聚合计算月度市场广度指标
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

# 添加路径
sys.path.insert(0, 'config')
import pymysql
from db_settings import get_default_db_config

class MonthlyAggregator:
    """月度数据聚合器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('monthly_aggregator')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get_daily_data_for_aggregation(self, lookback_days: int = 30) -> List[Dict]:
        """获取用于聚合的日线数据"""
        try:
            config = get_default_db_config()
            conn = pymysql.connect(**config)
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 获取最近N天的日线数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            query = """
            SELECT * FROM market_breadth_metrics_gics 
            WHERE timeframe = '1d' 
            AND recorded_at >= %s 
            AND recorded_at <= %s
            ORDER BY market, recorded_at
            """
            
            cursor.execute(query, (start_date, end_date))
            daily_data = cursor.fetchall()
            
            conn.close()
            
            self.logger.info(f"获取到 {len(daily_data)} 条日线数据用于月度聚合")
            return daily_data
            
        except Exception as e:
            self.logger.error(f"获取日线数据失败: {e}")
            return []
    
    def aggregate_monthly_metrics(self, daily_data: List[Dict]) -> List[Dict]:
        """聚合月度指标"""
        if not daily_data:
            return []
        
        # 按市场分组
        market_groups = {}
        for record in daily_data:
            market = record['market']
            if market not in market_groups:
                market_groups[market] = []
            market_groups[market].append(record)
        
        monthly_metrics = []
        
        for market, records in market_groups.items():
            if len(records) < 1:  # 至少需要1天的数据（降低要求）
                self.logger.warning(f"市场 {market} 数据不足，跳过月度聚合")
                continue
            
            try:
                monthly_metric = self._calculate_monthly_metric(market, records)
                if monthly_metric:
                    monthly_metrics.append(monthly_metric)
            except Exception as e:
                self.logger.error(f"计算 {market} 月度指标失败: {e}")
                continue
        
        return monthly_metrics
    
    def _calculate_monthly_metric(self, market: str, daily_records: List[Dict]) -> Optional[Dict]:
        """计算单个市场的月度指标"""
        try:
            # 转换为DataFrame便于计算
            df = pd.DataFrame(daily_records)
            df['recorded_at'] = pd.to_datetime(df['recorded_at'])
            df = df.sort_values('recorded_at')
            
            # 基础聚合指标
            total_advances = df['advances'].sum()
            total_declines = df['declines'].sum()
            total_unchanged = df['unchanged'].sum()
            total_stocks = df['total_stocks'].mean()  # 使用平均值
            
            # 成交量聚合
            total_advancing_volume = df['advancing_volume'].sum()
            total_declining_volume = df['declining_volume'].sum()
            total_volume = df['total_volume'].sum()
            
            # 计算月度比率
            advance_decline_ratio = total_advances / max(total_declines, 1)
            advance_decline_line = total_advances - total_declines
            
            # 成交量比率
            if total_declining_volume > 0:
                updown_volume_ratio = total_advancing_volume / total_declining_volume
            else:
                updown_volume_ratio = float('inf') if total_advancing_volume > 0 else 1.0
            
            # RSI聚合 - 使用最新值
            avg_rsi = df['avg_rsi'].iloc[-1] if not df['avg_rsi'].isna().all() else 50.0
            
            # 内部健康度 - 使用平均值
            internal_health = df['internal_health'].mean()

            # 动量一致性 - 使用平均值 (需要在使用前定义)
            momentum_coherence = df['momentum_coherence'].mean()

            # 新高新低聚合 (使用正确的字段名)
            total_new_highs = df['new_highs_52w'].sum()
            total_new_lows = df['new_lows_52w'].sum()

            # 其他指标 - 使用现有字段
            above_ma50 = df['above_ma50'].sum()
            above_ma200 = df['above_ma200'].sum()

            # 收益率聚合 - 使用最新值或平均值
            market_cap_weighted_return = df['market_cap_weighted_return'].iloc[-1] if not df['market_cap_weighted_return'].isna().all() else 0.0
            equal_weighted_return = df['equal_weighted_return'].iloc[-1] if not df['equal_weighted_return'].isna().all() else 0.0

            # 纯度指标 - 使用平均值
            purity = df['purity'].mean() if not df['purity'].isna().all() else 0.5

            # 背离相关指标 - 使用最新值
            divergence_type = df['divergence_type'].iloc[-1] if not df['divergence_type'].isna().all() else 'none'
            divergence_severity = df['divergence_severity'].iloc[-1] if not df['divergence_severity'].isna().all() else 0.0
            divergence_confidence = df['divergence_confidence'].iloc[-1] if not df['divergence_confidence'].isna().all() else 0.0
            divergence_risk_level = df['divergence_risk_level'].iloc[-1] if not df['divergence_risk_level'].isna().all() else 'medium'

            # 详细信息 - 符合enhanced-divergence-algorithm.md标准
            enhanced_divergence_details = self._generate_enhanced_description(
                market, divergence_type, divergence_severity,
                total_advances, total_declines, total_stocks
            )
            coherence_details = self._generate_coherence_recommendation(
                divergence_type, divergence_severity, divergence_confidence,
                momentum_coherence
            )
            

            
            # 构建月度指标
            monthly_metric = {
                'market': market,
                'timeframe': '1M',
                'advances': int(total_advances),
                'declines': int(total_declines),
                'unchanged': int(total_unchanged),
                'total_stocks': int(total_stocks),
                'advancing_volume': float(total_advancing_volume),
                'declining_volume': float(total_declining_volume),
                'total_volume': float(total_volume),
                'advance_decline_ratio': float(advance_decline_ratio),
                'advance_decline_line': float(advance_decline_line),
                'updown_volume_ratio': float(updown_volume_ratio),
                'avg_rsi': float(avg_rsi),
                'internal_health': float(internal_health),
                'new_highs_52w': int(total_new_highs),
                'new_lows_52w': int(total_new_lows),
                'above_ma50': int(above_ma50),
                'above_ma200': int(above_ma200),
                'momentum_coherence': float(momentum_coherence),
                'market_cap_weighted_return': float(market_cap_weighted_return),
                'equal_weighted_return': float(equal_weighted_return),
                'purity': float(purity),
                'divergence_type': divergence_type,
                'divergence_severity': float(divergence_severity),
                'divergence_confidence': float(divergence_confidence),
                'divergence_risk_level': divergence_risk_level,
                'coherence_details': coherence_details,
                'enhanced_divergence_details': enhanced_divergence_details,
                'recorded_at': datetime.now(),
                'data_source': 'aggregated_from_daily',
                'aggregation_period_days': len(daily_records),
                'start_date': df['recorded_at'].min(),
                'end_date': df['recorded_at'].max()
            }
            
            return monthly_metric
            
        except Exception as e:
            self.logger.error(f"计算 {market} 月度指标时出错: {e}")
            return None
    
    def save_monthly_metrics(self, monthly_metrics: List[Dict]) -> bool:
        """保存月度指标到数据库"""
        if not monthly_metrics:
            self.logger.warning("没有月度指标需要保存")
            return False
        
        try:
            config = get_default_db_config()
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            # 删除现有的月度数据（避免重复）
            cursor.execute("DELETE FROM market_breadth_metrics_gics WHERE timeframe = '1M'")
            
            # 插入新的月度数据 (包含所有字段)
            insert_query = """
            INSERT INTO market_breadth_metrics_gics (
                market, timeframe, advances, declines, unchanged, total_stocks,
                advancing_volume, declining_volume, total_volume,
                avg_rsi, internal_health, new_highs_52w, new_lows_52w,
                above_ma50, above_ma200, momentum_coherence,
                market_cap_weighted_return, equal_weighted_return, purity,
                divergence_type, divergence_severity, divergence_confidence, divergence_risk_level,
                coherence_details, enhanced_divergence_details, recorded_at
            ) VALUES (
                %(market)s, %(timeframe)s, %(advances)s, %(declines)s, %(unchanged)s, %(total_stocks)s,
                %(advancing_volume)s, %(declining_volume)s, %(total_volume)s,
                %(avg_rsi)s, %(internal_health)s, %(new_highs_52w)s, %(new_lows_52w)s,
                %(above_ma50)s, %(above_ma200)s, %(momentum_coherence)s,
                %(market_cap_weighted_return)s, %(equal_weighted_return)s, %(purity)s,
                %(divergence_type)s, %(divergence_severity)s, %(divergence_confidence)s, %(divergence_risk_level)s,
                %(coherence_details)s, %(enhanced_divergence_details)s, %(recorded_at)s
            )
            """
            
            cursor.executemany(insert_query, monthly_metrics)
            conn.commit()
            conn.close()
            
            self.logger.info(f"成功保存 {len(monthly_metrics)} 条月度指标")
            return True
            
        except Exception as e:
            self.logger.error(f"保存月度指标失败: {e}")
            return False
    
    def generate_monthly_data(self, lookback_days: int = 30) -> bool:
        """生成月度数据的主函数"""
        self.logger.info("开始生成月度市场广度数据")
        
        # 1. 获取日线数据
        daily_data = self.get_daily_data_for_aggregation(lookback_days)
        if not daily_data:
            self.logger.error("没有获取到日线数据")
            return False
        
        # 2. 聚合月度指标
        monthly_metrics = self.aggregate_monthly_metrics(daily_data)
        if not monthly_metrics:
            self.logger.error("没有生成月度指标")
            return False
        
        # 3. 保存到数据库
        success = self.save_monthly_metrics(monthly_metrics)
        
        if success:
            self.logger.info(f"月度数据生成完成，共 {len(monthly_metrics)} 个市场")
        
        return success

    def _generate_enhanced_description(self, market: str, divergence_type: str,
                                     severity: float, advances: int,
                                     declines: int, total_stocks: int) -> str:
        """生成与日线、周线一致的JSON格式背离描述"""
        import json

        enhanced_details = {
            "base_severity": 0.0,
            "enhanced_severity": float(severity),
            "adjustments": {},
            "supporting_evidence": [],
            "recommendation": "正常操作" if divergence_type == 'none' else f"{divergence_type} divergence detected"
        }

        return json.dumps(enhanced_details, ensure_ascii=False)

    def _generate_coherence_recommendation(self, divergence_type: str,
                                         severity: float, confidence: float,
                                         momentum_coherence: float) -> str:
        """生成与日线、周线一致的JSON格式一致性详情"""
        import json

        # 模拟一致性数据结构，与日线、周线保持一致
        coherence_details = {
            "direction_coherence": round(momentum_coherence, 3),
            "magnitude_coherence": round(momentum_coherence * 0.9, 3),  # 略低于方向一致性
            "technical_coherence": round(momentum_coherence * 1.1, 3),  # 略高于方向一致性
            "overall_coherence": round(momentum_coherence, 3),
            "positive_stocks": 0,  # 月度聚合无法精确计算，设为0
            "negative_stocks": 0,
            "neutral_stocks": 0,
            "advance_ratio": 0.0,
            "decline_ratio": 0.0,
            "ma50_ratio": 0.0,
            "ma200_ratio": 0.0,
            "coherence_breakdown": {
                "strong_positive": 0,
                "weak_positive": 0,
                "strong_negative": 0,
                "weak_negative": 0
            },
            "coherence_type": "monthly_aggregated" if momentum_coherence > 0.6 else "monthly_mixed",
            "rsi_factor": 1.0,
            "calculation_method": "monthly_aggregation"
        }

        return json.dumps(coherence_details, ensure_ascii=False)


def generate_monthly_breadth_data(lookback_days: int = 30) -> bool:
    """生成月度市场广度数据的便捷函数"""
    aggregator = MonthlyAggregator()
    return aggregator.generate_monthly_data(lookback_days)


if __name__ == "__main__":
    # 生成月度数据
    success = generate_monthly_breadth_data()
    if success:
        print("✅ 月度数据生成成功")
    else:
        print("❌ 月度数据生成失败")
