#!/usr/bin/env python3
"""
增强版市场广度计算器 - 集成了改进的数据获取逻辑
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from market_breadth_calculator import MarketBreadthCalculator
from enhanced_data_fetcher import download_hist_price_with_fallback
from utils import download_hist_price

class EnhancedMarketBreadthCalculator(MarketBreadthCalculator):
    """增强版市场广度计算器"""
    
    def __init__(self, timeframe: str, market: str = None):
        super().__init__(timeframe, market)
        self.use_enhanced_fetcher = True
        self.max_fallback_days = 30
        self.missing_stocks_cache = set()  # 缓存已知缺失的股票
        
    def get_price_data(self, companies: List[str], start_date: str = None, end_date: str = None) -> Dict:
        """增强版价格数据获取"""
        # 计算时间范围
        if start_date is None:
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            else:
                end_dt = datetime.now()
            
            from timeframe_config import get_history_period
            period = get_history_period(self.timeframe)
            if period['type'] == 'days':
                start_dt = end_dt - timedelta(days=period['value'])
            elif period['type'] == 'weeks':
                start_dt = end_dt - timedelta(weeks=period['value'])
            elif period['type'] == 'months':
                start_dt = end_dt - timedelta(days=period['value'] * 30)  # 近似
            
            start_date = start_dt.strftime('%Y-%m-%d')
        
        self.logger.info(f"Fetching {self.timeframe} data for {len(companies)} stocks from {start_date}")
        
        # 使用增强版数据获取器
        if self.use_enhanced_fetcher:
            company_price = download_hist_price_with_fallback(
                symbols=companies,
                interval=self.config['interval'],
                start=start_date,
                end=end_date,
                columns=['h', 'l', 'c', 'v'],
                threads=10,
                max_fallback_days=self.max_fallback_days,
                verbose=False  # 避免过多日志
            )
            
            # 更新缺失股票缓存
            fetched_symbols = set(company_price.keys())
            all_symbols = set(companies)
            current_missing = all_symbols - fetched_symbols
            
            if current_missing:
                self.missing_stocks_cache.update(current_missing)
                self.logger.warning(f"Unable to fetch data for {len(current_missing)} stocks: {sorted(list(current_missing))[:5]}...")
        else:
            # 使用原始数据获取器
            company_price = download_hist_price(
                symbols=companies,
                interval=self.config['interval'],
                start=start_date,
                end=end_date,
                columns=['h', 'l', 'c', 'v'],
                threads=10
            )
        
        return company_price
    
    def get_missing_stocks_info(self) -> Dict[str, Any]:
        """获取缺失股票的信息"""
        return {
            'missing_stocks': sorted(list(self.missing_stocks_cache)),
            'missing_count': len(self.missing_stocks_cache),
            'last_updated': datetime.now().isoformat()
        }
    
    def calculate_breadth_with_fallback(self, companies: List[str]) -> Dict[str, Any]:
        """
        计算市场广度指标，包含回退策略
        """
        # 获取价格数据
        price_data = self.get_price_data(companies)
        
        # 计算基础广度指标
        basic_metrics = self.calculate_basic_breadth(price_data)
        
        # 记录数据获取情况
        total_requested = len(companies)
        total_fetched = len(price_data)
        success_rate = (total_fetched / total_requested) * 100 if total_requested > 0 else 0
        
        self.logger.info(f"Data fetch success rate: {success_rate:.1f}% ({total_fetched}/{total_requested})")
        
        # 如果成功率太低，记录警告
        if success_rate < 90:
            self.logger.warning(f"Low data fetch success rate: {success_rate:.1f}%")
        
        # 继续计算其他指标...
        # 这里可以添加其他指标的计算逻辑
        
        # 添加数据质量信息
        basic_metrics.update({
            'data_quality': {
                'total_requested': total_requested,
                'total_fetched': total_fetched,
                'success_rate': round(success_rate, 2),
                'missing_stocks': sorted(list(self.missing_stocks_cache))
            }
        })
        
        return basic_metrics

def test_enhanced_calculator():
    """测试增强版计算器"""
    print("🧪 测试增强版市场广度计算器")
    print("=" * 60)
    
    # 创建增强版计算器
    calculator = EnhancedMarketBreadthCalculator('1h')
    
    # 测试SP500股票（包含已知缺失的股票）
    test_companies = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',  # 正常股票
        'ANSS', 'HES'  # 已知缺失的股票
    ]
    
    print(f"\n📊 测试股票列表: {test_companies}")
    
    # 计算市场广度
    result = calculator.calculate_breadth_with_fallback(test_companies)
    
    print(f"\n📊 计算结果:")
    print(f"  总股票数: {result['total_stocks']}")
    print(f"  有效股票数: {result['valid_stocks']}")
    print(f"  上涨: {result['advances']}")
    print(f"  下跌: {result['declines']}")
    print(f"  不变: {result['unchanged']}")
    
    # 显示数据质量信息
    quality = result['data_quality']
    print(f"\n📊 数据质量:")
    print(f"  请求股票数: {quality['total_requested']}")
    print(f"  获取股票数: {quality['total_fetched']}")
    print(f"  成功率: {quality['success_rate']}%")
    
    if quality['missing_stocks']:
        print(f"  缺失股票: {quality['missing_stocks']}")
    
    # 获取缺失股票信息
    missing_info = calculator.get_missing_stocks_info()
    print(f"\n📊 缺失股票详情:")
    print(f"  缺失数量: {missing_info['missing_count']}")
    print(f"  缺失股票: {missing_info['missing_stocks']}")
    print(f"  更新时间: {missing_info['last_updated']}")

def compare_calculators():
    """对比原版和增强版计算器"""
    print("\n🔄 对比原版和增强版计算器")
    print("=" * 60)
    
    # 测试股票
    test_companies = ['AAPL', 'MSFT', 'ANSS', 'HES', 'GOOGL']
    
    # 原版计算器
    print("\n📊 原版计算器:")
    original_calculator = MarketBreadthCalculator('1h')
    original_data = original_calculator.get_price_data(test_companies)
    print(f"  获取到数据: {len(original_data)}/{len(test_companies)} 只股票")
    
    # 增强版计算器
    print("\n📊 增强版计算器:")
    enhanced_calculator = EnhancedMarketBreadthCalculator('1h')
    enhanced_data = enhanced_calculator.get_price_data(test_companies)
    print(f"  获取到数据: {len(enhanced_data)}/{len(test_companies)} 只股票")
    
    # 对比结果
    improvement = len(enhanced_data) - len(original_data)
    print(f"\n📊 改进效果:")
    print(f"  原版: {len(original_data)} 只股票")
    print(f"  增强版: {len(enhanced_data)} 只股票")
    print(f"  改进: +{improvement} 只股票")
    
    if improvement > 0:
        print("  ✅ 增强版成功恢复了更多股票数据")
    elif improvement == 0:
        print("  ⚠️  两个版本结果相同（可能是数据源限制）")
    else:
        print("  ❌ 增强版结果更差（不应该发生）")

def main():
    """主函数"""
    test_enhanced_calculator()
    compare_calculators()

if __name__ == "__main__":
    main()
