#!/usr/bin/env python3
"""
测试市场分化是否正确
验证不同市场的数据是否真的不同
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
config_path = os.path.join(parent_dir, 'config')
if config_path not in sys.path:
    sys.path.insert(0, config_path)

import pymysql
from db_settings import get_default_db_config

def test_market_differentiation():
    """测试市场分化"""
    print("=== 测试市场分化 ===")
    
    try:
        # 连接数据库
        db_config = get_default_db_config()
        conn = pymysql.Connection(**db_config)
        cursor = conn.cursor()
        
        # 查询最新的数据
        sql = """
        SELECT market, recorded_at, total_stocks, advances, declines, avg_rsi
        FROM market_breadth_metrics_gics 
        ORDER BY recorded_at DESC, market
        LIMIT 50
        """
        
        cursor.execute(sql)
        results = cursor.fetchall()
        
        if not results:
            print("❌ 数据库中没有数据")
            return False
        
        print(f"✅ 找到 {len(results)} 条记录")
        
        # 按日期分组
        date_groups = {}
        for market, recorded_at, total_stocks, advances, declines, avg_rsi in results:
            date_str = recorded_at.strftime('%Y-%m-%d')
            if date_str not in date_groups:
                date_groups[date_str] = []
            date_groups[date_str].append({
                'market': market,
                'total_stocks': total_stocks,
                'advances': advances,
                'declines': declines,
                'avg_rsi': avg_rsi
            })
        
        # 检查每个日期的市场数据是否不同
        all_different = True
        for date_str, markets in date_groups.items():
            print(f"\n📅 {date_str} 的市场数据:")
            
            # 检查是否所有市场数据都相同
            if len(markets) > 1:
                first_market = markets[0]
                same_data = True
                
                for market in markets[1:]:
                    if (market['total_stocks'] != first_market['total_stocks'] or
                        market['advances'] != first_market['advances'] or
                        market['declines'] != first_market['declines'] or
                        abs(market['avg_rsi'] - first_market['avg_rsi']) > 0.1):
                        same_data = False
                        break
                
                if same_data:
                    print(f"❌ 所有市场数据相同！")
                    all_different = False
                    # 显示相同的数据
                    print(f"   相同数据: 总股票={first_market['total_stocks']}, "
                          f"上涨={first_market['advances']}, 下跌={first_market['declines']}, "
                          f"平均RSI={first_market['avg_rsi']:.2f}")
                else:
                    print(f"✅ 市场数据不同")
            
            # 显示各市场的详细数据
            for market_data in markets[:5]:  # 只显示前5个市场
                print(f"   {market_data['market']}: "
                      f"股票={market_data['total_stocks']}, "
                      f"上涨={market_data['advances']}, "
                      f"下跌={market_data['declines']}, "
                      f"RSI={market_data['avg_rsi']:.2f}")
        
        conn.close()
        
        if all_different:
            print("\n🎉 市场分化测试通过！不同市场有不同的数据")
        else:
            print("\n❌ 市场分化测试失败！存在相同数据的市场")
        
        return all_different
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_distribution():
    """测试股票分布"""
    print("\n=== 测试股票分布 ===")
    
    try:
        from historical_breadth_calculator import get_companies_by_gics_sector
        
        markets = [
            'Technology', 'Healthcare', 'Financial Services', 'Consumer Cyclical',
            'Communication Services', 'Industrials', 'Consumer Defensive'
        ]
        
        total_unique_stocks = set()
        
        for market in markets:
            companies = get_companies_by_gics_sector(market)
            total_unique_stocks.update(companies)
            print(f"📊 {market}: {len(companies)} 只股票")
            
            # 显示前几只股票作为示例
            if companies:
                sample_stocks = companies[:5]
                print(f"   示例: {', '.join(sample_stocks)}")
        
        print(f"\n📈 总计: {len(total_unique_stocks)} 只不重复股票")
        
        # 检查是否有重叠
        overlaps = 0
        for i, market1 in enumerate(markets):
            companies1 = set(get_companies_by_gics_sector(market1))
            for market2 in markets[i+1:]:
                companies2 = set(get_companies_by_gics_sector(market2))
                overlap = companies1.intersection(companies2)
                if overlap:
                    overlaps += len(overlap)
                    print(f"⚠️ {market1} 和 {market2} 有 {len(overlap)} 只重叠股票")
        
        if overlaps == 0:
            print("✅ 没有股票重叠，分组正确")
        else:
            print(f"⚠️ 总共有 {overlaps} 只股票重叠")
        
        return len(total_unique_stocks) > 100  # 至少应该有100只股票
        
    except Exception as e:
        print(f"❌ 股票分布测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试市场分化...")
    print("=" * 50)
    
    # 测试市场分化
    test1_result = test_market_differentiation()
    
    # 测试股票分布
    test2_result = test_stock_distribution()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！市场分化正常工作")
        print("\n✅ 确认:")
        print("- 不同市场有不同的股票组合")
        print("- 不同市场的广度指标不同")
        print("- 股票分布合理")
    else:
        print("❌ 部分测试失败")
        if not test1_result:
            print("- 市场数据仍然相同，需要进一步修复")
        if not test2_result:
            print("- 股票分布有问题")

if __name__ == "__main__":
    main()
