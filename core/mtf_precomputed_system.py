#!/usr/bin/env python3
"""
多时间框架预计算系统
支持5分钟到月线的所有时间框架的历史数据预计算
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging
import pymysql
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import json
from dataclasses import dataclass

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

from utils import download_hist_price
from db_settings import get_default_db_config
from mtf_historical_config import get_mtf_config, get_supported_timeframes

@dataclass
class TimeframeConfig:
    """时间框架配置"""
    interval: str
    display_name: str
    history_days: int
    min_data_points: int
    supports_ma: bool
    supports_52w: bool
    batch_size: int

class MTFPrecomputedSystem:
    """多时间框架预计算系统"""
    
    def __init__(self):
        self.db_config = get_default_db_config()
        self.logger = self._setup_logger()
        self.supported_timeframes = get_supported_timeframes()
        self._ensure_mtf_tables_exist()
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('mtf_precomputed')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_mtf_tables_exist(self):
        """创建多时间框架预计算表"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            # 多时间框架MA指标表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_ma_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                close_price DECIMAL(12,6),
                high_price DECIMAL(12,6),
                low_price DECIMAL(12,6),
                volume BIGINT,
                ma5 DECIMAL(12,6),
                ma10 DECIMAL(12,6),
                ma20 DECIMAL(12,6),
                ma50 DECIMAL(12,6),
                ma200 DECIMAL(12,6),
                above_ma5 BOOLEAN DEFAULT FALSE,
                above_ma10 BOOLEAN DEFAULT FALSE,
                above_ma20 BOOLEAN DEFAULT FALSE,
                above_ma50 BOOLEAN DEFAULT FALSE,
                above_ma200 BOOLEAN DEFAULT FALSE,
                rsi_14 DECIMAL(8,4),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe_datetime (symbol, timeframe, datetime),
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 多时间框架52周指标表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_52w_indicators (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                datetime DATETIME NOT NULL,
                close_price DECIMAL(12,6),
                high_price DECIMAL(12,6),
                low_price DECIMAL(12,6),
                volume BIGINT,
                high_52w DECIMAL(12,6),
                low_52w DECIMAL(12,6),
                high_20d DECIMAL(12,6),
                low_20d DECIMAL(12,6),
                is_new_high_52w BOOLEAN DEFAULT FALSE,
                is_new_low_52w BOOLEAN DEFAULT FALSE,
                is_new_high_20d BOOLEAN DEFAULT FALSE,
                is_new_low_20d BOOLEAN DEFAULT FALSE,
                days_from_high_52w INT DEFAULT 0,
                days_from_low_52w INT DEFAULT 0,
                days_from_high_20d INT DEFAULT 0,
                days_from_low_20d INT DEFAULT 0,
                price_position_52w DECIMAL(8,4),  -- 在52周区间的位置 (0-1)
                price_position_20d DECIMAL(8,4),  -- 在20日区间的位置 (0-1)
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe_datetime (symbol, timeframe, datetime),
                INDEX idx_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe_datetime (timeframe, datetime),
                INDEX idx_datetime (datetime),
                INDEX idx_new_high_52w (is_new_high_52w),
                INDEX idx_new_low_52w (is_new_low_52w)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            # 预计算状态跟踪表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS mtf_precomputed_status (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                last_calculated_datetime DATETIME,
                total_records INT DEFAULT 0,
                ma_complete BOOLEAN DEFAULT FALSE,
                w52_complete BOOLEAN DEFAULT FALSE,
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_symbol_timeframe (symbol, timeframe),
                INDEX idx_timeframe (timeframe),
                INDEX idx_last_update (last_update)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """)
            
            conn.commit()
            self.logger.info("多时间框架预计算表创建/验证完成")
            
        except Exception as e:
            self.logger.error(f"创建多时间框架预计算表失败: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()
    
    def calculate_historical_data(self,
                                symbols: List[str] = None,
                                timeframes: List[str] = None,
                                history_months: int = 3,
                                force_recalculate: bool = False,
                                max_workers: int = 1) -> Dict[str, Any]:
        """
        计算历史数据

        Args:
            symbols: 股票代码列表，None表示所有股票
            timeframes: 时间框架列表，None表示所有时间框架
            history_months: 历史数据月数
            force_recalculate: 是否强制重新计算
            max_workers: 最大并行进程数，1表示单进程
        """
        if symbols is None:
            symbols = self.get_all_symbols()
        
        if timeframes is None:
            timeframes = self.supported_timeframes
        
        self.logger.info(f"开始历史数据计算: {len(symbols)}只股票, {len(timeframes)}个时间框架, {history_months}个月历史")
        
        results = {
            'total_symbols': len(symbols),
            'total_timeframes': len(timeframes),
            'success_count': 0,
            'failed_count': 0,
            'timeframe_results': {},
            'start_time': datetime.now(),
            'end_time': None
        }
        
        # 按时间框架分别处理
        for timeframe in timeframes:
            self.logger.info(f"处理时间框架: {timeframe}")
            
            tf_result = self._calculate_timeframe_historical_data(
                symbols, timeframe, history_months, force_recalculate, max_workers
            )
            
            results['timeframe_results'][timeframe] = tf_result
            results['success_count'] += tf_result['success_count']
            results['failed_count'] += tf_result['failed_count']
        
        results['end_time'] = datetime.now()
        results['total_time'] = (results['end_time'] - results['start_time']).total_seconds()
        
        self.logger.info(f"历史数据计算完成: 成功={results['success_count']}, 失败={results['failed_count']}, 耗时={results['total_time']:.1f}秒")
        
        return results
    
    def _calculate_timeframe_historical_data(self,
                                           symbols: List[str],
                                           timeframe: str,
                                           history_months: int,
                                           force_recalculate: bool,
                                           max_workers: int = 1) -> Dict[str, Any]:
        """计算单个时间框架的历史数据"""
        
        tf_config = get_mtf_config(timeframe)
        
        # 计算数据获取的时间范围
        end_date = datetime.now()
        # 确保有足够数据计算技术指标：52周需要252个交易日，约等于365个日历日，额外加200天缓冲
        min_days_needed = 252 * 1.4 + 200  # 252个交易日 * 1.4倍(考虑周末节假日) + 200天缓冲 = 553天
        actual_days_needed = max(history_months * 30, min_days_needed)
        start_date = end_date - timedelta(days=actual_days_needed)
        
        result = {
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': 0,
            'total_records': 0,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        }
        
        # 根据并行设置调整批处理
        if max_workers > 1:
            # 并行处理：每个进程处理一只股票
            result = self._process_symbols_parallel(symbols, timeframe, start_date, end_date, force_recalculate, max_workers)
        else:
            # 单进程处理：分批处理股票
            batch_size = tf_config.get('batch_size', 50)

            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                self.logger.info(f"  处理批次 {i//batch_size + 1}/{(len(symbols)-1)//batch_size + 1}: {len(batch_symbols)}只股票")

                try:
                    # 特殊处理月度数据 - 从日线数据聚合
                    if timeframe == '1M':
                        price_data = self._aggregate_monthly_data_from_daily(
                            batch_symbols, start_date, end_date
                        )
                    else:
                        # 获取价格数据
                        price_data = download_hist_price(
                            symbols=batch_symbols,
                            interval=tf_config['interval'],
                            start=start_date.strftime('%Y-%m-%d'),
                            end=end_date.strftime('%Y-%m-%d'),
                            columns=['h', 'l', 'c', 'v'],
                            threads=min(10, len(batch_symbols)),
                            verbose=False
                        )

                    # 处理每只股票的数据
                    for symbol, df in price_data.items():
                        if self._process_symbol_data(symbol, timeframe, df, force_recalculate):
                            result['success_count'] += 1
                            result['total_records'] += len(df) if df is not None and not df.empty else 0
                        else:
                            result['failed_count'] += 1

                except Exception as e:
                    self.logger.error(f"  批次处理失败: {e}")
                result['failed_count'] += len(batch_symbols)
        
        return result

    def _process_symbols_parallel(self, symbols: List[str], timeframe: str, start_date: datetime, end_date: datetime, force_recalculate: bool, max_workers: int) -> Dict[str, Any]:
        """并行处理股票列表 - 带重试机制"""

        result = {
            'timeframe': timeframe,
            'success_count': 0,
            'failed_count': 0,
            'total_records': 0,
            'retry_count': 0,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        }

        self.logger.info(f"  开始并行处理 {len(symbols)} 只股票，使用 {max_workers} 个进程")

        # 分批处理，每批使用max_workers个进程
        batch_size = max_workers
        batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]

        # 第一轮处理
        failed_symbols = []

        for batch_idx, symbol_batch in enumerate(batches):
            self.logger.info(f"  处理第 {batch_idx + 1}/{len(batches)} 批: {len(symbol_batch)} 只股票")

            batch_failed = self._process_batch_with_retry(
                symbol_batch, timeframe, start_date, end_date, force_recalculate, max_workers, result
            )
            failed_symbols.extend(batch_failed)

        # 多轮重试失败的股票
        retry_round = 1
        max_retries = 3

        while failed_symbols and retry_round <= max_retries:
            self.logger.info(f"  第{retry_round}轮重试: {len(failed_symbols)} 只失败的股票")

            # 重试策略：逐步减少并发度，增加稳定性
            if retry_round == 1:
                retry_workers = min(max_workers // 2, 12)  # 第1次重试：减半
                retry_batch_size = 5
                delay = 0.5
            elif retry_round == 2:
                retry_workers = min(max_workers // 4, 6)   # 第2次重试：减少到1/4
                retry_batch_size = 3
                delay = 1.0
            else:  # retry_round == 3
                retry_workers = 1                          # 第3次重试：单进程
                retry_batch_size = 1
                delay = 2.0

            self.logger.info(f"    重试配置: {retry_workers}个worker, 批次大小{retry_batch_size}, 延迟{delay}秒")

            retry_batches = [failed_symbols[i:i + retry_batch_size]
                           for i in range(0, len(failed_symbols), retry_batch_size)]

            current_round_failed = []

            for batch_idx, retry_batch in enumerate(retry_batches):
                self.logger.info(f"    处理第{batch_idx+1}/{len(retry_batches)}批: {len(retry_batch)}只股票")

                # 添加延迟避免竞争
                import time
                time.sleep(delay)

                batch_failed = self._process_batch_with_retry(
                    retry_batch, timeframe, start_date, end_date, force_recalculate,
                    retry_workers, result, is_retry=True
                )
                current_round_failed.extend(batch_failed)

            # 更新失败列表为本轮仍然失败的股票
            failed_symbols = current_round_failed
            retry_round += 1

        # 最终仍然失败的股票
        if failed_symbols:
            self.logger.warning(f"  经过{max_retries}轮重试后，仍有{len(failed_symbols)}只股票失败: {failed_symbols}")
            result['final_failed_symbols'] = failed_symbols

        return result

    def _process_batch_with_retry(self, symbol_batch: List[str], timeframe: str, start_date: datetime,
                                end_date: datetime, force_recalculate: bool, max_workers: int,
                                result: Dict[str, Any], is_retry: bool = False) -> List[str]:
        """处理一批股票，返回失败的股票列表"""

        failed_symbols = []

        # 准备任务参数
        tasks = []
        for symbol in symbol_batch:
            tasks.append((symbol, timeframe, start_date, end_date, force_recalculate))

        # 使用进程池并行处理
        with ProcessPoolExecutor(max_workers=min(max_workers, len(tasks))) as executor:
            # 提交所有任务
            futures = [executor.submit(process_single_symbol_mtf_worker, task) for task in tasks]

            # 收集结果
            for future in as_completed(futures):
                try:
                    task_result = future.result()
                    if task_result['success']:
                        result['success_count'] += 1
                        result['total_records'] += task_result['records']

                        retry_prefix = "重试成功" if is_retry else "保存"
                        self.logger.info(f"{retry_prefix} {task_result['symbol']} {timeframe}: "
                                       f"总计算{task_result['total_calculated']}条，实际存储{task_result['records']}条")

                        if is_retry:
                            result['retry_count'] += 1
                    else:
                        failed_symbols.append(task_result['symbol'])
                        if not is_retry:  # 只在第一次失败时计入失败数
                            result['failed_count'] += 1

                        error_msg = task_result.get('error', 'Unknown error')
                        retry_prefix = "重试仍失败" if is_retry else "失败"
                        self.logger.warning(f"  {task_result['symbol']} {retry_prefix}: {error_msg.split(chr(10))[0]}")

                except Exception as e:
                    # 无法确定是哪个股票，记录为未知失败
                    if not is_retry:
                        result['failed_count'] += 1
                    self.logger.error(f"  任务执行失败: {e}")

        return failed_symbols

    def _process_symbol_data(self, symbol: str, timeframe: str, df: pd.DataFrame, force_recalculate: bool) -> bool:
        """处理单个股票的数据"""
        if df is None or df.empty:
            return False

        try:
            # 检查是否需要重新计算
            if not force_recalculate and self._is_symbol_up_to_date(symbol, timeframe, df.index[-1]):
                return True

            # 确保数据格式正确
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            df = df.sort_index()

            # 重命名列
            df = df.rename(columns={
                'h': 'high',
                'l': 'low',
                'c': 'close',
                'v': 'volume'
            })

            # 计算技术指标
            df_with_indicators = self._calculate_technical_indicators(df, timeframe)

            # 保存到数据库
            success = self._save_symbol_data(symbol, timeframe, df_with_indicators)

            if success:
                self._update_status(symbol, timeframe, df.index[-1], len(df))

            return success

        except Exception as e:
            self.logger.error(f"处理 {symbol} {timeframe} 数据失败: {e}")
            return False

    def _is_symbol_up_to_date(self, symbol: str, timeframe: str, latest_date: pd.Timestamp) -> bool:
        """检查股票数据是否已是最新"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            cursor.execute("""
            SELECT last_calculated_datetime, ma_complete, w52_complete
            FROM mtf_precomputed_status
            WHERE symbol = %s AND timeframe = %s
            """, (symbol, timeframe))

            result = cursor.fetchone()
            if not result:
                return False

            last_calc, ma_complete, w52_complete = result

            # 检查是否完整且最新
            if not (ma_complete and w52_complete):
                return False

            if last_calc is None:
                return False

            # 检查时间差
            time_diff = (latest_date - pd.Timestamp(last_calc)).total_seconds()

            # 根据时间框架设置容忍度
            tolerance_map = {
                '5m': 300,      # 5分钟
                '15m': 900,     # 15分钟
                '1h': 3600,     # 1小时
                '1d': 86400,    # 1天
                '1w': 604800,   # 1周
                '1M': 2592000   # 1月
            }

            tolerance = tolerance_map.get(timeframe, 86400)
            return time_diff <= tolerance

        finally:
            conn.close()

    def _calculate_technical_indicators(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """计算技术指标"""
        df = df.copy()

        # 计算移动平均线
        periods = [5, 10, 20, 50, 200]
        for period in periods:
            if len(df) >= period:
                df[f'ma{period}'] = df['close'].rolling(window=period, min_periods=period).mean()
                df[f'above_ma{period}'] = df['close'] > df[f'ma{period}']

        # 计算RSI
        if len(df) >= 14:
            df['rsi_14'] = self._calculate_rsi(df['close'], 14)

        # 计算52周高低点（按领导要求：所有时间框架都支持）
        tf_config = get_mtf_config(timeframe)
        if tf_config.get('supports_52w', False):
            # 根据时间框架计算52周对应的数据点数
            window_52w_map = {
                '5m': 20280,    # 52周 × 5天 × 6.5小时 × 12个5分钟
                '15m': 6760,    # 52周 × 5天 × 6.5小时 × 4个15分钟
                '1h': 1690,     # 52周 × 5天 × 6.5小时
                '1d': 252,      # 52周 × 5天
                '1w': 52,       # 52周
                '1M': 12        # 12个月
            }

            window_52w = window_52w_map.get(timeframe, 252)

            # 严格按照hist_data.py的逻辑：即使数据不足也计算52周指标
            # hist_data.py使用 high[-252:] 的方式，意味着用全部可用数据
            if len(df) >= 20:  # 至少需要20条数据才有意义
                actual_window = min(len(df), window_52w)
                df['high_52w'] = df['high'].rolling(window=actual_window, min_periods=20).max()
                df['low_52w'] = df['low'].rolling(window=actual_window, min_periods=20).min()
                df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
                df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
                df['days_from_high_52w'] = self._calculate_days_from_extreme(df['high'], df['high_52w'])
                df['days_from_low_52w'] = self._calculate_days_from_extreme(df['low'], df['low_52w'], is_high=False)
                df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])

        # 计算20日高低点（所有时间框架）
        period_map = {
            '5m': 288,    # 20日 * 24小时 * 12个5分钟
            '15m': 96,    # 20日 * 24小时 * 4个15分钟
            '1h': 24,     # 20日 * 24小时
            '1d': 20,     # 20日
            '1w': 20,     # 20周
            '1M': 20      # 20月
        }

        period_20 = period_map.get(timeframe, 20)
        if len(df) >= period_20:
            df['high_20d'] = df['high'].rolling(window=period_20, min_periods=period_20).max()
            df['low_20d'] = df['low'].rolling(window=period_20, min_periods=period_20).min()
            df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
            df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
            df['days_from_high_20d'] = self._calculate_days_from_extreme(df['high'], df['high_20d'])
            df['days_from_low_20d'] = self._calculate_days_from_extreme(df['low'], df['low_20d'], is_high=False)
            df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])

        return df

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_days_from_extreme(self, prices: pd.Series, extremes: pd.Series, is_high: bool = True) -> pd.Series:
        """计算距离极值的天数"""
        days_from_extreme = pd.Series(index=prices.index, dtype=int)

        for i in range(len(prices)):
            if pd.isna(extremes.iloc[i]):
                days_from_extreme.iloc[i] = 0
                continue

            # 查找最近的极值位置
            if is_high:
                # 查找最近的最高点
                recent_data = prices.iloc[max(0, i-252):i+1]
                extreme_positions = recent_data[recent_data >= extremes.iloc[i] * 0.999]
            else:
                # 查找最近的最低点
                recent_data = prices.iloc[max(0, i-252):i+1]
                extreme_positions = recent_data[recent_data <= extremes.iloc[i] * 1.001]

            if len(extreme_positions) > 0:
                last_extreme_idx = extreme_positions.index[-1]
                days_from_extreme.iloc[i] = i - prices.index.get_loc(last_extreme_idx)
            else:
                days_from_extreme.iloc[i] = 252  # 超过252天

        return days_from_extreme

    def _save_symbol_data(self, symbol: str, timeframe: str, df: pd.DataFrame) -> bool:
        """保存股票数据到数据库 - 只保存最近几天的数据"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 根据时间框架动态设置存储天数 - 60天配置
            # 注意：df已经在_process_symbol_data中计算过技术指标了
            if timeframe == '5m':
                recent_records = 60 * 24 * 12  # 60天 * 24小时 * 12个5分钟 = 17280条
            elif timeframe == '15m':
                recent_records = 60 * 24 * 4   # 60天 * 24小时 * 4个15分钟 = 5760条
            elif timeframe == '1h':
                recent_records = 60 * 24       # 60天 * 24小时 = 1440条
            elif timeframe == '1d':
                recent_records = 60            # 60天 = 60条
            elif timeframe == '1w':
                recent_records = 12            # 12周 = 12条 (约3个月)
            elif timeframe == '1M':
                recent_records = 3             # 3个月 = 3条
            else:
                recent_records = 7            # 默认7条

            df_recent = df.tail(recent_records)

            self.logger.info(f"保存 {symbol} {timeframe}: 总计算{len(df)}条，实际存储{len(df_recent)}条")

            # 准备MA指标数据
            ma_records = []
            w52_records = []

            for date, row in df_recent.iterrows():
                # MA指标记录
                ma_record = (
                    symbol, timeframe, date,
                    float(row['close']) if not pd.isna(row['close']) else None,
                    float(row['high']) if not pd.isna(row['high']) else None,
                    float(row['low']) if not pd.isna(row['low']) else None,
                    int(row['volume']) if not pd.isna(row['volume']) else None,
                    float(row['ma5']) if 'ma5' in row and not pd.isna(row['ma5']) else None,
                    float(row['ma10']) if 'ma10' in row and not pd.isna(row['ma10']) else None,
                    float(row['ma20']) if 'ma20' in row and not pd.isna(row['ma20']) else None,
                    float(row['ma50']) if 'ma50' in row and not pd.isna(row['ma50']) else None,
                    float(row['ma200']) if 'ma200' in row and not pd.isna(row['ma200']) else None,
                    bool(row['above_ma5']) if 'above_ma5' in row and not pd.isna(row['above_ma5']) else False,
                    bool(row['above_ma10']) if 'above_ma10' in row and not pd.isna(row['above_ma10']) else False,
                    bool(row['above_ma20']) if 'above_ma20' in row and not pd.isna(row['above_ma20']) else False,
                    bool(row['above_ma50']) if 'above_ma50' in row and not pd.isna(row['above_ma50']) else False,
                    bool(row['above_ma200']) if 'above_ma200' in row and not pd.isna(row['above_ma200']) else False,
                    float(row['rsi_14']) if 'rsi_14' in row and not pd.isna(row['rsi_14']) else None
                )
                ma_records.append(ma_record)

                # 52周指标记录
                w52_record = (
                    symbol, timeframe, date,
                    float(row['close']) if not pd.isna(row['close']) else None,
                    float(row['high']) if not pd.isna(row['high']) else None,
                    float(row['low']) if not pd.isna(row['low']) else None,
                    int(row['volume']) if not pd.isna(row['volume']) else None,
                    float(row['high_52w']) if 'high_52w' in row and not pd.isna(row['high_52w']) else None,
                    float(row['low_52w']) if 'low_52w' in row and not pd.isna(row['low_52w']) else None,
                    float(row['high_20d']) if 'high_20d' in row and not pd.isna(row['high_20d']) else None,
                    float(row['low_20d']) if 'low_20d' in row and not pd.isna(row['low_20d']) else None,
                    bool(row['is_new_high_52w']) if 'is_new_high_52w' in row and not pd.isna(row['is_new_high_52w']) else False,
                    bool(row['is_new_low_52w']) if 'is_new_low_52w' in row and not pd.isna(row['is_new_low_52w']) else False,
                    bool(row['is_new_high_20d']) if 'is_new_high_20d' in row and not pd.isna(row['is_new_high_20d']) else False,
                    bool(row['is_new_low_20d']) if 'is_new_low_20d' in row and not pd.isna(row['is_new_low_20d']) else False,
                    int(row['days_from_high_52w']) if 'days_from_high_52w' in row and not pd.isna(row['days_from_high_52w']) else 0,
                    int(row['days_from_low_52w']) if 'days_from_low_52w' in row and not pd.isna(row['days_from_low_52w']) else 0,
                    int(row['days_from_high_20d']) if 'days_from_high_20d' in row and not pd.isna(row['days_from_high_20d']) else 0,
                    int(row['days_from_low_20d']) if 'days_from_low_20d' in row and not pd.isna(row['days_from_low_20d']) else 0,
                    float(row['price_position_52w']) if 'price_position_52w' in row and not pd.isna(row['price_position_52w']) else None,
                    float(row['price_position_20d']) if 'price_position_20d' in row and not pd.isna(row['price_position_20d']) else None
                )
                w52_records.append(w52_record)

            # 批量插入MA指标
            if ma_records:
                cursor.executemany("""
                INSERT INTO mtf_precomputed_ma_indicators
                (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
                 ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                close_price = VALUES(close_price), high_price = VALUES(high_price), low_price = VALUES(low_price),
                volume = VALUES(volume), ma5 = VALUES(ma5), ma10 = VALUES(ma10), ma20 = VALUES(ma20),
                ma50 = VALUES(ma50), ma200 = VALUES(ma200), above_ma5 = VALUES(above_ma5),
                above_ma10 = VALUES(above_ma10), above_ma20 = VALUES(above_ma20), above_ma50 = VALUES(above_ma50),
                above_ma200 = VALUES(above_ma200), rsi_14 = VALUES(rsi_14), updated_at = CURRENT_TIMESTAMP
                """, ma_records)

            # 批量插入52周指标
            if w52_records:
                cursor.executemany("""
                INSERT INTO mtf_precomputed_52w_indicators
                (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
                 high_52w, low_52w, high_20d, low_20d, is_new_high_52w, is_new_low_52w,
                 is_new_high_20d, is_new_low_20d, days_from_high_52w, days_from_low_52w,
                 days_from_high_20d, days_from_low_20d, price_position_52w, price_position_20d)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                close_price = VALUES(close_price), high_price = VALUES(high_price), low_price = VALUES(low_price),
                volume = VALUES(volume), high_52w = VALUES(high_52w), low_52w = VALUES(low_52w),
                high_20d = VALUES(high_20d), low_20d = VALUES(low_20d), is_new_high_52w = VALUES(is_new_high_52w),
                is_new_low_52w = VALUES(is_new_low_52w), is_new_high_20d = VALUES(is_new_high_20d),
                is_new_low_20d = VALUES(is_new_low_20d), days_from_high_52w = VALUES(days_from_high_52w),
                days_from_low_52w = VALUES(days_from_low_52w), days_from_high_20d = VALUES(days_from_high_20d),
                days_from_low_20d = VALUES(days_from_low_20d), price_position_52w = VALUES(price_position_52w),
                price_position_20d = VALUES(price_position_20d), updated_at = CURRENT_TIMESTAMP
                """, w52_records)

            conn.commit()
            return True

        except Exception as e:
            self.logger.error(f"保存 {symbol} {timeframe} 数据失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def _update_status(self, symbol: str, timeframe: str, last_datetime: pd.Timestamp, record_count: int):
        """更新计算状态"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            cursor.execute("""
            INSERT INTO mtf_precomputed_status
            (symbol, timeframe, last_calculated_datetime, total_records, ma_complete, w52_complete)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            last_calculated_datetime = VALUES(last_calculated_datetime),
            total_records = VALUES(total_records),
            ma_complete = VALUES(ma_complete),
            w52_complete = VALUES(w52_complete),
            last_update = CURRENT_TIMESTAMP
            """, (symbol, timeframe, last_datetime, record_count, True, True))

            conn.commit()
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
        finally:
            conn.close()

    def _aggregate_monthly_data_from_daily(self, symbols: List[str], start_date: datetime, end_date: datetime) -> Dict[str, pd.DataFrame]:
        """从日线数据聚合月度数据"""
        try:
            # 获取日线数据
            daily_data = download_hist_price(
                symbols=symbols,
                interval='1d',
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                columns=['h', 'l', 'c', 'v'],
                threads=min(10, len(symbols)),
                verbose=False
            )

            monthly_data = {}

            for symbol, df in daily_data.items():
                if df is None or df.empty:
                    continue

                # 确保索引是datetime
                if not isinstance(df.index, pd.DatetimeIndex):
                    df.index = pd.to_datetime(df.index)

                # 重命名列
                df = df.rename(columns={
                    'h': 'high',
                    'l': 'low',
                    'c': 'close',
                    'v': 'volume'
                })

                # 按月聚合
                monthly_df = df.resample('M').agg({
                    'high': 'max',      # 月最高价
                    'low': 'min',       # 月最低价
                    'close': 'last',    # 月收盘价
                    'volume': 'sum'     # 月成交量
                }).dropna()

                if not monthly_df.empty:
                    monthly_data[symbol] = monthly_df

            self.logger.info(f"成功聚合 {len(monthly_data)} 只股票的月度数据")
            return monthly_data

        except Exception as e:
            self.logger.error(f"月度数据聚合失败: {e}")
            return {}

    def get_precomputed_data(self,
                           symbols: List[str],
                           timeframe: str,
                           start_date: str = None,
                           end_date: str = None) -> Dict[str, pd.DataFrame]:
        """获取预计算数据"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            # 构建查询条件
            where_conditions = ["symbol IN ({})".format(','.join(['%s'] * len(symbols))), "timeframe = %s"]
            params = symbols + [timeframe]

            if start_date:
                where_conditions.append("datetime >= %s")
                params.append(start_date)

            if end_date:
                where_conditions.append("datetime <= %s")
                params.append(end_date)

            where_clause = " AND ".join(where_conditions)

            # 查询MA数据
            cursor.execute(f"""
            SELECT symbol, datetime, close_price, high_price, low_price, volume,
                   ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14
            FROM mtf_precomputed_ma_indicators
            WHERE {where_clause}
            ORDER BY symbol, datetime
            """, params)

            ma_data = cursor.fetchall()

            # 查询52周数据
            cursor.execute(f"""
            SELECT symbol, datetime, high_52w, low_52w, high_20d, low_20d,
                   is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
                   days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
                   price_position_52w, price_position_20d
            FROM mtf_precomputed_52w_indicators
            WHERE {where_clause}
            ORDER BY symbol, datetime
            """, params)

            w52_data = cursor.fetchall()

            # 组织数据
            result = {}

            # 处理MA数据
            ma_dict = {}
            for row in ma_data:
                symbol = row[0]
                if symbol not in ma_dict:
                    ma_dict[symbol] = []
                ma_dict[symbol].append(row)

            # 处理52周数据
            w52_dict = {}
            for row in w52_data:
                symbol = row[0]
                if symbol not in w52_dict:
                    w52_dict[symbol] = []
                w52_dict[symbol].append(row)

            # 合并数据为DataFrame
            for symbol in symbols:
                if symbol in ma_dict:
                    ma_df = pd.DataFrame(ma_dict[symbol], columns=[
                        'symbol', 'datetime', 'close', 'high', 'low', 'volume',
                        'ma5', 'ma10', 'ma20', 'ma50', 'ma200',
                        'above_ma5', 'above_ma10', 'above_ma20', 'above_ma50', 'above_ma200', 'rsi_14'
                    ])
                    ma_df['datetime'] = pd.to_datetime(ma_df['datetime'])
                    ma_df.set_index('datetime', inplace=True)

                    if symbol in w52_dict:
                        w52_df = pd.DataFrame(w52_dict[symbol], columns=[
                            'symbol', 'datetime', 'high_52w', 'low_52w', 'high_20d', 'low_20d',
                            'is_new_high_52w', 'is_new_low_52w', 'is_new_high_20d', 'is_new_low_20d',
                            'days_from_high_52w', 'days_from_low_52w', 'days_from_high_20d', 'days_from_low_20d',
                            'price_position_52w', 'price_position_20d'
                        ])
                        w52_df['datetime'] = pd.to_datetime(w52_df['datetime'])
                        w52_df.set_index('datetime', inplace=True)

                        # 合并数据
                        combined_df = ma_df.join(w52_df.drop('symbol', axis=1), how='outer')
                    else:
                        combined_df = ma_df

                    result[symbol] = combined_df.drop('symbol', axis=1)

            return result

        finally:
            conn.close()

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()

        try:
            status = {
                'timeframes': {},
                'total_symbols': 0,
                'total_records': 0,
                'last_update': None
            }

            # 获取各时间框架状态
            for timeframe in self.supported_timeframes:
                cursor.execute("""
                SELECT COUNT(DISTINCT symbol) as symbols,
                       SUM(total_records) as records,
                       MAX(last_update) as last_update,
                       SUM(CASE WHEN ma_complete AND w52_complete THEN 1 ELSE 0 END) as complete_symbols
                FROM mtf_precomputed_status
                WHERE timeframe = %s
                """, (timeframe,))

                result = cursor.fetchone()
                if result:
                    symbols, records, last_update, complete_symbols = result
                    status['timeframes'][timeframe] = {
                        'symbols': symbols or 0,
                        'records': records or 0,
                        'complete_symbols': complete_symbols or 0,
                        'last_update': last_update,
                        'completion_rate': (complete_symbols / symbols * 100) if symbols > 0 else 0
                    }

                    status['total_symbols'] += symbols or 0
                    status['total_records'] += records or 0

                    if last_update and (not status['last_update'] or last_update > status['last_update']):
                        status['last_update'] = last_update

            return status

        finally:
            conn.close()


# 使用示例和主函数
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='多时间框架预计算系统')
    parser.add_argument('--init', action='store_true', help='初始化历史数据')
    parser.add_argument('--update', action='store_true', help='增量更新')
    parser.add_argument('--status', action='store_true', help='查看系统状态')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码')
    parser.add_argument('--timeframes', nargs='+', help='指定时间框架')
    parser.add_argument('--months', type=int, default=3, help='历史数据月数')
    parser.add_argument('--force', action='store_true', help='强制重新计算')

    args = parser.parse_args()

    system = MTFPrecomputedSystem()

    if args.status:
        print("📊 多时间框架预计算系统状态")
        print("=" * 50)
        status = system.get_system_status()

        print(f"总股票数: {status['total_symbols']}")
        print(f"总记录数: {status['total_records']:,}")
        print(f"最后更新: {status['last_update']}")
        print()

        for tf, tf_status in status['timeframes'].items():
            print(f"{tf:>4}: {tf_status['symbols']:>4}只股票, {tf_status['records']:>8,}条记录, "
                  f"完成率{tf_status['completion_rate']:>5.1f}%")

    elif args.init:
        print("🚀 开始历史数据初始化...")
        results = system.calculate_historical_data(
            symbols=args.symbols,
            timeframes=args.timeframes,
            history_months=args.months,
            force_recalculate=args.force
        )

        print(f"\n✅ 初始化完成!")
        print(f"   总耗时: {results['total_time']:.1f}秒")
        print(f"   成功: {results['success_count']}")
        print(f"   失败: {results['failed_count']}")

        for tf, tf_result in results['timeframe_results'].items():
            print(f"   {tf}: 成功{tf_result['success_count']}, 失败{tf_result['failed_count']}")

    else:
        print("请指定操作参数:")
        print("  --init    : 初始化历史数据")
        print("  --update  : 增量更新")
        print("  --status  : 查看系统状态")
        print("  --months 3: 指定历史数据月数")
        print("  --force   : 强制重新计算")


def process_single_symbol_mtf_worker(task_data):
    """
    处理单个股票的工作函数 - 用于并行处理

    Args:
        task_data: (symbol, timeframe, start_date, end_date, force_recalculate)
    """
    symbol, timeframe, start_date, end_date, force_recalculate = task_data

    try:
        # 导入必要模块 - 使用绝对路径
        import sys
        import os

        # 获取当前工作目录
        current_dir = os.getcwd()
        market_breadth_path = os.path.join(current_dir, 'market-breadth-task')
        config_path = os.path.join(current_dir, 'config')

        if market_breadth_path not in sys.path:
            sys.path.insert(0, market_breadth_path)
        if config_path not in sys.path:
            sys.path.insert(0, config_path)

        from utils import download_hist_price
        from db_settings import get_default_db_config
        from mtf_historical_config import get_mtf_config

        # 获取时间框架配置
        tf_config = get_mtf_config(timeframe)

        # 下载数据
        if timeframe == '1M':
            # 月度数据特殊处理 - 从日线聚合
            price_data = download_hist_price(
                symbols=[symbol],
                interval='1d',
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                columns=['h', 'l', 'c', 'v'],
                threads=1,
                verbose=False
            )

            if symbol in price_data and price_data[symbol] is not None:
                # 聚合为月度数据
                df_daily = price_data[symbol]
                # 检查列名格式
                if 'h' in df_daily.columns:
                    agg_dict = {'h': 'max', 'l': 'min', 'c': 'last', 'v': 'sum'}
                else:
                    agg_dict = {'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'}

                df = df_daily.resample('M').agg(agg_dict).dropna()
            else:
                df = None
        else:
            price_data = download_hist_price(
                symbols=[symbol],
                interval=tf_config['interval'],
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                columns=['h', 'l', 'c', 'v'],
                threads=1,
                verbose=False
            )
            df = price_data.get(symbol)

        if df is None or df.empty:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'error': 'No data downloaded',
                'records': 0,
                'total_calculated': 0
            }

        # 确保列名正确 - 处理两种可能的列名格式
        if 'h' in df.columns:
            df = df.rename(columns={'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'})
        # 如果已经是正确的列名，不需要重命名
        df = df.sort_index()

        total_calculated = len(df)

        # 计算技术指标 - 使用MTF系统的逻辑
        df = calculate_technical_indicators_for_parallel(df, timeframe)

        # 保存数据 - 使用MTF系统的60天存储逻辑
        success, records_saved = save_symbol_data_for_parallel(symbol, timeframe, df)

        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': success,
            'records': records_saved,
            'total_calculated': total_calculated
        }

    except Exception as e:
        import traceback
        error_detail = f"{str(e)}\n{traceback.format_exc()}"
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': False,
            'error': error_detail,
            'records': 0,
            'total_calculated': 0
        }


def calculate_technical_indicators_for_parallel(df, timeframe):
    """计算技术指标 - 并行处理版本"""

    # 基础移动平均
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma10'] = df['close'].rolling(10).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma200'] = df['close'].rolling(200).mean()

    # 布尔值指标
    df['above_ma5'] = df['close'] > df['ma5']
    df['above_ma10'] = df['close'] > df['ma10']
    df['above_ma20'] = df['close'] > df['ma20']
    df['above_ma50'] = df['close'] > df['ma50']
    df['above_ma200'] = df['close'] > df['ma200']

    # RSI计算
    if len(df) >= 14:
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi_14'] = 100 - (100 / (1 + rs))

    # 52周指标
    from mtf_historical_config import get_mtf_config
    tf_config = get_mtf_config(timeframe)

    if tf_config.get('supports_52w', False):
        window_52w_map = {
            '5m': 20280,
            '15m': 6760,
            '1h': 1690,
            '1d': 252,
            '1w': 52,
            '1M': 12
        }

        window_52w = window_52w_map.get(timeframe, 252)

        if len(df) >= 20:
            actual_window = min(len(df), window_52w)
            df['high_52w'] = df['high'].rolling(window=actual_window, min_periods=20).max()
            df['low_52w'] = df['low'].rolling(window=actual_window, min_periods=20).min()
            df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
            df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
            df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])

    # 20日指标
    if len(df) >= 20:
        df['high_20d'] = df['high'].rolling(20).max()
        df['low_20d'] = df['low'].rolling(20).min()
        df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
        df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
        df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])

    return df


def save_symbol_data_for_parallel(symbol: str, timeframe: str, df) -> tuple:
    """保存股票数据 - 并行处理版本，返回(成功标志, 保存记录数)"""

    import pandas as pd
    import numpy as np
    import pymysql
    from db_settings import get_default_db_config

    # 60天存储配置 - 与MTF系统一致
    storage_config = {
        '5m': 60 * 24 * 12,   # 17280条
        '15m': 60 * 24 * 4,   # 5760条
        '1h': 60 * 24,        # 1440条
        '1d': 60,             # 60条
        '1w': 12,             # 12条
        '1M': 3               # 3条
    }

    recent_records = storage_config.get(timeframe, 60)
    df_recent = df.tail(recent_records)

    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()

    try:
        # 删除旧数据
        cursor.execute("""
        DELETE FROM mtf_precomputed_ma_indicators
        WHERE symbol = %s AND timeframe = %s
        """, (symbol, timeframe))

        cursor.execute("""
        DELETE FROM mtf_precomputed_52w_indicators
        WHERE symbol = %s AND timeframe = %s
        """, (symbol, timeframe))

        # 准备数据
        ma_records = []
        w52_records = []

        for date, row in df_recent.iterrows():
            # MA记录
            ma_record = (
                symbol, timeframe, date,
                float(row['close']) if pd.notna(row['close']) else None,
                float(row['high']) if pd.notna(row['high']) else None,
                float(row['low']) if pd.notna(row['low']) else None,
                int(row['volume']) if pd.notna(row['volume']) and row['volume'] > 0 else None,
                float(row['ma5']) if pd.notna(row['ma5']) else None,
                float(row['ma10']) if pd.notna(row['ma10']) else None,
                float(row['ma20']) if pd.notna(row['ma20']) else None,
                float(row['ma50']) if pd.notna(row['ma50']) else None,
                float(row['ma200']) if pd.notna(row['ma200']) else None,
                bool(row['above_ma5']) if pd.notna(row['above_ma5']) else False,
                bool(row['above_ma10']) if pd.notna(row['above_ma10']) else False,
                bool(row['above_ma20']) if pd.notna(row['above_ma20']) else False,
                bool(row['above_ma50']) if pd.notna(row['above_ma50']) else False,
                bool(row['above_ma200']) if pd.notna(row['above_ma200']) else False,
                float(row['rsi_14']) if pd.notna(row['rsi_14']) else None
            )
            ma_records.append(ma_record)

            # 52周记录
            w52_record = (
                symbol, timeframe, date,
                float(row['close']) if pd.notna(row['close']) else None,
                float(row['high']) if pd.notna(row['high']) else None,
                float(row['low']) if pd.notna(row['low']) else None,
                int(row['volume']) if pd.notna(row['volume']) and row['volume'] > 0 else None,
                float(row.get('high_52w', 0)) if pd.notna(row.get('high_52w', np.nan)) else None,
                float(row.get('low_52w', 0)) if pd.notna(row.get('low_52w', np.nan)) else None,
                float(row.get('high_20d', 0)) if pd.notna(row.get('high_20d', np.nan)) else None,
                float(row.get('low_20d', 0)) if pd.notna(row.get('low_20d', np.nan)) else None,
                bool(row.get('is_new_high_52w', False)),
                bool(row.get('is_new_low_52w', False)),
                bool(row.get('is_new_high_20d', False)),
                bool(row.get('is_new_low_20d', False)),
                0, 0, 0, 0,  # days_from 字段
                float(row.get('price_position_52w', 0.5)) if pd.notna(row.get('price_position_52w', np.nan)) else 0.5,
                float(row.get('price_position_20d', 0.5)) if pd.notna(row.get('price_position_20d', np.nan)) else 0.5
            )
            w52_records.append(w52_record)

        # 批量插入MA数据
        if ma_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_ma_indicators
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, ma_records)

        # 批量插入52周数据
        if w52_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_52w_indicators
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             high_52w, low_52w, high_20d, low_20d,
             is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
             days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
             price_position_52w, price_position_20d)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, w52_records)

        # 更新状态表
        cursor.execute("""
        INSERT INTO mtf_precomputed_status (symbol, timeframe, last_calculated_datetime, total_records, ma_complete, w52_complete)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        last_calculated_datetime = VALUES(last_calculated_datetime),
        total_records = VALUES(total_records),
        ma_complete = VALUES(ma_complete),
        w52_complete = VALUES(w52_complete)
        """, (symbol, timeframe, df_recent.index[-1], len(df_recent), True, True))

        conn.commit()
        return True, len(df_recent)

    except Exception as e:
        conn.rollback()
        return False, 0
    finally:
        conn.close()
