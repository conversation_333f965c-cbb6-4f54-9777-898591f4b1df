#!/usr/bin/env python3
"""
多时间框架分析器兼容层使用示例
展示如何同时满足老板的文档要求和开发者的创新需求
"""

import sys
import os
import importlib.util

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入兼容层
compatibility_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'mtf_compatibility_layer.py')
spec = importlib.util.spec_from_file_location("mtf_compatibility_layer", compatibility_path)
compatibility_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(compatibility_module)

MTFCompatibilityLayer = compatibility_module.MTFCompatibilityLayer


class MockTimeframeResult:
    def __init__(self, signal_strength=0.5, confidence=0.7):
        self.signal_strength = signal_strength
        self.confidence = confidence


class MockRotationMetrics:
    def __init__(self, unified_rii=0.5):
        self.unified_rii = unified_rii


class MockBreadthMetrics:
    def __init__(self, internal_health=50, momentum_coherence=0.5, 
                 participation_rate=0.6, price_breadth_divergence=None):
        self.internal_health = internal_health
        self.momentum_coherence = momentum_coherence
        self.participation_rate = participation_rate
        self.price_breadth_divergence = price_breadth_divergence or {'type': 'none'}


def demonstrate_boss_view():
    """演示老板看到的标准格式输出"""
    print("👔 === 老板视角：标准文档格式输出 ===\n")
    
    # 创建分析器
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 模拟真实市场数据
    timeframe_results = {
        'daily': MockTimeframeResult(0.78, 0.85),
        'weekly': MockTimeframeResult(0.72, 0.80),
        'monthly': MockTimeframeResult(0.68, 0.75)
    }
    
    dynamic_weights = {'daily': 0.5, 'weekly': 0.3, 'monthly': 0.2}
    
    breadth_metrics = {
        'Technology': MockBreadthMetrics(82, 0.8, 0.78),
        'Healthcare': MockBreadthMetrics(75, 0.72, 0.75),
        'Finance': MockBreadthMetrics(58, 0.55, 0.62),
        'Energy': MockBreadthMetrics(35, 0.3, 0.45, {'type': 'negative', 'severity': 0.3}),
        'Consumer': MockBreadthMetrics(68, 0.65, 0.68),
        'Industrial': MockBreadthMetrics(45, 0.4, 0.52),
        'Materials': MockBreadthMetrics(28, 0.25, 0.38, {'type': 'negative', 'severity': 0.4})
    }
    
    rotation_metrics = MockRotationMetrics(0.75)
    market_regime = 'trending_stable'
    
    # 执行分析
    result = mtf_analyzer.analyze_timeframes(
        timeframe_results, dynamic_weights, breadth_metrics, 
        rotation_metrics, market_regime
    )
    
    # 老板关心的核心信息
    print(f"📊 投资建议摘要:")
    print(f"建议仓位: {result.suggested_position}%")
    print(f"信号强度: {result.signal_strength:.3f}")
    print(f"决策置信度: {result.confidence:.3f}")
    
    print(f"\n📈 推荐配置 (前3名):")
    for i, sector in enumerate(result.top_sectors[:3], 1):
        print(f"{i}. {sector['sector']}: {sector['weight']}% (健康度{sector['health']})")
    
    print(f"\n🚫 避免板块:")
    for sector in result.avoid_sectors:
        print(f"- {sector['sector']}: {sector['risk_level']}风险")
    
    print(f"\n💡 操作建议:")
    pos_rec = result.position_recommendation
    print(f"{pos_rec['current_recommendation']}")
    print(f"理由: {pos_rec['reasoning']}")
    
    print(f"\n📋 决策依据:")
    print(f"{result.decision_rationale}")
    
    return result


def demonstrate_developer_view(result):
    """演示开发者看到的增强功能"""
    print("\n\n🔧 === 开发者视角：增强功能详情 ===\n")
    
    # 访问增强的信号详情
    if result.signal_details:
        signal = result.signal_details
        print(f"🎯 信号强度详细分析:")
        print(f"总体强度: {signal.overall_strength:.4f} (等级: {signal.signal_grade})")
        
        print(f"\n📊 分量贡献:")
        for component, contribution in signal.component_contributions.items():
            print(f"  {component}: {contribution:.4f}")
        
        print(f"\n🔍 质量评估:")
        for factor, value in signal.quality_assessment.items():
            print(f"  {factor}: {value:.4f}")
        
        print(f"\n⚡ 非线性调整:")
        top_adjustments = sorted(signal.non_linear_adjustments.items(), 
                               key=lambda x: abs(x[1]), reverse=True)[:3]
        for component, adjustment in top_adjustments:
            print(f"  {component}: {adjustment:+.4f}")
    
    # 访问增强的决策详情
    if result.enhanced_details:
        decision = result.enhanced_details
        print(f"\n🧠 决策过程详细分析:")
        print(f"决策步骤数: {len(decision.decision_steps)}")
        
        print(f"\n📋 关键决策步骤:")
        for i, step in enumerate(decision.decision_steps[:3], 1):
            print(f"  {i}. {step['step_name']}: {step['description']}")
        
        print(f"\n⚠️ 风险评估详情:")
        risk = decision.risk_assessment
        print(f"  风险等级: {risk['overall_risk_level']}")
        print(f"  风险评分: {risk['risk_score']:.3f}")
        print(f"  主要风险因素:")
        for factor in risk['risk_factors'][:2]:
            print(f"    - {factor}")
        
        print(f"\n🔄 备选场景分析:")
        for scenario in decision.alternative_scenarios:
            print(f"  {scenario['scenario']}: {scenario['decision']} (概率{scenario['probability']:.1%})")
        
        print(f"\n📝 支持证据:")
        for evidence in decision.supporting_evidence[:2]:
            print(f"  - {evidence}")
    
    # 访问避免清单详情
    if result.avoid_details:
        avoid = result.avoid_details
        print(f"\n🚫 避免清单详细分析:")
        print(f"避免板块数: {len(avoid.avoid_list)}")
        print(f"替代机会数: {len(avoid.alternative_opportunities)}")
        
        if avoid.avoid_list:
            print(f"\n详细避免原因 (示例):")
            sample_sector = avoid.avoid_list[0]
            if sample_sector in avoid.avoid_reasons:
                reasons = avoid.avoid_reasons[sample_sector]
                print(f"  {sample_sector}:")
                print(f"    严重度: {reasons['severity_score']:.3f}")
                if reasons['primary_issues']:
                    print(f"    主要问题: {reasons['primary_issues'][0]}")
        
        if avoid.alternative_opportunities:
            print(f"\n🔄 推荐替代机会:")
            for alt in avoid.alternative_opportunities[:3]:
                print(f"  - {alt}")


def demonstrate_legacy_compatibility():
    """演示与遗留系统的兼容性"""
    print("\n\n🔄 === 遗留系统兼容性演示 ===\n")
    
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 模拟遗留系统调用
    print("📞 遗留系统调用示例:")
    
    # 1. 简单信号强度计算
    rotation_metrics = MockRotationMetrics(0.6)
    breadth_metrics = {'TestSector': MockBreadthMetrics(60, 0.6, 0.6)}
    
    signal_strength = mtf_analyzer.calculate_signal_strength(
        rotation_metrics, breadth_metrics, 'daily'
    )
    print(f"1. calculate_signal_strength() -> {signal_strength:.4f}")
    
    # 2. 统一决策生成
    timeframe_results = {'daily': MockTimeframeResult(0.7, 0.8)}
    dynamic_weights = {'daily': 1.0}
    
    unified_decision = mtf_analyzer.generate_unified_decision(
        timeframe_results, dynamic_weights, 'normal_market'
    )
    print(f"2. generate_unified_decision() -> dict with {len(unified_decision)} keys")
    print(f"   suggested_position: {unified_decision['suggested_position']}%")
    print(f"   top_sectors: {len(unified_decision['top_sectors'])} sectors")
    print(f"   avoid_sectors: {len(unified_decision['avoid_sectors'])} sectors")
    
    print(f"\n✅ 遗留系统可以无缝使用新的增强功能！")


def main():
    """主演示函数"""
    print("🚀 多时间框架分析器兼容层使用演示")
    print("=" * 60)
    print("💡 目标：既满足老板的文档要求，又保留开发者的创新功能")
    print("=" * 60)
    
    # 1. 老板视角：标准格式输出
    result = demonstrate_boss_view()
    
    # 2. 开发者视角：增强功能
    demonstrate_developer_view(result)
    
    # 3. 遗留系统兼容性
    demonstrate_legacy_compatibility()
    
    print("\n" + "=" * 60)
    print("🎯 === 总结 ===")
    print("✅ 老板满意：输出格式完全符合文档要求")
    print("✅ 开发者满意：保留所有创新功能和增强特性")
    print("✅ 系统稳定：与遗留代码完全兼容")
    print("✅ 渐进升级：可以逐步迁移到新功能")
    
    print("\n💡 最佳实践建议:")
    print("1. 对外接口使用标准格式，确保兼容性")
    print("2. 内部实现使用增强功能，提升质量")
    print("3. 提供可选的详细信息访问，满足高级用户需求")
    print("4. 保持向后兼容，确保系统稳定性")
    
    print("\n🚀 这就是既创新又稳妥的解决方案！")


if __name__ == "__main__":
    main()
