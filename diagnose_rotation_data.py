#!/usr/bin/env python3
"""
板块轮动数据诊断脚本
诊断为什么轮动指标为0或N/A
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def check_market_breadth_data():
    """检查市场广度数据"""
    print("📊 检查市场广度数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查各时间框架的数据量
        cursor.execute("""
        SELECT timeframe, 
               COUNT(*) as total_records,
               COUNT(DISTINCT market) as markets,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest,
               AVG(advances) as avg_advances,
               AVG(declines) as avg_declines,
               AVG(avg_rsi) as avg_rsi
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 WHEN '15m' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("📈 市场广度数据统计:")
            print("-" * 80)
            print(f"{'时间框架':<8} {'记录数':<8} {'市场数':<6} {'最早时间':<12} {'平均涨':<6} {'平均跌':<6} {'平均RSI':<8}")
            print("-" * 80)
            
            total_records = 0
            for row in results:
                tf, records, markets, earliest, latest, avg_adv, avg_dec, avg_rsi = row
                total_records += records
                earliest_str = earliest.strftime('%m-%d') if earliest else 'N/A'
                print(f"{tf:<8} {records:<8} {markets:<6} {earliest_str:<12} {avg_adv or 0:<6.0f} {avg_dec or 0:<6.0f} {avg_rsi or 0:<8.1f}")
            
            print(f"\n✅ 总记录数: {total_records}")
            
            # 检查最近7天的数据
            cursor.execute("""
            SELECT timeframe, COUNT(*) as recent_count
            FROM market_breadth_metrics_gics 
            WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY timeframe
            """)
            
            recent_results = cursor.fetchall()
            if recent_results:
                print("\n📅 最近7天数据:")
                for tf, count in recent_results:
                    print(f"   {tf}: {count} 条记录")
            else:
                print("\n⚠️  最近7天无新数据")
                
        else:
            print("❌ 未找到市场广度数据")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_sector_rotation_data():
    """检查板块轮动数据"""
    print("\n🔄 检查板块轮动数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查轮动指标数据
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT sector) as sectors,
            AVG(rotation_intensity_index) as avg_rii,
            AVG(price_dispersion) as avg_price_disp,
            AVG(rank_velocity) as avg_rank_vel,
            AVG(volume_concentration) as avg_vol_conc,
            MAX(recorded_at) as latest_time
        FROM sector_rotation_metrics
        """)
        
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            total, sectors, avg_rii, avg_price, avg_rank, avg_vol, latest = result
            
            print("📊 板块轮动数据统计:")
            print(f"   总记录数: {total}")
            print(f"   板块数量: {sectors}")
            print(f"   平均RII: {avg_rii or 0:.4f}")
            print(f"   平均价格离散度: {avg_price or 0:.4f}")
            print(f"   平均排名速度: {avg_rank or 0:.4f}")
            print(f"   平均成交量集中度: {avg_vol or 0:.4f}")
            print(f"   最新时间: {latest}")
            
            # 检查具体的轮动指标
            cursor.execute("""
            SELECT sector, 
                   rotation_intensity_index,
                   price_dispersion,
                   rank_velocity,
                   rotation_stage,
                   risk_level
            FROM sector_rotation_metrics 
            WHERE recorded_at = (SELECT MAX(recorded_at) FROM sector_rotation_metrics)
            ORDER BY rotation_intensity_index DESC
            LIMIT 5
            """)
            
            latest_results = cursor.fetchall()
            if latest_results:
                print("\n📈 最新轮动指标（前5名）:")
                print("-" * 60)
                print(f"{'板块':<15} {'RII':<8} {'价格离散':<8} {'排名速度':<8} {'阶段':<8}")
                print("-" * 60)
                
                for sector, rii, price_disp, rank_vel, stage, risk in latest_results:
                    print(f"{sector:<15} {rii or 0:<8.4f} {price_disp or 0:<8.4f} {rank_vel or 0:<8.4f} {stage or 'N/A':<8}")
            
            # 诊断为什么指标为0
            zero_count = sum(1 for row in latest_results if (row[1] or 0) == 0)
            if zero_count > 0:
                print(f"\n⚠️  发现 {zero_count} 个板块的RII指数为0")
                print("💡 可能原因:")
                print("   - 缺少足够的历史数据")
                print("   - 时间框架数据不完整")
                print("   - 需要更多天数的数据积累")
            
        else:
            print("❌ 未找到板块轮动数据")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_historical_data_depth():
    """检查历史数据深度"""
    print("\n📅 检查历史数据深度...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查数据的时间跨度
        cursor.execute("""
        SELECT 
            MIN(recorded_at) as earliest,
            MAX(recorded_at) as latest,
            DATEDIFF(MAX(recorded_at), MIN(recorded_at)) as days_span
        FROM market_breadth_metrics_gics
        """)
        
        result = cursor.fetchone()
        
        if result and result[0]:
            earliest, latest, days_span = result
            print(f"📊 数据时间跨度:")
            print(f"   最早数据: {earliest}")
            print(f"   最新数据: {latest}")
            print(f"   跨度天数: {days_span} 天")
            
            # 评估数据充足性
            if days_span < 7:
                print("⚠️  数据跨度不足7天，轮动指标可能不准确")
                print("💡 建议: 连续运行数据收集至少7天")
            elif days_span < 30:
                print("⚠️  数据跨度不足30天，长期轮动指标可能不稳定")
                print("💡 建议: 继续积累数据至30天以上")
            else:
                print("✅ 数据跨度充足，支持准确的轮动分析")
            
            # 检查数据连续性
            cursor.execute("""
            SELECT DATE(recorded_at) as date, COUNT(*) as records
            FROM market_breadth_metrics_gics
            WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(recorded_at)
            ORDER BY date DESC
            """)
            
            daily_results = cursor.fetchall()
            if daily_results:
                print(f"\n📈 最近7天数据连续性:")
                for date, records in daily_results:
                    print(f"   {date}: {records} 条记录")
                
                if len(daily_results) < 3:
                    print("⚠️  最近数据不连续，影响轮动指标计算")
            
        else:
            print("❌ 无法获取数据时间信息")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("🔧 立即执行的步骤:")
    print("1. 运行市场广度计算:")
    print("   python run_market_breadth_calculation.py")
    print()
    print("2. 等待5分钟后运行板块轮动计算:")
    print("   python run_sector_rotation_calculation.py")
    print()
    print("3. 重新测试Web界面分析")
    
    print("\n⏰ 数据积累建议:")
    print("- 连续运行3-7天建立基础数据")
    print("- 每日运行市场广度计算")
    print("- 每日运行板块轮动计算")
    print("- 配置自动化任务确保数据连续性")
    
    print("\n🎯 预期改善:")
    print("- 3天后: 基础轮动指标开始有效")
    print("- 7天后: 价格离散度和排名速度稳定")
    print("- 30天后: 风险评估和仓位建议准确")

def main():
    """主函数"""
    print("🔍 板块轮动数据诊断")
    print("=" * 50)
    print(f"⏰ 诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 诊断1: 市场广度数据
    breadth_ok = check_market_breadth_data()
    
    # 诊断2: 板块轮动数据
    rotation_ok = check_sector_rotation_data()
    
    # 诊断3: 历史数据深度
    history_ok = check_historical_data_depth()
    
    # 提供解决方案
    suggest_solutions()
    
    print("\n" + "=" * 50)
    print("📋 诊断结果总结:")
    print(f"   市场广度数据: {'✅ 正常' if breadth_ok else '❌ 缺失'}")
    print(f"   板块轮动数据: {'✅ 存在' if rotation_ok else '❌ 缺失'}")
    print(f"   历史数据深度: {'✅ 充足' if history_ok else '⚠️ 不足'}")
    
    if not breadth_ok:
        print("\n🚨 关键问题: 缺少市场广度基础数据")
        print("   立即运行: python run_market_breadth_calculation.py")
    elif not rotation_ok:
        print("\n🚨 关键问题: 缺少板块轮动计算结果")
        print("   立即运行: python run_sector_rotation_calculation.py")
    elif not history_ok:
        print("\n⚠️  数据深度不足，需要继续积累数据")
    else:
        print("\n✅ 数据状态良好，轮动指标应该正常")
    
    return breadth_ok and rotation_ok and history_ok

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
