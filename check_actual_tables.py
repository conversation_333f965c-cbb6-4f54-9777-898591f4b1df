#!/usr/bin/env python3
"""
检查实际的数据库表结构
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def check_actual_tables():
    """检查实际的数据库表结构"""
    try:
        from db_settings import get_default_db_config
        import pymysql
        
        config = get_default_db_config()
        
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 获取所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print("=== 数据库中的所有表 ===")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 查找包含 breadth 或 rotation 的表
        relevant_tables = []
        for table in tables:
            table_name = table[0].lower()
            if any(keyword in table_name for keyword in ['breadth', 'rotation', 'mtf', 'gics']):
                relevant_tables.append(table[0])
        
        print(f"\n=== 相关表结构 ===")
        for table in relevant_tables:
            print(f"\n📋 {table}")
            
            # 查看表结构
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            print(f"  列结构:")
            for col in columns:
                print(f"    {col[0]} - {col[1]} - {col[2]} - {col[3]}")
            
            # 查看数据量
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count}")
            
            # 查看最新的几条记录
            try:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                column_names = [desc[0] for desc in cursor.description]
                print(f"  列名: {column_names}")
                print(f"  示例数据:")
                for i, row in enumerate(sample_data, 1):
                    print(f"    记录{i}: {row}")
            except Exception as e:
                print(f"  无法查看示例数据: {e}")
            
            print()
        
        cursor.close()
        connection.close()
        
        return relevant_tables
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    check_actual_tables()
