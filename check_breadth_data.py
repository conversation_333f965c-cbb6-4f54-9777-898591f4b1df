#!/usr/bin/env python3
"""
检查市场广度数据的实际情况
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def check_breadth_data_details():
    """详细检查市场广度数据"""
    print("🔍 详细检查市场广度数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 1. 检查总体数据情况
        cursor.execute("""
        SELECT timeframe,
               COUNT(*) as total_records,
               COUNT(DISTINCT DATE(recorded_at)) as unique_dates,
               COUNT(DISTINCT market) as unique_markets,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 WHEN '15m' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        print("📊 各时间框架数据统计:")
        print("-" * 80)
        print(f"{'时间框架':<8} {'总记录':<8} {'天数':<6} {'市场数':<8} {'最早':<12} {'最新':<12}")
        print("-" * 80)
        
        for row in results:
            tf, total, days, markets, earliest, latest = row
            earliest_str = earliest.strftime('%m-%d %H:%M') if earliest else 'N/A'
            latest_str = latest.strftime('%m-%d %H:%M') if latest else 'N/A'
            print(f"{tf:<8} {total:<8} {days:<6} {markets:<8} {earliest_str:<12} {latest_str:<12}")
        
        # 2. 专门检查1d时间框架的详细情况
        print(f"\n📅 1d时间框架详细分析:")
        cursor.execute("""
        SELECT DATE(recorded_at) as date,
               COUNT(DISTINCT market) as market_count,
               COUNT(*) as total_records,
               AVG(total_stocks) as avg_stocks,
               MIN(recorded_at) as earliest_time,
               MAX(recorded_at) as latest_time
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d'
        GROUP BY DATE(recorded_at)
        ORDER BY date DESC
        LIMIT 10
        """)
        
        daily_results = cursor.fetchall()
        
        if daily_results:
            print("-" * 90)
            print(f"{'日期':<12} {'市场数':<8} {'记录数':<8} {'平均股票数':<12} {'时间范围':<20}")
            print("-" * 90)
            
            for row in daily_results:
                date, market_count, records, avg_stocks, earliest_time, latest_time = row
                time_range = f"{earliest_time.strftime('%H:%M')}-{latest_time.strftime('%H:%M')}"
                avg_stocks_str = f"{avg_stocks:.0f}" if avg_stocks else "0"
                print(f"{date:<12} {market_count:<8} {records:<8} {avg_stocks_str:<12} {time_range:<20}")
        
        # 3. 检查为什么只找到1天数据
        print(f"\n🔍 检查筛选条件:")
        
        # 原始查询条件
        cursor.execute("""
        SELECT DATE(recorded_at) as date, 
               COUNT(DISTINCT market) as market_count,
               AVG(total_stocks) as avg_stocks
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d' 
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(recorded_at)
        ORDER BY date DESC
        """)
        
        filter_results = cursor.fetchall()
        
        print(f"最近30天所有1d数据:")
        for date, market_count, avg_stocks in filter_results:
            status = "✅ 符合条件" if market_count >= 10 else f"❌ 只有{market_count}个市场"
            print(f"   {date}: {market_count} 个市场, 平均{avg_stocks:.0f}股票 - {status}")
        
        # 4. 降低筛选条件重新查询
        print(f"\n💡 降低筛选条件 (>=5个市场):")
        cursor.execute("""
        SELECT DATE(recorded_at) as date, 
               COUNT(DISTINCT market) as market_count,
               AVG(total_stocks) as avg_stocks
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d' 
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(recorded_at)
        HAVING COUNT(DISTINCT market) >= 5
        ORDER BY date DESC
        """)
        
        relaxed_results = cursor.fetchall()
        
        print(f"符合条件的日期 (>=5个市场):")
        for date, market_count, avg_stocks in relaxed_results:
            print(f"   {date}: {market_count} 个市场, 平均{avg_stocks:.0f}股票")
        
        print(f"\n📊 总结:")
        print(f"   原条件(>=10市场): {len([r for r in filter_results if r[1] >= 10])} 天")
        print(f"   放宽条件(>=5市场): {len(relaxed_results)} 天")
        print(f"   所有1d数据: {len(filter_results)} 天")
        
        conn.close()
        return len(relaxed_results)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """主函数"""
    print("🔍 市场广度数据检查工具")
    print("=" * 50)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    available_days = check_breadth_data_details()
    
    print(f"\n💡 建议:")
    if available_days == 0:
        print("- 没有足够的市场广度数据")
        print("- 请先运行: python run_market_breadth_calculation.py")
    elif available_days == 1:
        print("- 只有1天数据，可能是筛选条件太严格")
        print("- 建议降低市场数量要求")
        print("- 或者运行更多天的市场广度计算")
    else:
        print(f"- 找到 {available_days} 天可用数据")
        print("- 可以修改轮动计算脚本使用这些数据")
    
    return available_days

if __name__ == "__main__":
    result = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0)
