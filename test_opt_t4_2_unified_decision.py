#!/usr/bin/env python3
"""
测试 OPT-T4.2 统一决策生成
验证增强版多时间框架分析器的统一决策生成功能
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版多时间框架分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_multi_timeframe_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_multi_timeframe_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedMultiTimeframeAnalyzer = analyzer_module.EnhancedMultiTimeframeAnalyzer
UnifiedDecisionDetails = analyzer_module.UnifiedDecisionDetails


class MockTimeframeResult:
    """模拟时间框架结果"""
    def __init__(self, signal_strength=0.5, confidence=0.7):
        self.signal_strength = signal_strength
        self.confidence = confidence


def test_unified_decision_basic():
    """测试基础统一决策生成"""
    print("=== 测试基础统一决策生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建多时间框架结果
    timeframe_results = {
        'daily': MockTimeframeResult(0.75, 0.85),
        'weekly': MockTimeframeResult(0.68, 0.80),
        'monthly': MockTimeframeResult(0.72, 0.75)
    }
    
    # 动态权重
    dynamic_weights = {
        'daily': 0.5,
        'weekly': 0.3,
        'monthly': 0.2
    }
    
    market_regime = 'normal_market'
    
    print(f"📊 输入数据:")
    print(f"时间框架数: {len(timeframe_results)}")
    print(f"市场状态: {market_regime}")
    for tf, result in timeframe_results.items():
        weight = dynamic_weights[tf]
        print(f"  {tf}: 强度{result.signal_strength:.2f}, 置信度{result.confidence:.2f}, 权重{weight:.1f}")
    
    # 生成统一决策
    decision_result = analyzer.generate_unified_decision_enhanced(
        timeframe_results=timeframe_results,
        dynamic_weights=dynamic_weights,
        market_regime=market_regime
    )
    
    print(f"\n🎯 统一决策结果:")
    print(f"主要决策: {decision_result.primary_decision}")
    print(f"决策置信度: {decision_result.decision_confidence:.3f}")
    print(f"决策理由: {decision_result.decision_rationale}")
    
    print(f"\n📋 决策步骤 ({len(decision_result.decision_steps)}步):")
    for i, step in enumerate(decision_result.decision_steps, 1):
        print(f"  {i}. {step['step_name']}: {step['description']}")
    
    print(f"\n⚠️ 风险评估:")
    risk = decision_result.risk_assessment
    print(f"  总体风险等级: {risk['overall_risk_level']}")
    print(f"  风险评分: {risk['risk_score']:.3f}")
    print(f"  风险因素数: {len(risk['risk_factors'])}")
    
    print(f"\n🔄 备选场景 ({len(decision_result.alternative_scenarios)}个):")
    for scenario in decision_result.alternative_scenarios:
        print(f"  - {scenario['scenario']}: {scenario['decision']} (概率{scenario['probability']:.1%})")
    
    print(f"\n📝 支持证据 ({len(decision_result.supporting_evidence)}条):")
    for evidence in decision_result.supporting_evidence[:3]:  # 显示前3条
        print(f"  - {evidence}")
    
    return True


def test_market_regime_adjustment():
    """测试市场状态调整"""
    print("\n=== 测试市场状态调整 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 固定的时间框架结果
    timeframe_results = {
        'daily': MockTimeframeResult(0.7, 0.8),
        'weekly': MockTimeframeResult(0.6, 0.7)
    }
    
    dynamic_weights = {'daily': 0.7, 'weekly': 0.3}
    
    # 测试不同市场状态
    market_regimes = [
        'trending_stable',
        'normal_market', 
        'high_rotation',
        'short_term_stress',
        'regime_transition',
        'divergent_market'
    ]
    
    print(f"市场状态调整测试:")
    print(f"{'市场状态':<18} {'调整前信号':<10} {'调整后信号':<10} {'最终决策':<12} {'置信度'}")
    print("-" * 70)
    
    for regime in market_regimes:
        decision_result = analyzer.generate_unified_decision_enhanced(
            timeframe_results=timeframe_results,
            dynamic_weights=dynamic_weights,
            market_regime=regime
        )
        
        # 从决策步骤中提取调整信息
        adjustment_step = None
        for step in decision_result.decision_steps:
            if step['step_name'] == 'market_regime_adjustment':
                adjustment_step = step
                break
        
        if adjustment_step:
            original_signal = adjustment_step.get('original_signal', 0)
            adjusted_signal = adjustment_step.get('adjusted_signal', 0)
        else:
            original_signal = adjusted_signal = 0
        
        print(f"{regime:<18} {original_signal:<10.3f} {adjusted_signal:<10.3f} {decision_result.primary_decision:<12} {decision_result.decision_confidence:.3f}")
    
    return True


def test_decision_consistency():
    """测试决策一致性"""
    print("\n=== 测试决策一致性 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同一致性场景
    consistency_scenarios = [
        {
            'name': '高一致性',
            'timeframes': {
                'daily': MockTimeframeResult(0.8, 0.9),
                'weekly': MockTimeframeResult(0.75, 0.85),
                'monthly': MockTimeframeResult(0.78, 0.8)
            }
        },
        {
            'name': '中等一致性',
            'timeframes': {
                'daily': MockTimeframeResult(0.7, 0.8),
                'weekly': MockTimeframeResult(0.5, 0.7),
                'monthly': MockTimeframeResult(0.6, 0.75)
            }
        },
        {
            'name': '低一致性',
            'timeframes': {
                'daily': MockTimeframeResult(0.8, 0.9),
                'weekly': MockTimeframeResult(0.3, 0.6),
                'monthly': MockTimeframeResult(0.2, 0.5)
            }
        }
    ]
    
    dynamic_weights = {'daily': 0.5, 'weekly': 0.3, 'monthly': 0.2}
    
    for scenario in consistency_scenarios:
        print(f"--- {scenario['name']} ---")
        
        decision_result = analyzer.generate_unified_decision_enhanced(
            timeframe_results=scenario['timeframes'],
            dynamic_weights=dynamic_weights,
            market_regime='normal_market'
        )
        
        # 计算信号标准差
        signals = [tf.signal_strength for tf in scenario['timeframes'].values()]
        signal_std = np.std(signals)
        
        print(f"时间框架信号: {[f'{s:.2f}' for s in signals]}")
        print(f"信号标准差: {signal_std:.3f}")
        print(f"最终决策: {decision_result.primary_decision}")
        print(f"决策置信度: {decision_result.decision_confidence:.3f}")
        
        # 检查风险评估中是否识别了分歧
        risk_factors = decision_result.risk_assessment['risk_factors']
        has_divergence_risk = any('分歧' in factor for factor in risk_factors)
        
        if signal_std > 0.3 and has_divergence_risk:
            print("✅ 正确识别时间框架分歧风险")
        elif signal_std <= 0.3 and not has_divergence_risk:
            print("✅ 正确识别时间框架一致性")
        else:
            print("⚠️  分歧风险识别可能有误")
        
        print()
    
    return True


def test_risk_assessment():
    """测试风险评估功能"""
    print("\n=== 测试风险评估功能 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同风险场景
    risk_scenarios = [
        {
            'name': '低风险场景',
            'timeframes': {
                'daily': MockTimeframeResult(0.6, 0.8),
                'weekly': MockTimeframeResult(0.55, 0.75)
            },
            'market_regime': 'trending_stable'
        },
        {
            'name': '中等风险场景',
            'timeframes': {
                'daily': MockTimeframeResult(0.8, 0.7),
                'weekly': MockTimeframeResult(0.4, 0.6)
            },
            'market_regime': 'normal_market'
        },
        {
            'name': '高风险场景',
            'timeframes': {
                'daily': MockTimeframeResult(0.9, 0.6),
                'weekly': MockTimeframeResult(0.2, 0.5)
            },
            'market_regime': 'regime_transition'
        }
    ]
    
    dynamic_weights = {'daily': 0.7, 'weekly': 0.3}
    
    for scenario in risk_scenarios:
        print(f"--- {scenario['name']} ---")
        
        decision_result = analyzer.generate_unified_decision_enhanced(
            timeframe_results=scenario['timeframes'],
            dynamic_weights=dynamic_weights,
            market_regime=scenario['market_regime']
        )
        
        risk = decision_result.risk_assessment
        
        print(f"风险等级: {risk['overall_risk_level']}")
        print(f"风险评分: {risk['risk_score']:.3f}")
        print(f"风险因素:")
        for factor in risk['risk_factors']:
            print(f"  - {factor}")
        
        print(f"风险缓解措施:")
        for mitigation in risk['risk_mitigation'][:2]:  # 显示前2条
            print(f"  - {mitigation}")
        
        # 验证风险等级合理性
        expected_risk_levels = {
            '低风险场景': ['low', 'low_medium'],
            '中等风险场景': ['medium', 'medium_high'],
            '高风险场景': ['medium_high', 'high']
        }
        
        expected = expected_risk_levels[scenario['name']]
        if risk['overall_risk_level'] in expected:
            print("✅ 风险等级评估合理")
        else:
            print(f"⚠️  风险等级可能不准确，预期{expected}，实际{risk['overall_risk_level']}")
        
        print()
    
    return True


def test_alternative_scenarios():
    """测试备选场景生成"""
    print("\n=== 测试备选场景生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建测试数据
    timeframe_results = {
        'daily': MockTimeframeResult(0.65, 0.75),
        'weekly': MockTimeframeResult(0.58, 0.70)
    }
    
    dynamic_weights = {'daily': 0.6, 'weekly': 0.4}
    
    decision_result = analyzer.generate_unified_decision_enhanced(
        timeframe_results=timeframe_results,
        dynamic_weights=dynamic_weights,
        market_regime='normal_market'
    )
    
    print(f"主要决策: {decision_result.primary_decision}")
    print(f"备选场景分析:")
    
    total_probability = 0
    for scenario in decision_result.alternative_scenarios:
        print(f"\n--- {scenario['scenario'].upper()} ---")
        print(f"描述: {scenario['description']}")
        print(f"决策: {scenario['decision']}")
        print(f"信号强度: {scenario['signal_strength']:.3f}")
        print(f"概率: {scenario['probability']:.1%}")
        print(f"理由: {scenario['rationale']}")
        
        total_probability += scenario['probability']
    
    print(f"\n备选场景概率总和: {total_probability:.1%}")
    
    # 验证场景合理性
    scenario_names = [s['scenario'] for s in decision_result.alternative_scenarios]
    expected_scenarios = ['conservative', 'aggressive', 'wait_and_see']
    
    missing_scenarios = set(expected_scenarios) - set(scenario_names)
    if not missing_scenarios:
        print("✅ 包含所有预期的备选场景")
    else:
        print(f"⚠️  缺少场景: {missing_scenarios}")
    
    return True


def test_decision_rationale():
    """测试决策理由生成"""
    print("\n=== 测试决策理由生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同决策类型
    decision_scenarios = [
        {
            'name': '强买入信号',
            'timeframes': {
                'daily': MockTimeframeResult(0.85, 0.9),
                'weekly': MockTimeframeResult(0.80, 0.85)
            },
            'market_regime': 'trending_stable'
        },
        {
            'name': '持有信号',
            'timeframes': {
                'daily': MockTimeframeResult(0.55, 0.7),
                'weekly': MockTimeframeResult(0.50, 0.65)
            },
            'market_regime': 'normal_market'
        },
        {
            'name': '卖出信号',
            'timeframes': {
                'daily': MockTimeframeResult(0.25, 0.6),
                'weekly': MockTimeframeResult(0.20, 0.55)
            },
            'market_regime': 'short_term_stress'
        }
    ]
    
    dynamic_weights = {'daily': 0.6, 'weekly': 0.4}
    
    for scenario in decision_scenarios:
        print(f"--- {scenario['name']} ---")
        
        decision_result = analyzer.generate_unified_decision_enhanced(
            timeframe_results=scenario['timeframes'],
            dynamic_weights=dynamic_weights,
            market_regime=scenario['market_regime']
        )
        
        print(f"决策: {decision_result.primary_decision}")
        print(f"置信度: {decision_result.decision_confidence:.3f}")
        print(f"决策理由:")
        print(f"  {decision_result.decision_rationale}")
        
        # 验证理由包含关键信息
        rationale = decision_result.decision_rationale
        has_decision = decision_result.primary_decision in rationale
        has_steps = str(len(decision_result.decision_steps)) in rationale
        has_risk = decision_result.risk_assessment['overall_risk_level'] in rationale
        
        completeness_score = sum([has_decision, has_steps, has_risk])
        print(f"理由完整性: {completeness_score}/3 {'✅' if completeness_score >= 2 else '⚠️'}")
        
        print()
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T4.2 统一决策生成\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础统一决策生成
        test_results.append(("基础统一决策生成", test_unified_decision_basic()))
        
        print("=" * 60)
        
        # 2. 测试市场状态调整
        test_results.append(("市场状态调整", test_market_regime_adjustment()))
        
        print("=" * 60)
        
        # 3. 测试决策一致性
        test_results.append(("决策一致性", test_decision_consistency()))
        
        print("=" * 60)
        
        # 4. 测试风险评估
        test_results.append(("风险评估", test_risk_assessment()))
        
        print("=" * 60)
        
        # 5. 测试备选场景
        test_results.append(("备选场景生成", test_alternative_scenarios()))
        
        print("=" * 60)
        
        # 6. 测试决策理由
        test_results.append(("决策理由生成", test_decision_rationale()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T4.2 统一决策生成完成！")
            print("🚀 主要改进:")
            print("  - 多时间框架信号整合")
            print("  - 市场状态动态调整")
            print("  - 全面风险评估")
            print("  - 多场景决策分析")
            print("  - 详细决策理由生成")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
