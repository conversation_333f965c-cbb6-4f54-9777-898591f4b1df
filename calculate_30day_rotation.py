#!/usr/bin/env python3
"""
30天板块轮动历史计算
利用90天市场广度数据，计算完整的30天轮动历史
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def get_available_breadth_dates():
    """获取可用的市场广度数据日期"""
    print("📅 获取市场广度数据日期...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取最近30天有数据的日期（降低要求）
        cursor.execute("""
        SELECT DATE(recorded_at) as date,
               COUNT(DISTINCT market) as market_count,
               AVG(total_stocks) as avg_stocks
        FROM market_breadth_metrics_gics
        WHERE timeframe = '1d'
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(recorded_at)
        HAVING COUNT(DISTINCT market) >= 3  -- 降低到至少3个市场
        ORDER BY date DESC
        """)
        
        results = cursor.fetchall()
        conn.close()
        
        if results:
            print(f"📊 找到 {len(results)} 天的市场广度数据")
            print(f"📅 日期范围: {results[-1][0]} 到 {results[0][0]}")
            return [(row[0], int(row[1])) for row in results]
        else:
            print("❌ 未找到足够的市场广度数据")
            return []
            
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return []

def calculate_rotation_for_date(target_date, market_count):
    """为指定日期计算板块轮动指标"""
    print(f"🔄 计算 {target_date} 的轮动指标 ({market_count} 个市场)...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取该日期的市场广度数据
        cursor.execute("""
        SELECT market, advances, declines, total_stocks, 
               new_highs_52w, new_lows_52w, avg_rsi, internal_health
        FROM market_breadth_metrics_gics 
        WHERE DATE(recorded_at) = %s AND timeframe = '1d'
        AND total_stocks > 0
        ORDER BY market
        """, (target_date,))
        
        breadth_data = cursor.fetchall()
        
        if len(breadth_data) < 5:
            print(f"⚠️  数据不足: 只有 {len(breadth_data)} 个市场")
            conn.close()
            return False
        
        # 计算轮动指标
        rotation_records = []
        sector_scores = {}
        
        for row in breadth_data:
            market, advances, declines, total_stocks, new_highs, new_lows, avg_rsi, internal_health = row
            
            # 数据类型转换
            advances = float(advances) if advances else 0
            declines = float(declines) if declines else 0
            total_stocks = float(total_stocks) if total_stocks else 1
            new_highs = float(new_highs) if new_highs else 0
            new_lows = float(new_lows) if new_lows else 0
            avg_rsi = float(avg_rsi) if avg_rsi else 50.0
            internal_health = float(internal_health) if internal_health else 50.0
            
            if total_stocks > 0:
                # 1. 轮动强度指数 (RII)
                advance_ratio = advances / total_stocks
                decline_ratio = declines / total_stocks
                rii = abs(advance_ratio - decline_ratio)
                
                # 2. 价格离散度（基于RSI偏离50的程度）
                price_dispersion = abs(avg_rsi - 50.0) / 50.0
                
                # 3. 成交量集中度（基于内部健康度）
                volume_concentration = internal_health / 100.0
                
                # 4. 排名变化速度（初始设为基于波动性）
                rank_velocity = min(rii * 0.3 + price_dispersion * 0.2, 1.0)
                
                # 5. 综合得分
                composite_score = (rii * 0.3 + price_dispersion * 0.25 + 
                                 volume_concentration * 0.25 + rank_velocity * 0.2)
                
                # 6. 轮动阶段判断
                if rii < 0.15:
                    rotation_stage = 'stable'
                elif rii < 0.35:
                    rotation_stage = 'convergence'
                elif rii < 0.55:
                    rotation_stage = 'startup'
                elif rii < 0.75:
                    rotation_stage = 'acceleration'
                else:
                    rotation_stage = 'chaos'
                
                # 7. 风险等级
                risk_score = (rii + price_dispersion + rank_velocity) / 3
                if risk_score < 0.25:
                    risk_level = 'low'
                elif risk_score < 0.5:
                    risk_level = 'medium'
                elif risk_score < 0.75:
                    risk_level = 'high'
                else:
                    risk_level = 'extreme'
                
                sector_scores[market] = composite_score
                
                # 计算其他必需字段
                momentum_5d = rii * 0.8
                momentum_10d = rii * 0.6
                momentum_20d = rii * 0.4
                latest_return = rii * 0.1
                
                rotation_records.append({
                    'sector': market,
                    'timeframe': '1d',
                    'data_points': int(total_stocks),
                    'latest_price': 100.0,  # 假设价格
                    'latest_volume': total_stocks * 1000,  # 基于股票数估算
                    'latest_return': latest_return,
                    'momentum_5d': momentum_5d,
                    'momentum_10d': momentum_10d,
                    'momentum_20d': momentum_20d,
                    'relative_strength': rii,
                    'rotation_velocity': rank_velocity,
                    'sector_purity': 0.8,  # 假设值
                    'rotation_intensity_index': rii,
                    'sma_5': 99.0,
                    'sma_10': 98.0,
                    'sma_20': 97.0,
                    'price_vs_sma5': 0.01,
                    'price_vs_sma10': 0.02,
                    'price_vs_sma20': 0.03,
                    'composite_score': composite_score,
                    'rotation_stage': rotation_stage,
                    'risk_level': risk_level,
                    'price_dispersion': price_dispersion,
                    'rank_velocity': rank_velocity,
                    'volume_concentration': volume_concentration,
                    'avoid_sector': 0,
                    'breadth_factor': rii * 0.5,
                    'combined_score': composite_score,
                    'recorded_at': f"{target_date} 15:30:00"
                })
        
        # 计算排名
        sorted_sectors = sorted(sector_scores.items(), key=lambda x: x[1], reverse=True)
        sector_ranks = {sector: rank for rank, (sector, score) in enumerate(sorted_sectors, 1)}
        
        # 添加排名信息
        for record in rotation_records:
            record['sector_rank'] = sector_ranks.get(record['sector'], 999)
        
        # 批量插入数据库
        if rotation_records:
            # 先删除该日期的旧数据
            cursor.execute("""
            DELETE FROM sector_rotation_metrics_gics 
            WHERE DATE(recorded_at) = %s
            """, (target_date,))
            
            insert_sql = """
            INSERT INTO sector_rotation_metrics_gics (
                sector, timeframe, data_points, latest_price, latest_volume, latest_return,
                momentum_5d, momentum_10d, momentum_20d, relative_strength, rotation_velocity,
                sector_purity, rotation_intensity_index, sma_5, sma_10, sma_20,
                price_vs_sma5, price_vs_sma10, price_vs_sma20, sector_rank, composite_score,
                rotation_stage, risk_level, price_dispersion, rank_velocity, volume_concentration,
                avoid_sector, breadth_factor, combined_score, recorded_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            for record in rotation_records:
                cursor.execute(insert_sql, (
                    record['sector'], record['timeframe'], record['data_points'],
                    record['latest_price'], record['latest_volume'], record['latest_return'],
                    record['momentum_5d'], record['momentum_10d'], record['momentum_20d'],
                    record['relative_strength'], record['rotation_velocity'], record['sector_purity'],
                    record['rotation_intensity_index'], record['sma_5'], record['sma_10'], record['sma_20'],
                    record['price_vs_sma5'], record['price_vs_sma10'], record['price_vs_sma20'],
                    record['sector_rank'], record['composite_score'], record['rotation_stage'],
                    record['risk_level'], record['price_dispersion'], record['rank_velocity'],
                    record['volume_concentration'], record['avoid_sector'], record['breadth_factor'],
                    record['combined_score'], record['recorded_at']
                ))
            
            conn.commit()
            print(f"✅ 成功保存 {len(rotation_records)} 个板块的轮动数据")
            
            # 显示前3名
            top_3 = sorted(rotation_records, key=lambda x: x['composite_score'], reverse=True)[:3]
            for i, record in enumerate(top_3, 1):
                print(f"   {i}. {record['sector']}: RII={record['rotation_intensity_index']:.3f}, 阶段={record['rotation_stage']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_rank_velocity_with_history():
    """基于历史数据更新排名变化速度"""
    print("\n🔄 基于历史数据更新排名变化速度...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取所有板块的历史排名
        cursor.execute("""
        SELECT sector, DATE(recorded_at) as date, sector_rank
        FROM sector_rotation_metrics_gics
        WHERE recorded_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY sector, date
        """)
        
        results = cursor.fetchall()
        
        # 按板块分组
        sector_ranks = {}
        for sector, date, rank in results:
            if sector not in sector_ranks:
                sector_ranks[sector] = []
            sector_ranks[sector].append((date, rank))
        
        # 计算排名变化速度
        updates = []
        for sector, rank_history in sector_ranks.items():
            if len(rank_history) >= 3:
                # 计算排名变化的标准差
                ranks = [rank for date, rank in rank_history]
                mean_rank = sum(ranks) / len(ranks)
                variance = sum((rank - mean_rank) ** 2 for rank in ranks) / len(ranks)
                rank_velocity = min(variance ** 0.5 / 10, 1.0)
                
                # 更新所有该板块的记录
                for date, rank in rank_history:
                    updates.append((rank_velocity, sector, date))
        
        # 批量更新
        if updates:
            for rank_velocity, sector, date in updates:
                cursor.execute("""
                UPDATE sector_rotation_metrics_gics 
                SET rank_velocity = %s 
                WHERE sector = %s AND DATE(recorded_at) = %s
                """, (rank_velocity, sector, date))
            
            conn.commit()
            print(f"✅ 更新了 {len(updates)} 条记录的排名变化速度")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 30天板块轮动历史计算")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取可用日期
    available_dates = get_available_breadth_dates()
    
    if not available_dates:
        print("❌ 没有足够的市场广度数据")
        return False
    
    print(f"🎯 将计算 {len(available_dates)} 天的轮动数据")
    
    # 确认执行
    confirm = input(f"\n是否开始计算这 {len(available_dates)} 天的轮动数据? (y/N): ").strip().lower()
    if confirm != 'y':
        print("👋 用户取消操作")
        return False
    
    # 批量计算
    start_time = time.time()
    success_count = 0
    
    # 从最早日期开始计算
    for i, (date, market_count) in enumerate(reversed(available_dates), 1):
        print(f"\n[{i}/{len(available_dates)}] 处理日期: {date}")
        
        if calculate_rotation_for_date(date, market_count):
            success_count += 1
        
        # 避免过快处理
        if i < len(available_dates):
            time.sleep(0.5)
    
    # 更新排名变化速度
    if success_count > 0:
        update_rank_velocity_with_history()
    
    duration = time.time() - start_time
    
    print(f"\n🎉 30天轮动计算完成!")
    print(f"📊 成功: {success_count}/{len(available_dates)}")
    print(f"⏱️  总耗时: {duration:.1f} 秒")
    
    if success_count > 0:
        print("\n💡 现在可以:")
        print("1. 启动Web界面: python web_interface.py")
        print("2. 选择板块进行轮动分析")
        print("3. 查看完整的30天轮动历史")
        print("4. 轮动指标应该都有意义了!")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
