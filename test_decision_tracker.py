#!/usr/bin/env python3
"""
测试决策追踪器功能
验证决策透明度和可解释性
"""

import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.decision_tracker import DecisionTracker
from core.improved_momentum_coherence import ImprovedMomentumCoherence
from core.market_breadth_analyzer import MarketBreadthAnalyzer, SectorBreadthData


def test_decision_tracker_basic():
    """测试决策追踪器基础功能"""
    print("=== 测试决策追踪器基础功能 ===\n")
    
    tracker = DecisionTracker()
    
    # 开始一个决策过程
    tracker.start_decision('test_analysis', {
        'sector': 'Technology',
        'data_points': 100,
        'timeframe': 'daily'
    })
    
    # 添加决策步骤
    tracker.add_step(
        'data_validation',
        {'valid': True, 'errors': []},
        '数据验证通过，所有字段完整',
        confidence=0.95,
        validation_time=0.1
    )
    
    tracker.add_step(
        'calculation',
        {'result': 0.75, 'method': 'improved'},
        '使用改进算法计算，结果稳定',
        confidence=0.88,
        calculation_time=0.3
    )
    
    # 添加置信度因素
    tracker.add_confidence_factor(
        'data_quality',
        0.9,
        '数据质量高，样本充足',
        weight=1.2
    )
    
    tracker.add_confidence_factor(
        'algorithm_stability',
        0.85,
        '算法在类似场景下表现稳定',
        weight=1.0
    )
    
    # 完成决策
    final_output = {
        'recommendation': 'buy',
        'confidence': 0.82,
        'risk_level': 'medium'
    }
    
    decision_record = tracker.complete_decision(final_output, 0.82)
    
    print("决策记录:")
    print(f"类型: {decision_record['type']}")
    print(f"总置信度: {decision_record['overall_confidence']:.2f}")
    print(f"步骤数: {len(decision_record['steps'])}")
    print(f"耗时: {decision_record['duration']:.3f}秒")
    
    print("\n决策步骤:")
    for i, step in enumerate(decision_record['steps'], 1):
        print(f"{i}. {step.name}: {step.reasoning} (置信度: {step.confidence:.2f})")
    
    print("\n置信度因素:")
    for factor, cf in decision_record['confidence_factors'].items():
        print(f"- {factor}: {cf.value:.2f} ({cf.impact})")
    
    # 获取决策摘要
    summary = tracker.get_decision_summary()
    print(f"\n决策摘要:")
    print(f"决策类型: {summary['decision_type']}")
    print(f"关键输入: {summary['key_inputs']}")
    print(f"最终置信度: {summary['final_confidence']:.2f}")
    print(f"步骤数: {summary['step_count']}")


def test_coherence_with_tracker():
    """测试带决策追踪的动量一致性计算"""
    print("\n=== 测试带决策追踪的动量一致性计算 ===\n")
    
    tracker = DecisionTracker()
    calculator = ImprovedMomentumCoherence(tracker)
    
    # 测试数据
    returns = [0.02, 0.05, 0.01, 0.08, 0.03, 0.06, 0.04, -0.01, 0.02, 0.03]
    
    print(f"输入收益率: {[f'{r:.3f}' for r in returns]}")
    
    # 计算一致性（会自动记录决策过程）
    overall_coherence, details = calculator.calculate_coherence(returns)
    
    print(f"\n计算结果:")
    print(f"综合一致性: {details.overall_coherence:.3f}")
    print(f"一致性类型: {details.coherence_type}")
    print(f"解释: {details.interpretation}")
    
    # 获取决策摘要
    summary = tracker.get_decision_summary()
    if summary:
        print(f"\n决策过程:")
        for step in summary['decision_path']:
            print(f"- {step['step']}: {step['reasoning']} (置信度: {step['confidence']:.2f})")


def test_breadth_analyzer_with_tracker():
    """测试带决策追踪的市场广度分析"""
    print("\n=== 测试带决策追踪的市场广度分析 ===\n")
    
    tracker = DecisionTracker()
    analyzer = MarketBreadthAnalyzer(tracker)
    
    # 创建测试数据
    sector_data = SectorBreadthData(
        sector_name="Technology",
        timestamp=datetime.now(),
        total_stocks=100,
        advances=65,
        declines=30,
        unchanged=5,
        advancing_volume=1000000,
        declining_volume=400000,
        total_volume=1500000,
        new_highs_52w=15,
        new_lows_52w=5,
        above_ma50=70,
        above_ma200=60,
        avg_rsi=62.5,
        individual_returns=[0.02, 0.05, 0.01, 0.08, 0.03, 0.06, 0.04, -0.01, 0.02, 0.03]
    )
    
    print(f"分析板块: {sector_data.sector_name}")
    print(f"股票数量: {sector_data.total_stocks}")
    print(f"涨跌家数: {sector_data.advances}/{sector_data.declines}")
    
    # 执行分析（会记录决策过程）
    result = analyzer.analyze_sector_breadth(sector_data, price_change=0.025)
    
    print(f"\n分析结果:")
    print(f"内部健康度: {result.internal_health:.2f}")
    print(f"动量一致性: {result.momentum_coherence:.3f}")
    
    if result.coherence_details:
        print(f"一致性类型: {result.coherence_details.coherence_type}")
    
    if result.price_breadth_divergence:
        div = result.price_breadth_divergence
        print(f"背离检测: {div['type']} (严重度: {div['severity']:.4f})")
    else:
        print("背离检测: 无背离")
    
    # 获取决策摘要
    summary = tracker.get_decision_summary()
    if summary:
        print(f"\n决策过程摘要:")
        print(f"总步骤数: {summary['step_count']}")
        print(f"最终置信度: {summary['final_confidence']:.2f}")
        
        print("\n关键决策步骤:")
        for step in summary['decision_path'][-3:]:  # 显示最后3步
            print(f"- {step['step']}: {step['reasoning']}")


def test_decision_export():
    """测试决策日志导出"""
    print("\n=== 测试决策日志导出 ===\n")
    
    tracker = DecisionTracker()
    
    # 模拟多个决策过程
    for i in range(3):
        tracker.start_decision(f'analysis_{i+1}', {'iteration': i+1})
        
        tracker.add_step(
            f'step_{i+1}',
            {'value': i * 0.1},
            f'第{i+1}次分析步骤',
            confidence=0.8 + i * 0.05
        )
        
        tracker.complete_decision({'result': f'outcome_{i+1}'}, 0.8 + i * 0.05)
    
    # 获取决策历史
    history = tracker.get_full_decision_history()
    print(f"总决策记录数: {len(history)}")
    
    recent = tracker.get_recent_decisions(2)
    print(f"最近2条记录数: {len(recent)}")
    
    # 导出到文件
    export_file = 'decision_log_test.json'
    tracker.export_decision_log(export_file)
    
    # 检查文件是否创建
    if os.path.exists(export_file):
        print(f"✅ 决策日志已成功导出到: {export_file}")
        
        # 读取并显示文件大小
        file_size = os.path.getsize(export_file)
        print(f"文件大小: {file_size} 字节")
        
        # 清理测试文件
        os.remove(export_file)
        print("测试文件已清理")
    else:
        print("❌ 决策日志导出失败")


def main():
    """主测试函数"""
    print("开始测试决策追踪器功能\n")
    print("=" * 60)
    
    try:
        test_decision_tracker_basic()
        print("=" * 60)
        
        test_coherence_with_tracker()
        print("=" * 60)
        
        test_breadth_analyzer_with_tracker()
        print("=" * 60)
        
        test_decision_export()
        print("=" * 60)
        
        print("✅ 所有决策追踪器测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
