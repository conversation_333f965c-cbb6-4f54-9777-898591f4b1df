#!/usr/bin/env python3
"""
测试 OPT-T4.1 增强信号强度计算
验证增强版多时间框架分析器的信号强度计算功能
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版多时间框架分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_multi_timeframe_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_multi_timeframe_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedMultiTimeframeAnalyzer = analyzer_module.EnhancedMultiTimeframeAnalyzer
SignalStrengthDetails = analyzer_module.SignalStrengthDetails


class MockRotationMetrics:
    """模拟轮动指标"""
    def __init__(self, unified_rii=0.5, sector_momentum=None):
        self.unified_rii = unified_rii
        if sector_momentum is None:
            self.sector_momentum = np.array([1.2, -0.8, 2.1, 0.5, -1.1])
        else:
            self.sector_momentum = sector_momentum


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health=50, momentum_coherence=0.5, 
                 participation_rate=0.6, price_breadth_divergence=None):
        self.internal_health = internal_health
        self.momentum_coherence = momentum_coherence
        self.participation_rate = participation_rate
        self.price_breadth_divergence = price_breadth_divergence or {'type': 'none'}


def test_signal_strength_basic():
    """测试基础信号强度计算"""
    print("=== 测试基础信号强度计算 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建测试数据
    rotation_metrics = MockRotationMetrics(unified_rii=0.7)
    
    breadth_metrics = {
        'Technology': MockBreadthMetrics(75, 0.8, 0.75),
        'Healthcare': MockBreadthMetrics(65, 0.6, 0.65),
        'Finance': MockBreadthMetrics(45, 0.4, 0.55),
        'Energy': MockBreadthMetrics(35, 0.3, 0.45),
        'Consumer': MockBreadthMetrics(60, 0.7, 0.70)
    }
    
    print(f"📊 输入数据:")
    print(f"RII: {rotation_metrics.unified_rii}")
    print(f"板块数: {len(breadth_metrics)}")
    
    # 计算信号强度
    result = analyzer.calculate_signal_strength_enhanced(
        rotation_metrics=rotation_metrics,
        breadth_metrics=breadth_metrics,
        timeframe='daily'
    )
    
    print(f"\n🎯 信号强度结果:")
    print(f"总体强度: {result.overall_strength:.4f}")
    print(f"信号等级: {result.signal_grade}")
    
    print(f"\n📈 分量贡献:")
    for component, contribution in result.component_contributions.items():
        print(f"  {component}: {contribution:.4f}")
    
    print(f"\n🔍 质量评估:")
    for factor, value in result.quality_assessment.items():
        print(f"  {factor}: {value:.4f}")
    
    print(f"\n⚡ 非线性调整:")
    for component, adjustment in result.non_linear_adjustments.items():
        print(f"  {component}: {adjustment:+.4f}")
    
    print(f"\n📊 置信度因子:")
    for factor, value in result.confidence_factors.items():
        print(f"  {factor}: {value:.4f}")
    
    # 验证结果合理性
    if 0 <= result.overall_strength <= 1:
        print(f"\n✅ 信号强度在合理范围内")
    else:
        print(f"\n⚠️  信号强度超出范围: {result.overall_strength}")
    
    return True


def test_non_linear_mapping():
    """测试非线性映射功能"""
    print("\n=== 测试非线性映射功能 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同强度的输入
    test_scenarios = [
        {'name': '低强度', 'rii': 0.2, 'health': 30, 'coherence': 0.2},
        {'name': '中强度', 'rii': 0.5, 'health': 60, 'coherence': 0.5},
        {'name': '高强度', 'rii': 0.8, 'health': 85, 'coherence': 0.8}
    ]
    
    for scenario in test_scenarios:
        print(f"--- {scenario['name']} ---")
        
        rotation_metrics = MockRotationMetrics(unified_rii=scenario['rii'])
        breadth_metrics = {
            'TestSector': MockBreadthMetrics(
                scenario['health'], 
                scenario['coherence'], 
                0.6
            )
        }
        
        # 计算原始分量
        raw_components = analyzer._calculate_raw_signal_components(
            rotation_metrics, breadth_metrics
        )
        
        # 应用非线性映射
        non_linear_components = analyzer._apply_non_linear_mapping(raw_components)
        
        print(f"原始分量 vs 非线性映射:")
        for component in raw_components:
            raw_val = raw_components[component]
            nl_val = non_linear_components[component]
            adjustment = nl_val - raw_val
            print(f"  {component}: {raw_val:.3f} -> {nl_val:.3f} ({adjustment:+.3f})")
        
        print()
    
    return True


def test_quality_assessment():
    """测试质量评估功能"""
    print("\n=== 测试质量评估功能 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同质量的数据
    quality_scenarios = [
        {
            'name': '高质量数据',
            'breadth_metrics': {
                f'Sector{i}': MockBreadthMetrics(70 + i*2, 0.7 + i*0.02, 0.7)
                for i in range(10)
            }
        },
        {
            'name': '低质量数据',
            'breadth_metrics': {
                f'Sector{i}': MockBreadthMetrics(30 + i*5, 0.2 + i*0.1, 0.4)
                for i in range(3)
            }
        },
        {
            'name': '混合质量数据',
            'breadth_metrics': {
                'HighQuality': MockBreadthMetrics(80, 0.8, 0.8),
                'LowQuality': MockBreadthMetrics(20, 0.1, 0.3),
                'MediumQuality': MockBreadthMetrics(50, 0.5, 0.6)
            }
        }
    ]
    
    for scenario in quality_scenarios:
        print(f"--- {scenario['name']} ---")
        
        # 计算原始分量
        raw_components = analyzer._calculate_raw_signal_components(
            MockRotationMetrics(), scenario['breadth_metrics']
        )
        
        # 评估质量
        quality_assessment = analyzer._assess_signal_quality(
            raw_components, scenario['breadth_metrics']
        )
        
        print(f"质量评估结果:")
        for factor, value in quality_assessment.items():
            print(f"  {factor}: {value:.4f}")
        
        # 质量等级判断
        overall_quality = quality_assessment['overall_quality']
        if overall_quality > 0.8:
            quality_grade = "优秀"
        elif overall_quality > 0.6:
            quality_grade = "良好"
        elif overall_quality > 0.4:
            quality_grade = "一般"
        else:
            quality_grade = "较差"
        
        print(f"  质量等级: {quality_grade}")
        print()
    
    return True


def test_confidence_factors():
    """测试置信度因子计算"""
    print("\n=== 测试置信度因子计算 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同时间框架和样本量
    confidence_scenarios = [
        {'timeframe': 'daily', 'sample_size': 25, 'avg_strength': 0.8},
        {'timeframe': 'intraday_5min', 'sample_size': 5, 'avg_strength': 0.3},
        {'timeframe': 'weekly', 'sample_size': 15, 'avg_strength': 0.6},
        {'timeframe': 'monthly', 'sample_size': 50, 'avg_strength': 0.9}
    ]
    
    for scenario in confidence_scenarios:
        print(f"--- {scenario['timeframe']} 时间框架 ---")
        
        # 创建测试数据
        breadth_metrics = {
            f'Sector{i}': MockBreadthMetrics(60, 0.6, 0.6)
            for i in range(scenario['sample_size'])
        }
        
        # 模拟原始分量
        raw_components = {
            'rii_strength': scenario['avg_strength'],
            'health_strength': scenario['avg_strength'],
            'momentum_strength': scenario['avg_strength'],
            'coherence_strength': scenario['avg_strength'],
            'divergence_strength': 0.2,
            'participation_strength': 0.6
        }
        
        # 计算置信度因子
        confidence_factors = analyzer._calculate_confidence_factors(
            raw_components, breadth_metrics, scenario['timeframe']
        )
        
        print(f"置信度因子:")
        for factor, value in confidence_factors.items():
            print(f"  {factor}: {value:.4f}")
        
        print()
    
    return True


def test_signal_grading():
    """测试信号分级功能"""
    print("\n=== 测试信号分级功能 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同强度和质量组合
    grading_scenarios = [
        {'strength': 0.9, 'quality': 0.9, 'expected': 'A+'},
        {'strength': 0.8, 'quality': 0.7, 'expected': 'A'},
        {'strength': 0.6, 'quality': 0.6, 'expected': 'B+'},
        {'strength': 0.5, 'quality': 0.5, 'expected': 'B'},
        {'strength': 0.3, 'quality': 0.3, 'expected': 'C'},
        {'strength': 0.1, 'quality': 0.2, 'expected': 'D'}
    ]
    
    print(f"信号分级测试:")
    print(f"{'强度':<8} {'质量':<8} {'等级':<6} {'预期':<6} {'匹配'}")
    print("-" * 40)
    
    for scenario in grading_scenarios:
        quality_assessment = {'overall_quality': scenario['quality']}
        
        grade = analyzer._grade_signal_strength(
            scenario['strength'], quality_assessment
        )
        
        match = "✅" if grade == scenario['expected'] else "❌"
        
        print(f"{scenario['strength']:<8.1f} {scenario['quality']:<8.1f} {grade:<6} {scenario['expected']:<6} {match}")
    
    return True


def test_comprehensive_scenario():
    """测试综合场景"""
    print("\n=== 测试综合场景 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建复杂的市场场景
    rotation_metrics = MockRotationMetrics(
        unified_rii=0.65,
        sector_momentum=np.array([2.1, -1.2, 0.8, 1.5, -0.5, 0.3, 1.8])
    )
    
    breadth_metrics = {
        'Technology': MockBreadthMetrics(
            82, 0.75, 0.80, 
            {'type': 'positive', 'severity': 0.15}
        ),
        'Healthcare': MockBreadthMetrics(
            68, 0.65, 0.70,
            {'type': 'none'}
        ),
        'Finance': MockBreadthMetrics(
            45, 0.35, 0.50,
            {'type': 'negative', 'severity': 0.25}
        ),
        'Energy': MockBreadthMetrics(
            28, 0.20, 0.35,
            {'type': 'negative', 'severity': 0.40}
        ),
        'Consumer': MockBreadthMetrics(
            72, 0.70, 0.75,
            {'type': 'positive', 'severity': 0.10}
        ),
        'Industrial': MockBreadthMetrics(
            55, 0.55, 0.60,
            {'type': 'none'}
        ),
        'Materials': MockBreadthMetrics(
            38, 0.30, 0.45,
            {'type': 'negative', 'severity': 0.20}
        )
    }
    
    print(f"📊 综合场景分析:")
    print(f"RII: {rotation_metrics.unified_rii}")
    print(f"板块数: {len(breadth_metrics)}")
    print(f"动量范围: {rotation_metrics.sector_momentum.min():.1f} ~ {rotation_metrics.sector_momentum.max():.1f}")
    
    # 执行完整分析
    result = analyzer.calculate_signal_strength_enhanced(
        rotation_metrics=rotation_metrics,
        breadth_metrics=breadth_metrics,
        timeframe='daily'
    )
    
    print(f"\n🎯 综合分析结果:")
    print(f"总体信号强度: {result.overall_strength:.4f}")
    print(f"信号等级: {result.signal_grade}")
    print(f"总体质量: {result.quality_assessment['overall_quality']:.4f}")
    print(f"总体置信度: {result.confidence_factors['overall_confidence']:.4f}")
    
    # 分析最强和最弱的分量
    contributions = result.component_contributions
    strongest = max(contributions.items(), key=lambda x: x[1])
    weakest = min(contributions.items(), key=lambda x: x[1])
    
    print(f"\n📈 分量分析:")
    print(f"最强分量: {strongest[0]} ({strongest[1]:.4f})")
    print(f"最弱分量: {weakest[0]} ({weakest[1]:.4f})")
    
    # 非线性调整分析
    adjustments = result.non_linear_adjustments
    max_adjustment = max(adjustments.items(), key=lambda x: abs(x[1]))
    
    print(f"\n⚡ 非线性调整:")
    print(f"最大调整: {max_adjustment[0]} ({max_adjustment[1]:+.4f})")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T4.1 增强信号强度计算\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础信号强度计算
        test_results.append(("基础信号强度计算", test_signal_strength_basic()))
        
        print("=" * 60)
        
        # 2. 测试非线性映射
        test_results.append(("非线性映射", test_non_linear_mapping()))
        
        print("=" * 60)
        
        # 3. 测试质量评估
        test_results.append(("质量评估", test_quality_assessment()))
        
        print("=" * 60)
        
        # 4. 测试置信度因子
        test_results.append(("置信度因子", test_confidence_factors()))
        
        print("=" * 60)
        
        # 5. 测试信号分级
        test_results.append(("信号分级", test_signal_grading()))
        
        print("=" * 60)
        
        # 6. 测试综合场景
        test_results.append(("综合场景", test_comprehensive_scenario()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T4.1 信号强度计算增强完成！")
            print("🚀 主要改进:")
            print("  - 6维度信号分量计算")
            print("  - 非线性映射优化")
            print("  - 多层次质量评估")
            print("  - 动态置信度调整")
            print("  - 智能信号分级")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
