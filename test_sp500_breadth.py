#!/usr/bin/env python3
"""
测试SP500市场广度计算
使用系统现有的MarketBreadthCalculator，只测试SP500一个市场
避免被坑，先验证计算逻辑是否正确
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def test_sp500_breadth():
    """测试SP500最近40天的5分钟市场广度计算"""
    print("🧪 测试SP500最近40天的5分钟市场广度计算")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 按照您的要求：40天，5分钟，SP500
    test_timeframe = '5m'
    test_market = 'SP500'
    test_days = 40

    print(f"📊 测试配置:")
    print(f"   时间框架: {test_timeframe}")
    print(f"   测试市场: {test_market}")
    print(f"   测试天数: {test_days} 天")
    print("💡 使用系统现有的MarketBreadthCalculator")
    print()
    
    try:
        from market_breadth_calculator import MarketBreadthCalculator
        import pymysql
        from db_settings import get_default_db_config
        
        # 确认执行
        confirm = input(f"是否开始测试SP500最近{test_days}天的{test_timeframe}市场广度计算? (y/N): ").strip().lower()
        if confirm != 'y':
            print("👋 用户取消测试")
            return False

        print(f"\n🔄 开始测试SP500最近{test_days}天的{test_timeframe}市场广度计算...")

        # 禁用预计算数据，强制使用实时计算
        print(f"🔧 禁用预计算数据，强制使用实时计算...")

        # 临时禁用预计算
        import market_breadth_calculator
        original_precomputed = market_breadth_calculator.PRECOMPUTED_AVAILABLE
        market_breadth_calculator.PRECOMPUTED_AVAILABLE = False

        # 创建市场广度计算器
        calculator = MarketBreadthCalculator(test_timeframe)
        print(f"✅ MarketBreadthCalculator({test_timeframe}) 创建成功（禁用预计算）")

        # 获取SP500的股票列表
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        cursor.execute("SELECT company FROM index_company_mapping_gics WHERE market='SP500'")
        sp500_companies = [row[0] for row in cursor.fetchall()]
        conn.close()

        if not sp500_companies:
            print("❌ 没有找到SP500股票列表")
            return False

        print(f"📊 SP500包含 {len(sp500_companies)} 只股票")
        print(f"📝 前10只股票: {sp500_companies[:10]}")

        # 检查5分钟时间框架的支持情况
        try:
            from timeframe_config import get_timeframe_config
            config_5m = get_timeframe_config('5m')
            print(f"📋 5m时间框架配置:")
            print(f"   支持52周新高新低: {config_5m.get('supports_52w_highs', False)}")
            print(f"   支持MA指标: {config_5m.get('supports_ma', False)}")
            print(f"   支持成交量: {config_5m.get('supports_volume', True)}")
        except Exception as e:
            print(f"⚠️  无法获取5m配置: {e}")

        start_time = time.time()

        # 使用MarketBreadthCalculator计算SP500市场广度
        print(f"\n🚀 开始计算SP500 {test_timeframe}市场广度...")
        print(f"💡 注意：")
        print(f"   - 应该下载365天数据来计算40天结果")
        print(f"   - 5分钟数据可能不支持52周新高新低和长期MA指标")
        print(f"   - 但涨跌家数和RSI应该是真实计算的")

        try:
            # 方法1：只计算不保存（用于验证计算逻辑）
            result = calculator.calculate_market_breadth('SP500', sp500_companies)

            duration = time.time() - start_time

            if result:
                print(f"✅ SP500 {test_timeframe} 计算成功！")
                print(f"⏱️  耗时: {duration:.1f} 秒")

                # 显示计算结果
                print(f"\n📊 计算结果预览:")
                if isinstance(result, dict):
                    for key, value in list(result.items())[:10]:  # 只显示前10个字段
                        print(f"   {key}: {value}")
                else:
                    print(f"   结果类型: {type(result)}")
                    print(f"   结果: {result}")

                # 方法2：手动保存SP500结果到数据库
                print(f"\n💾 现在手动保存SP500结果到数据库...")

                try:
                    from market_breadth_calculator import save_breadth_metrics_to_db

                    # 只保存SP500的结果
                    save_breadth_metrics_to_db([result])

                    print(f"✅ SP500数据已保存到数据库")

                    # 验证数据库中的结果
                    time.sleep(1)  # 等待数据库写入
                    verify_sp500_5m_results(test_timeframe, test_days)

                except Exception as e:
                    print(f"❌ 保存到数据库异常: {e}")
                    import traceback
                    traceback.print_exc()

                    # 如果保存失败，至少显示计算结果的分析
                    print(f"\n🔍 计算结果分析:")
                    analyze_calculation_result(result)

                # 恢复预计算设置
                market_breadth_calculator.PRECOMPUTED_AVAILABLE = original_precomputed

                return True
            else:
                print(f"❌ SP500 {test_timeframe} 计算返回空结果")
                # 恢复预计算设置
                market_breadth_calculator.PRECOMPUTED_AVAILABLE = original_precomputed
                return False

        except Exception as e:
            print(f"❌ SP500 {test_timeframe} 计算失败: {e}")
            import traceback
            traceback.print_exc()
            # 恢复预计算设置
            market_breadth_calculator.PRECOMPUTED_AVAILABLE = original_precomputed
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_calculation_result(result):
    """分析计算结果，检查是否使用了预计算数据"""
    print(f"📊 计算结果详细分析:")

    # 基础指标
    total = result.get('total_stocks', 0)
    advances = result.get('advances', 0)
    declines = result.get('declines', 0)
    unchanged = result.get('unchanged', 0)

    print(f"   总股票: {total}")
    print(f"   涨跌分布: 涨{advances} 跌{declines} 平{unchanged}")

    # 检查涨跌比例
    if total > 0:
        adv_pct = advances / total * 100
        dec_pct = declines / total * 100
        unch_pct = unchanged / total * 100
        print(f"   涨跌比例: 涨{adv_pct:.1f}% 跌{dec_pct:.1f}% 平{unch_pct:.1f}%")

        if unch_pct > 80:
            print(f"   ❌ 异常：{unch_pct:.1f}%股票不变，不符合真实市场")
        elif adv_pct + dec_pct > 80:
            print(f"   ✅ 正常：{adv_pct + dec_pct:.1f}%股票有涨跌变化")

    # 高级指标
    new_highs = result.get('new_highs_52w', 0)
    new_lows = result.get('new_lows_52w', 0)
    above_ma50 = result.get('above_ma50', 0)
    above_ma200 = result.get('above_ma200', 0)
    avg_rsi = result.get('avg_rsi', 50.0)

    print(f"   52周新高: {new_highs}")
    print(f"   52周新低: {new_lows}")
    print(f"   MA50以上: {above_ma50}")
    print(f"   MA200以上: {above_ma200}")
    print(f"   平均RSI: {avg_rsi}")

    # 检查预计算数据的问题
    issues = []

    if new_highs == 0 and new_lows == 0:
        issues.append("⚠️  52周新高新低都是0（可能使用了不准确的预计算数据）")

    if above_ma50 > 0 and above_ma200 > 0:
        print(f"   ✅ MA指标有数据（可能来自预计算）")

    if abs(avg_rsi - 50.0) < 0.01:
        issues.append("❌ RSI是固定50.0（使用了默认值）")
    else:
        print(f"   ✅ RSI有变化（{avg_rsi}）")

    # 成交量数据
    total_volume = result.get('total_volume', 0)
    adv_volume = result.get('advancing_volume', 0)
    dec_volume = result.get('declining_volume', 0)

    if total_volume > 0:
        print(f"   总成交量: {total_volume:,.0f}")
        print(f"   上涨成交量: {adv_volume:,.0f}")
        print(f"   下跌成交量: {dec_volume:,.0f}")
        print(f"   ✅ 成交量数据正常")

    if issues:
        print(f"\n⚠️  发现的问题:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print(f"\n✅ 计算结果看起来正常")

def verify_sp500_5m_results(timeframe, days):
    """验证SP500最近40天的5分钟计算结果"""
    print(f"\n🔍 验证SP500最近{days}天的{timeframe}计算结果...")

    try:
        import pymysql
        from db_settings import get_default_db_config
        from datetime import datetime, timedelta

        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        # 计算40天前的日期
        start_date = datetime.now() - timedelta(days=days)

        # 查询最近40天的SP500 5分钟数据
        cursor.execute("""
        SELECT COUNT(*) as total_records,
               COUNT(DISTINCT DATE(recorded_at)) as unique_days,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest,
               AVG(total_stocks) as avg_total_stocks,
               AVG(advances) as avg_advances,
               AVG(declines) as avg_declines,
               AVG(unchanged) as avg_unchanged,
               AVG(new_highs_52w) as avg_new_highs,
               AVG(new_lows_52w) as avg_new_lows,
               AVG(above_ma50) as avg_above_ma50,
               AVG(above_ma200) as avg_above_ma200,
               AVG(avg_rsi) as avg_rsi,
               AVG(internal_health) as avg_internal_health
        FROM market_breadth_metrics_gics
        WHERE market = 'SP500'
        AND timeframe = %s
        AND recorded_at >= %s
        """, (timeframe, start_date))

        result = cursor.fetchone()

        if result and result[0] > 0:
            total_records, unique_days, earliest, latest, avg_total, avg_adv, avg_dec, avg_unch, avg_highs, avg_lows, avg_ma50, avg_ma200, avg_rsi, avg_health = result

            print(f"📊 SP500 {timeframe} 最近{days}天数据统计:")
            print(f"   总记录数: {total_records}")
            print(f"   覆盖天数: {unique_days}")
            print(f"   时间范围: {earliest} 到 {latest}")
            print(f"   平均总股票数: {avg_total:.0f}")
            print(f"   平均上涨: {avg_adv:.1f}")
            print(f"   平均下跌: {avg_dec:.1f}")
            print(f"   平均不变: {avg_unch:.1f}")
            print(f"   平均52周新高: {avg_highs:.1f}")
            print(f"   平均52周新低: {avg_lows:.1f}")
            print(f"   平均MA50以上: {avg_ma50:.1f}")
            print(f"   平均MA200以上: {avg_ma200:.1f}")
            print(f"   平均RSI: {avg_rsi:.2f}")
            print(f"   平均内部健康度: {avg_health:.2f}")

            # 检查数据质量
            issues = []

            # 检查记录数量（5分钟数据，40天应该有很多记录）
            expected_min_records = days * 6 * 60 / 5  # 每天6小时交易，每5分钟一条
            if total_records < expected_min_records * 0.1:  # 至少10%的预期记录
                issues.append(f"❌ 记录数量过少: {total_records} < {expected_min_records * 0.1:.0f}")

            # 转换Decimal为float避免类型错误
            avg_total = float(avg_total) if avg_total else 0
            avg_adv = float(avg_adv) if avg_adv else 0
            avg_dec = float(avg_dec) if avg_dec else 0
            avg_unch = float(avg_unch) if avg_unch else 0
            avg_rsi = float(avg_rsi) if avg_rsi else 50.0
            avg_health = float(avg_health) if avg_health else 0

            # 检查涨跌家数 - 这是关键指标！
            if avg_adv < 1 and avg_dec < 1 and avg_unch > avg_total * 0.9:
                issues.append("❌ 涨跌家数严重异常：几乎所有股票都是不变！这说明计算逻辑有问题")
            elif avg_adv + avg_dec < avg_total * 0.1:
                issues.append("❌ 涨跌家数异常：涨跌股票太少，可能使用了简化计算")
            elif avg_adv + avg_dec + avg_unch < avg_total * 0.8:
                issues.append("⚠️  涨跌家数统计可能不完整")

            # 对于5分钟数据，52周新高新低可能为0（正常）
            if avg_highs == 0 and avg_lows == 0:
                print("   💡 52周新高新低为0（5分钟数据可能不支持此指标）")

            # 对于5分钟数据，MA指标可能为0（正常）
            if avg_ma50 == 0 and avg_ma200 == 0:
                print("   💡 MA指标为0（5分钟数据可能不支持长期MA）")

            # RSI检查 - 这是关键指标！
            if abs(avg_rsi - 50.0) < 0.01:
                issues.append("❌ RSI严重异常：使用了固定默认值50.0！这说明RSI计算有问题")
            elif avg_rsi < 10 or avg_rsi > 90:
                issues.append(f"⚠️  RSI值异常: {avg_rsi:.2f}")

            # 内部健康度检查
            if avg_health < 1.0:
                issues.append("❌ 内部健康度异常：接近0，可能计算有问题")
            elif avg_health < 0 or avg_health > 100:
                issues.append(f"⚠️  内部健康度超出范围: {avg_health:.2f}")

            # 总体数据质量评估
            print(f"\n🔍 数据质量分析:")
            print(f"   涨跌比例: 涨{avg_adv/avg_total*100:.1f}% 跌{avg_dec/avg_total*100:.1f}% 平{avg_unch/avg_total*100:.1f}%")

            if avg_unch/avg_total > 0.8:
                issues.append("❌ 数据质量严重问题：80%以上股票都是不变，这不符合真实市场情况")

            if issues:
                print(f"\n❌ 发现严重问题:")
                for issue in issues:
                    print(f"   {issue}")
                print(f"\n💡 问题分析:")
                print(f"   从您的数据看：平均涨{avg_adv:.1f} 跌{avg_dec:.1f} 平{avg_unch:.1f}")
                print(f"   这说明MarketBreadthCalculator仍然使用了简化计算逻辑！")
                print(f"   RSI={avg_rsi:.2f} 也是固定的50.0默认值")
                print(f"\n🔧 建议:")
                print(f"   1. 检查MarketBreadthCalculator的源码")
                print(f"   2. 确认是否真的下载了365天数据")
                print(f"   3. 验证涨跌计算是否基于真实价格变化")

                # 显示最新几条记录来证实问题
                show_recent_records(cursor, timeframe, 5)

                return False
            else:
                print(f"\n✅ SP500 {timeframe} 最近{days}天数据质量良好！")

                # 显示最新几条记录
                show_recent_records(cursor, timeframe, 5)

                return True
        else:
            print(f"❌ 没有找到SP500最近{days}天的{timeframe}数据")
            return False

        conn.close()

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_recent_records(cursor, timeframe, limit=5):
    """显示最新的几条记录"""
    print(f"\n📋 最新{limit}条记录:")

    cursor.execute("""
    SELECT recorded_at, total_stocks, advances, declines, unchanged, avg_rsi, internal_health
    FROM market_breadth_metrics_gics
    WHERE market = 'SP500' AND timeframe = %s
    ORDER BY recorded_at DESC
    LIMIT %s
    """, (timeframe, limit))

    records = cursor.fetchall()

    if records:
        print(f"{'时间':<20} {'总数':<6} {'涨':<6} {'跌':<6} {'平':<6} {'RSI':<8} {'健康度':<8}")
        print("-" * 70)
        for record in records:
            recorded, total, adv, dec, unch, rsi, health = record
            time_str = recorded.strftime('%m-%d %H:%M')
            print(f"{time_str:<20} {total:<6} {adv:<6} {dec:<6} {unch:<6} {rsi:<8.2f} {health:<8.2f}")
    else:
        print("   没有找到记录")

def verify_sp500_results(timeframe):
    """验证SP500的计算结果"""
    print(f"\n🔍 验证SP500 {timeframe}的计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 查询最新的SP500数据
        cursor.execute("""
        SELECT market, timeframe, total_stocks, advances, declines, unchanged,
               new_highs_52w, new_lows_52w, above_ma50, above_ma200, avg_rsi, 
               internal_health, recorded_at
        FROM market_breadth_metrics_gics 
        WHERE market = 'SP500' AND timeframe = %s
        ORDER BY recorded_at DESC 
        LIMIT 1
        """, (timeframe,))
        
        result = cursor.fetchone()
        
        if result:
            market, tf, total, adv, dec, unch, highs, lows, ma50, ma200, rsi, health, recorded = result
            
            print(f"📊 SP500 {timeframe} 最新数据:")
            print(f"   记录时间: {recorded}")
            print(f"   总股票数: {total}")
            print(f"   上涨: {adv}")
            print(f"   下跌: {dec}")
            print(f"   不变: {unch}")
            print(f"   52周新高: {highs}")
            print(f"   52周新低: {lows}")
            print(f"   MA50以上: {ma50}")
            print(f"   MA200以上: {ma200}")
            print(f"   平均RSI: {rsi}")
            print(f"   内部健康度: {health}")
            
            # 检查是否有简化指标
            issues = []
            
            if adv == 0 and dec == 0 and unch > 0:
                issues.append("❌ 涨跌家数异常：所有股票都是不变")
            
            if highs == 0 and lows == 0:
                issues.append("⚠️  52周新高新低都是0（可能正常）")
            
            if ma50 == 0 and ma200 == 0:
                issues.append("❌ MA指标异常：没有股票在MA之上")
            
            if rsi == 50.0:
                issues.append("❌ RSI异常：使用了默认值50.0")
            
            if health == 0.0:
                issues.append("❌ 内部健康度异常：为0")
            
            if issues:
                print(f"\n⚠️  发现问题:")
                for issue in issues:
                    print(f"   {issue}")
                print(f"\n💡 这些可能是简化指标，需要检查计算逻辑")
                return False
            else:
                print(f"\n✅ 所有指标看起来都是真实计算的！")
                return True
        else:
            print(f"❌ 没有找到SP500 {timeframe}的数据")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_multiple_timeframes():
    """测试多个时间框架"""
    print(f"\n🔄 测试多个时间框架...")
    
    timeframes = ['1d', '1h', '5m']  # 从最可靠的开始测试
    results = {}
    
    for tf in timeframes:
        print(f"\n📈 测试 {tf} 时间框架...")
        
        try:
            from market_breadth_calculator import MarketBreadthCalculator
            
            calculator = MarketBreadthCalculator(tf)
            
            # 获取SP500股票列表
            import pymysql
            from db_settings import get_default_db_config
            
            config = get_default_db_config()
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            cursor.execute("SELECT company FROM index_company_mapping_gics WHERE market='SP500'")
            sp500_companies = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            if not sp500_companies:
                print(f"❌ {tf}: 没有找到SP500股票列表")
                results[tf] = False
                continue
            
            # 计算市场广度
            result = calculator.calculate_market_breadth('SP500', sp500_companies)
            
            if result:
                print(f"✅ {tf}: 计算成功")
                # 验证结果
                is_valid = verify_sp500_results(tf)
                results[tf] = is_valid
            else:
                print(f"❌ {tf}: 计算失败")
                results[tf] = False
                
        except Exception as e:
            print(f"❌ {tf}: 异常 - {e}")
            results[tf] = False
    
    # 总结
    print(f"\n📊 测试总结:")
    for tf, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {tf}: {status}")
    
    return results

def main():
    """主函数"""
    print("🧪 SP500最近40天5分钟市场广度计算测试工具")
    print("测试系统现有的MarketBreadthCalculator是否能正确计算5分钟数据")
    print()

    # 测试SP500最近40天的5分钟数据
    success = test_sp500_breadth()

    if success:
        print(f"\n🎉 SP500 5分钟40天测试成功！")
        print("💡 测试结果说明:")
        print("1. 如果涨跌家数有真实值 → 基础计算正确")
        print("2. 如果RSI不是50.0 → RSI计算正确")
        print("3. 52周新高新低可能为0 → 5分钟数据不支持此指标（正常）")
        print("4. MA指标可能为0 → 5分钟数据不支持长期MA（正常）")

        # 询问是否测试其他时间框架对比
        test_more = input(f"\n是否测试其他时间框架进行对比? (y/N): ").strip().lower()
        if test_more == 'y':
            results = test_multiple_timeframes()

            success_count = sum(1 for v in results.values() if v)
            total_count = len(results)

            print(f"\n📊 多时间框架测试结果: {success_count}/{total_count} 成功")

            if success_count >= total_count * 0.5:
                print("✅ 大部分时间框架测试成功，MarketBreadthCalculator工作正常！")
                print("💡 现在可以运行完整的40天6时间框架计算了")
            else:
                print("⚠️  多数时间框架有问题，需要检查MarketBreadthCalculator")
    else:
        print(f"\n❌ SP500 5分钟测试失败")
        print("💡 可能的问题:")
        print("1. MarketBreadthCalculator不支持5m时间框架")
        print("2. 5分钟数据下载失败")
        print("3. 计算逻辑有错误")
        print("4. 数据库连接问题")

        # 建议测试其他时间框架
        test_other = input(f"\n是否测试1d时间框架确认系统是否正常? (y/N): ").strip().lower()
        if test_other == 'y':
            print("\n🔄 测试1d时间框架...")
            try:
                from market_breadth_calculator import MarketBreadthCalculator
                import pymysql
                from db_settings import get_default_db_config

                calculator = MarketBreadthCalculator('1d')

                config = get_default_db_config()
                conn = pymysql.connect(**config)
                cursor = conn.cursor()
                cursor.execute("SELECT company FROM index_company_mapping_gics WHERE market='SP500'")
                sp500_companies = [row[0] for row in cursor.fetchall()]
                conn.close()

                result = calculator.calculate_market_breadth('SP500', sp500_companies)

                if result:
                    print("✅ 1d时间框架测试成功，说明MarketBreadthCalculator基本正常")
                    print("⚠️  问题可能出在5m时间框架的支持上")
                else:
                    print("❌ 1d时间框架也失败，MarketBreadthCalculator有问题")

            except Exception as e:
                print(f"❌ 1d测试异常: {e}")

    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
