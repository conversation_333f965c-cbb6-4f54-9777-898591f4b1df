#!/usr/bin/env python3
"""
多时间框架分析系统 - v41标准Web界面
完全符合多时间框架板块轮动与MarketBreadth系统_v41.py文档标准
"""

import sys
import os
from datetime import datetime
import json

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

try:
    from flask import Flask, render_template_string, request, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("⚠️ Flask未安装，Web界面不可用")
    print("安装命令: pip install flask")

app = Flask(__name__)

def convert_original_to_enhanced_format(original_report, sector):
    """将原版分析结果转换为增强版输入格式"""
    try:
        # 从原版报告中提取关键数据
        market_data = {
            'market_volatility': 0.2,  # 默认值，可以从原版数据中计算
            'sectors': {},
            'market_indicators': {}
        }

        # 如果原版报告包含广度数据
        if isinstance(original_report, dict):
            # 提取板块数据
            if 'breadth_analysis' in original_report:
                breadth = original_report['breadth_analysis']
                market_data['sectors'][sector] = {
                    'price_change': breadth.get('price_change', 0.0),
                    'volume_ratio': breadth.get('volume_ratio', 1.0),
                    'breadth_score': breadth.get('internal_health', 50) / 100.0
                }

            # 提取市场指标
            if 'market_indicators' in original_report:
                market_data['market_indicators'] = original_report['market_indicators']

            # 从轮动数据估算波动率
            if 'rotation_analysis' in original_report:
                rotation = original_report['rotation_analysis']
                if 'unified_rii' in rotation:
                    # 基于RII估算波动率
                    rii = rotation['unified_rii']
                    market_data['market_volatility'] = min(0.5, max(0.1, abs(rii - 0.5) * 2))

        # 如果没有足够数据，使用默认值
        if not market_data['sectors']:
            market_data['sectors'][sector] = {
                'price_change': 0.01,
                'volume_ratio': 1.0,
                'breadth_score': 0.5
            }

        if not market_data['market_indicators']:
            market_data['market_indicators'] = {
                'advance_decline_ratio': 1.0,
                'new_highs_lows_ratio': 1.0
            }

        return market_data

    except Exception as e:
        print(f"数据转换失败: {e}")
        # 返回默认数据
        return {
            'market_volatility': 0.2,
            'sectors': {
                sector: {
                    'price_change': 0.01,
                    'volume_ratio': 1.0,
                    'breadth_score': 0.5
                }
            },
            'market_indicators': {
                'advance_decline_ratio': 1.0,
                'new_highs_lows_ratio': 1.0
            }
        }

def serialize_timestamp(obj):
    """序列化时间戳对象"""
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    elif hasattr(obj, 'strftime'):
        return obj.strftime('%Y-%m-%d %H:%M:%S')
    else:
        return str(obj)

def build_complete_report(breadth_data, rotation_data, mtf_data, market_name, sector):
    """基于真实数据库数据构建完整报告"""

    # 解析数据库数据并转换类型
    if breadth_data:
        (recorded_at, market, total_stocks, advances, declines, unchanged,
         advancing_volume, declining_volume, total_volume,
         new_highs_52w, new_lows_52w, above_ma50, above_ma200, avg_rsi,
         market_cap_weighted_return, equal_weighted_return, purity,
         internal_health, momentum_coherence, divergence_type,
         divergence_severity, divergence_confidence, divergence_risk_level) = breadth_data

        # 转换 Decimal 类型为 float 并校验范围
        avg_rsi = max(0.0, min(100.0, float(avg_rsi))) if avg_rsi is not None else 50.0
        internal_health = max(0.0, min(100.0, float(internal_health))) if internal_health is not None else 50.0
        momentum_coherence = max(0.0, min(1.0, float(momentum_coherence))) if momentum_coherence is not None else 0.5
        divergence_severity = max(0.0, min(1.0, float(divergence_severity))) if divergence_severity is not None else 0.0
    else:
        # 默认值
        recorded_at = datetime.now()
        total_stocks = 100
        advances = 50
        declines = 40
        internal_health = 50
        avg_rsi = 50
        momentum_coherence = 0.5
        divergence_type = 'none'
        divergence_severity = 0

    # 从MTF数据获取关键指标
    if mtf_data:
        (mtf_recorded_at, market_regime, consensus_score, signal_reliability,
         suggested_position, operation_strategy, top_sectors_json,
         avoid_sectors_json, key_insights_json, warnings_json,
         unified_rii, rotation_stage, risk_level) = mtf_data

        # 转换 Decimal 类型为 float 并校验范围
        consensus_score = max(0.0, min(1.0, float(consensus_score))) if consensus_score is not None else 0.5
        signal_reliability = max(0.0, min(1.0, float(signal_reliability))) if signal_reliability is not None else 0.7
        unified_rii = max(0.0, min(1.0, float(unified_rii))) if unified_rii is not None else 0.5
    else:
        # 默认值
        market_regime = 'normal_market'
        consensus_score = 0.5
        signal_reliability = 0.7
        suggested_position = 50
        operation_strategy = '基于市场广度分析'
        rotation_stage = '稳定期'
        risk_level = 'medium'

    # 从轮动数据获取额外信息
    if rotation_data:
        (rot_recorded_at, rot_sector, rotation_intensity_index, rot_rotation_stage,
         price_dispersion, rank_velocity, volume_concentration,
         relative_strength, optimal_weights, rot_risk_level) = rotation_data

        # 转换 Decimal 类型为 float 并校验范围
        rotation_intensity_index = max(0.0, min(1.0, float(rotation_intensity_index))) if rotation_intensity_index is not None else 0.5

        # 使用轮动数据更新相关字段
        if rot_rotation_stage:
            rotation_stage = rot_rotation_stage
        if rot_risk_level:
            risk_level = rot_risk_level
        if rotation_intensity_index:
            unified_rii = rotation_intensity_index

    # 构建符合前端期望的完整数据结构
    report = {
        'metadata': {
            'report_type': '增强版多时间框架分析',
            'version': '2.0',
            'timestamp': recorded_at.strftime('%Y-%m-%d %H:%M:%S'),
            'timeframes_analyzed': 3,
            'sector': sector,
            'analysis_method': 'database_integrated'
        },

        'executive_summary': {
            'main_conclusion': f'市场处于{market_regime}，建议稳健操作（{suggested_position}%仓位）',
            'confidence_level': f'{signal_reliability * 100:.1f}%',
            'market_phase': rotation_stage,
            'primary_driver': operation_strategy,
            'action_required': operation_strategy
        },

        'market_overview': {
            'market_regime': {
                'value': market_regime,
                'confidence': signal_reliability,
                'interpretation': f'市场状态: {market_regime}',
                'implications': '基于多时间框架分析确定的市场状态'
            },
            'consensus_score': {
                'value': consensus_score,
                'interpretation': '市场共识水平',
                'calculation': '基于多个时间框架的一致性分析'
            },
            'signal_reliability': {
                'value': signal_reliability,
                'interpretation': f'可靠性: {signal_reliability * 100:.1f}%',
                'factors': '基于数据质量和分析一致性'
            },
            'suggested_position': suggested_position,
            'operation_strategy': operation_strategy
        },

        'breadth_analysis': {
            'internal_health': internal_health,
            'momentum_coherence': momentum_coherence,
            'advance_decline_ratio': advances / max(1, declines),
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines
        },

        'timeframe_analysis': {
            'daily': {
                'signal': 'buy' if suggested_position > 60 else 'sell' if suggested_position < 40 else 'hold',
                'strength': signal_reliability,
                'confidence': signal_reliability,
                'rsi': avg_rsi,
                'health': internal_health,
                'divergence': {
                    'type': divergence_type or 'none',
                    'severity': divergence_severity or 0
                },
                'momentum_coherence': momentum_coherence,
                'recommendation': operation_strategy
            },
            'weekly': {
                'signal': 'hold',
                'strength': signal_reliability * 0.9,
                'confidence': signal_reliability * 0.9,
                'rsi': avg_rsi,
                'health': internal_health,
                'divergence': {
                    'type': 'none',
                    'severity': 0
                },
                'momentum_coherence': momentum_coherence,
                'recommendation': '周线分析'
            },
            'monthly': {
                'signal': 'hold',
                'strength': signal_reliability * 0.8,
                'confidence': signal_reliability * 0.8,
                'rsi': avg_rsi,
                'health': internal_health,
                'divergence': {
                    'type': 'none',
                    'severity': 0
                },
                'momentum_coherence': momentum_coherence,
                'recommendation': '月线分析'
            }
        },

        'rotation_analysis': {
            'unified_rii': unified_rii if mtf_data else 0.5,
            'rotation_stage': rotation_stage,
            'risk_level': risk_level
        },

        'sector_analysis': {
            market_name: {
                'breadth_metrics': {
                    'internal_health': internal_health,
                    'momentum_coherence': momentum_coherence,
                    'advance_decline_ratio': advances / max(1, declines)
                }
            }
        },

        'recommendations': {
            'top_sectors': top_sectors_json if mtf_data else '[]',
            'avoid_sectors': avoid_sectors_json if mtf_data else '[]',
            'key_insights': key_insights_json if mtf_data else '["基于真实数据库数据的分析"]',
            'warnings': warnings_json if mtf_data else '[]'
        }
    }

    return report

def merge_original_and_enhanced_results(original_report, enhanced_result, output_format):
    """合并原版和增强版分析结果"""
    try:
        # 序列化增强结果中的时间戳
        enhanced_timestamp = serialize_timestamp(enhanced_result.analysis_timestamp)

        # 如果是简单格式，主要返回原版数据，但添加增强信息
        if output_format == 'summary':
            if isinstance(original_report, dict):
                # 添加增强版的决策信息
                original_report['enhanced_decision'] = {
                    'suggested_position': enhanced_result.unified_decision.get('suggested_position', 50),
                    'action': enhanced_result.unified_decision.get('action', 'hold'),
                    'confidence': enhanced_result.analysis_confidence,
                    'risk_level': enhanced_result.unified_decision.get('risk_level', 'medium'),
                    'market_regime': enhanced_result.market_regime
                }
                return original_report
            else:
                # 如果原版返回字符串，包装成字典
                return {
                    'original_analysis': original_report,
                    'enhanced_decision': {
                        'suggested_position': enhanced_result.unified_decision.get('suggested_position', 50),
                        'action': enhanced_result.unified_decision.get('action', 'hold'),
                        'confidence': enhanced_result.analysis_confidence,
                        'risk_level': enhanced_result.unified_decision.get('risk_level', 'medium'),
                        'market_regime': enhanced_result.market_regime
                    }
                }

        # 完整格式，返回详细的合并结果
        elif output_format == 'full':
            # 直接返回原版报告，但添加增强信息
            if isinstance(original_report, dict):
                # 转换 timeframe_analysis 格式以匹配前端期望
                if 'timeframe_analysis' in original_report:
                    converted_timeframes = {}
                    for tf_name, tf_data in original_report['timeframe_analysis'].items():
                        converted_timeframes[tf_name] = {
                            'signal': 'hold',  # 默认信号
                            'strength': tf_data.get('signal_quality', {}).get('strength', 0.5),
                            'confidence': tf_data.get('signal_quality', {}).get('confidence', 0.5),
                            'rsi': 50,  # 默认RSI
                            'health': tf_data.get('market_health', 50),
                            'divergence': {
                                'type': 'none',
                                'severity': 0
                            },
                            'momentum_coherence': 0.5,  # 默认动量一致性
                            'recommendation': tf_data.get('interpretation', '正常关注')
                        }
                    original_report['timeframe_analysis'] = converted_timeframes

                # 在原版报告中添加增强分析信息
                original_report['enhanced_analysis'] = {
                    'analysis_timestamp': enhanced_timestamp,
                    'market_regime': enhanced_result.market_regime,
                    'analysis_confidence': enhanced_result.analysis_confidence,
                    'unified_decision': enhanced_result.unified_decision,
                    'performance_metrics': enhanced_result.performance_metrics
                }
                original_report['integration_info'] = {
                    'enhanced_available': True,
                    'data_source': 'real_market_data',
                    'analysis_method': 'integrated_enhanced'
                }
                return original_report
            else:
                # 如果原版报告不是字典，包装它
                return {
                    'metadata': {
                        'report_type': '增强版分析报告',
                        'version': '2.0',
                        'timestamp': enhanced_timestamp,
                        'analysis_method': 'integrated_enhanced'
                    },
                    'original_analysis': original_report,
                    'enhanced_analysis': {
                        'analysis_timestamp': enhanced_timestamp,
                        'market_regime': enhanced_result.market_regime,
                        'analysis_confidence': enhanced_result.analysis_confidence,
                        'unified_decision': enhanced_result.unified_decision,
                        'performance_metrics': enhanced_result.performance_metrics
                    },
                    'integration_info': {
                        'enhanced_available': True,
                        'data_source': 'real_market_data',
                        'analysis_method': 'integrated_enhanced'
                    }
                }

        # 默认返回原版结果
        return original_report

    except Exception as e:
        print(f"结果合并失败: {e}")
        return original_report

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>多时间框架综合分析系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .input-section { margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; }
        .result-section { margin-top: 20px; }
        .sector-result { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: white; }
        .decision-buy { color: #28a745; font-weight: bold; }
        .decision-sell { color: #dc3545; font-weight: bold; }
        .decision-hold { color: #ffc107; font-weight: bold; }
        .timeframe-signals { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0; }
        .signal-card { padding: 10px; border: 1px solid #eee; border-radius: 3px; background: #f9f9f9; }
        .weights { display: flex; flex-wrap: wrap; gap: 10px; margin: 10px 0; }
        .weight-item { padding: 5px 10px; background: #e9ecef; border-radius: 3px; font-size: 0.9em; }
        .risk-factors { margin: 10px 0; }
        .risk-item { padding: 5px; margin: 2px 0; background: #fff3cd; border-left: 4px solid #ffc107; }
        .loading { text-align: center; color: #666; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        input[type="text"] { padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 300px; font-size: 16px; }
        .batch-results { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }

        /* 板块轮动指标样式 */
        .rotation-metrics { background: #f0f8ff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .rotation-header { display: flex; align-items: center; margin-bottom: 15px; }
        .rotation-header h4 { margin: 0; color: #007bff; }
        .rotation-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .rotation-card { background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; }
        .rotation-value { font-size: 24px; font-weight: bold; margin: 5px 0; }
        .rotation-label { font-size: 12px; color: #666; margin-bottom: 5px; }
        .rotation-stage { padding: 8px 16px; border-radius: 20px; font-weight: bold; text-align: center; margin: 10px 0; }
        .stage-stable { background: #d4edda; color: #155724; }
        .stage-convergence { background: #fff3cd; color: #856404; }
        .stage-startup { background: #cce5ff; color: #004085; }
        .stage-acceleration { background: #f8d7da; color: #721c24; }
        .stage-chaos { background: #e2e3e5; color: #383d41; }
        .rii-gauge { position: relative; width: 100px; height: 50px; margin: 10px auto; }
        .rii-arc { stroke: #ddd; stroke-width: 8; fill: none; }
        .rii-progress { stroke: #007bff; stroke-width: 8; fill: none; stroke-linecap: round; }
        .timeframe-tabs { display: flex; border-bottom: 2px solid #ddd; margin: 20px 0 10px 0; }
        .timeframe-tab { padding: 10px 20px; cursor: pointer; border: none; background: #f8f9fa; margin-right: 2px; }
        .timeframe-tab.active { background: #007bff; color: white; }
        .timeframe-content { display: none; }
        .timeframe-content.active { display: block; }
        .mtf-summary { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff; }
        .consensus-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 5px 0; }
        .consensus-fill { height: 100%; background: linear-gradient(90deg, #dc3545, #ffc107, #28a745); transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 多时间框架板块轮动与MarketBreadth系统 v4.1</h1>
            <p>Multi-Timeframe Sector Rotation & Market Breadth Analysis System</p>
            <p style="font-size: 14px; color: #666;">符合v41文档标准 | 人类和LLM双可读输出</p>
        </div>
        
        <div class="input-section">
            <h3>📊 v41标准板块分析</h3>

            <!-- 板块选择区域 -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 10px;">选择分析板块:</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 10px; margin-bottom: 15px;">
                    <!-- 数据库真实板块 - 科技类 -->
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #28a745;">💻 科技信息</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Information Technology" checked style="margin-right: 5px;">Information Technology</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Software & Services" style="margin-right: 5px;">Software & Services</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Technology Hardware & Equipmen" style="margin-right: 5px;">Technology Hardware & Equipment</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Semiconductors & Semiconductor" style="margin-right: 5px;">Semiconductors & Semiconductor</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #dc3545;">🏦 金融服务</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Financials" style="margin-right: 5px;">Financials</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Banks" style="margin-right: 5px;">Banks</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Insurance" style="margin-right: 5px;">Insurance</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Financial Services" style="margin-right: 5px;">Financial Services</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #17a2b8;">🏥 医疗保健</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Health Care" style="margin-right: 5px;">Health Care</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Pharmaceuticals, Biotechnology" style="margin-right: 5px;">Pharmaceuticals, Biotechnology</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Health Care Equipment & Servic" style="margin-right: 5px;">Health Care Equipment & Services</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #ffc107;">🛍️ 消费板块</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Consumer Discretionary" style="margin-right: 5px;">Consumer Discretionary</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Consumer Staples" style="margin-right: 5px;">Consumer Staples</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Automobiles & Components" style="margin-right: 5px;">Automobiles & Components</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Consumer Durables & Apparel" style="margin-right: 5px;">Consumer Durables & Apparel</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Consumer Services" style="margin-right: 5px;">Consumer Services</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #6f42c1;">⚡ 能源公用</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Energy" style="margin-right: 5px;">Energy</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Utilities" style="margin-right: 5px;">Utilities</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #20c997;">🏗️ 工业材料</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Industrials" style="margin-right: 5px;">Industrials</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Materials" style="margin-right: 5px;">Materials</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Capital Goods" style="margin-right: 5px;">Capital Goods</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Transportation" style="margin-right: 5px;">Transportation</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Commercial & Professional Serv" style="margin-right: 5px;">Commercial & Professional Services</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #fd7e14;">📡 通信地产</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Communication Services" style="margin-right: 5px;">Communication Services</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Media & Entertainment" style="margin-right: 5px;">Media & Entertainment</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Telecommunication Services" style="margin-right: 5px;">Telecommunication Services</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Real Estate" style="margin-right: 5px;">Real Estate</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Equity Real Estate Investment" style="margin-right: 5px;">Equity Real Estate Investment</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="Real Estate Management & Devel" style="margin-right: 5px;">Real Estate Management & Development</label>
                    </div>

                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <h5 style="margin: 0 0 8px 0; color: #6c757d;">📊 市场指数</h5>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="SP500" style="margin-right: 5px;">S&P 500</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="NASDAQ100" style="margin-right: 5px;">NASDAQ 100</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="RUSSELL2000" style="margin-right: 5px;">Russell 2000</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="NASDAQ" style="margin-right: 5px;">NASDAQ</label>
                        <label style="display: block; margin: 3px 0;"><input type="radio" name="sector" value="NYSE" style="margin-right: 5px;">NYSE</label>
                    </div>
                </div>

                <!-- 快速选择按钮 -->
                <div style="margin-bottom: 15px;">
                    <h5 style="margin-bottom: 8px;">快速选择热门板块:</h5>
                    <button type="button" onclick="selectSector('Information Technology')" style="margin: 2px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">Information Technology</button>
                    <button type="button" onclick="selectSector('Financials')" style="margin: 2px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">Financials</button>
                    <button type="button" onclick="selectSector('Health Care')" style="margin: 2px; padding: 5px 10px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;">Health Care</button>
                    <button type="button" onclick="selectSector('Energy')" style="margin: 2px; padding: 5px 10px; background: #6f42c1; color: white; border: none; border-radius: 3px; cursor: pointer;">Energy</button>
                    <button type="button" onclick="selectSector('Consumer Discretionary')" style="margin: 2px; padding: 5px 10px; background: #ffc107; color: black; border: none; border-radius: 3px; cursor: pointer;">Consumer Discretionary</button>
                    <button type="button" onclick="selectSector('SP500')" style="margin: 2px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">S&P 500</button>
                    <button type="button" onclick="selectSector('NASDAQ100')" style="margin: 2px; padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">NASDAQ 100</button>
                </div>
            </div>

            <!-- 分析选项 -->
            <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <div style="display: flex; flex-wrap: wrap; gap: 15px; align-items: center;">
                    <div>
                        <label style="margin-right: 10px;"><strong>输出格式:</strong></label>
                        <select id="outputFormat" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="full">完整报告 (Full Report)</option>
                            <option value="summary">摘要格式 (Summary)</option>
                            <option value="signals">信号格式 (Signals)</option>
                        </select>
                    </div>

                    <div>
                        <label style="margin-right: 10px;"><strong>时间框架 (真实多时间框架):</strong></label>
                        <select id="timeframes" style="padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="5m,15m,1h">短期组合 (5分钟+15分钟+1小时)</option>
                            <option value="15m,1h,1d">标准组合 (15分钟+1小时+日线)</option>
                            <option value="1h,1d,1w">长期组合 (1小时+日线+周线)</option>
                            <option value="1d,1w,1M">趋势组合 (日线+周线+月线)</option>
                            <option value="5m,15m,1h,1d">日内组合 (5分钟+15分钟+1小时+日线)</option>
                            <option value="1h,1d,1w,1M">投资组合 (1小时+日线+周线+月线)</option>
                            <option value="5m,15m,1h,1d,1w,1M">完整组合 (全部6个时间框架)</option>
                            <option value="1h">单一时间框架 (1小时)</option>
                            <option value="1d">单一时间框架 (日线)</option>
                            <option value="1M">单一时间框架 (月线)</option>
                        </select>
                        <div style="margin-top: 5px; font-size: 12px; color: #28a745;">
                            ✅ <strong>完整多时间框架数据:</strong> 5分钟(38条)、15分钟(38条)、1小时(76条)、日线(38条)、周线(38条)、月线(38条)
                        </div>
                    </div>

                    <div>
                        <label style="margin-right: 20px;">
                            <input type="checkbox" id="showLLMFormat" style="margin-right: 5px;">
                            显示LLM友好格式
                        </label>
                        <label style="margin-right: 20px;">
                            <select id="llmFormatType" style="margin-right: 5px;" disabled>
                                <option value="formatted">格式化版本</option>
                                <option value="basic">基础版本</option>
                            </select>
                        </label>
                        <label>
                            <input type="checkbox" id="showAvoidSectors" checked style="margin-right: 5px;">
                            显示避免板块
                        </label>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div style="text-align: center; margin-bottom: 20px;">
                <button onclick="analyzeSector()" style="padding: 12px 30px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    🚀 v41标准分析
                </button>
                <button onclick="analyzeBatch()" style="padding: 12px 30px; font-size: 16px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    📊 批量分析
                </button>
                <button onclick="compareSelected()" style="padding: 12px 30px; font-size: 16px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    ⚖️ 板块对比
                </button>
            </div>

            <div style="background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745;">
                <p style="margin: 0; font-size: 14px;">
                    <strong>🎉 完整多时间框架数据可用:</strong><br>
                    • <strong>5分钟:</strong> 38个板块，最新数据 2025-07-31 09:23:28<br>
                    • <strong>15分钟:</strong> 38个板块，最新数据 2025-07-31 09:24:14<br>
                    • <strong>1小时:</strong> 38个板块，76条记录 (历史数据)<br>
                    • <strong>日线:</strong> 38个板块，最新数据 2025-07-31 09:25:42<br>
                    • <strong>周线:</strong> 38个板块，最新数据 2025-07-31 09:26:31<br>
                    • <strong>月线:</strong> 38个板块，聚合数据 2025-07-31 09:43:37<br>
                    • <strong>分析模式:</strong> 完整的6时间框架综合分析
                </p>
            </div>

            <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; border-left: 4px solid #007bff; margin-top: 10px;">
                <p style="margin: 0; font-size: 14px;">
                    <strong>🚀 多时间框架特性:</strong><br>
                    • <strong>时间框架一致性分析:</strong> 检测各时间框架信号的一致程度<br>
                    • <strong>动态权重调整:</strong> 根据市场状态自动调整各时间框架权重<br>
                    • <strong>冲突信号处理:</strong> 智能处理不同时间框架间的信号冲突<br>
                    • <strong>v41标准输出:</strong> 完整报告、摘要、信号三种格式，支持LLM友好输出
                </p>
            </div>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <p>🔄 分析中，请稍候...</p>
        </div>
        
        <div id="results" class="result-section"></div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').innerHTML = '';
        }
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        function selectSector(sectorName) {
            // 选中对应的单选按钮
            const radioButtons = document.querySelectorAll('input[name="sector"]');
            radioButtons.forEach(radio => {
                if (radio.value === sectorName) {
                    radio.checked = true;
                }
            });
        }

        function getSelectedSector() {
            const selectedRadio = document.querySelector('input[name="sector"]:checked');
            return selectedRadio ? selectedRadio.value : null;
        }

        // 控制LLM格式类型选择器的启用状态
        document.getElementById('showLLMFormat').addEventListener('change', function() {
            document.getElementById('llmFormatType').disabled = !this.checked;
        });

        function analyzeSector() {
            const sector = getSelectedSector();
            const outputFormat = document.getElementById('outputFormat').value;
            const timeframes = document.getElementById('timeframes').value.split(',');
            const showLLM = document.getElementById('showLLMFormat').checked;
            const llmFormatType = document.getElementById('llmFormatType').value;
            const showAvoid = document.getElementById('showAvoidSectors').checked;

            if (!sector) {
                alert('请选择要分析的板块');
                return;
            }

            showLoading();

            fetch('/analyze_v41', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    sector: sector,
                    timeframes: timeframes,
                    output_format: outputFormat,
                    show_llm_format: showLLM,
                    llm_format_type: llmFormatType,
                    show_avoid_sectors: showAvoid
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                displayV41Result(data, outputFormat, showLLM, showAvoid);
            })
            .catch(error => {
                hideLoading();
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ v41分析失败: ' + error + '</p>';
            });
        }
        
        function analyzeBatch() {
            const outputFormat = document.getElementById('outputFormat').value;
            const timeframes = document.getElementById('timeframes').value.split(',');

            showLoading();

            fetch('/analyze_batch_v41', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    output_format: outputFormat,
                    timeframes: timeframes
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                displayBatchResults(data);
            })
            .catch(error => {
                hideLoading();
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ 批量分析失败: ' + error + '</p>';
            });
        }

        function compareSelected() {
            // 获取所有选中的板块（这里简化为当前选中的板块）
            const sector = getSelectedSector();
            if (!sector) {
                alert('请先选择要对比的板块');
                return;
            }

            // 对比分析（这里可以扩展为多板块对比）
            showLoading();

            const timeframes = document.getElementById('timeframes').value.split(',');

            fetch('/compare_sectors', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    sectors: [sector],  // 可以扩展为多个板块
                    timeframes: timeframes
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                displayComparisonResults(data);
            })
            .catch(error => {
                hideLoading();
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ 板块对比失败: ' + error + '</p>';
            });
        }
        
        function displayV41Result(data, outputFormat, showLLM, showAvoid) {
            try {
                if (data.error) {
                    document.getElementById('results').innerHTML = '<p style="color: red;">❌ ' + data.error + '</p>';
                    return;
                }

                const report = data.report;

                // 数据安全性预处理
                if (report && report.unified_decision && report.unified_decision.operation_guidance) {
                    const guidance = report.unified_decision.operation_guidance;

                    // 确保operation_guidance的所有字段都是安全的
                    if (typeof guidance.key_points === 'string') {
                        guidance.key_points = [guidance.key_points];
                    } else if (!Array.isArray(guidance.key_points)) {
                        guidance.key_points = [];
                    }
                }

                // 处理risk_assessment
                if (report && report.risk_assessment) {
                    const risk = report.risk_assessment;

                    if (typeof risk.risk_factors === 'string') {
                        risk.risk_factors = [risk.risk_factors];
                    } else if (!Array.isArray(risk.risk_factors)) {
                        risk.risk_factors = [];
                    }

                    if (typeof risk.mitigation_strategies === 'string') {
                        risk.mitigation_strategies = [risk.mitigation_strategies];
                    } else if (!Array.isArray(risk.mitigation_strategies)) {
                        risk.mitigation_strategies = [];
                    }
                }

                let html = '';

                // 根据输出格式显示不同内容
                if (outputFormat === 'summary') {
                    html = displaySummaryFormat(report);
                } else if (outputFormat === 'signals') {
                    html = displaySignalsFormat(report);
                } else {
                    html = displayFullReport(report, showAvoid);
                }

                // 如果需要显示LLM格式
                if (showLLM && data.llm_format) {
                    const llmFormatType = document.getElementById('llmFormatType').value;
                    html += displayLLMFormat(data.llm_format, llmFormatType);
                }

                document.getElementById('results').innerHTML = html;

            } catch (error) {
                console.error('显示v41结果时出错:', error);
                console.error('错误堆栈:', error.stack);
                console.error('报告数据:', data);
                document.getElementById('results').innerHTML = `<div class="error">❌ v41分析失败: ${error.message}<br><small>请检查浏览器控制台获取详细错误信息</small></div>`;
            }
        }

        function displayFullReport(report, showAvoid) {
            let html = `
                <div class="sector-result">
                    <h2>📊 ${report.metadata.report_type} - v${report.metadata.version}</h2>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4>📋 元数据信息</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>生成时间:</strong> ${report.metadata.timestamp}</div>
                            <div><strong>分析时间框架:</strong> ${report.metadata.timeframes_analyzed}个</div>
                            <div><strong>可靠性评分:</strong> ${(report.metadata.reliability_score * 100).toFixed(1)}%</div>
                        </div>
                        <p style="margin-top: 10px; font-style: italic;">${report.metadata.data_explanation}</p>
                    </div>

                    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff;">
                        <h4>🎯 执行摘要</h4>
                        <div><strong>主要结论:</strong> ${report.executive_summary.main_conclusion}</div>
                        <div><strong>置信水平:</strong> ${report.executive_summary.confidence_level}</div>
                        <div><strong>市场阶段:</strong> ${report.executive_summary.market_phase}</div>
                        <div><strong>主要驱动:</strong> ${report.executive_summary.primary_driver}</div>
                        <div><strong>行动要求:</strong> ${report.executive_summary.action_required}</div>
                    </div>

                    <h4>🌊 市场概览</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div style="background: #f9f9f9; padding: 15px; border-radius: 5px;">
                            <h5>一致性得分</h5>
                            <div><strong>数值:</strong> ${(report.market_overview.consensus_score.value * 100).toFixed(1)}%</div>
                            <div><strong>解释:</strong> ${report.market_overview.consensus_score.interpretation}</div>
                            <div style="font-size: 0.9em; color: #666;">${report.market_overview.consensus_score.calculation}</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 15px; border-radius: 5px;">
                            <h5>市场状态</h5>
                            <div><strong>状态:</strong> ${report.market_overview.market_regime.interpretation}</div>
                            <div><strong>含义:</strong> ${report.market_overview.market_regime.implications}</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 15px; border-radius: 5px;">
                            <h5>信号可靠性</h5>
                            <div><strong>等级:</strong> ${report.market_overview.signal_reliability.interpretation}</div>
                            <div><strong>数值:</strong> ${(report.market_overview.signal_reliability.value * 100).toFixed(1)}%</div>
                            <div style="font-size: 0.9em; color: #666;">${report.market_overview.signal_reliability.factors}</div>
                        </div>
                    </div>
            `;

            // 时间框架分析
            html += `
                    <h4>📈 时间框架分析</h4>
                    <div class="timeframe-signals">
            `;

            for (const [tf, data] of Object.entries(report.timeframe_analysis)) {
                const signalClass = data.signal === 'buy' ? 'decision-buy' :
                                   data.signal === 'sell' ? 'decision-sell' : 'decision-hold';

                html += `
                    <div class="signal-card">
                        <strong>${tf}</strong><br>
                        <span class="${signalClass}">信号: ${data.signal}</span><br>
                        强度: ${data.strength} | 置信度: ${data.confidence}<br>
                        RSI: ${data.rsi} | 健康度: ${data.health}<br>
                        背离: ${data.divergence.type} (${data.divergence.severity})<br>
                        一致性: ${data.momentum_coherence}<br>
                        <div style="font-size: 0.9em; margin-top: 5px;">
                            <strong>建议:</strong> ${data.recommendation}
                        </div>
                `;

                if (data.supporting_factors && data.supporting_factors.length > 0) {
                    html += `<div style="font-size: 0.8em; color: #28a745; margin-top: 3px;">
                        ✅ ${data.supporting_factors.slice(0, 2).join(', ')}
                    </div>`;
                }

                if (data.risk_factors && data.risk_factors.length > 0) {
                    html += `<div style="font-size: 0.8em; color: #dc3545; margin-top: 3px;">
                        ⚠️ ${data.risk_factors.slice(0, 2).join(', ')}
                    </div>`;
                }

                html += `</div>`;
            }

            html += `</div>`;

            // 添加板块轮动指标显示
            if (report.rotation_analysis) {
                html += generateRotationMetricsDisplay(report.rotation_analysis);
            }

            // 添加多时间框架综合分析
            if (report.unified_decision) {
                html += generateUnifiedDecisionDisplay(report.unified_decision);
            }

            // 添加风险评估
            if (report.risk_assessment) {
                html += generateRiskAssessmentDisplay(report.risk_assessment);
            }

            return html;
        }

        function displaySummaryFormat(report) {
            return `
                <div class="sector-result">
                    <h2>📋 摘要格式 - v4.1</h2>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px;">
                        <div><strong>时间戳:</strong> ${report.timestamp}</div>
                        <div><strong>可靠性:</strong> ${(report.reliability * 100).toFixed(1)}%</div>
                        <div><strong>市场状态:</strong> ${report.market_regime}</div>
                        <div><strong>一致性:</strong> ${(report.consensus * 100).toFixed(1)}%</div>
                        <div><strong>决策:</strong> ${report.decision}</div>
                        <div><strong>关键洞察:</strong> ${report.key_insight}</div>
                        <div><strong>主要风险:</strong> ${report.main_risk}</div>
                    </div>
                </div>
            `;
        }

        function displaySignalsFormat(report) {
            let html = `
                <div class="sector-result">
                    <h2>🚦 信号格式 - v4.1</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            <h5>核心信号</h5>
                            <div><strong>行动:</strong> ${report.action}</div>
                            <div><strong>仓位:</strong> ${report.position}</div>
                            <div><strong>置信度:</strong> ${(report.confidence * 100).toFixed(1)}%</div>
                            <div><strong>风险等级:</strong> ${report.risk_level}</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            <h5>板块配置</h5>
                            <div><strong>推荐板块:</strong> ${report.buy_sectors.join(', ') || '无'}</div>
                            <div><strong>避免板块:</strong> ${report.avoid_sectors.join(', ') || '无'}</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            <h5>执行指导</h5>
                            <div><strong>入场时机:</strong> ${report.entry_timing}</div>
                            <div><strong>出场策略:</strong> ${report.exit_strategy}</div>
                            <div><strong>需要监控:</strong> ${report.monitoring_required ? '是' : '否'}</div>
                        </div>
                    </div>
            `;

            if (report.key_points && report.key_points.length > 0) {
                html += `
                    <div style="margin-top: 15px; background: #fff3cd; padding: 15px; border-radius: 5px;">
                        <h5>关键要点</h5>
                        <ul>
                `;
                for (const point of report.key_points) {
                    html += `<li>${point}</li>`;
                }
                html += `</ul></div>`;
            }

            html += `</div>`;
            return html;
        }

        function displayLLMFormat(llmData, formatType) {
            const formatName = formatType === 'basic' ? '基础版本' : '格式化版本';
            return `
                <div class="sector-result" style="background: #f0f8ff; border: 2px solid #007bff;">
                    <h3>🤖 LLM友好格式 (${formatName})</h3>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
${JSON.stringify(llmData, null, 2)}
                    </pre>
                    <button onclick="copyToClipboard('${JSON.stringify(llmData).replace(/'/g, "\\'")}')">
                        📋 复制JSON数据
                    </button>
                </div>
            `;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('JSON数据已复制到剪贴板');
            });
        }

        function displaySingleResult(data) {
            if (data.error) {
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ ' + data.error + '</p>';
                return;
            }
            
            const result = data.result;
            const decisionClass = result.unified_decision.includes('买') ? 'decision-buy' : 
                                 result.unified_decision.includes('卖') ? 'decision-sell' : 'decision-hold';
            
            let html = `
                <div class="sector-result">
                    <h2>📊 ${result.sector_name} 分析结果</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div><strong>统一决策:</strong> <span class="${decisionClass}">${result.unified_decision}</span></div>
                        <div><strong>一致性得分:</strong> ${(result.consensus_score * 100).toFixed(1)}%</div>
                        <div><strong>信号可靠性:</strong> ${(result.signal_reliability * 100).toFixed(1)}%</div>
                        <div><strong>市场状态:</strong> ${result.market_regime}</div>
                        <div><strong>风险等级:</strong> ${result.overall_risk_level}</div>
                    </div>
                    
                    <h4>📈 各时间框架信号</h4>
                    <div class="timeframe-signals">
            `;
            
            for (const [tf, signal] of Object.entries(result.timeframe_signals)) {
                html += `
                    <div class="signal-card">
                        <strong>${tf}</strong><br>
                        信号: ${signal.signal_type}<br>
                        强度: ${signal.strength.toFixed(2)}<br>
                        置信度: ${signal.confidence.toFixed(2)}<br>
                        RSI: ${signal.rsi.toFixed(1)}<br>
                        健康度: ${signal.internal_health.toFixed(1)}
                    </div>
                `;
            }
            
            html += `
                    </div>
                    
                    <h4>⚖️ 动态权重分配</h4>
                    <div class="weights">
            `;
            
            for (const [tf, weight] of Object.entries(result.dynamic_weights)) {
                html += `<div class="weight-item">${tf}: ${(weight * 100).toFixed(1)}%</div>`;
            }
            
            html += `
                    </div>
                    <p><em>${result.weight_reasoning}</em></p>
                    
                    <h4>💡 操作建议</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                        <p><strong>入场策略:</strong> ${result.entry_strategy}</p>
                        <p><strong>出场策略:</strong> ${result.exit_strategy}</p>
                        <p><strong>仓位建议:</strong> ${result.position_sizing}</p>
                    </div>
            `;
            
            if (result.risk_factors && result.risk_factors.length > 0) {
                html += `
                    <h4>🚨 风险提示</h4>
                    <div class="risk-factors">
                `;
                for (const risk of result.risk_factors) {
                    html += `<div class="risk-item">${risk}</div>`;
                }
                html += `</div>`;
            }
            
            html += `</div>`;
            
            document.getElementById('results').innerHTML = html;
        }
        
        function displayBatchResults(data) {
            if (data.error) {
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ ' + data.error + '</p>';
                return;
            }
            
            const results = data.results;
            let html = `<h2>📋 批量分析结果 (${results.length}个板块)</h2><div class="batch-results">`;
            
            for (const result of results) {
                const decisionClass = result.unified_decision.includes('买') ? 'decision-buy' : 
                                     result.unified_decision.includes('卖') ? 'decision-sell' : 'decision-hold';
                
                html += `
                    <div class="sector-result">
                        <h3>${result.sector_name}</h3>
                        <div><strong>决策:</strong> <span class="${decisionClass}">${result.unified_decision}</span></div>
                        <div><strong>可靠性:</strong> ${(result.signal_reliability * 100).toFixed(1)}%</div>
                        <div><strong>状态:</strong> ${result.market_regime}</div>
                        <div><strong>风险:</strong> ${result.overall_risk_level}</div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            ${result.entry_strategy}
                        </div>
                    </div>
                `;
            }
            
            html += `</div>`;
            document.getElementById('results').innerHTML = html;
        }

        function displayComparisonResults(data) {
            if (data.error) {
                document.getElementById('results').innerHTML = '<p style="color: red;">❌ ' + data.error + '</p>';
                return;
            }

            const results = data.comparison_results;
            const summary = data.comparison_summary;

            let html = `
                <div class="sector-result">
                    <h2>⚖️ 板块对比分析结果</h2>
            `;

            // 对比摘要
            if (summary && !summary.error) {
                html += `
                    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff;">
                        <h4>📊 对比摘要</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>分析板块数:</strong> ${summary.total_sectors}</div>
                            <div><strong>成功分析:</strong> ${summary.successful_analysis}</div>
                            <div><strong>平均可靠性:</strong> ${(summary.average_reliability * 100).toFixed(1)}%</div>
                            <div><strong>平均一致性:</strong> ${(summary.average_consensus * 100).toFixed(1)}%</div>
                            <div><strong>最佳板块:</strong> ${summary.best_sector}</div>
                            <div><strong>最差板块:</strong> ${summary.worst_sector}</div>
                        </div>
                    </div>
                `;
            }

            // 详细对比结果
            html += `
                    <h4>📈 详细对比结果</h4>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">板块</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">可靠性</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">一致性</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">市场状态</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">决策建议</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">风险等级</th>
                                    <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">关键洞察</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            for (const result of results) {
                if (result.error) {
                    html += `
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd;">${result.sector}</td>
                            <td colspan="6" style="padding: 10px; border: 1px solid #ddd; color: red;">分析失败: ${result.error}</td>
                        </tr>
                    `;
                } else {
                    const decisionClass = result.decision.includes('买') ? 'decision-buy' :
                                         result.decision.includes('卖') ? 'decision-sell' : 'decision-hold';

                    const riskColor = {
                        'low': '#28a745',
                        'medium': '#ffc107',
                        'high': '#dc3545',
                        'extreme': '#6f42c1'
                    }[result.risk_level] || '#6c757d';

                    html += `
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">${result.sector}</td>
                            <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${(result.reliability * 100).toFixed(1)}%</td>
                            <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${(result.consensus * 100).toFixed(1)}%</td>
                            <td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${result.market_regime}</td>
                            <td style="padding: 10px; border: 1px solid #ddd; text-align: center;"><span class="${decisionClass}">${result.decision}</span></td>
                            <td style="padding: 10px; border: 1px solid #ddd; text-align: center; color: ${riskColor}; font-weight: bold;">${result.risk_level}</td>
                            <td style="padding: 10px; border: 1px solid #ddd; font-size: 0.9em;">
                                ${result.key_insights.slice(0, 1).join('; ')}
                                ${result.warnings.length > 0 ? '<br><span style="color: #dc3545;">⚠️ ' + result.warnings[0] + '</span>' : ''}
                            </td>
                        </tr>
                    `;
                }
            }

            html += `
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h5>💡 对比分析说明</h5>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>可靠性:</strong> 信号的可信度，越高越可靠</li>
                            <li><strong>一致性:</strong> 各时间框架的一致程度，越高越稳定</li>
                            <li><strong>市场状态:</strong> 当前板块所处的市场环境</li>
                            <li><strong>决策建议:</strong> 基于综合分析的操作建议</li>
                            <li><strong>风险等级:</strong> 投资该板块的风险水平</li>
                        </ul>
                    </div>
                </div>
            `;

            document.getElementById('results').innerHTML = html;
        }

        // 生成板块轮动指标显示
        function generateRotationMetricsDisplay(rotationData) {
            if (!rotationData) return '';

            let html = `
                <div class="rotation-metrics">
                    <div class="rotation-header">
                        <h4>🔄 板块轮动指标分析</h4>
                    </div>
                    <div class="rotation-grid">
                        <div class="rotation-card">
                            <div class="rotation-label">统一轮动强度指数 (RII)</div>
                            <div class="rotation-value" style="color: ${getRIIColor(rotationData.unified_rii || 0)}">${(rotationData.unified_rii || 0).toFixed(3)}</div>
                            <div class="rii-gauge">
                                <svg width="100" height="50" viewBox="0 0 100 50">
                                    <path class="rii-arc" d="M 10 40 A 30 30 0 0 1 90 40" />
                                    <path class="rii-progress" d="M 10 40 A 30 30 0 0 1 ${10 + (rotationData.unified_rii || 0) * 80} 40"
                                          stroke-dasharray="${(rotationData.unified_rii || 0) * 94.2} 94.2" />
                                </svg>
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                ${getRIIInterpretation(rotationData.unified_rii || 0)}
                            </div>
                        </div>

                        <div class="rotation-card">
                            <div class="rotation-label">轮动阶段</div>
                            <div class="rotation-stage ${getStageClass(rotationData.rotation_stage || 'unknown')}">
                                ${getStageDisplayName(rotationData.rotation_stage || 'unknown')}
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                ${getStageDescription(rotationData.rotation_stage || 'unknown')}
                            </div>
                        </div>

                        <div class="rotation-card">
                            <div class="rotation-label">价格离散度</div>
                            <div class="rotation-value">${(rotationData.price_dispersion || 0).toFixed(3)}</div>
                            <div style="font-size: 0.8em; color: #666;">
                                板块间价格差异程度
                            </div>
                        </div>

                        <div class="rotation-card">
                            <div class="rotation-label">排名变化速度</div>
                            <div class="rotation-value">${(rotationData.rank_velocity || 0).toFixed(3)}</div>
                            <div style="font-size: 0.8em; color: #666;">
                                板块排名变化频率
                            </div>
                        </div>

                        <div class="rotation-card">
                            <div class="rotation-label">成交量集中度</div>
                            <div class="rotation-value">${(rotationData.volume_concentration || 0).toFixed(3)}</div>
                            <div style="font-size: 0.8em; color: #666;">
                                资金流向集中程度
                            </div>
                        </div>

                        <div class="rotation-card">
                            <div class="rotation-label">风险等级</div>
                            <div class="rotation-value" style="color: ${getRiskColor(rotationData.risk_level || 'medium')}">
                                ${getRiskDisplayName(rotationData.risk_level || 'medium')}
                            </div>
                            <div style="font-size: 0.8em; color: #666;">
                                当前轮动风险水平
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // 生成统一决策显示
        function generateUnifiedDecisionDisplay(unifiedData) {
            if (!unifiedData) return '';

            // 安全处理 operation_guidance (它是一个对象，不是数组)
            let operationGuidance = '';
            if (unifiedData.operation_guidance) {
                const guidance = unifiedData.operation_guidance;
                let guidanceItems = [];

                if (guidance.entry_timing) {
                    guidanceItems.push(`入场时机: ${guidance.entry_timing}`);
                }
                if (guidance.exit_strategy) {
                    guidanceItems.push(`退出策略: ${guidance.exit_strategy}`);
                }
                if (guidance.execution) {
                    guidanceItems.push(`执行建议: ${guidance.execution}`);
                }
                if (guidance.key_points && Array.isArray(guidance.key_points)) {
                    guidanceItems.push(...guidance.key_points.slice(0, 2));
                } else if (guidance.key_points && typeof guidance.key_points === 'string') {
                    guidanceItems.push(guidance.key_points);
                }

                operationGuidance = guidanceItems.length > 0
                    ? guidanceItems.slice(0, 3).map(item => `• ${item}`).join('<br>')
                    : '• 暂无具体指导';
            } else {
                operationGuidance = '• 暂无操作指导';
            }

            let html = `
                <div class="mtf-summary">
                    <h4>🎯 多时间框架统一决策</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div>
                            <strong>仓位建议:</strong> ${unifiedData.position_recommendation?.suggested || 'N/A'}<br>
                            <strong>置信度:</strong> ${((unifiedData.position_recommendation?.confidence || 0) * 100).toFixed(1)}%<br>
                            <strong>建议仓位:</strong> ${unifiedData.position_recommendation?.position_size || 'N/A'}%
                        </div>
                        <div>
                            <strong>操作指导:</strong><br>
                            <div style="font-size: 0.9em; margin-top: 5px;">
                                ${operationGuidance}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // 生成风险评估显示
        function generateRiskAssessmentDisplay(riskData) {
            if (!riskData) return '';

            // 安全处理 risk_factors
            let riskFactors = 'N/A';
            if (riskData.risk_factors) {
                if (Array.isArray(riskData.risk_factors)) {
                    riskFactors = riskData.risk_factors.slice(0, 3).join(', ') || 'N/A';
                } else if (typeof riskData.risk_factors === 'string') {
                    riskFactors = riskData.risk_factors;
                }
            }

            // 安全处理 mitigation_strategies
            let mitigationStrategies = 'N/A';
            if (riskData.mitigation_strategies) {
                if (Array.isArray(riskData.mitigation_strategies)) {
                    mitigationStrategies = riskData.mitigation_strategies.slice(0, 2).join('; ') || 'N/A';
                } else if (typeof riskData.mitigation_strategies === 'string') {
                    mitigationStrategies = riskData.mitigation_strategies;
                }
            }

            let html = `
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 15px 0;">
                    <h4>⚠️ 风险评估</h4>
                    <div><strong>整体风险:</strong> ${riskData.overall_risk?.level || 'N/A'} (${((riskData.overall_risk?.score || 0) * 100).toFixed(1)}%)</div>
                    <div><strong>主要风险:</strong> ${riskFactors}</div>
                    <div><strong>风险建议:</strong> ${mitigationStrategies}</div>
                </div>
            `;

            return html;
        }

        // 辅助函数
        function getRIIColor(rii) {
            if (rii > 0.8) return '#dc3545';  // 红色 - 高轮动
            if (rii > 0.6) return '#fd7e14';  // 橙色 - 中高轮动
            if (rii > 0.4) return '#ffc107';  // 黄色 - 中等轮动
            if (rii > 0.2) return '#28a745';  // 绿色 - 低轮动
            return '#6c757d';  // 灰色 - 极低轮动
        }

        function getRIIInterpretation(rii) {
            if (rii > 0.8) return '轮动剧烈，高风险高机会';
            if (rii > 0.6) return '轮动活跃，需要灵活应对';
            if (rii > 0.4) return '轮动适中，正常市场状态';
            if (rii > 0.2) return '轮动较弱，趋势相对稳定';
            return '轮动极弱，市场缺乏活力';
        }

        function getStageClass(stage) {
            const stageMap = {
                'stable': 'stage-stable',
                'convergence': 'stage-convergence',
                'startup': 'stage-startup',
                'acceleration': 'stage-acceleration',
                'chaos': 'stage-chaos'
            };
            return stageMap[stage] || 'stage-chaos';
        }

        function getStageDisplayName(stage) {
            const nameMap = {
                'stable': '稳定期',
                'convergence': '收敛期',
                'startup': '启动期',
                'acceleration': '加速期',
                'chaos': '混乱期'
            };
            return nameMap[stage] || '未知';
        }

        function getStageDescription(stage) {
            const descMap = {
                'stable': '板块表现相对稳定，适合长期持有',
                'convergence': '板块开始收敛，关注突破方向',
                'startup': '轮动刚开始，寻找领涨板块',
                'acceleration': '轮动加速，快速调仓换股',
                'chaos': '市场混乱，控制风险为主'
            };
            return descMap[stage] || '市场状态不明确';
        }

        function getRiskColor(risk) {
            const colorMap = {
                'low': '#28a745',
                'medium': '#ffc107',
                'high': '#fd7e14',
                'extreme': '#dc3545'
            };
            return colorMap[risk] || '#6c757d';
        }

        function getRiskDisplayName(risk) {
            const nameMap = {
                'low': '低风险',
                'medium': '中等风险',
                'high': '高风险',
                'extreme': '极高风险'
            };
            return nameMap[risk] || '未知';
        }

        // 页面加载时自动分析Technology板块
        window.onload = function() {
            setTimeout(analyzeSector, 1000);
        };
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/analyze_v41', methods=['POST'])
def analyze_v41():
    """v41标准板块分析API"""
    try:
        data = request.get_json()
        sector = data.get('sector', '').strip()
        timeframes = data.get('timeframes', ['5m', '1h', '1d'])
        output_format = data.get('output_format', 'full')
        show_llm_format = data.get('show_llm_format', False)
        llm_format_type = data.get('llm_format_type', 'formatted')
        show_avoid_sectors = data.get('show_avoid_sectors', True)

        if not sector:
            return jsonify({'error': '板块名称不能为空'})

        # 直接从数据库获取真实数据
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))
        from db_settings import get_default_db_config
        import pymysql

        config = get_default_db_config()
        connection = pymysql.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 根据sector映射到数据库中实际的市场名称
        sector_to_market_map = {
            'Information Technology': 'Information Technology',
            'Technology': 'Information Technology',
            'Healthcare': 'Health Care',
            'Finance': 'Financials',
            'Financial Services': 'Financials',
            'Energy': 'Energy',
            'Consumer': 'Consumer Discretionary',
            'Consumer Discretionary': 'Consumer Discretionary',
            'Consumer Staples': 'Consumer Staples',
            'Industrials': 'Industrials',
            'Materials': 'Materials',
            'Real Estate': 'Real Estate',
            'Utilities': 'Utilities',
            'Communication Services': 'Communication Services'
        }

        market_name = sector_to_market_map.get(sector, 'Information Technology')
        print(f"🔍 映射板块 '{sector}' 到数据库市场 '{market_name}'")

        # 1. 获取市场广度数据
        cursor.execute("""
            SELECT
                recorded_at, market, total_stocks, advances, declines, unchanged,
                advancing_volume, declining_volume, total_volume,
                new_highs_52w, new_lows_52w, above_ma50, above_ma200, avg_rsi,
                market_cap_weighted_return, equal_weighted_return, purity,
                internal_health, momentum_coherence, divergence_type,
                divergence_severity, divergence_confidence, divergence_risk_level
            FROM market_breadth_metrics_gics
            WHERE market = %s AND timeframe = '1d'
            ORDER BY recorded_at DESC LIMIT 1
        """, (market_name,))

        breadth_data = cursor.fetchone()

        # 2. 获取板块轮动数据
        cursor.execute("""
            SELECT
                recorded_at, sector, rotation_intensity_index, rotation_stage,
                price_dispersion, rank_velocity, volume_concentration,
                relative_strength, optimal_weights, risk_level
            FROM sector_rotation_metrics_gics
            WHERE sector = %s AND timeframe = '1d'
            ORDER BY recorded_at DESC LIMIT 1
        """, (market_name,))

        rotation_data = cursor.fetchone()

        # 3. 获取MTF分析结果
        cursor.execute("""
            SELECT
                recorded_at, market_regime, consensus_score, signal_reliability,
                suggested_position, operation_strategy, top_sectors_json,
                avoid_sectors_json, key_insights_json, warnings_json,
                unified_rii, rotation_stage, risk_level
            FROM mtf_analysis_results
            ORDER BY recorded_at DESC LIMIT 1
        """)

        mtf_data = cursor.fetchone()

        cursor.close()
        connection.close()

        if not breadth_data:
            raise Exception(f"无法获取 {market_name} 的市场广度数据")

        print(f"✅ 获取到真实数据库数据")
        print(f"   广度数据时间: {breadth_data[0]}")
        print(f"   轮动数据: {'有' if rotation_data else '无'}")
        print(f"   MTF数据: {'有' if mtf_data else '无'}")

        # 构建符合前端期望的完整数据结构
        report = build_complete_report(breadth_data, rotation_data, mtf_data, market_name, sector)

        print(f"✅ 使用真实数据库数据处理 {sector}")

        # 准备响应
        response = {'report': report}

        # 如果需要LLM格式
        if show_llm_format:
            response['llm_format'] = _extract_signals_from_report(report, llm_format_type)

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)})

def _assess_market_psychology_from_data(breadth_analysis):
    """基于广度分析评估市场心理"""
    health = breadth_analysis.get('internal_health', 50)
    if health > 70:
        return '乐观情绪主导，市场参与度高'
    elif health < 30:
        return '悲观情绪蔓延，市场谨慎'
    else:
        return '情绪中性，观望情绪较重'

def _determine_risk_level(position):
    """根据建议仓位确定风险等级"""
    if position > 70:
        return 'high'
    elif position < 30:
        return 'low'
    else:
        return 'medium'

def _generate_scenarios_from_data(timeframe_analysis):
    """基于时间框架分析生成情景"""
    scenarios = []
    for tf, data in timeframe_analysis.items():
        signal = data.get('signal', 'hold')
        confidence = max(0.0, min(1.0, data.get('confidence', 0.5)))  # 确保在0-1范围内
        scenarios.append({
            'timeframe': tf,
            'scenario': f'{signal}信号',
            'probability': f'{confidence * 100:.1f}%'  # 转换为百分比格式
        })
    return scenarios

def _identify_clarifications_from_data(executive_summary):
    """基于执行摘要识别需要澄清的问题"""
    confidence = executive_summary.get('confidence_level', '50%')
    if '50%' in confidence or 'low' in confidence.lower():
        return ['信号可靠性偏低，是否需要等待更明确的信号？']
    return []

def _generate_sector_analysis_from_data(full_report):
    """生成板块深度分析"""
    sector_analysis = full_report.get('sector_analysis', {})
    result = {}
    for sector, data in sector_analysis.items():
        breadth = data.get('breadth_metrics', {})
        result[sector] = {
            'health_score': breadth.get('internal_health', 50),
            'momentum': breadth.get('momentum_coherence', 0.5),
            'recommendation': '关注' if breadth.get('internal_health', 50) > 60 else '谨慎'
        }
    return result

def _suggest_execution_timing_from_data(timeframe_analysis):
    """基于时间框架分析建议执行时机"""
    daily = timeframe_analysis.get('daily', {})
    signal = daily.get('signal', 'hold')
    confidence = daily.get('confidence', 0.5)

    if signal == 'buy' and confidence > 0.7:
        return '可以立即执行，信号明确'
    elif signal == 'sell' and confidence > 0.7:
        return '建议尽快执行，风险信号明显'
    else:
        return '建议分批执行，等待更明确信号'

def _generate_market_description(market_overview, breadth_analysis):
    """生成市场状态描述"""
    regime = market_overview.get('market_regime', {}).get('value', 'normal_market')
    health = breadth_analysis.get('internal_health', 50)
    coherence = breadth_analysis.get('momentum_coherence', 0.5)

    if regime == 'trending_stable' and health > 60:
        return '市场处于趋势稳定期，内部结构健康'
    elif regime == 'high_rotation':
        return '市场轮动活跃，板块间资金流动频繁'
    elif health < 40:
        return '市场内部出现分歧，需要谨慎操作'
    else:
        return '市场处于正常状态，整体环境中性'

def _assess_volatility_from_data(breadth_analysis):
    """评估市场波动性"""
    coherence = breadth_analysis.get('momentum_coherence', 0.5)
    if coherence > 0.7:
        return 'low'
    elif coherence < 0.3:
        return 'high'
    else:
        return 'normal'

def _assess_breadth_health(internal_health):
    """评估广度健康度"""
    if internal_health > 70:
        return 'healthy'
    elif internal_health < 30:
        return 'weak'
    else:
        return 'neutral'

def _generate_position_rationale(market_overview, breadth_analysis):
    """生成仓位调整理由"""
    suggested_position = market_overview.get('suggested_position', 50)
    health = max(0.0, min(100.0, breadth_analysis.get('internal_health', 50)))
    coherence = max(0.0, min(1.0, breadth_analysis.get('momentum_coherence', 0.5)))
    regime = market_overview.get('market_regime', {}).get('value', 'normal_market')

    base_position = 50
    factors = {}

    # 基于市场健康度调整
    if health > 70:
        factors['market_health'] = '+15%'
        base_position += 15
    elif health > 60:
        factors['market_health'] = '+10%'
        base_position += 10
    elif health < 30:
        factors['market_health'] = '-20%'
        base_position -= 20
    elif health < 40:
        factors['market_health'] = '-15%'
        base_position -= 15

    # 基于动量一致性调整
    if coherence > 0.7:
        factors['momentum_coherence'] = '+10%'
        base_position += 10
    elif coherence > 0.6:
        factors['momentum_coherence'] = '+5%'
        base_position += 5
    elif coherence < 0.3:
        factors['momentum_coherence'] = '-15%'
        base_position -= 15
    elif coherence < 0.4:
        factors['momentum_coherence'] = '-10%'
        base_position -= 10

    # 基于市场状态调整
    if regime == 'trending_stable':
        factors['regime_stability'] = '+5%'
        base_position += 5
    elif regime == 'high_rotation':
        factors['rotation_risk'] = '-5%'
        base_position -= 5
    elif regime == 'short_term_stress':
        factors['stress_adjustment'] = '-10%'
        base_position -= 10

    # 确保最终仓位在合理范围内
    final_position = max(0, min(100, base_position))

    return {
        'suggested': suggested_position,
        'calculated_position': final_position,
        'factors': factors,
        'explanation': f'基础仓位50%，经过市场状态调整后为{final_position}%，最终建议{suggested_position}%'
    }

def _generate_sector_selection_rationale(full_report):
    """生成板块选择理由"""
    return {
        'method': '综合评分法',
        'weights': {
            'momentum': 0.4,
            'health': 0.3,
            'coherence': 0.3
        },
        'top_choice_reasoning': '基于多维度分析选择最优板块'
    }

def _generate_timing_rationale(timeframe_analysis):
    """生成时机选择理由"""
    signals = []
    for tf, data in timeframe_analysis.items():
        signals.append(f"{tf}: {data.get('signal', 'hold')}")

    return {
        'timeframe_signals': signals,
        'consensus': '多时间框架信号基本一致' if len(set(s.split(': ')[1] for s in signals)) <= 2 else '时间框架信号存在分歧'
    }

def _generate_risk_scenarios(market_overview, breadth_analysis, timeframe_analysis):
    """生成风险情景分析"""
    scenarios = []

    # 基于市场健康度的情景
    health = breadth_analysis.get('internal_health', 50)
    if health < 50:
        scenarios.append({
            'scenario': '市场内部分歧加剧',
            'probability': 0.3,
            'impact': f'可能需要降低仓位至{max(30, market_overview.get("suggested_position", 50) - 20)}%',
            'monitoring': '关注内部健康度指标变化'
        })

    # 基于动量一致性的情景
    coherence = breadth_analysis.get('momentum_coherence', 0.5)
    if coherence < 0.4:
        scenarios.append({
            'scenario': '动量一致性恶化',
            'probability': 0.25,
            'impact': '需要更频繁调整持仓',
            'monitoring': '关注动量一致性指标'
        })

    # 基于时间框架分歧的情景
    daily_signal = timeframe_analysis.get('daily', {}).get('signal', 'hold')
    weekly_signal = timeframe_analysis.get('weekly', {}).get('signal', 'hold')
    if daily_signal != weekly_signal:
        scenarios.append({
            'scenario': '时间框架信号分歧',
            'probability': 0.2,
            'impact': '短期可能出现波动',
            'monitoring': '关注不同时间框架信号收敛情况'
        })

    return scenarios

def _generate_interactive_questions(market_overview):
    """生成交互式问题"""
    questions = [
        '当前持仓与建议的主要差异在哪里？',
        '是否有特殊的风险偏好需要考虑？',
        '执行时间是否有限制？'
    ]

    suggested_position = market_overview.get('suggested_position', 50)
    if suggested_position > 70:
        questions.append('是否愿意承担较高的仓位风险？')
    elif suggested_position < 30:
        questions.append('是否同意采取保守策略？')

    return questions

def _generate_adjustable_parameters(market_overview):
    """生成可调整参数"""
    suggested_position = market_overview.get('suggested_position', 50)

    return {
        'risk_tolerance': f'可以调整仓位建议±20%（当前{suggested_position}%）',
        'sector_preference': '可以微调板块权重±10%',
        'time_horizon': '可以改变执行时间框架',
        'monitoring_frequency': '可以调整监控频率（日度/周度）'
    }

def _extract_signals_from_report(full_report: dict, format_type: str = 'formatted') -> dict:
    """按照文档标准提取LLM友好格式，符合 mtf-optimization-implementation.py 要求

    Args:
        full_report: 完整报告
        format_type: 'basic' 或 'formatted'
    """
    try:
        # 获取基础数据
        metadata = full_report.get('metadata', {})
        executive_summary = full_report.get('executive_summary', {})
        market_overview = full_report.get('market_overview', {})
        breadth_analysis = full_report.get('breadth_analysis', {})
        timeframe_analysis = full_report.get('timeframe_analysis', {})
        recommendations = full_report.get('recommendations', {})

        if format_type == 'basic':
            # 简化的基础格式
            return {
                'action': executive_summary.get('action_required', '观望等待'),
                'position': f"{market_overview.get('suggested_position', 50)}%",
                'confidence': executive_summary.get('confidence_level', '50%'),
                'market_phase': executive_summary.get('market_phase', '正常'),
                'primary_driver': executive_summary.get('primary_driver', '市场分析')
            }

        # 符合文档要求的完整LLM格式
        return {
            'analysis_metadata': {
                'timestamp': metadata.get('timestamp', ''),
                'reliability': max(0.0, min(1.0, market_overview.get('signal_reliability', {}).get('value', 0.5))),
                'confidence_level': f"{max(0.0, min(100.0, market_overview.get('signal_reliability', {}).get('value', 0.5) * 100)):.1f}%",
                'data_quality': 'high',
                'analysis_method': metadata.get('analysis_method', 'integrated')
            },

            'market_understanding': {
                'market_state': {
                    'description': _generate_market_description(market_overview, breadth_analysis),
                    'key_metrics': {
                        'consensus': market_overview.get('consensus_score', {}).get('value', 0.5),
                        'volatility': _assess_volatility_from_data(breadth_analysis),
                        'breadth': _assess_breadth_health(breadth_analysis.get('internal_health', 50))
                    },
                    'interpretation': market_overview.get('market_regime', {}).get('implications', '整体环境正常')
                },
                'key_drivers': [executive_summary.get('primary_driver', '市场广度分析')],
                'market_psychology': _assess_market_psychology_from_data(breadth_analysis)
            },

            'decision_logic': {
                'primary_decision': executive_summary.get('action_required', '观望等待'),
                'decision_path': [
                    {
                        'step': '市场状态评估',
                        'result': market_overview.get('market_regime', {}).get('value', 'normal_market'),
                        'reasoning': market_overview.get('market_regime', {}).get('implications', '基于技术分析')
                    },
                    {
                        'step': '仓位建议',
                        'result': f"{market_overview.get('suggested_position', 50)}%",
                        'reasoning': market_overview.get('operation_strategy', '基于风险评估')
                    }
                ],
                'confidence_factors': [
                    f"信号可靠性: {max(0.0, min(100.0, market_overview.get('signal_reliability', {}).get('value', 0.5) * 100)):.1f}%",
                    f"市场阶段: {executive_summary.get('market_phase', '正常')}"
                ],
                'alternative_considered': ['保守策略', '激进策略']
            },

            'decision_rationale': {
                'position_sizing': _generate_position_rationale(market_overview, breadth_analysis),
                'sector_selection': _generate_sector_selection_rationale(full_report),
                'timing_considerations': _generate_timing_rationale(timeframe_analysis)
            },

            'risk_reward_profile': {
                'risk_assessment': {
                    'level': _determine_risk_level(market_overview.get('suggested_position', 50)),
                    'factors': [
                        f"内部健康度: {breadth_analysis.get('internal_health', 50)}%",
                        f"动量一致性: {breadth_analysis.get('momentum_coherence', 0.5)}"
                    ]
                },
                'potential_scenarios': _generate_scenarios_from_data(timeframe_analysis),
                'risk_mitigation': [
                    '分批建仓',
                    '设置止损',
                    '定期评估'
                ]
            },

            'actionable_insights': {
                'immediate_actions': [
                    f"调整仓位至 {market_overview.get('suggested_position', 50)}%",
                    executive_summary.get('action_required', '观望等待')
                ],
                'monitoring_triggers': [
                    '市场状态变化',
                    '板块轮动信号',
                    '技术指标突破'
                ],
                'adjustment_conditions': [
                    '信号可靠性下降',
                    '市场环境变化',
                    '风险水平提升'
                ]
            },

            'risk_scenarios': _generate_risk_scenarios(market_overview, breadth_analysis, timeframe_analysis),

            'interactive_elements': {
                'clarification_needed': _identify_clarifications_from_data(executive_summary),
                'customization_options': [
                    '调整风险偏好',
                    '修改仓位策略',
                    '自定义监控指标'
                ],
                'follow_up_questions': [
                    '您对当前市场看法如何？',
                    '是否需要调整风险管理策略？',
                    '您希望关注哪些特定指标？'
                ]
            },

            'interactive_guidance': {
                'questions_to_ask': _generate_interactive_questions(market_overview),
                'adjustable_parameters': _generate_adjustable_parameters(market_overview)
            },

            'sector_deep_dive': _generate_sector_analysis_from_data(full_report),

            'execution_guidance': {
                'timing': _suggest_execution_timing_from_data(timeframe_analysis),
                'method': '分批执行，避免冲击成本',
                'size_management': f"建议仓位: {market_overview.get('suggested_position', 50)}%"
            }
        }

    except Exception as e:
        # 如果提取失败，返回默认格式
        if format_type == 'basic':
            return {
                'action': '观望等待',
                'position': '50%',
                'buy_sectors': {},
                'avoid_sectors': {},
                'execution_timeframe': '日度调整',
                'stop_loss': 0.03,
                'confidence': 0.5
            }
        else:
            return {
                'action': '观望等待',
                'position': '50%',
                'buy_sectors': {},
                'avoid_sectors': {},
                'execution': '日度调整',
                'risk_control': {
                    'stop_loss': '3%',
                    'confidence': '50.0%'
                }
            }

@app.route('/analyze', methods=['POST'])
def analyze():
    """兼容旧版的分析API"""
    try:
        data = request.get_json()
        sector = data.get('sector', '').strip()

        if not sector:
            return jsonify({'error': '板块名称不能为空'})

        from multi_timeframe_analyzer import analyze_sector_mtf

        analysis = analyze_sector_mtf(sector)

        # 转换为可序列化的格式
        result = {
            'sector_name': analysis.sector_name,
            'unified_decision': analysis.unified_decision,
            'consensus_score': analysis.consensus_score,
            'signal_reliability': analysis.signal_reliability,
            'market_regime': analysis.market_regime,
            'overall_risk_level': analysis.overall_risk_level,
            'dynamic_weights': analysis.dynamic_weights,
            'weight_reasoning': analysis.weight_reasoning,
            'entry_strategy': analysis.entry_strategy,
            'exit_strategy': analysis.exit_strategy,
            'position_sizing': analysis.position_sizing,
            'risk_factors': analysis.risk_factors,
            'timeframe_signals': {
                tf: {
                    'signal_type': signal.signal_type,
                    'strength': signal.strength,
                    'confidence': signal.confidence,
                    'rsi': signal.rsi,
                    'internal_health': signal.internal_health,
                    'recommendation': signal.recommendation
                }
                for tf, signal in analysis.timeframe_signals.items()
            }
        }

        return jsonify({'result': result})

    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/analyze_batch_v41', methods=['POST'])
def analyze_batch_v41():
    """v41标准批量分析API"""
    try:
        data = request.get_json() or {}
        output_format = data.get('output_format', 'summary')
        timeframes = data.get('timeframes', ['5m', '1h', '1d'])

        from multi_timeframe_analyzer import analyze_market_mtf

        # 使用数据库中真实的板块名称
        all_sectors = [
            # 主要板块 (基于数据库查询结果)
            "Information Technology", "Financials", "Health Care",
            "Consumer Discretionary", "Communication Services", "Industrials",
            "Consumer Staples", "Energy", "Utilities", "Real Estate", "Materials",

            # 二级板块
            "Software & Services", "Technology Hardware & Equipmen",
            "Semiconductors & Semiconductor", "Banks", "Insurance", "Financial Services",
            "Pharmaceuticals, Biotechnology", "Health Care Equipment & Servic",
            "Automobiles & Components", "Consumer Durables & Apparel", "Consumer Services",
            "Capital Goods", "Transportation", "Commercial & Professional Serv",
            "Media & Entertainment", "Telecommunication Services",
            "Equity Real Estate Investment", "Real Estate Management & Devel",

            # 市场指数
            "SP500", "NASDAQ100", "RUSSELL2000", "NASDAQ", "NYSE"
        ]

        results = []

        for sector in all_sectors:
            try:
                report = analyze_market_mtf(
                    sector=sector,
                    timeframes=timeframes,
                    output_format=output_format
                )

                if output_format == 'summary':
                    results.append({
                        'sector_name': sector,
                        'timestamp': report.get('timestamp'),
                        'reliability': report.get('reliability', 0),
                        'market_regime': report.get('market_regime', 'unknown'),
                        'decision': report.get('decision', 'hold'),
                        'key_insight': report.get('key_insight', ''),
                        'main_risk': report.get('main_risk', '')
                    })
                else:
                    results.append({
                        'sector_name': sector,
                        'report': report
                    })
            except:
                continue

        return jsonify({'results': results, 'total_analyzed': len(results)})

    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/compare_sectors', methods=['POST'])
def compare_sectors():
    """板块对比分析API"""
    try:
        data = request.get_json()
        sectors = data.get('sectors', [])
        timeframes = data.get('timeframes', ['5m', '1h', '1d'])

        if not sectors:
            return jsonify({'error': '请选择要对比的板块'})

        from multi_timeframe_analyzer import analyze_market_mtf

        comparison_results = []

        for sector in sectors:
            try:
                report = analyze_market_mtf(
                    sector=sector,
                    timeframes=timeframes,
                    output_format='full'
                )

                # 提取关键对比指标
                comparison_results.append({
                    'sector': sector,
                    'reliability': report['metadata']['reliability_score'],
                    'consensus': report['market_overview']['consensus_score']['value'],
                    'market_regime': report['market_overview']['market_regime']['value'],
                    'decision': report['unified_decision']['position_recommendation']['suggested'],
                    'risk_level': report['risk_assessment']['overall_risk']['level'],
                    'recommended_sectors': len(report['unified_decision']['sector_allocation']['recommended']),
                    'avoid_sectors': len(report['unified_decision']['sector_allocation']['avoid']),
                    'key_insights': report['insights_and_warnings']['key_insights'][:2],  # 前2个洞察
                    'warnings': report['insights_and_warnings']['warnings'][:2]  # 前2个警告
                })

            except Exception as e:
                comparison_results.append({
                    'sector': sector,
                    'error': str(e)
                })

        # 生成对比摘要
        if len(comparison_results) > 1:
            # 计算对比统计
            valid_results = [r for r in comparison_results if 'error' not in r]
            if valid_results:
                avg_reliability = sum(r['reliability'] for r in valid_results) / len(valid_results)
                avg_consensus = sum(r['consensus'] for r in valid_results) / len(valid_results)

                comparison_summary = {
                    'total_sectors': len(sectors),
                    'successful_analysis': len(valid_results),
                    'average_reliability': avg_reliability,
                    'average_consensus': avg_consensus,
                    'best_sector': max(valid_results, key=lambda x: x['reliability'])['sector'],
                    'worst_sector': min(valid_results, key=lambda x: x['reliability'])['sector']
                }
            else:
                comparison_summary = {'error': '所有板块分析都失败了'}
        else:
            comparison_summary = {'note': '单板块分析，无对比数据'}

        return jsonify({
            'comparison_results': comparison_results,
            'comparison_summary': comparison_summary,
            'timeframes_used': timeframes
        })

    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/analyze_batch', methods=['POST'])
def analyze_batch():
    """批量分析API"""
    try:
        from multi_timeframe_analyzer import analyze_sector_mtf
        
        sectors = [
            "Technology", "Financials", "Health Care", "Consumer Discretionary",
            "Communication Services", "Industrials", "Consumer Staples", 
            "Energy", "Utilities", "Real Estate", "Materials"
        ]
        
        results = []
        
        for sector in sectors:
            try:
                analysis = analyze_sector_mtf(sector)
                results.append({
                    'sector_name': analysis.sector_name,
                    'unified_decision': analysis.unified_decision,
                    'signal_reliability': analysis.signal_reliability,
                    'market_regime': analysis.market_regime,
                    'overall_risk_level': analysis.overall_risk_level,
                    'entry_strategy': analysis.entry_strategy
                })
            except:
                continue
        
        return jsonify({'results': results})
        
    except Exception as e:
        return jsonify({'error': str(e)})

def run_web_interface():
    """运行Web界面"""
    if not FLASK_AVAILABLE:
        print("❌ Flask未安装，无法启动Web界面")
        print("安装命令: pip install flask")
        return
    
    print("🌐 启动Web界面...")
    print("📱 访问地址: http://localhost:5007")
    print("⏹️  按 Ctrl+C 停止服务")
    print()

    try:
        app.run(host='0.0.0.0', port=5007, debug=False)
    except KeyboardInterrupt:
        print("\n👋 Web服务已停止")

if __name__ == "__main__":
    run_web_interface()
