#!/usr/bin/env python3
"""
测试 hist_data.py 修复
验证导入问题和日志重复问题是否解决
"""

import sys
import os
import time
import multiprocessing

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()


def test_import_fix():
    """测试导入修复"""
    print("=== 测试导入修复 ===\n")
    
    try:
        # 测试从 utils 导入 download_hist_price
        from utils import download_hist_price
        print("✅ 成功从 utils 导入 download_hist_price")
        
        # 测试从 core.utils 导入
        from core.utils import download_hist_price as core_download_hist_price
        print("✅ 成功从 core.utils 导入 download_hist_price")
        
        # 验证函数是否可调用
        if callable(download_hist_price):
            print("✅ download_hist_price 函数可调用")
        else:
            print("❌ download_hist_price 不是函数")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_logging_fix():
    """测试日志修复"""
    print("\n=== 测试日志修复 ===\n")
    
    try:
        # 导入 hist_data 模块
        from core.hist_data import setup_logging, logger
        
        # 重新设置日志
        test_logger = setup_logging()
        
        print("测试日志输出（应该只出现一次）:")
        test_logger.info("🧪 这是一条测试日志消息")
        logger.info("🧪 这是另一条测试日志消息")
        
        print("✅ 日志配置完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志测试失败: {e}")
        return False


def test_basic_functions():
    """测试基础函数"""
    print("\n=== 测试基础函数 ===\n")
    
    try:
        from core.hist_data import get_markets_from_config, get_companies
        
        # 测试获取市场列表
        markets = get_markets_from_config()
        print(f"📈 可用市场: {markets}")
        
        if not markets:
            print("❌ 无法获取市场列表")
            return False
        
        # 测试获取公司列表
        test_market = markets[0]
        companies = get_companies([test_market])
        print(f"🏢 {test_market} 包含 {len(companies)} 只股票")
        
        if not companies:
            print("❌ 无法获取公司列表")
            return False
        
        print("✅ 基础函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基础函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parallel_config():
    """测试并行配置"""
    print("\n=== 测试并行配置 ===\n")
    
    cpu_count = multiprocessing.cpu_count()
    print(f"🖥️  CPU核心数: {cpu_count}")
    
    # 测试激进配置逻辑
    if cpu_count >= 64:
        expected_processes = 48
        expected_batch = 15
        expected_stock_batch = 5000
        expected_threads = 50
        server_type = "64核高性能服务器"
    elif cpu_count >= 32:
        expected_processes = 24
        expected_batch = 12
        expected_stock_batch = 3000
        expected_threads = 30
        server_type = "32核服务器"
    elif cpu_count >= 16:
        expected_processes = 12
        expected_batch = 8
        expected_stock_batch = 2000
        expected_threads = 20
        server_type = "16核服务器"
    else:
        expected_processes = max(4, cpu_count - 2)
        expected_batch = 5
        expected_stock_batch = 1000
        expected_threads = 10
        server_type = "标准服务器"
    
    print(f"🏷️  服务器类型: {server_type}")
    print(f"🚀 预期主进程数: {expected_processes}")
    print(f"📅 预期日期批大小: {expected_batch}")
    print(f"📈 预期股票批大小: {expected_stock_batch}")
    print(f"🌐 预期下载线程数: {expected_threads}")
    
    # 计算CPU利用率
    cpu_utilization = expected_processes / cpu_count * 100
    print(f"⚡ 预期CPU利用率: {cpu_utilization:.1f}%")
    
    if cpu_utilization > 90:
        print("🔥 激进配置：高CPU利用率")
    elif cpu_utilization > 70:
        print("💪 平衡配置：中等CPU利用率")
    else:
        print("✅ 保守配置：低CPU利用率")
    
    return True


def test_small_data_download():
    """测试小量数据下载"""
    print("\n=== 测试小量数据下载 ===\n")
    
    try:
        from utils import download_hist_price
        from core.hist_data import get_companies, get_markets_from_config
        
        # 获取少量股票进行测试
        markets = get_markets_from_config()
        if not markets:
            print("❌ 无法获取市场列表")
            return False
        
        test_market = markets[0]
        companies = get_companies([test_market])
        
        # 只测试前3只股票
        test_companies = companies[:3]
        print(f"🧪 测试下载 {len(test_companies)} 只股票: {test_companies}")
        
        start_time = time.time()
        
        # 下载数据
        price_data = download_hist_price(
            symbols=test_companies,
            interval='1d',
            start='2024-12-01',
            end='2024-12-31',
            columns=['h', 'l', 'c', 'v'],
            threads=2,
            verbose=True
        )
        
        download_time = time.time() - start_time
        
        if price_data:
            print(f"✅ 成功下载 {len(price_data)} 只股票的数据")
            print(f"⏱️  下载耗时: {download_time:.2f}秒")
            
            # 显示数据样本
            for symbol, data in list(price_data.items())[:2]:
                print(f"  📊 {symbol}: {len(data)} 条记录")
                if not data.empty:
                    print(f"    最新价格: {data['close'].iloc[-1]:.2f}")
            
            return True
        else:
            print("❌ 下载数据为空")
            return False
            
    except Exception as e:
        print(f"❌ 数据下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 hist_data.py 修复验证测试\n")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试导入修复
    test_results.append(("导入修复", test_import_fix()))
    
    print("=" * 60)
    
    # 2. 测试日志修复
    test_results.append(("日志修复", test_logging_fix()))
    
    print("=" * 60)
    
    # 3. 测试基础函数
    test_results.append(("基础函数", test_basic_functions()))
    
    print("=" * 60)
    
    # 4. 测试并行配置
    test_results.append(("并行配置", test_parallel_config()))
    
    print("=" * 60)
    
    # 5. 测试小量数据下载
    test_results.append(("数据下载", test_small_data_download()))
    
    print("=" * 60)
    
    # 总结
    print("🎯 === 测试总结 ===")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！hist_data.py 已修复，可以运行激进并行计算")
        print("🚀 建议运行: python core/hist_data.py")
    else:
        print("⚠️  部分测试失败，需要进一步修复")
    
    return passed == total


if __name__ == "__main__":
    main()
