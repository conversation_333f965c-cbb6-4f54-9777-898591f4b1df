#!/usr/bin/env python3
"""
测试 OPT-T3.3 统一RII计算优化
验证统一RII计算的详细分解和动态权重功能
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版板块轮动分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_sector_rotation_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_sector_rotation_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedSectorRotationAnalyzer = analyzer_module.EnhancedSectorRotationAnalyzer
UnifiedRIIDetails = analyzer_module.UnifiedRIIDetails


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health):
        self.internal_health = internal_health


def test_unified_rii_basic():
    """测试基础统一RII计算"""
    print("=== 测试基础统一RII计算 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试不同的输入组合
    test_scenarios = [
        {
            'name': '低活跃度场景',
            'price_dispersion': 0.2,
            'rank_velocity': 0.15,
            'volume_concentration': 0.3,
            'avg_health': 70
        },
        {
            'name': '中等活跃度场景',
            'price_dispersion': 0.5,
            'rank_velocity': 0.45,
            'volume_concentration': 0.6,
            'avg_health': 55
        },
        {
            'name': '高活跃度场景',
            'price_dispersion': 0.8,
            'rank_velocity': 0.75,
            'volume_concentration': 0.85,
            'avg_health': 40
        },
        {
            'name': '不平衡场景1',
            'price_dispersion': 0.9,
            'rank_velocity': 0.2,
            'volume_concentration': 0.7,
            'avg_health': 60
        },
        {
            'name': '不平衡场景2',
            'price_dispersion': 0.3,
            'rank_velocity': 0.8,
            'volume_concentration': 0.4,
            'avg_health': 35
        }
    ]
    
    for scenario in test_scenarios:
        print(f"--- {scenario['name']} ---")
        print(f"输入:")
        print(f"  价格离散度: {scenario['price_dispersion']:.2f}")
        print(f"  排名速度: {scenario['rank_velocity']:.2f}")
        print(f"  成交量集中度: {scenario['volume_concentration']:.2f}")
        print(f"  平均健康度: {scenario['avg_health']}")
        
        # 创建广度指标
        breadth_metrics = {
            f'Sector{i}': MockBreadthMetrics(
                scenario['avg_health'] + np.random.normal(0, 5)
            ) for i in range(5)
        }
        
        # 计算统一RII
        result = analyzer.calculate_unified_rii_enhanced(
            price_dispersion=scenario['price_dispersion'],
            rank_velocity=scenario['rank_velocity'],
            volume_concentration=scenario['volume_concentration'],
            breadth_metrics=breadth_metrics
        )
        
        print(f"\n结果:")
        print(f"  统一RII: {result.unified_rii:.4f}")
        
        print(f"\n分量贡献:")
        for component, contribution in result.component_contributions.items():
            print(f"  {component}: {contribution:.4f}")
        
        print(f"\n中间变量:")
        for var, value in result.intermediate_variables.items():
            print(f"  {var}: {value:.4f}")
        
        print(f"\n归一化因子:")
        for factor, value in result.normalization_factors.items():
            if isinstance(value, (int, float)):
                print(f"  {factor}: {value:.4f}")
            else:
                print(f"  {factor}: {value}")
        
        print(f"\n计算步骤数: {len(result.calculation_steps)}")
        
        # 验证RII范围
        if 0 <= result.unified_rii <= 1:
            print("✅ RII在合理范围内 [0, 1]")
        else:
            print(f"⚠️  RII超出范围: {result.unified_rii:.4f}")
        
        print()
    
    return True


def test_calculation_steps_detail():
    """测试计算步骤详细信息"""
    print("\n=== 测试计算步骤详细信息 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 使用标准测试数据
    breadth_metrics = {
        'Tech': MockBreadthMetrics(75),
        'Health': MockBreadthMetrics(60),
        'Finance': MockBreadthMetrics(45),
        'Energy': MockBreadthMetrics(30)
    }
    
    result = analyzer.calculate_unified_rii_enhanced(
        price_dispersion=0.6,
        rank_velocity=0.5,
        volume_concentration=0.7,
        breadth_metrics=breadth_metrics
    )
    
    print(f"详细计算步骤:")
    for i, step in enumerate(result.calculation_steps, 1):
        print(f"\n步骤 {i}: {step['step_name']}")
        print(f"  描述: {step['description']}")
        if 'formula' in step:
            print(f"  公式: {step['formula']}")
        if 'result' in step:
            if isinstance(step['result'], (int, float)):
                print(f"  结果: {step['result']:.4f}")
            else:
                print(f"  结果: {step['result']}")

        if 'components' in step:
            print(f"  输入分量:")
            for comp, value in step['components'].items():
                if isinstance(value, (int, float)):
                    print(f"    {comp}: {value:.4f}")
                else:
                    print(f"    {comp}: {value}")

        if 'weighted_components' in step:
            print(f"  加权分量:")
            for comp, value in step['weighted_components'].items():
                if isinstance(value, (int, float)):
                    print(f"    {comp}: {value:.4f}")
                else:
                    print(f"    {comp}: {value}")

        if 'final_weights' in step:
            print(f"  最终权重:")
            for comp, value in step['final_weights'].items():
                if isinstance(value, (int, float)):
                    print(f"    {comp}: {value:.4f}")
                else:
                    print(f"    {comp}: {value}")
    
    return True


def test_dynamic_weights():
    """测试动态权重计算"""
    print("\n=== 测试动态权重计算 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试不同健康度场景下的权重调整
    health_scenarios = [
        {'name': '高健康度', 'health_values': [80, 85, 75, 90, 82]},
        {'name': '中等健康度', 'health_values': [55, 60, 50, 65, 58]},
        {'name': '低健康度', 'health_values': [25, 30, 35, 20, 28]},
        {'name': '混合健康度', 'health_values': [85, 45, 70, 25, 60]}
    ]
    
    for scenario in health_scenarios:
        print(f"--- {scenario['name']} ---")
        
        # 创建广度指标
        breadth_metrics = {
            f'Sector{i}': MockBreadthMetrics(health)
            for i, health in enumerate(scenario['health_values'])
        }
        
        avg_health = np.mean(scenario['health_values'])
        print(f"平均健康度: {avg_health:.1f}")
        
        # 计算动态权重
        dynamic_weights, weight_step = analyzer._calculate_dynamic_weights(
            price_rii=0.6,
            breadth_factor=0.4,
            breadth_metrics=breadth_metrics
        )
        
        print(f"动态权重:")
        print(f"  价格权重: {dynamic_weights['price_weight']:.3f}")
        print(f"  广度权重: {dynamic_weights['breadth_weight']:.3f}")
        
        print(f"权重调整信息:")
        adjustments = weight_step['adjustments']
        print(f"  健康度: {adjustments['avg_health']:.1f}")
        print(f"  广度权重调整: {adjustments['breadth_weight_adjustment']:.3f}")
        
        # 验证权重总和
        total_weight = dynamic_weights['price_weight'] + dynamic_weights['breadth_weight']
        if abs(total_weight - 1.0) < 0.001:
            print("✅ 权重总和正确")
        else:
            print(f"⚠️  权重总和异常: {total_weight:.3f}")
        
        print()
    
    return True


def test_component_contributions():
    """测试分量贡献度分析"""
    print("\n=== 测试分量贡献度分析 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试极端情况
    extreme_cases = [
        {
            'name': '价格主导',
            'price_dispersion': 0.9,
            'rank_velocity': 0.8,
            'volume_concentration': 0.85,
            'health': 70
        },
        {
            'name': '广度主导',
            'price_dispersion': 0.2,
            'rank_velocity': 0.15,
            'volume_concentration': 0.25,
            'health': 30
        },
        {
            'name': '平衡情况',
            'price_dispersion': 0.5,
            'rank_velocity': 0.45,
            'volume_concentration': 0.55,
            'health': 55
        }
    ]
    
    for case in extreme_cases:
        print(f"--- {case['name']} ---")
        
        breadth_metrics = {
            'TestSector': MockBreadthMetrics(case['health'])
        }
        
        result = analyzer.calculate_unified_rii_enhanced(
            price_dispersion=case['price_dispersion'],
            rank_velocity=case['rank_velocity'],
            volume_concentration=case['volume_concentration'],
            breadth_metrics=breadth_metrics
        )
        
        print(f"统一RII: {result.unified_rii:.4f}")
        
        # 分析各分量的相对贡献
        contributions = result.component_contributions
        total_contribution = sum(abs(v) for v in contributions.values())
        
        print(f"分量贡献度 (相对):")
        for component, contribution in contributions.items():
            relative_contrib = abs(contribution) / total_contribution if total_contribution > 0 else 0
            print(f"  {component}: {contribution:.4f} ({relative_contrib:.1%})")
        
        # 分析价格vs广度的总体贡献
        price_total = (contributions['price_dispersion'] + 
                      contributions['rank_velocity'] + 
                      contributions['volume_concentration'])
        breadth_total = (contributions['breadth_dispersion'] + 
                        contributions['health_risk'])
        
        print(f"总体贡献:")
        print(f"  价格维度: {price_total:.4f}")
        print(f"  广度维度: {breadth_total:.4f}")
        print(f"  价格占比: {abs(price_total)/(abs(price_total)+abs(breadth_total)):.1%}")
        
        print()
    
    return True


def test_normalization_methods():
    """测试归一化方法"""
    print("\n=== 测试归一化方法 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    # 测试不同的原始RII值
    raw_rii_values = [-0.5, 0.0, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0]
    
    print(f"归一化测试:")
    print(f"{'原始RII':<10} {'归一化RII':<12} {'方法':<10}")
    print("-" * 35)
    
    for raw_rii in raw_rii_values:
        normalized_rii, norm_factors = analyzer._normalize_rii(
            unified_rii=raw_rii,
            price_rii=0.6,
            breadth_factor=0.4
        )
        
        print(f"{raw_rii:<10.2f} {normalized_rii:<12.4f} {norm_factors['normalization_method']:<10}")
    
    print()
    
    # 验证归一化特性
    print(f"归一化特性验证:")
    
    # 测试单调性
    test_values = [0.1, 0.3, 0.5, 0.7, 0.9]
    normalized_values = []
    
    for val in test_values:
        norm_val, _ = analyzer._normalize_rii(val, 0.5, 0.5)
        normalized_values.append(norm_val)
    
    is_monotonic = all(normalized_values[i] <= normalized_values[i+1] 
                      for i in range(len(normalized_values)-1))
    
    print(f"  单调性: {'✅' if is_monotonic else '❌'}")
    
    # 测试范围
    all_in_range = all(0 <= val <= 1 for val in normalized_values)
    print(f"  范围[0,1]: {'✅' if all_in_range else '❌'}")
    
    # 测试中点映射
    mid_point, _ = analyzer._normalize_rii(0.5, 0.5, 0.5)
    mid_point_ok = abs(mid_point - 0.5) < 0.1  # 允许一定误差
    print(f"  中点映射: {'✅' if mid_point_ok else '❌'} (0.5 -> {mid_point:.3f})")
    
    return True


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===\n")
    
    analyzer = EnhancedSectorRotationAnalyzer()
    
    edge_cases = [
        {
            'name': '全零输入',
            'price_dispersion': 0.0,
            'rank_velocity': 0.0,
            'volume_concentration': 0.0,
            'breadth_metrics': {}
        },
        {
            'name': '全最大输入',
            'price_dispersion': 1.0,
            'rank_velocity': 1.0,
            'volume_concentration': 1.0,
            'breadth_metrics': {'Sector1': MockBreadthMetrics(100)}
        },
        {
            'name': '空广度指标',
            'price_dispersion': 0.5,
            'rank_velocity': 0.4,
            'volume_concentration': 0.6,
            'breadth_metrics': {}
        },
        {
            'name': '单一板块',
            'price_dispersion': 0.7,
            'rank_velocity': 0.6,
            'volume_concentration': 0.8,
            'breadth_metrics': {'OnlySector': MockBreadthMetrics(50)}
        }
    ]
    
    for case in edge_cases:
        print(f"--- {case['name']} ---")
        
        try:
            result = analyzer.calculate_unified_rii_enhanced(
                price_dispersion=case['price_dispersion'],
                rank_velocity=case['rank_velocity'],
                volume_concentration=case['volume_concentration'],
                breadth_metrics=case['breadth_metrics']
            )
            
            print(f"  统一RII: {result.unified_rii:.4f}")
            print(f"  计算步骤数: {len(result.calculation_steps)}")
            print(f"  分量数: {len(result.component_contributions)}")
            
            # 验证结果合理性
            if 0 <= result.unified_rii <= 1:
                print("  ✅ 结果在合理范围内")
            else:
                print(f"  ⚠️  结果超出范围: {result.unified_rii:.4f}")
            
            # 检查是否有NaN或无穷大
            if np.isnan(result.unified_rii) or np.isinf(result.unified_rii):
                print("  ❌ 结果包含NaN或无穷大")
            else:
                print("  ✅ 结果数值正常")
                
        except Exception as e:
            print(f"  ❌ 计算失败: {e}")
        
        print()
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T3.3 统一RII计算优化\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础统一RII计算
        test_results.append(("基础统一RII计算", test_unified_rii_basic()))
        
        print("=" * 60)
        
        # 2. 测试计算步骤详细信息
        test_results.append(("计算步骤详细信息", test_calculation_steps_detail()))
        
        print("=" * 60)
        
        # 3. 测试动态权重
        test_results.append(("动态权重计算", test_dynamic_weights()))
        
        print("=" * 60)
        
        # 4. 测试分量贡献度
        test_results.append(("分量贡献度分析", test_component_contributions()))
        
        print("=" * 60)
        
        # 5. 测试归一化方法
        test_results.append(("归一化方法", test_normalization_methods()))
        
        print("=" * 60)
        
        # 6. 测试边界情况
        test_results.append(("边界情况", test_edge_cases()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T3.3 统一RII计算优化完成！")
            print("🚀 主要改进:")
            print("  - 详细的计算步骤分解")
            print("  - 动态权重调整机制")
            print("  - 分量贡献度分析")
            print("  - 稳健的归一化方法")
            print("  - 完善的边界情况处理")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
