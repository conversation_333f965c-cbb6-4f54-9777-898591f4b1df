#!/usr/bin/env python3
"""
板块轮动指标计算 - 手动运行脚本
专门用于计算多时间框架的板块轮动指标
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def run_sector_rotation_calculation():
    """运行板块轮动指标计算"""
    print("🔄 开始多时间框架板块轮动指标计算")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 导入板块轮动调度器和配置
        from sector_rotation_scheduler import SectorRotationScheduler
        from sector_settings import get_mtf_config, get_run_mode_config

        # 检查MTF配置
        mtf_config = get_mtf_config()
        run_config = get_run_mode_config()

        print("📊 正在计算板块轮动指标...")
        print("   包括：RII指数、价格离散度、排名变化速度、轮动阶段等")
        print(f"   MTF分析: {'✅ 启用' if mtf_config.ENABLE_MTF_ANALYSIS else '❌ 禁用'}")
        print(f"   独立模式MTF: {'✅ 启用' if run_config.STANDALONE_ENABLE_MTF else '❌ 禁用'}")
        print(f"   MTF数据天数: {mtf_config.MTF_ANALYSIS_DAYS} 天")
        print()

        # 创建调度器实例
        scheduler = SectorRotationScheduler()

        # 检查MTF分析器是否可用
        if scheduler.mtf_analyzer:
            print("✅ MTF分析器初始化成功，支持多时间框架分析")
        else:
            print("⚠️  MTF分析器不可用，将使用基础轮动计算")

        # 执行计算
        start_time = time.time()
        success = scheduler.calculate_rotation_metrics()
        end_time = time.time()
        
        duration = end_time - start_time
        
        if success:
            print(f"\n✅ 板块轮动指标计算完成!")
            print(f"⏱️  总耗时: {duration:.1f} 秒")
            print(f"🏁 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 验证计算结果
            verify_rotation_results()
            
            return True
        else:
            print(f"\n❌ 板块轮动指标计算失败!")
            print("💡 可能原因:")
            print("   - 缺少基础市场广度数据")
            print("   - 数据库连接问题")
            print("   - 配置文件错误")
            return False
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_rotation_results():
    """验证板块轮动计算结果"""
    print("\n🔍 验证板块轮动计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 查询轮动指标数据
        cursor.execute("""
        SELECT sector, 
               rotation_intensity_index, 
               price_dispersion, 
               rank_velocity, 
               volume_concentration,
               rotation_stage,
               risk_level,
               sector_rank,
               recorded_at
        FROM sector_rotation_metrics_gics
        WHERE DATE(recorded_at) = CURDATE()
        ORDER BY sector_rank ASC
        LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("📊 板块轮动指标数据（今日前10名）:")
            print("-" * 80)
            print(f"{'排名':<4} {'板块':<15} {'RII指数':<8} {'价格离散':<8} {'排名速度':<8} {'轮动阶段':<10}")
            print("-" * 80)
            
            for row in results:
                sector, rii, price_disp, rank_vel, vol_conc, stage, risk, rank, recorded_at = row
                print(f"{rank:<4} {sector:<15} {rii:<8.3f} {price_disp:<8.3f} {rank_vel:<8.3f} {stage:<10}")
            
            print(f"\n📅 数据时间: {results[0][8]}")
            
            # 检查关键指标是否有效
            valid_count = sum(1 for row in results if row[1] > 0)  # RII > 0
            print(f"✅ 有效轮动指标数量: {valid_count}/{len(results)}")
            
            if valid_count > 0:
                print("🎉 板块轮动指标计算成功，数据有效！")
            else:
                print("⚠️  轮动指标大多为0，可能需要更多基础数据")
        else:
            print("❌ 未找到今日的板块轮动数据")
            
            # 检查历史数据
            cursor.execute("""
            SELECT COUNT(*) as total_count, MAX(recorded_at) as latest_time
            FROM sector_rotation_metrics_gics
            """)
            
            history_result = cursor.fetchone()
            if history_result and history_result[0] > 0:
                print(f"📊 历史数据: 共{history_result[0]}条记录")
                print(f"📅 最新时间: {history_result[1]}")
            else:
                print("❌ 数据库中无任何轮动指标数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_web_interface_integration():
    """测试Web界面集成"""
    print("\n🌐 测试Web界面集成...")
    
    try:
        from multi_timeframe_analyzer import analyze_market_mtf
        
        print("📊 测试Technology板块轮动分析...")
        
        # 测试轮动分析
        result = analyze_market_mtf(
            sector="Technology",
            timeframes=['1d'],
            output_format='full'
        )
        
        if result and 'rotation_analysis' in result:
            rotation = result['rotation_analysis']
            print("✅ Web界面轮动分析正常:")
            print(f"   RII指数: {rotation.get('unified_rii', 'N/A')}")
            print(f"   价格离散度: {rotation.get('price_dispersion', 'N/A')}")
            print(f"   轮动阶段: {rotation.get('rotation_stage', 'N/A')}")
            print(f"   风险等级: {rotation.get('risk_level', 'N/A')}")
            
            # 检查是否不再是0
            rii = rotation.get('unified_rii', 0)
            if rii > 0:
                print("🎉 轮动指标不再是0，计算成功！")
            else:
                print("⚠️  轮动指标仍为0，可能需要更多数据积累")
            
            return True
        else:
            print("⚠️  Web界面轮动分析异常")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 板块轮动指标计算系统")
    print("专门计算多时间框架的板块轮动指标")
    print()
    
    # 步骤1: 运行板块轮动计算
    calc_success = run_sector_rotation_calculation()
    
    if calc_success:
        # 步骤2: 测试Web界面集成
        web_success = test_web_interface_integration()
        
        print("\n" + "=" * 60)
        print("📋 运行结果总结:")
        print(f"   板块轮动计算: {'✅ 成功' if calc_success else '❌ 失败'}")
        print(f"   Web界面集成: {'✅ 正常' if web_success else '⚠️ 异常'}")
        
        if calc_success and web_success:
            print("\n🎉 系统运行成功!")
            print("\n💡 下一步操作:")
            print("1. 启动Web界面: python web_interface.py")
            print("2. 访问 http://localhost:5000")
            print("3. 选择板块进行轮动分析")
            print("4. 现在应该能看到有效的轮动指标了！")
            
            print("\n🔄 轮动指标说明:")
            print("- RII指数: 统一轮动强度指数 (0-1)")
            print("- 价格离散度: 板块间价格差异程度")
            print("- 排名变化速度: 板块排名变化频率")
            print("- 轮动阶段: 稳定期/收敛期/启动期/加速期/混乱期")
            
            print("\n⏰ 自动化建议:")
            print("- 每日运行此脚本更新轮动指标")
            print("- 或配置Windows任务计划程序自动运行")
            print("- 建议在市场广度计算后运行")
        else:
            print("\n⚠️ 部分功能异常，请检查系统配置")
    
    else:
        print("\n❌ 板块轮动计算失败")
        print("💡 故障排除建议:")
        print("1. 先运行市场广度计算: python run_market_breadth_calculation.py")
        print("2. 检查数据库连接配置")
        print("3. 确认Redis服务是否运行")
        print("4. 查看错误日志信息")
        print("5. 尝试运行: python core/sector_rotation_scheduler.py --standalone")
    
    print(f"\n📝 详细日志可查看系统日志文件")
    
    return calc_success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
