#!/usr/bin/env python3
"""
测试 OPT-T4.3 避免清单生成
验证增强版多时间框架分析器的避免清单生成功能
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入增强版多时间框架分析器
analyzer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'enhanced_multi_timeframe_analyzer.py')
spec = importlib.util.spec_from_file_location("enhanced_multi_timeframe_analyzer", analyzer_path)
analyzer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(analyzer_module)

EnhancedMultiTimeframeAnalyzer = analyzer_module.EnhancedMultiTimeframeAnalyzer
AvoidListDetails = analyzer_module.AvoidListDetails


class MockRotationMetrics:
    """模拟轮动指标"""
    def __init__(self):
        self.unified_rii = 0.5


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health=50, momentum_coherence=0.5, 
                 participation_rate=0.6, price_breadth_divergence=None):
        self.internal_health = internal_health
        self.momentum_coherence = momentum_coherence
        self.participation_rate = participation_rate
        self.price_breadth_divergence = price_breadth_divergence or {'type': 'none'}


def test_avoid_list_basic():
    """测试基础避免清单生成"""
    print("=== 测试基础避免清单生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建测试数据 - 包含不同质量的板块
    breadth_metrics = {
        'Technology': MockBreadthMetrics(75, 0.8, 0.75),  # 优质板块
        'Healthcare': MockBreadthMetrics(65, 0.6, 0.65),  # 中等板块
        'Finance': MockBreadthMetrics(35, 0.3, 0.45, {'type': 'negative', 'severity': 0.3}),  # 差板块
        'Energy': MockBreadthMetrics(25, 0.2, 0.35, {'type': 'negative', 'severity': 0.4}),  # 很差板块
        'Consumer': MockBreadthMetrics(60, 0.7, 0.70),   # 良好板块
        'Industrial': MockBreadthMetrics(40, 0.4, 0.50), # 一般板块
        'Materials': MockBreadthMetrics(20, 0.15, 0.30, {'type': 'negative', 'severity': 0.5})  # 最差板块
    }
    
    rotation_metrics = MockRotationMetrics()
    market_regime = 'normal_market'
    
    print(f"📊 输入数据:")
    print(f"板块数: {len(breadth_metrics)}")
    print(f"市场状态: {market_regime}")
    
    print(f"\n板块质量概览:")
    for sector, metrics in breadth_metrics.items():
        print(f"  {sector}: 健康度{metrics.internal_health}, 一致性{metrics.momentum_coherence:.2f}, 参与率{metrics.participation_rate:.2f}")
    
    # 生成避免清单
    avoid_result = analyzer.generate_avoid_list_enhanced(
        breadth_metrics=breadth_metrics,
        rotation_metrics=rotation_metrics,
        market_regime=market_regime
    )
    
    print(f"\n🚫 避免清单结果:")
    print(f"避免板块数: {len(avoid_result.avoid_list)}")
    print(f"避免清单: {avoid_result.avoid_list}")
    
    print(f"\n⚠️ 严重度分级:")
    severity_counts = {}
    for sector, severity in avoid_result.severity_levels.items():
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
        if sector in avoid_result.avoid_list:
            print(f"  {sector}: {severity}")
    
    print(f"\n📊 严重度统计:")
    for severity, count in severity_counts.items():
        print(f"  {severity}: {count}个板块")
    
    print(f"\n🔄 替代机会 ({len(avoid_result.alternative_opportunities)}个):")
    for alt in avoid_result.alternative_opportunities:
        print(f"  - {alt}")
    
    return True


def test_avoid_score_calculation():
    """测试避免分数计算"""
    print("\n=== 测试避免分数计算 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 测试不同质量的板块
    test_sectors = {
        'Excellent': MockBreadthMetrics(90, 0.9, 0.85),
        'Good': MockBreadthMetrics(70, 0.7, 0.75),
        'Average': MockBreadthMetrics(50, 0.5, 0.60),
        'Poor': MockBreadthMetrics(30, 0.3, 0.45, {'type': 'negative', 'severity': 0.2}),
        'Terrible': MockBreadthMetrics(15, 0.1, 0.25, {'type': 'negative', 'severity': 0.5})
    }
    
    rotation_metrics = MockRotationMetrics()
    
    # 计算避免分数
    avoid_scores = analyzer._calculate_avoid_scores(test_sectors, rotation_metrics)
    
    print(f"避免分数计算:")
    print(f"{'板块':<12} {'健康度':<8} {'一致性':<8} {'参与率':<8} {'避免分数':<10} {'等级'}")
    print("-" * 65)
    
    for sector, score in avoid_scores.items():
        metrics = test_sectors[sector]
        
        # 确定等级
        if score >= 0.8:
            grade = "严重避免"
        elif score >= 0.6:
            grade = "高度避免"
        elif score >= 0.4:
            grade = "中度避免"
        elif score >= 0.2:
            grade = "轻度关注"
        else:
            grade = "可接受"
        
        print(f"{sector:<12} {metrics.internal_health:<8} {metrics.momentum_coherence:<8.2f} {metrics.participation_rate:<8.2f} {score:<10.3f} {grade}")
    
    # 验证分数合理性
    excellent_score = avoid_scores['Excellent']
    terrible_score = avoid_scores['Terrible']
    
    if excellent_score < terrible_score:
        print(f"\n✅ 避免分数计算合理：优质板块({excellent_score:.3f}) < 劣质板块({terrible_score:.3f})")
    else:
        print(f"\n⚠️  避免分数可能有误：优质板块({excellent_score:.3f}) >= 劣质板块({terrible_score:.3f})")
    
    return True


def test_market_regime_adjustment():
    """测试市场状态调整"""
    print("\n=== 测试市场状态调整 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 固定的板块数据
    breadth_metrics = {
        'TestSector1': MockBreadthMetrics(40, 0.4, 0.5),
        'TestSector2': MockBreadthMetrics(60, 0.6, 0.7),
        'TestSector3': MockBreadthMetrics(30, 0.3, 0.4)
    }
    
    rotation_metrics = MockRotationMetrics()
    
    # 测试不同市场状态
    market_regimes = [
        'trending_stable',
        'normal_market',
        'high_rotation', 
        'short_term_stress',
        'regime_transition',
        'divergent_market'
    ]
    
    print(f"市场状态对避免清单的影响:")
    print(f"{'市场状态':<18} {'避免板块数':<10} {'严重避免数':<10} {'调整效果'}")
    print("-" * 55)
    
    baseline_count = None
    
    for regime in market_regimes:
        avoid_result = analyzer.generate_avoid_list_enhanced(
            breadth_metrics=breadth_metrics,
            rotation_metrics=rotation_metrics,
            market_regime=regime
        )
        
        avoid_count = len(avoid_result.avoid_list)
        critical_count = sum(1 for s in avoid_result.severity_levels.values() if s == 'critical')
        
        if baseline_count is None:
            baseline_count = avoid_count
            effect = "基准"
        elif avoid_count > baseline_count:
            effect = "增加避免"
        elif avoid_count < baseline_count:
            effect = "减少避免"
        else:
            effect = "无变化"
        
        print(f"{regime:<18} {avoid_count:<10} {critical_count:<10} {effect}")
    
    return True


def test_avoid_reasons_generation():
    """测试避免原因生成"""
    print("\n=== 测试避免原因生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建有明确问题的板块
    problem_sectors = {
        'HealthProblem': MockBreadthMetrics(25, 0.6, 0.7),  # 健康度问题
        'CoherenceProblem': MockBreadthMetrics(70, 0.2, 0.7),  # 一致性问题
        'ParticipationProblem': MockBreadthMetrics(70, 0.7, 0.3),  # 参与率问题
        'DivergenceProblem': MockBreadthMetrics(60, 0.6, 0.6, {'type': 'negative', 'severity': 0.4}),  # 背离问题
        'MultipleProblem': MockBreadthMetrics(20, 0.15, 0.25, {'type': 'negative', 'severity': 0.5})  # 多重问题
    }
    
    rotation_metrics = MockRotationMetrics()
    
    # 生成避免清单
    avoid_result = analyzer.generate_avoid_list_enhanced(
        breadth_metrics=problem_sectors,
        rotation_metrics=rotation_metrics,
        market_regime='normal_market'
    )
    
    print(f"避免原因分析:")
    
    for sector in avoid_result.avoid_list:
        if sector in avoid_result.avoid_reasons:
            reasons = avoid_result.avoid_reasons[sector]
            
            print(f"\n--- {sector} ---")
            print(f"严重度评分: {reasons['severity_score']:.3f}")
            
            if reasons['primary_issues']:
                print(f"主要问题:")
                for issue in reasons['primary_issues']:
                    print(f"  - {issue}")
            
            if reasons['secondary_issues']:
                print(f"次要问题:")
                for issue in reasons['secondary_issues']:
                    print(f"  - {issue}")
            
            print(f"详细分析: {len(reasons['detailed_analysis'])}项")
    
    return True


def test_improvement_conditions():
    """测试改善条件生成"""
    print("\n=== 测试改善条件生成 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建需要改善的板块
    weak_sectors = {
        'WeakSector1': MockBreadthMetrics(30, 0.3, 0.4),
        'WeakSector2': MockBreadthMetrics(25, 0.2, 0.35, {'type': 'negative', 'severity': 0.3})
    }
    
    rotation_metrics = MockRotationMetrics()
    
    # 生成避免清单
    avoid_result = analyzer.generate_avoid_list_enhanced(
        breadth_metrics=weak_sectors,
        rotation_metrics=rotation_metrics,
        market_regime='normal_market'
    )
    
    print(f"改善条件分析:")
    
    for sector in avoid_result.avoid_list:
        if sector in avoid_result.improvement_conditions:
            conditions = avoid_result.improvement_conditions[sector]
            
            print(f"\n--- {sector} ---")
            print(f"改善条件 ({len(conditions)}项):")
            for i, condition in enumerate(conditions, 1):
                print(f"  {i}. {condition}")
    
    # 测试重新评估触发条件
    print(f"\n重新评估触发条件:")
    
    for sector in avoid_result.avoid_list:
        if sector in avoid_result.reassessment_triggers:
            triggers = avoid_result.reassessment_triggers[sector]
            
            print(f"\n--- {sector} ---")
            print(f"触发条件 ({len(triggers)}项):")
            for i, trigger in enumerate(triggers, 1):
                print(f"  {i}. {trigger}")
    
    return True


def test_alternative_opportunities():
    """测试替代机会寻找"""
    print("\n=== 测试替代机会寻找 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建混合质量的板块组合
    mixed_sectors = {
        'Avoid1': MockBreadthMetrics(20, 0.2, 0.3),  # 应该避免
        'Avoid2': MockBreadthMetrics(25, 0.15, 0.25), # 应该避免
        'Good1': MockBreadthMetrics(80, 0.8, 0.8),   # 好机会
        'Good2': MockBreadthMetrics(75, 0.75, 0.75), # 好机会
        'Average1': MockBreadthMetrics(50, 0.5, 0.6), # 一般
        'Average2': MockBreadthMetrics(55, 0.55, 0.65), # 一般
        'Excellent': MockBreadthMetrics(90, 0.9, 0.9, {'type': 'positive', 'severity': 0.2}) # 优秀机会
    }
    
    rotation_metrics = MockRotationMetrics()
    
    # 生成避免清单
    avoid_result = analyzer.generate_avoid_list_enhanced(
        breadth_metrics=mixed_sectors,
        rotation_metrics=rotation_metrics,
        market_regime='normal_market'
    )
    
    print(f"📊 板块分类结果:")
    print(f"避免清单: {avoid_result.avoid_list}")
    print(f"替代机会: {avoid_result.alternative_opportunities}")
    
    # 验证替代机会质量
    print(f"\n🔍 替代机会质量验证:")
    
    for alt in avoid_result.alternative_opportunities:
        if alt in mixed_sectors:
            metrics = mixed_sectors[alt]
            print(f"  {alt}: 健康度{metrics.internal_health}, 一致性{metrics.momentum_coherence:.2f}, 参与率{metrics.participation_rate:.2f}")
    
    # 检查是否避免了低质量板块
    avoid_set = set(avoid_result.avoid_list)
    alt_set = set(avoid_result.alternative_opportunities)
    
    overlap = avoid_set & alt_set
    if not overlap:
        print(f"\n✅ 避免清单和替代机会无重叠，分类正确")
    else:
        print(f"\n⚠️  避免清单和替代机会有重叠: {overlap}")
    
    return True


def test_comprehensive_scenario():
    """测试综合场景"""
    print("\n=== 测试综合场景 ===\n")
    
    analyzer = EnhancedMultiTimeframeAnalyzer()
    
    # 创建复杂的市场场景
    comprehensive_sectors = {
        'Tech_Leader': MockBreadthMetrics(85, 0.85, 0.8, {'type': 'positive', 'severity': 0.1}),
        'Tech_Laggard': MockBreadthMetrics(45, 0.4, 0.5),
        'Health_Strong': MockBreadthMetrics(78, 0.75, 0.75),
        'Health_Weak': MockBreadthMetrics(32, 0.3, 0.4, {'type': 'negative', 'severity': 0.25}),
        'Finance_Stable': MockBreadthMetrics(65, 0.6, 0.65),
        'Finance_Troubled': MockBreadthMetrics(28, 0.25, 0.35, {'type': 'negative', 'severity': 0.4}),
        'Energy_Disaster': MockBreadthMetrics(15, 0.1, 0.2, {'type': 'negative', 'severity': 0.6}),
        'Consumer_Mixed': MockBreadthMetrics(55, 0.5, 0.6),
        'Industrial_Declining': MockBreadthMetrics(35, 0.35, 0.45, {'type': 'negative', 'severity': 0.3}),
        'Materials_Recovery': MockBreadthMetrics(68, 0.65, 0.7, {'type': 'positive', 'severity': 0.15})
    }
    
    rotation_metrics = MockRotationMetrics()
    market_regime = 'high_rotation'  # 高轮动市场
    
    print(f"📊 综合场景分析:")
    print(f"板块总数: {len(comprehensive_sectors)}")
    print(f"市场状态: {market_regime}")
    
    # 生成完整分析
    avoid_result = analyzer.generate_avoid_list_enhanced(
        breadth_metrics=comprehensive_sectors,
        rotation_metrics=rotation_metrics,
        market_regime=market_regime
    )
    
    print(f"\n🎯 综合分析结果:")
    print(f"避免板块数: {len(avoid_result.avoid_list)}")
    print(f"替代机会数: {len(avoid_result.alternative_opportunities)}")
    
    # 按严重度分类
    severity_groups = {}
    for sector, severity in avoid_result.severity_levels.items():
        if severity not in severity_groups:
            severity_groups[severity] = []
        severity_groups[severity].append(sector)
    
    print(f"\n📊 严重度分布:")
    for severity in ['critical', 'high', 'medium', 'low']:
        if severity in severity_groups:
            count = len(severity_groups[severity])
            print(f"  {severity}: {count}个板块 - {severity_groups[severity]}")
    
    print(f"\n🔄 投资建议:")
    print(f"强烈避免: {[s for s, sev in avoid_result.severity_levels.items() if sev == 'critical']}")
    print(f"优先考虑: {avoid_result.alternative_opportunities[:3]}")  # 前3个机会
    
    # 生成总体建议
    avoid_ratio = len(avoid_result.avoid_list) / len(comprehensive_sectors)
    if avoid_ratio > 0.6:
        market_assessment = "市场环境恶劣，大部分板块需要避免"
    elif avoid_ratio > 0.4:
        market_assessment = "市场环境一般，需要精选板块"
    else:
        market_assessment = "市场环境良好，有较多投资机会"
    
    print(f"\n💡 市场评估: {market_assessment} (避免比例: {avoid_ratio:.1%})")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T4.3 避免清单生成\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础避免清单生成
        test_results.append(("基础避免清单生成", test_avoid_list_basic()))
        
        print("=" * 60)
        
        # 2. 测试避免分数计算
        test_results.append(("避免分数计算", test_avoid_score_calculation()))
        
        print("=" * 60)
        
        # 3. 测试市场状态调整
        test_results.append(("市场状态调整", test_market_regime_adjustment()))
        
        print("=" * 60)
        
        # 4. 测试避免原因生成
        test_results.append(("避免原因生成", test_avoid_reasons_generation()))
        
        print("=" * 60)
        
        # 5. 测试改善条件
        test_results.append(("改善条件生成", test_improvement_conditions()))
        
        print("=" * 60)
        
        # 6. 测试替代机会
        test_results.append(("替代机会寻找", test_alternative_opportunities()))
        
        print("=" * 60)
        
        # 7. 测试综合场景
        test_results.append(("综合场景", test_comprehensive_scenario()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T4.3 避免清单生成完成！")
            print("🚀 主要改进:")
            print("  - 多维度避免分数计算")
            print("  - 市场状态动态调整")
            print("  - 详细避免原因分析")
            print("  - 改善条件和触发机制")
            print("  - 智能替代机会推荐")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
