#!/usr/bin/env python3
"""
计算SP500最近40天的5分钟市场广度数据
下载365天数据，计算40天的每个5分钟时间点
"""

import sys
import os
import time
from datetime import datetime, timedelta
import pandas as pd

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def calculate_sp500_40day_5m():
    """计算SP500最近40天的5分钟市场广度数据"""
    print("🎯 计算SP500最近40天的5分钟市场广度数据")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📊 计算配置:")
    print("   市场: SP500")
    print("   时间框架: 5分钟")
    print("   计算天数: 最近40天")
    print("   数据下载: 365天（足够计算52周指标）")
    print()
    
    try:
        import pymysql
        from db_settings import get_default_db_config

        # 检查关键环境变量
        if 'SECURITY_DB_HOST' not in os.environ:
            print("❌ 缺少 SECURITY_DB_HOST 环境变量")
            print("💡 请确认 .env 文件已正确加载")
            print(f"💡 当前工作目录: {os.getcwd()}")
            print(f"💡 .env 文件是否存在: {os.path.exists('.env')}")
            return False

        print(f"✅ 环境变量加载成功")

        from utils import download_hist_price
        
        # 确认执行
        confirm = input("是否开始计算SP500最近40天的5分钟市场广度数据? (y/N): ").strip().lower()
        if confirm != 'y':
            print("👋 用户取消操作")
            return False
        
        print("\n🔄 开始计算...")
        
        # 获取SP500股票列表
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("SELECT company FROM index_company_mapping_gics WHERE market='SP500'")
        sp500_companies = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not sp500_companies:
            print("❌ 没有找到SP500股票列表")
            return False
        
        print(f"📊 SP500包含 {len(sp500_companies)} 只股票")
        
        # 计算日期范围
        end_date = datetime.now()
        data_start_date = end_date - timedelta(days=365)  # 下载365天数据
        calc_start_date = end_date - timedelta(days=40)   # 计算40天数据
        
        print(f"📅 数据下载范围: {data_start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} (365天)")
        print(f"📅 计算范围: {calc_start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} (40天)")
        
        # 下载365天的5分钟数据
        print(f"\n📥 下载SP500的5分钟数据...")
        start_time = time.time()
        
        price_data = download_hist_price(
            symbols=sp500_companies,
            interval='5min',
            start=data_start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=8
        )
        
        download_time = time.time() - start_time
        
        if not price_data:
            print("❌ 股价数据下载失败")
            return False
        
        print(f"✅ 数据下载完成，耗时 {download_time:.1f} 秒")
        print(f"📊 获得 {len(price_data)} 只股票的数据")
        
        # 获取所有5分钟时间点
        all_timestamps = set()
        valid_companies = 0
        
        for company, data in price_data.items():
            if data is not None and hasattr(data, 'index') and len(data) > 0:
                all_timestamps.update(data.index)
                valid_companies += 1
        
        print(f"📊 有效股票: {valid_companies}/{len(sp500_companies)}")
        print(f"📅 总时间点: {len(all_timestamps)}")
        
        # 筛选最近40天的时间点
        calc_timestamps = [ts for ts in all_timestamps if ts >= calc_start_date]
        calc_timestamps = sorted(calc_timestamps)
        
        print(f"📅 需要计算的时间点: {len(calc_timestamps)}")
        
        if len(calc_timestamps) == 0:
            print("❌ 没有找到需要计算的时间点")
            return False
        
        print(f"📅 时间范围: {calc_timestamps[0]} 到 {calc_timestamps[-1]}")
        
        # 为每个时间点计算市场广度
        print(f"\n🚀 开始计算每个时间点的市场广度...")
        
        success_count = 0
        batch_data = []
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        for i, timestamp in enumerate(calc_timestamps):
            try:
                # 显示进度
                if i % 100 == 0 or i == len(calc_timestamps) - 1:
                    progress = (i + 1) / len(calc_timestamps) * 100
                    print(f"📈 进度: {i+1}/{len(calc_timestamps)} ({progress:.1f}%) - {timestamp}")
                
                # 计算该时间点的市场广度
                breadth_metrics = calculate_breadth_at_timestamp(
                    timestamp, sp500_companies, price_data
                )
                
                if breadth_metrics:
                    # 添加到批量数据
                    batch_data.append(('SP500', '5m', timestamp, breadth_metrics))
                    success_count += 1
                    
                    # 每500条记录批量保存一次
                    if len(batch_data) >= 500:
                        batch_save_breadth_to_db(cursor, batch_data)
                        conn.commit()
                        batch_data = []
                        print(f"💾 已保存 {success_count} 条记录")
                
            except Exception as e:
                print(f"⚠️  {timestamp} 计算失败: {e}")
                continue
        
        # 保存剩余数据
        if batch_data:
            batch_save_breadth_to_db(cursor, batch_data)
            conn.commit()
        
        conn.close()
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 计算完成!")
        print(f"📊 成功计算: {success_count}/{len(calc_timestamps)} 个时间点")
        print(f"⏱️  总耗时: {total_time:.1f} 秒")
        print(f"📈 平均速度: {success_count/total_time:.1f} 时间点/秒")
        
        # 验证结果
        verify_results()
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_breadth_at_timestamp(timestamp, companies, price_data):
    """计算指定时间点的市场广度指标"""
    try:
        total_stocks = 0
        advances = 0
        declines = 0
        unchanged = 0
        total_volume = 0
        advancing_volume = 0
        declining_volume = 0
        
        # 52周和MA指标
        new_highs_52w = 0
        new_lows_52w = 0
        above_ma50 = 0
        above_ma200 = 0
        rsi_values = []
        
        for company in companies:
            if company not in price_data:
                continue
                
            data = price_data[company]
            if data is None or not hasattr(data, 'index'):
                continue
            
            if timestamp not in data.index:
                continue
            
            total_stocks += 1
            
            # 获取当前价格数据
            current_row = data.loc[timestamp]
            current_price = current_row.get('c', current_row.get('close', 0))
            current_volume = current_row.get('v', current_row.get('volume', 0))
            
            # 计算涨跌（比较前一期价格）
            try:
                data_sorted = data.sort_index()
                current_idx = data_sorted.index.get_loc(timestamp)
                if current_idx > 0:
                    prev_price = data_sorted.iloc[current_idx - 1].get('c', current_price)
                    
                    if current_price > prev_price:
                        advances += 1
                        advancing_volume += current_volume
                    elif current_price < prev_price:
                        declines += 1
                        declining_volume += current_volume
                    else:
                        unchanged += 1
                else:
                    unchanged += 1
            except:
                unchanged += 1
            
            total_volume += current_volume
            
            # 计算52周新高新低
            try:
                week_52_ago = timestamp - timedelta(weeks=52)
                stock_52w_data = data[data.index >= week_52_ago]
                
                if len(stock_52w_data) > 0:
                    high_52w = stock_52w_data['h'].max() if 'h' in stock_52w_data.columns else stock_52w_data.get('high', pd.Series()).max()
                    low_52w = stock_52w_data['l'].min() if 'l' in stock_52w_data.columns else stock_52w_data.get('low', pd.Series()).min()
                    
                    current_high = current_row.get('h', current_row.get('high', current_price))
                    current_low = current_row.get('l', current_row.get('low', current_price))
                    
                    if pd.notna(high_52w) and current_high >= high_52w:
                        new_highs_52w += 1
                    if pd.notna(low_52w) and current_low <= low_52w:
                        new_lows_52w += 1
            except:
                pass
            
            # 计算MA指标（使用50天和200天的数据）
            try:
                data_sorted = data.sort_index()
                current_idx = data_sorted.index.get_loc(timestamp)
                close_prices = data_sorted.get('c', data_sorted.get('close', pd.Series()))
                
                if current_idx >= 49:  # MA50
                    ma50_data = close_prices.iloc[max(0, current_idx-49):current_idx+1]
                    ma50 = ma50_data.mean()
                    if pd.notna(ma50) and current_price > ma50:
                        above_ma50 += 1
                
                if current_idx >= 199:  # MA200
                    ma200_data = close_prices.iloc[max(0, current_idx-199):current_idx+1]
                    ma200 = ma200_data.mean()
                    if pd.notna(ma200) and current_price > ma200:
                        above_ma200 += 1
                
                # 计算RSI
                if current_idx >= 13:
                    rsi_data = close_prices.iloc[max(0, current_idx-13):current_idx+1]
                    rsi = calculate_rsi(rsi_data)
                    if not pd.isna(rsi):
                        rsi_values.append(rsi)
            except:
                pass
        
        if total_stocks == 0:
            return None
        
        # 计算平均RSI
        avg_rsi = sum(rsi_values) / len(rsi_values) if rsi_values else 50.0
        
        # 计算内部健康度
        advance_ratio = advances / total_stocks if total_stocks > 0 else 0.5
        internal_health = advance_ratio * 100
        
        return {
            'total_stocks': total_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'total_volume': total_volume,
            'advancing_volume': advancing_volume,
            'declining_volume': declining_volume,
            'new_highs_52w': new_highs_52w,
            'new_lows_52w': new_lows_52w,
            'above_ma50': above_ma50,
            'above_ma200': above_ma200,
            'avg_rsi': avg_rsi,
            'internal_health': internal_health
        }
        
    except Exception as e:
        return None

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    try:
        import pandas as pd
        
        if len(prices) < period + 1:
            return 50.0
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    except:
        return 50.0

def batch_save_breadth_to_db(cursor, batch_data):
    """批量保存广度指标到数据库"""
    try:
        insert_sql = """
        INSERT INTO market_breadth_metrics_gics (
            market, timeframe, total_stocks, advances, declines, unchanged,
            total_volume, advancing_volume, declining_volume,
            new_highs_52w, new_lows_52w, above_ma50, above_ma200,
            avg_rsi, internal_health, recorded_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            total_stocks = VALUES(total_stocks),
            advances = VALUES(advances),
            declines = VALUES(declines),
            unchanged = VALUES(unchanged),
            total_volume = VALUES(total_volume),
            advancing_volume = VALUES(advancing_volume),
            declining_volume = VALUES(declining_volume),
            new_highs_52w = VALUES(new_highs_52w),
            new_lows_52w = VALUES(new_lows_52w),
            above_ma50 = VALUES(above_ma50),
            above_ma200 = VALUES(above_ma200),
            avg_rsi = VALUES(avg_rsi),
            internal_health = VALUES(internal_health)
        """
        
        batch_values = []
        for market, timeframe, timestamp, metrics in batch_data:
            batch_values.append((
                market, timeframe,
                metrics['total_stocks'], metrics['advances'], metrics['declines'], metrics['unchanged'],
                metrics['total_volume'], metrics['advancing_volume'], metrics['declining_volume'],
                metrics['new_highs_52w'], metrics['new_lows_52w'], 
                metrics['above_ma50'], metrics['above_ma200'],
                metrics['avg_rsi'], metrics['internal_health'],
                timestamp
            ))
        
        cursor.executemany(insert_sql, batch_values)
        return True
        
    except Exception as e:
        print(f"❌ 批量保存失败: {e}")
        return False

def verify_results():
    """验证计算结果"""
    print(f"\n🔍 验证计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT COUNT(*) as total_records,
               COUNT(DISTINCT DATE(recorded_at)) as unique_days,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest,
               AVG(advances) as avg_advances,
               AVG(declines) as avg_declines,
               AVG(avg_rsi) as avg_rsi
        FROM market_breadth_metrics_gics 
        WHERE market = 'SP500' AND timeframe = '5m'
        AND recorded_at >= DATE_SUB(NOW(), INTERVAL 40 DAY)
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0] > 0:
            total, days, earliest, latest, avg_adv, avg_dec, avg_rsi = result
            print(f"📊 验证结果:")
            print(f"   总记录数: {total}")
            print(f"   覆盖天数: {days}")
            print(f"   时间范围: {earliest} 到 {latest}")
            print(f"   平均涨家数: {avg_adv:.1f}")
            print(f"   平均跌家数: {avg_dec:.1f}")
            print(f"   平均RSI: {avg_rsi:.2f}")
            
            if total > 1000:  # 40天应该有很多5分钟数据点
                print("✅ 数据量正常")
            else:
                print("⚠️  数据量偏少")
        else:
            print("❌ 没有找到计算结果")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print("🎯 SP500最近40天5分钟市场广度计算工具")
    print("下载365天数据，计算40天的每个5分钟时间点")
    print()
    
    success = calculate_sp500_40day_5m()
    
    if success:
        print("\n🎉 SP500 40天5分钟数据计算成功!")
        print("💡 现在数据库中应该有数千条5分钟记录")
        print("💡 每条记录都是基于365天数据计算的真实指标")
    else:
        print("\n❌ 计算失败")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
