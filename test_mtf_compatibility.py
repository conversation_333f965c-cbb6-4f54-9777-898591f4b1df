#!/usr/bin/env python3
"""
测试多时间框架分析器兼容层
验证增强实现与文档期望格式的兼容性
"""

import sys
import os
import numpy as np
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入兼容层
compatibility_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'mtf_compatibility_layer.py')
spec = importlib.util.spec_from_file_location("mtf_compatibility_layer", compatibility_path)
compatibility_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(compatibility_module)

MTFCompatibilityLayer = compatibility_module.MTFCompatibilityLayer
CompatibleUnifiedDecision = compatibility_module.CompatibleUnifiedDecision


class MockTimeframeResult:
    """模拟时间框架结果"""
    def __init__(self, signal_strength=0.5, confidence=0.7):
        self.signal_strength = signal_strength
        self.confidence = confidence


class MockRotationMetrics:
    """模拟轮动指标"""
    def __init__(self, unified_rii=0.5):
        self.unified_rii = unified_rii


class MockBreadthMetrics:
    """模拟广度指标"""
    def __init__(self, internal_health=50, momentum_coherence=0.5, 
                 participation_rate=0.6, price_breadth_divergence=None):
        self.internal_health = internal_health
        self.momentum_coherence = momentum_coherence
        self.participation_rate = participation_rate
        self.price_breadth_divergence = price_breadth_divergence or {'type': 'none'}


def test_compatibility_basic():
    """测试基础兼容性"""
    print("=== 测试基础兼容性 ===\n")
    
    # 创建兼容层
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 创建测试数据
    timeframe_results = {
        'daily': MockTimeframeResult(0.75, 0.85),
        'weekly': MockTimeframeResult(0.68, 0.80)
    }
    
    dynamic_weights = {'daily': 0.7, 'weekly': 0.3}
    
    breadth_metrics = {
        'Technology': MockBreadthMetrics(80, 0.8, 0.75),
        'Healthcare': MockBreadthMetrics(70, 0.7, 0.70),
        'Finance': MockBreadthMetrics(45, 0.4, 0.55),
        'Energy': MockBreadthMetrics(30, 0.3, 0.45, {'type': 'negative', 'severity': 0.3}),
        'Consumer': MockBreadthMetrics(65, 0.6, 0.65)
    }
    
    rotation_metrics = MockRotationMetrics(0.7)
    market_regime = 'normal_market'
    
    print(f"📊 输入数据:")
    print(f"时间框架: {list(timeframe_results.keys())}")
    print(f"板块数: {len(breadth_metrics)}")
    print(f"市场状态: {market_regime}")
    
    # 执行分析
    result = mtf_analyzer.analyze_timeframes(
        timeframe_results=timeframe_results,
        dynamic_weights=dynamic_weights,
        breadth_metrics=breadth_metrics,
        rotation_metrics=rotation_metrics,
        market_regime=market_regime
    )
    
    print(f"\n🎯 兼容格式输出:")
    print(f"建议仓位: {result.suggested_position}%")
    print(f"信号强度: {result.signal_strength:.4f}")
    print(f"置信度: {result.confidence:.3f}")
    print(f"市场状态: {result.market_regime}")
    
    print(f"\n📈 推荐板块 ({len(result.top_sectors)}个):")
    for sector in result.top_sectors:
        print(f"  {sector['sector']}: 权重{sector['weight']}%, 健康度{sector['health']}, 评分{sector['score']}")
    
    print(f"\n🚫 避免板块 ({len(result.avoid_sectors)}个):")
    for sector in result.avoid_sectors:
        print(f"  {sector['sector']}: {sector['risk_level']}风险, 严重度{sector['severity']}")
    
    print(f"\n💡 仓位建议:")
    pos_rec = result.position_recommendation
    print(f"  操作: {pos_rec['action']}")
    print(f"  目标仓位: {pos_rec['target_position']}%")
    print(f"  理由: {pos_rec['reasoning']}")
    
    return True


def test_document_format_compliance():
    """测试文档格式合规性"""
    print("\n=== 测试文档格式合规性 ===\n")
    
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 创建测试数据
    timeframe_results = {
        'daily': MockTimeframeResult(0.8, 0.9),
        'weekly': MockTimeframeResult(0.7, 0.8)
    }
    
    dynamic_weights = {'daily': 0.6, 'weekly': 0.4}
    
    breadth_metrics = {
        'Tech': MockBreadthMetrics(85, 0.85, 0.8),
        'Health': MockBreadthMetrics(75, 0.75, 0.75),
        'Finance': MockBreadthMetrics(40, 0.35, 0.5, {'type': 'negative', 'severity': 0.4})
    }
    
    rotation_metrics = MockRotationMetrics(0.75)
    
    result = mtf_analyzer.analyze_timeframes(
        timeframe_results, dynamic_weights, breadth_metrics, 
        rotation_metrics, 'trending_stable'
    )
    
    # 验证文档期望的字段
    required_fields = [
        'suggested_position', 'top_sectors', 'avoid_sectors', 
        'position_recommendation', 'signal_strength', 'confidence'
    ]
    
    print(f"📋 文档格式合规性检查:")
    
    missing_fields = []
    for field in required_fields:
        if hasattr(result, field):
            value = getattr(result, field)
            print(f"  ✅ {field}: {type(value).__name__}")
        else:
            missing_fields.append(field)
            print(f"  ❌ {field}: 缺失")
    
    # 验证 top_sectors 结构
    if result.top_sectors:
        print(f"\n📈 top_sectors 结构验证:")
        sector_fields = ['sector', 'weight', 'health', 'recommendation']
        for field in sector_fields:
            if field in result.top_sectors[0]:
                print(f"  ✅ {field}: 存在")
            else:
                print(f"  ❌ {field}: 缺失")
    
    # 验证 avoid_sectors 结构
    if result.avoid_sectors:
        print(f"\n🚫 avoid_sectors 结构验证:")
        avoid_fields = ['sector', 'risk_level', 'recommendation']
        for field in avoid_fields:
            if field in result.avoid_sectors[0]:
                print(f"  ✅ {field}: 存在")
            else:
                print(f"  ❌ {field}: 缺失")
    
    # 验证数值范围
    print(f"\n📊 数值范围验证:")
    
    # 建议仓位应该在0-100之间
    if 0 <= result.suggested_position <= 100:
        print(f"  ✅ suggested_position: {result.suggested_position}% (合理范围)")
    else:
        print(f"  ❌ suggested_position: {result.suggested_position}% (超出范围)")
    
    # 信号强度应该在0-1之间
    if 0 <= result.signal_strength <= 1:
        print(f"  ✅ signal_strength: {result.signal_strength:.3f} (合理范围)")
    else:
        print(f"  ❌ signal_strength: {result.signal_strength:.3f} (超出范围)")
    
    # 置信度应该在0-1之间
    if 0 <= result.confidence <= 1:
        print(f"  ✅ confidence: {result.confidence:.3f} (合理范围)")
    else:
        print(f"  ❌ confidence: {result.confidence:.3f} (超出范围)")
    
    # 权重总和应该接近100%
    if result.top_sectors:
        total_weight = sum(s['weight'] for s in result.top_sectors)
        if 95 <= total_weight <= 105:  # 允许5%误差
            print(f"  ✅ 权重总和: {total_weight:.1f}% (合理)")
        else:
            print(f"  ⚠️  权重总和: {total_weight:.1f}% (可能需要调整)")
    
    return len(missing_fields) == 0


def test_legacy_method_compatibility():
    """测试遗留方法兼容性"""
    print("\n=== 测试遗留方法兼容性 ===\n")
    
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 测试原始方法接口
    rotation_metrics = MockRotationMetrics(0.6)
    breadth_metrics = {
        'TestSector': MockBreadthMetrics(60, 0.6, 0.6)
    }
    
    # 测试 calculate_signal_strength 方法
    signal_strength = mtf_analyzer.calculate_signal_strength(
        rotation_metrics, breadth_metrics, 'daily'
    )
    
    print(f"📊 遗留方法测试:")
    print(f"  calculate_signal_strength: {signal_strength:.4f}")
    
    if 0 <= signal_strength <= 1:
        print(f"  ✅ 返回值在合理范围内")
    else:
        print(f"  ❌ 返回值超出范围")
    
    # 测试 generate_unified_decision 方法
    timeframe_results = {
        'daily': MockTimeframeResult(0.7, 0.8)
    }
    dynamic_weights = {'daily': 1.0}
    
    unified_decision = mtf_analyzer.generate_unified_decision(
        timeframe_results, dynamic_weights, 'normal_market'
    )
    
    print(f"\n📋 generate_unified_decision 测试:")
    print(f"  返回类型: {type(unified_decision).__name__}")
    
    # 验证返回的字典包含期望字段
    expected_keys = ['suggested_position', 'top_sectors', 'avoid_sectors']
    for key in expected_keys:
        if key in unified_decision:
            print(f"  ✅ {key}: 存在")
        else:
            print(f"  ❌ {key}: 缺失")
    
    return True


def test_enhanced_features_access():
    """测试增强功能访问"""
    print("\n=== 测试增强功能访问 ===\n")
    
    mtf_analyzer = MTFCompatibilityLayer()
    
    # 创建测试数据
    timeframe_results = {'daily': MockTimeframeResult(0.7, 0.8)}
    dynamic_weights = {'daily': 1.0}
    breadth_metrics = {'TestSector': MockBreadthMetrics(70, 0.7, 0.7)}
    rotation_metrics = MockRotationMetrics(0.7)
    
    result = mtf_analyzer.analyze_timeframes(
        timeframe_results, dynamic_weights, breadth_metrics, 
        rotation_metrics, 'normal_market'
    )
    
    print(f"🚀 增强功能访问测试:")
    
    # 测试增强详情访问
    if result.enhanced_details:
        print(f"  ✅ enhanced_details: 可访问")
        print(f"    决策步骤数: {len(result.enhanced_details.decision_steps)}")
        print(f"    备选场景数: {len(result.enhanced_details.alternative_scenarios)}")
        print(f"    支持证据数: {len(result.enhanced_details.supporting_evidence)}")
    else:
        print(f"  ❌ enhanced_details: 不可访问")
    
    if result.signal_details:
        print(f"  ✅ signal_details: 可访问")
        print(f"    信号等级: {result.signal_details.signal_grade}")
        print(f"    分量数: {len(result.signal_details.component_contributions)}")
    else:
        print(f"  ❌ signal_details: 不可访问")
    
    if result.avoid_details:
        print(f"  ✅ avoid_details: 可访问")
        print(f"    替代机会数: {len(result.avoid_details.alternative_opportunities)}")
    else:
        print(f"  ❌ avoid_details: 不可访问")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试多时间框架分析器兼容层\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试基础兼容性
        test_results.append(("基础兼容性", test_compatibility_basic()))
        
        print("=" * 60)
        
        # 2. 测试文档格式合规性
        test_results.append(("文档格式合规性", test_document_format_compliance()))
        
        print("=" * 60)
        
        # 3. 测试遗留方法兼容性
        test_results.append(("遗留方法兼容性", test_legacy_method_compatibility()))
        
        print("=" * 60)
        
        # 4. 测试增强功能访问
        test_results.append(("增强功能访问", test_enhanced_features_access()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 兼容层测试完成！")
            print("✨ 主要特性:")
            print("  - 完全兼容文档期望格式")
            print("  - 保留所有增强功能")
            print("  - 支持遗留方法调用")
            print("  - 提供增强详情访问")
            print("\n💡 使用建议:")
            print("  - 老板看到的是标准格式输出")
            print("  - 开发者可以访问增强功能")
            print("  - 渐进式迁移到新功能")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
