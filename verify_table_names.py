#!/usr/bin/env python3
"""
验证数据库表名
检查sector_rotation_metrics_gics表是否存在，如果不存在则创建
"""

import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def check_and_create_rotation_table():
    """检查并创建板块轮动指标表"""
    print("🔍 检查板块轮动指标表...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = %s 
        AND table_name = 'sector_rotation_metrics_gics'
        """, (config['database'],))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if table_exists:
            print("✅ sector_rotation_metrics_gics 表已存在")
            
            # 检查表结构
            cursor.execute("DESCRIBE sector_rotation_metrics_gics")
            columns = cursor.fetchall()
            
            print("📊 表结构:")
            for column in columns:
                field, type_, null, key, default, extra = column
                print(f"   {field}: {type_}")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM sector_rotation_metrics_gics")
            count = cursor.fetchone()[0]
            print(f"📈 数据量: {count} 条记录")
            
        else:
            print("⚠️  sector_rotation_metrics_gics 表不存在，正在创建...")
            
            # 创建表
            create_sql = """
            CREATE TABLE sector_rotation_metrics_gics (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                sector VARCHAR(100) NOT NULL,
                rotation_intensity_index DECIMAL(10,6) DEFAULT 0,
                price_dispersion DECIMAL(10,6) DEFAULT 0,
                rank_velocity DECIMAL(10,6) DEFAULT 0,
                volume_concentration DECIMAL(10,6) DEFAULT 0,
                rotation_stage VARCHAR(20) DEFAULT 'unknown',
                risk_level VARCHAR(20) DEFAULT 'medium',
                sector_rank INT DEFAULT 999,
                composite_score DECIMAL(10,6) DEFAULT 0,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_sector (sector),
                INDEX idx_recorded_at (recorded_at),
                INDEX idx_sector_recorded (sector, recorded_at),
                INDEX idx_rank (sector_rank)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='板块轮动指标表'
            """
            
            cursor.execute(create_sql)
            conn.commit()
            print("✅ sector_rotation_metrics_gics 表创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查/创建表失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_market_breadth_table():
    """检查市场广度表"""
    print("\n🔍 检查市场广度表...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = %s 
        AND table_name = 'market_breadth_metrics_gics'
        """, (config['database'],))
        
        table_exists = cursor.fetchone()[0] > 0
        
        if table_exists:
            print("✅ market_breadth_metrics_gics 表已存在")
            
            # 检查数据量和日期范围
            cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(DISTINCT timeframe) as timeframes,
                   COUNT(DISTINCT market) as markets,
                   MIN(recorded_at) as earliest,
                   MAX(recorded_at) as latest
            FROM market_breadth_metrics_gics
            """)
            
            result = cursor.fetchone()
            if result:
                total, timeframes, markets, earliest, latest = result
                print(f"📊 数据统计:")
                print(f"   总记录数: {total}")
                print(f"   时间框架数: {timeframes}")
                print(f"   市场数: {markets}")
                print(f"   时间范围: {earliest} 到 {latest}")
        else:
            print("❌ market_breadth_metrics_gics 表不存在")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查市场广度表失败: {e}")
        return False

def test_table_operations():
    """测试表操作"""
    print("\n🧪 测试表操作...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 测试插入一条记录
        test_sql = """
        INSERT INTO sector_rotation_metrics_gics (
            sector, rotation_intensity_index, price_dispersion, rank_velocity,
            volume_concentration, rotation_stage, risk_level, sector_rank,
            composite_score, recorded_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            rotation_intensity_index = VALUES(rotation_intensity_index)
        """
        
        test_data = (
            'TEST_SECTOR',
            0.123,
            0.456,
            0.789,
            0.321,
            'test_stage',
            'test_risk',
            999,
            0.654,
            datetime.now()
        )
        
        cursor.execute(test_sql, test_data)
        
        # 查询测试记录
        cursor.execute("""
        SELECT * FROM sector_rotation_metrics_gics 
        WHERE sector = 'TEST_SECTOR'
        ORDER BY recorded_at DESC
        LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ 表操作测试成功")
            print(f"   测试记录: {result[1]} - RII: {result[2]}")
            
            # 删除测试记录
            cursor.execute("DELETE FROM sector_rotation_metrics_gics WHERE sector = 'TEST_SECTOR'")
            conn.commit()
            print("✅ 测试记录已清理")
        else:
            print("❌ 表操作测试失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 表操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 数据库表名验证工具")
    print("=" * 50)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查1: 板块轮动表
    rotation_ok = check_and_create_rotation_table()
    
    # 检查2: 市场广度表
    breadth_ok = check_market_breadth_table()
    
    # 检查3: 表操作测试
    operation_ok = test_table_operations() if rotation_ok else False
    
    print("\n" + "=" * 50)
    print("📋 检查结果总结:")
    print(f"   板块轮动表: {'✅ 正常' if rotation_ok else '❌ 异常'}")
    print(f"   市场广度表: {'✅ 正常' if breadth_ok else '❌ 异常'}")
    print(f"   表操作测试: {'✅ 通过' if operation_ok else '❌ 失败'}")
    
    if rotation_ok and breadth_ok and operation_ok:
        print("\n🎉 所有表检查通过!")
        print("\n💡 现在可以安全运行:")
        print("   python backfill_rotation_data.py")
        print("   python run_sector_rotation_calculation.py")
    else:
        print("\n⚠️  部分检查失败")
        if not rotation_ok:
            print("   - 板块轮动表有问题，请检查数据库连接和权限")
        if not breadth_ok:
            print("   - 市场广度表不存在，请先运行市场广度计算")
        if not operation_ok:
            print("   - 表操作失败，请检查数据库权限")
    
    return rotation_ok and breadth_ok and operation_ok

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
