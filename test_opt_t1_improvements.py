#!/usr/bin/env python3
"""
测试 OPT-T1 优化改进
验证改进的动量一致性计算和增强版背离检测
"""

import sys
import os
import numpy as np
from datetime import datetime
from typing import List, Dict

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.improved_momentum_coherence import ImprovedMomentumCoherence, calculate_improved_momentum_coherence_simple
from core.market_breadth_analyzer import MarketBreadthAnalyzer, SectorBreadthData, BreadthMetrics
from core.enhanced_divergence_detector import EnhancedDivergenceDetector, DivergenceType


def test_improved_momentum_coherence():
    """测试改进的动量一致性算法"""
    print("=== 测试改进的动量一致性算法 ===\n")
    
    test_scenarios = {
        '场景1_小幅震荡': [0.005, -0.003, 0.002, -0.004, 0.001, -0.002, 0.003],
        '场景2_剧烈分化': [0.15, -0.12, 0.18, -0.16, -0.05, 0.20, -0.15],
        '场景3_方向一致': [0.02, 0.05, 0.01, 0.08, 0.03, 0.06, 0.04],
        '场景4_高度一致': [0.042, 0.038, 0.045, 0.041, 0.039, 0.043, 0.040],
        '场景5_均值接近0': [0.0001, -0.0002, 0.0003, -0.0001, 0.0002, -0.0003, 0.0001]
    }
    
    calculator = ImprovedMomentumCoherence()
    
    for scenario_name, returns in test_scenarios.items():
        print(f"--- {scenario_name} ---")
        print(f"输入收益率: {[f'{r:.4f}' for r in returns]}")
        
        overall_coherence, details = calculator.calculate_coherence(returns)
        
        print(f"综合一致性: {details.overall_coherence:.3f}")
        print(f"方向一致性: {details.direction_coherence:.3f}")
        print(f"幅度一致性: {details.magnitude_coherence:.3f}")
        print(f"一致性类型: {details.coherence_type}")
        print(f"主导方向: {details.dominant_direction}")
        print(f"解释: {details.interpretation}")
        print(f"建议: {details.action_suggestion}")
        print(f"计算方法: {details.calculation_method['magnitude']}")
        print()


def test_enhanced_divergence_detection():
    """测试增强版背离检测"""
    print("=== 测试增强版背离检测 ===\n")
    
    test_cases = [
        {
            'name': '负背离_价涨内弱',
            'price_change': 0.03,  # 价格上涨3%
            'breadth_metrics': {
                'sector_name': 'Technology',
                'ad_ratio': 0.6,  # 但只有60%股票上涨
                'volume_breadth': -0.2,  # 资金流出
                'nh_nl_ratio': 0.5,  # 新低多于新高
                'ma50_breadth': 0.4,  # 40%在50日均线上
                'ma200_breadth': 0.3,  # 30%在200日均线上
                'avg_rsi': 75,  # RSI超买
                'momentum_coherence': 0.3  # 低一致性
            }
        },
        {
            'name': '正背离_价跌内强',
            'price_change': -0.025,  # 价格下跌2.5%
            'breadth_metrics': {
                'sector_name': 'Healthcare',
                'ad_ratio': 1.8,  # 但涨跌比1.8
                'volume_breadth': 0.3,  # 资金流入
                'nh_nl_ratio': 3.0,  # 新高多于新低
                'ma50_breadth': 0.7,  # 70%在50日均线上
                'ma200_breadth': 0.6,  # 60%在200日均线上
                'avg_rsi': 25,  # RSI超卖
                'momentum_coherence': 0.8  # 高一致性
            }
        },
        {
            'name': '无背离_正常情况',
            'price_change': 0.015,  # 价格上涨1.5%
            'breadth_metrics': {
                'sector_name': 'Energy',
                'ad_ratio': 2.5,  # 涨跌比正常
                'volume_breadth': 0.1,  # 轻微资金流入
                'nh_nl_ratio': 2.0,  # 新高多于新低
                'ma50_breadth': 0.6,  # 60%在50日均线上
                'ma200_breadth': 0.5,  # 50%在200日均线上
                'avg_rsi': 55,  # RSI中性
                'momentum_coherence': 0.6  # 中等一致性
            }
        }
    ]
    
    detector = EnhancedDivergenceDetector()
    
    for case in test_cases:
        print(f"--- {case['name']} ---")
        print(f"价格变化: {case['price_change']:.1%}")
        print(f"板块: {case['breadth_metrics']['sector_name']}")
        
        result = detector.detect_divergence(
            case['price_change'],
            case['breadth_metrics']
        )
        
        print(f"背离类型: {result.divergence_type.value}")
        print(f"基础严重度: {result.base_severity:.4f}")
        print(f"增强严重度: {result.enhanced_severity:.4f}")
        print(f"置信度: {result.confidence:.3f}")
        print(f"风险等级: {result.risk_level}")
        print(f"描述: {result.description}")
        print(f"建议: {result.recommendation}")
        if result.supporting_evidence:
            print(f"支持证据: {len(result.supporting_evidence)}条")
            for evidence in result.supporting_evidence[:2]:  # 只显示前2条
                print(f"  - {evidence}")
        print()


def test_market_breadth_analyzer():
    """测试市场广度分析器"""
    print("=== 测试市场广度分析器 ===\n")
    
    # 创建测试数据
    sector_data = SectorBreadthData(
        sector_name="Technology",
        timestamp=datetime.now(),
        total_stocks=100,
        advances=65,
        declines=30,
        unchanged=5,
        advancing_volume=1000000,
        declining_volume=400000,
        total_volume=1500000,
        new_highs_52w=15,
        new_lows_52w=5,
        above_ma50=70,
        above_ma200=60,
        avg_rsi=62.5,
        individual_returns=[0.02, 0.05, 0.01, 0.08, 0.03, 0.06, 0.04, -0.01, 0.02, 0.03]
    )
    
    analyzer = MarketBreadthAnalyzer()
    
    # 测试基础分析
    print("--- 基础广度分析 ---")
    result = analyzer.analyze_sector_breadth(sector_data)
    
    print(f"板块: {result.sector_name}")
    print(f"涨跌比: {result.ad_ratio:.2f}")
    print(f"纯度: {result.purity:.3f}")
    print(f"成交量广度: {result.volume_breadth:.3f}")
    print(f"新高新低比: {result.nh_nl_ratio:.2f}")
    print(f"MA50广度: {result.ma50_breadth:.3f}")
    print(f"MA200广度: {result.ma200_breadth:.3f}")
    print(f"内部健康度: {result.internal_health:.2f}")
    print(f"参与率: {result.participation_rate:.3f}")
    
    if result.momentum_coherence:
        print(f"动量一致性: {result.momentum_coherence:.3f}")
    
    if result.coherence_details:
        print(f"一致性类型: {result.coherence_details.coherence_type}")
        print(f"解释: {result.coherence_details.interpretation}")
    
    # 测试带价格变化的分析（背离检测）
    print("\n--- 带背离检测的分析 ---")
    result_with_divergence = analyzer.analyze_sector_breadth(sector_data, price_change=0.03)
    
    if result_with_divergence.price_breadth_divergence:
        div = result_with_divergence.price_breadth_divergence
        print(f"检测到背离: {div['type']}")
        print(f"严重度: {div['severity']:.4f}")
        print(f"置信度: {div['confidence']:.3f}")
        print(f"风险等级: {div['risk_level']}")
        print(f"描述: {div['description']}")
    else:
        print("未检测到背离")


def test_data_validation():
    """测试数据验证功能"""
    print("=== 测试数据验证功能 ===\n")
    
    # 创建有问题的测试数据
    invalid_data = SectorBreadthData(
        sector_name="InvalidSector",
        timestamp=datetime.now(),
        total_stocks=5,  # 太少
        advances=60,
        declines=30,
        unchanged=5,  # 总数不匹配
        advancing_volume=1000000,
        declining_volume=400000,
        total_volume=1500000,
        new_highs_52w=15,
        new_lows_52w=5,
        above_ma50=120,  # 超过总数
        above_ma200=60,
        avg_rsi=150,  # 超出范围
        individual_returns=[0.6, -0.7, 0.8]  # 极端值
    )
    
    analyzer = MarketBreadthAnalyzer()
    
    print("--- 数据验证测试 ---")
    is_valid, errors = analyzer._validate_sector_data(invalid_data)
    
    print(f"数据是否有效: {is_valid}")
    if errors:
        print("发现的错误:")
        for error in errors:
            print(f"  - {error}")
    
    # 尝试分析无效数据
    print("\n--- 分析无效数据 ---")
    try:
        result = analyzer.analyze_sector_breadth(invalid_data)
        print("分析完成，但数据质量有问题")
        print(f"内部健康度: {result.internal_health:.2f}")
    except Exception as e:
        print(f"分析失败: {e}")


def main():
    """主测试函数"""
    print("开始测试 OPT-T1 优化改进\n")
    print("=" * 60)
    
    try:
        test_improved_momentum_coherence()
        print("=" * 60)
        
        test_enhanced_divergence_detection()
        print("=" * 60)
        
        test_market_breadth_analyzer()
        print("=" * 60)
        
        test_data_validation()
        print("=" * 60)
        
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
