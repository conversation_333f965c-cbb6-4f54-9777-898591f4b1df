#!/usr/bin/env python3
"""
预计算系统初始化脚本
一次性计算所有历史数据，后续只需增量更新
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from precomputed_indicators import PrecomputedIndicators
from precomputed_query import check_precomputed_data_status

def setup_precomputed_system():
    """设置预计算系统"""
    print("🚀 开始设置预计算系统...")
    
    # 初始化预计算指标系统
    indicator_system = PrecomputedIndicators()
    
    # 检查当前状态
    print("\n📊 检查当前数据状态...")
    status = check_precomputed_data_status()
    
    if status['status'] == 'ready':
        print("✅ 预计算数据已存在且新鲜")
        print(f"   MA数据: {status['freshness']['ma_fresh_records']} 条记录")
        print(f"   52周数据: {status['freshness']['52w_fresh_records']} 条记录")
        
        choice = input("\n是否重新初始化？(y/N): ").lower().strip()
        if choice != 'y':
            print("跳过初始化，系统已就绪")
            return True
    
    print("\n🔧 开始初始化预计算数据...")
    
    # 步骤1: 初始化MA指标
    print("\n📈 步骤1: 初始化MA指标 (MA50/MA200)")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    start_time = time.time()
    ma_success = indicator_system.initial_ma_calculation(days=400)
    ma_time = time.time() - start_time
    
    if ma_success:
        print(f"✅ MA指标初始化完成 (耗时: {ma_time:.1f}秒)")
    else:
        print("❌ MA指标初始化失败")
        return False
    
    # 步骤2: 初始化52周指标
    print("\n📊 步骤2: 初始化52周新高新低指标")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    start_time = time.time()
    w52_success = indicator_system.initial_52w_calculation(days=400)
    w52_time = time.time() - start_time
    
    if w52_success:
        print(f"✅ 52周指标初始化完成 (耗时: {w52_time:.1f}秒)")
    else:
        print("❌ 52周指标初始化失败")
        return False
    
    # 验证结果
    print("\n🔍 验证初始化结果...")
    final_status = check_precomputed_data_status()
    
    if final_status['status'] == 'ready':
        print("🎉 预计算系统初始化成功！")
        print(f"   MA数据覆盖: {final_status['coverage']['ma_indicators']['covered_symbols']} 只股票")
        print(f"   52周数据覆盖: {final_status['coverage']['52w_indicators']['covered_symbols']} 只股票")
        print(f"   总耗时: {(ma_time + w52_time):.1f}秒")
        
        # 显示使用说明
        print("\n📋 使用说明:")
        print("1. 现在可以使用快速的5分钟/15分钟/1小时市场广度计算")
        print("2. 系统会自动优先使用预计算数据")
        print("3. 建议每日运行增量更新:")
        print("   python core/precomputed_indicators.py --update")
        
        return True
    else:
        print("❌ 初始化验证失败")
        return False

def update_precomputed_data():
    """增量更新预计算数据"""
    print("🔄 开始增量更新预计算数据...")
    
    indicator_system = PrecomputedIndicators()
    
    start_time = time.time()
    results = indicator_system.update_latest_indicators()
    update_time = time.time() - start_time
    
    print(f"✅ 增量更新完成 (耗时: {update_time:.1f}秒)")
    print(f"   MA更新: {results['ma_updated']} 只股票")
    print(f"   52周更新: {results['52w_updated']} 只股票")
    print(f"   失败: {results['failed']} 只股票")
    
    return results['failed'] == 0

def test_precomputed_system():
    """测试预计算系统"""
    print("🧪 测试预计算系统...")
    
    # 测试查询功能
    from precomputed_query import get_precomputed_breadth_metrics
    
    test_companies = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
    
    print(f"测试查询 {len(test_companies)} 只股票的指标...")
    
    start_time = time.time()
    metrics = get_precomputed_breadth_metrics(test_companies)
    query_time = time.time() - start_time
    
    print(f"✅ 查询完成 (耗时: {query_time*1000:.1f}毫秒)")
    print(f"   52周新高: {metrics.new_highs_52w}")
    print(f"   52周新低: {metrics.new_lows_52w}")
    print(f"   MA50之上: {metrics.above_ma50}")
    print(f"   MA200之上: {metrics.above_ma200}")
    print(f"   数据日期: {metrics.data_date}")
    
    # 测试市场广度计算
    print("\n🧪 测试集成的市场广度计算...")
    
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
    from market_breadth_calculator import calculate_all_markets_breadth
    
    print("测试1小时时间框架...")
    start_time = time.time()
    results = calculate_all_markets_breadth('1h', save_to_db=False)
    calc_time = time.time() - start_time
    
    if results:
        print(f"✅ 市场广度计算完成 (耗时: {calc_time:.1f}秒)")
        print(f"   计算了 {len(results)} 个市场")
        
        # 显示样本结果
        sample = results[0]
        print(f"   样本结果 ({sample['market']}):")
        print(f"     52周新高: {sample['new_highs_52w']}")
        print(f"     52周新低: {sample['new_lows_52w']}")
        print(f"     MA50之上: {sample['above_ma50']}")
        print(f"     MA200之上: {sample['above_ma200']}")
    else:
        print("❌ 市场广度计算失败")
        return False
    
    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python scripts/setup_precomputed_system.py --init     # 初始化系统")
        print("  python scripts/setup_precomputed_system.py --update   # 增量更新")
        print("  python scripts/setup_precomputed_system.py --test     # 测试系统")
        print("  python scripts/setup_precomputed_system.py --status   # 检查状态")
        return
    
    command = sys.argv[1]
    
    if command == "--init":
        success = setup_precomputed_system()
        if success:
            print("\n🎯 建议接下来:")
            print("1. 运行测试: python scripts/setup_precomputed_system.py --test")
            print("2. 设置定时任务每日更新数据")
        sys.exit(0 if success else 1)
        
    elif command == "--update":
        success = update_precomputed_data()
        sys.exit(0 if success else 1)
        
    elif command == "--test":
        success = test_precomputed_system()
        sys.exit(0 if success else 1)
        
    elif command == "--status":
        status = check_precomputed_data_status()
        print("📊 预计算数据状态:")
        print(f"   整体状态: {status['status']}")
        
        if 'coverage' in status:
            print(f"   MA数据覆盖: {status['coverage']['ma_indicators']['covered_symbols']} 只股票")
            print(f"   MA最新日期: {status['coverage']['ma_indicators']['latest_date']}")
            print(f"   52周数据覆盖: {status['coverage']['52w_indicators']['covered_symbols']} 只股票")
            print(f"   52周最新日期: {status['coverage']['52w_indicators']['latest_date']}")
        
        if 'freshness' in status:
            print(f"   MA数据新鲜: {status['freshness']['ma_data_fresh']}")
            print(f"   52周数据新鲜: {status['freshness']['52w_data_fresh']}")
            print(f"   检查截止日期: {status['freshness']['cutoff_date']}")
        
    else:
        print(f"未知命令: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
