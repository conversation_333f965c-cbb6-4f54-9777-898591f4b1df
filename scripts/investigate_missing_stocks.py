#!/usr/bin/env python3
"""
调查缺失股票的详细情况
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))

import pymysql
from db_settings import get_default_db_config
from utils import download_hist_price

def investigate_missing_stocks():
    """调查缺失股票的详细情况"""
    missing_stocks = ['ANSS', 'HES']
    
    print("🔍 调查缺失股票的详细情况")
    print("=" * 60)
    
    # 1. 检查这些股票在映射表中的情况
    print("\n📊 检查股票在映射表中的情况:")
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    for stock in missing_stocks:
        cursor.execute("""
        SELECT market, company
        FROM index_company_mapping_gics 
        WHERE company = %s
        """, (stock,))
        
        results = cursor.fetchall()
        if results:
            print(f"  {stock}: ✅ 在映射表中")
            for market, company in results:
                print(f"    - 市场: {market}, 代码: {company}")
        else:
            print(f"  {stock}: ❌ 不在映射表中")
    
    conn.close()
    
    # 2. 尝试不同的时间范围获取数据
    print(f"\n📊 尝试不同时间范围获取数据:")
    
    time_ranges = [
        ("最近7天", 7),
        ("最近30天", 30),
        ("最近90天", 90),
        ("最近365天", 365)
    ]
    
    for range_name, days in time_ranges:
        print(f"\n🔍 {range_name}:")
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 尝试1小时数据
        try:
            hourly_data = download_hist_price(
                symbols=missing_stocks,
                interval='1h',
                start=start_date,
                end=end_date,
                columns=['c'],
                threads=2,
                verbose=False
            )
            
            for stock in missing_stocks:
                if stock in hourly_data and hourly_data[stock] is not None and not hourly_data[stock].empty:
                    print(f"  {stock}: ✅ 1小时数据 - {len(hourly_data[stock])} 条记录")
                else:
                    print(f"  {stock}: ❌ 1小时数据 - 无数据")
        except Exception as e:
            print(f"  1小时数据获取失败: {e}")
        
        # 尝试日度数据
        try:
            daily_data = download_hist_price(
                symbols=missing_stocks,
                interval='1d',
                start=start_date,
                end=end_date,
                columns=['c'],
                threads=2,
                verbose=False
            )
            
            for stock in missing_stocks:
                if stock in daily_data and daily_data[stock] is not None and not daily_data[stock].empty:
                    print(f"  {stock}: ✅ 日度数据 - {len(daily_data[stock])} 条记录")
                else:
                    print(f"  {stock}: ❌ 日度数据 - 无数据")
        except Exception as e:
            print(f"  日度数据获取失败: {e}")
    
    # 3. 检查这些股票的基本信息
    print(f"\n📊 股票基本信息分析:")
    
    stock_info = {
        'ANSS': {
            'name': 'ANSYS Inc.',
            'sector': 'Technology',
            'description': '工程仿真软件公司'
        },
        'HES': {
            'name': 'Hess Corporation',
            'sector': 'Energy',
            'description': '石油天然气公司'
        }
    }
    
    for stock, info in stock_info.items():
        print(f"\n  {stock} ({info['name']}):")
        print(f"    行业: {info['sector']}")
        print(f"    描述: {info['description']}")
        
        # 检查是否可能有股票代码变更
        possible_alternatives = []
        if stock == 'ANSS':
            possible_alternatives = ['ANSS', 'ANSYS']
        elif stock == 'HES':
            possible_alternatives = ['HES', 'HESS']
        
        print(f"    可能的代码: {possible_alternatives}")
    
    # 4. 建议解决方案
    print(f"\n💡 建议解决方案:")
    print(f"1. 数据源问题: 这两只股票可能在当前数据源中不可用")
    print(f"2. 股票代码变更: 可能需要检查是否有代码变更")
    print(f"3. 暂停交易: 可能暂时停止交易")
    print(f"4. 数据延迟: 可能数据更新有延迟")
    print(f"5. 权限问题: 可能需要特殊权限才能访问")
    
    print(f"\n🎯 推荐做法:")
    print(f"- 保持当前逻辑: total_stocks = 实际可获取数据的股票数量")
    print(f"- 监控缺失股票: 定期检查这些股票是否恢复")
    print(f"- 文档记录: 在系统文档中记录已知的缺失股票")
    print(f"- 替代方案: 考虑使用其他数据源或手动补充")

def main():
    """主函数"""
    investigate_missing_stocks()

if __name__ == "__main__":
    main()
