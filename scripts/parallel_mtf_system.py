#!/usr/bin/env python3
"""
并行MTF系统 - 基于可靠的MTF系统，增加48进程并行支持
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import pymysql
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
import time

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from utils import download_hist_price
from db_settings import get_default_db_config
from mtf_historical_config import get_mtf_config

class ParallelMTFSystem:
    """并行MTF系统"""
    
    def __init__(self, max_workers: int = 48):
        self.max_workers = max_workers
        self.db_config = get_default_db_config()
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('parallel_mtf')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def calculate_all_stocks_parallel(self, timeframe: str, history_months: int = 18):
        """
        并行计算全部股票的指定时间框架
        
        Args:
            timeframe: 时间框架 (如 '1d')
            history_months: 历史月数
        """
        
        start_time = datetime.now()
        self.logger.info(f"🚀 开始并行计算全市场 {timeframe} 数据")
        self.logger.info(f"   历史月数: {history_months}")
        self.logger.info(f"   并行进程: {self.max_workers}")
        
        # 获取所有股票
        symbols = self._get_all_symbols()
        self.logger.info(f"   股票总数: {len(symbols):,}")
        
        # 分批处理 - 每批48只股票
        batch_size = self.max_workers
        batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
        
        self.logger.info(f"   分为 {len(batches)} 批，每批最多 {batch_size} 只股票")
        
        total_success = 0
        total_failed = 0
        total_records = 0
        
        # 逐批处理
        for batch_idx, symbol_batch in enumerate(batches):
            batch_start = datetime.now()
            
            self.logger.info(f"\n📊 处理第 {batch_idx + 1}/{len(batches)} 批: {len(symbol_batch)} 只股票")
            
            # 并行处理这一批股票
            batch_success, batch_failed, batch_records = self._process_batch_parallel(
                symbol_batch, timeframe, history_months
            )
            
            total_success += batch_success
            total_failed += batch_failed
            total_records += batch_records
            
            batch_time = (datetime.now() - batch_start).total_seconds()
            
            self.logger.info(f"   第 {batch_idx + 1} 批完成: 成功{batch_success}, 失败{batch_failed}, 记录{batch_records:,}, 耗时{batch_time:.1f}秒")
            
            # 每10批显示总进度
            if (batch_idx + 1) % 10 == 0:
                progress = (batch_idx + 1) / len(batches) * 100
                elapsed = (datetime.now() - start_time).total_seconds()
                avg_time_per_batch = elapsed / (batch_idx + 1)
                remaining_batches = len(batches) - (batch_idx + 1)
                eta_seconds = remaining_batches * avg_time_per_batch
                
                self.logger.info(f"   📈 总进度: {progress:.1f}% ({batch_idx + 1}/{len(batches)})")
                self.logger.info(f"   📈 已成功: {total_success:,}, 已失败: {total_failed}")
                self.logger.info(f"   📈 预计剩余: {eta_seconds/60:.1f}分钟")
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        self.logger.info(f"\n🎉 {timeframe} 计算完成!")
        self.logger.info(f"   总耗时: {total_time/60:.1f}分钟")
        self.logger.info(f"   总成功: {total_success:,}")
        self.logger.info(f"   总失败: {total_failed}")
        self.logger.info(f"   总记录: {total_records:,}")
        self.logger.info(f"   平均速度: {total_success/(total_time/60):.0f}股票/分钟")
        
        return {
            'timeframe': timeframe,
            'success_count': total_success,
            'failed_count': total_failed,
            'total_records': total_records,
            'total_time_minutes': total_time / 60
        }
    
    def _process_batch_parallel(self, symbols: List[str], timeframe: str, history_months: int):
        """并行处理一批股票"""
        
        # 准备任务参数
        tasks = []
        for symbol in symbols:
            tasks.append((symbol, timeframe, history_months))
        
        success_count = 0
        failed_count = 0
        total_records = 0
        
        # 使用进程池并行处理
        with ProcessPoolExecutor(max_workers=min(self.max_workers, len(tasks))) as executor:
            # 提交所有任务
            futures = [executor.submit(process_single_stock, task) for task in tasks]
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result['success']:
                        success_count += 1
                        total_records += result['records']
                    else:
                        failed_count += 1
                        self.logger.warning(f"   {result['symbol']} 失败: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"   任务执行失败: {e}")
        
        return success_count, failed_count, total_records
    
    def _get_all_symbols(self) -> List[str]:
        """获取所有股票代码"""
        conn = pymysql.connect(**self.db_config)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics ORDER BY company")
            symbols = [row[0] for row in cursor.fetchall()]
            return symbols
        finally:
            conn.close()


def process_single_stock(task_data):
    """
    处理单个股票的工作函数 - 基于可靠的MTF系统逻辑
    
    Args:
        task_data: (symbol, timeframe, history_months)
    """
    symbol, timeframe, history_months = task_data
    
    try:
        # 导入必要模块
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))
        
        from utils import download_hist_price
        from db_settings import get_default_db_config
        from mtf_historical_config import get_mtf_config
        
        # 计算时间范围 - 使用MTF系统的逻辑
        end_date = datetime.now()
        min_days_needed = 252 * 1.4 + 200  # 确保足够计算52周指标
        actual_days_needed = max(history_months * 30, min_days_needed)
        start_date = end_date - timedelta(days=actual_days_needed)
        
        # 获取时间框架配置
        tf_config = get_mtf_config(timeframe)
        
        # 下载数据
        price_data = download_hist_price(
            symbols=[symbol],
            interval=tf_config['interval'],
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=1,
            verbose=False
        )
        
        if symbol not in price_data or price_data[symbol] is None or price_data[symbol].empty:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'success': False,
                'error': 'No data downloaded'
            }
        
        df = price_data[symbol]
        
        # 重命名列 - 使用MTF系统的逻辑
        df = df.rename(columns={'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'})
        df = df.sort_index()
        
        # 计算技术指标 - 使用MTF系统的逻辑
        df = calculate_technical_indicators_mtf(df, timeframe)
        
        # 保存数据 - 使用MTF系统的逻辑
        success = save_symbol_data_mtf(symbol, timeframe, df)
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': success,
            'records': len(df) if success else 0
        }
        
    except Exception as e:
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'success': False,
            'error': str(e)
        }


def calculate_technical_indicators_mtf(df, timeframe):
    """计算技术指标 - 完全基于MTF系统的逻辑"""
    
    # 基础移动平均
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma10'] = df['close'].rolling(10).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma200'] = df['close'].rolling(200).mean()
    
    # 布尔值指标
    df['above_ma5'] = df['close'] > df['ma5']
    df['above_ma10'] = df['close'] > df['ma10']
    df['above_ma20'] = df['close'] > df['ma20']
    df['above_ma50'] = df['close'] > df['ma50']
    df['above_ma200'] = df['close'] > df['ma200']
    
    # RSI计算
    if len(df) >= 14:
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi_14'] = 100 - (100 / (1 + rs))
    
    # 52周指标 - 使用MTF系统的逻辑
    from mtf_historical_config import get_mtf_config
    tf_config = get_mtf_config(timeframe)
    
    if tf_config.get('supports_52w', False):
        window_52w_map = {
            '5m': 20280,
            '15m': 6760,
            '1h': 1690,
            '1d': 252,
            '1w': 52,
            '1M': 12
        }
        
        window_52w = window_52w_map.get(timeframe, 252)
        
        if len(df) >= 20:
            actual_window = min(len(df), window_52w)
            df['high_52w'] = df['high'].rolling(window=actual_window, min_periods=20).max()
            df['low_52w'] = df['low'].rolling(window=actual_window, min_periods=20).min()
            df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
            df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
            df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])
    
    # 20日指标
    if len(df) >= 20:
        df['high_20d'] = df['high'].rolling(20).max()
        df['low_20d'] = df['low'].rolling(20).min()
        df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
        df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
        df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])
    
    return df


def save_symbol_data_mtf(symbol: str, timeframe: str, df) -> bool:
    """保存股票数据 - 完全基于MTF系统的逻辑，60天存储"""
    
    import pandas as pd
    import numpy as np
    from db_settings import get_default_db_config
    
    # 60天存储配置 - 与MTF系统一致
    storage_config = {
        '5m': 60 * 24 * 12,   # 17280条
        '15m': 60 * 24 * 4,   # 5760条
        '1h': 60 * 24,        # 1440条
        '1d': 60,             # 60条
        '1w': 12,             # 12条
        '1M': 3               # 3条
    }
    
    recent_records = storage_config.get(timeframe, 60)
    df_recent = df.tail(recent_records)
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 准备MA数据
        ma_records = []
        w52_records = []
        
        for date, row in df_recent.iterrows():
            # MA记录
            ma_record = (
                symbol, timeframe, date,
                float(row['close']) if pd.notna(row['close']) else None,
                float(row['high']) if pd.notna(row['high']) else None,
                float(row['low']) if pd.notna(row['low']) else None,
                int(row['volume']) if pd.notna(row['volume']) else None,
                float(row['ma5']) if pd.notna(row['ma5']) else None,
                float(row['ma10']) if pd.notna(row['ma10']) else None,
                float(row['ma20']) if pd.notna(row['ma20']) else None,
                float(row['ma50']) if pd.notna(row['ma50']) else None,
                float(row['ma200']) if pd.notna(row['ma200']) else None,
                bool(row['above_ma5']) if pd.notna(row['above_ma5']) else False,
                bool(row['above_ma10']) if pd.notna(row['above_ma10']) else False,
                bool(row['above_ma20']) if pd.notna(row['above_ma20']) else False,
                bool(row['above_ma50']) if pd.notna(row['above_ma50']) else False,
                bool(row['above_ma200']) if pd.notna(row['above_ma200']) else False,
                float(row['rsi_14']) if pd.notna(row['rsi_14']) else None
            )
            ma_records.append(ma_record)
            
            # 52周记录
            w52_record = (
                symbol, timeframe, date,
                float(row.get('high_52w', 0)) if pd.notna(row.get('high_52w', np.nan)) else None,
                float(row.get('low_52w', 0)) if pd.notna(row.get('low_52w', np.nan)) else None,
                float(row.get('high_20d', 0)) if pd.notna(row.get('high_20d', np.nan)) else None,
                float(row.get('low_20d', 0)) if pd.notna(row.get('low_20d', np.nan)) else None,
                bool(row.get('is_new_high_52w', False)),
                bool(row.get('is_new_low_52w', False)),
                bool(row.get('is_new_high_20d', False)),
                bool(row.get('is_new_low_20d', False)),
                0, 0, 0, 0,  # days_from 字段
                float(row.get('price_position_52w', 0.5)) if pd.notna(row.get('price_position_52w', np.nan)) else 0.5,
                float(row.get('price_position_20d', 0.5)) if pd.notna(row.get('price_position_20d', np.nan)) else 0.5
            )
            w52_records.append(w52_record)
        
        # 批量插入MA数据
        if ma_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_ma_indicators
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            close_price = VALUES(close_price), updated_at = CURRENT_TIMESTAMP
            """, ma_records)
        
        # 批量插入52周数据
        if w52_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_52w_indicators
            (symbol, timeframe, datetime, high_52w, low_52w, high_20d, low_20d,
             is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
             days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
             price_position_52w, price_position_20d)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            high_52w = VALUES(high_52w), updated_at = CURRENT_TIMESTAMP
            """, w52_records)
        
        # 更新状态表
        cursor.execute("""
        INSERT INTO mtf_precomputed_status (symbol, timeframe, last_calculated_datetime, total_records, ma_complete, w52_complete)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        last_calculated_datetime = VALUES(last_calculated_datetime),
        total_records = VALUES(total_records),
        ma_complete = VALUES(ma_complete),
        w52_complete = VALUES(w52_complete)
        """, (symbol, timeframe, df_recent.index[-1], len(df_recent), True, True))
        
        conn.commit()
        return True
        
    except Exception as e:
        conn.rollback()
        return False
    finally:
        conn.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='并行MTF系统')
    parser.add_argument('--timeframe', default='1d', help='时间框架 (默认1d)')
    parser.add_argument('--months', type=int, default=18, help='历史月数 (默认18)')
    parser.add_argument('--workers', type=int, default=48, help='并行进程数 (默认48)')
    
    args = parser.parse_args()
    
    print("🚀 并行MTF系统 - 基于可靠的MTF逻辑")
    print("=" * 60)
    print(f"时间框架: {args.timeframe}")
    print(f"历史月数: {args.months}")
    print(f"并行进程: {args.workers}")
    print()
    
    system = ParallelMTFSystem(max_workers=args.workers)
    
    results = system.calculate_all_stocks_parallel(
        timeframe=args.timeframe,
        history_months=args.months
    )
    
    print(f"\n🎉 计算完成!")
    print(f"时间框架: {results['timeframe']}")
    print(f"成功: {results['success_count']:,}")
    print(f"失败: {results['failed_count']:,}")
    print(f"记录: {results['total_records']:,}")
    print(f"耗时: {results['total_time_minutes']:.1f}分钟")
