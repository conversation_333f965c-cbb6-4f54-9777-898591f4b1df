#!/usr/bin/env python3
"""
计算最近2个月15分钟市场广度数据 - 不使用预计算数据，强制实时计算
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def disable_precomputed_data():
    """禁用预计算数据，强制使用实时计算"""
    # 临时修改环境变量或全局设置
    os.environ['DISABLE_PRECOMPUTED'] = '1'
    
    # 修改market_breadth_calculator中的全局变量
    try:
        import market_breadth_calculator
        # 临时禁用预计算数据
        market_breadth_calculator.PRECOMPUTED_AVAILABLE = False
        print("✅ 已禁用预计算数据，将使用实时计算")
    except Exception as e:
        print(f"⚠️ 禁用预计算数据时出现警告: {e}")

def check_database_connection():
    """检查数据库连接"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        conn.close()
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def clear_recent_15m_data():
    """清除最近的15分钟数据"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 删除最近2个月的15分钟数据
        two_months_ago = datetime.now() - timedelta(days=60)
        
        cursor.execute("""
        DELETE FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m' AND recorded_at >= %s
        """, (two_months_ago,))
        
        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"🗑️ 清除了最近2个月的15分钟数据: {deleted_count} 条记录")
        return True
        
    except Exception as e:
        print(f"❌ 清除数据失败: {e}")
        return False

def calculate_15m_realtime_breadth():
    """计算15分钟实时市场广度数据"""
    print("🚀 开始计算15分钟实时市场广度数据（最近2个月）")
    print("=" * 70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📋 配置:")
    print("   - 时间范围: 最近2个月")
    print("   - 计算方式: 实时计算（不使用预计算数据）")
    print("   - 时间框架: 15分钟")
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 数据库连接失败，退出程序")
        return False
    
    # 禁用预计算数据
    disable_precomputed_data()
    
    # 询问是否清除现有数据
    print(f"\n⚠️ 是否清除现有的15分钟数据？")
    print("   这将删除最近2个月的所有15分钟市场广度数据")
    response = input("确认清除现有数据？(y/N): ").strip().lower()
    
    if response == 'y':
        if not clear_recent_15m_data():
            print("❌ 清除数据失败，退出程序")
            return False
    
    print(f"\n🚀 开始15分钟实时市场广度计算...")
    print(f"   时间框架: 15m")
    print(f"   计算方式: 实时计算")
    print(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    
    try:
        # 导入市场广度计算器
        from market_breadth_calculator import calculate_all_markets_breadth
        
        # 使用64核心服务器的优化配置
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        
        if cpu_count >= 64:
            num_processes = 16  # 实时计算使用较少进程，避免API限制
        elif cpu_count >= 32:
            num_processes = 12
        elif cpu_count >= 16:
            num_processes = 8
        else:
            num_processes = max(4, cpu_count - 2)
        
        print(f"   并行配置: {num_processes}个进程 (CPU核心数: {cpu_count})")
        print(f"   计算模式: 实时计算（禁用预计算数据）")
        
        # 计算15分钟市场广度 - 实时计算
        results = calculate_all_markets_breadth('15m', num_processes=num_processes, save_to_db=True)
        calc_time = time.time() - start_time
        
        if results:
            print(f"✅ 15分钟实时计算完成!")
            print(f"   处理市场数: {len(results)} 个")
            print(f"   计算耗时: {calc_time:.1f} 秒")
            print(f"   平均每市场: {calc_time/len(results):.2f} 秒")
            
            # 显示详细结果
            print(f"\n📊 详细结果:")
            for i, result in enumerate(results):
                market = result.get('market', 'Unknown')
                advances = result.get('advances', 0)
                declines = result.get('declines', 0)
                unchanged = result.get('unchanged', 0)
                total = result.get('total_stocks', 0)
                health = result.get('internal_health', 0)
                rsi = result.get('avg_rsi', 0)
                
                # 新高新低数据
                new_highs = result.get('new_highs_52w', 0)
                new_lows = result.get('new_lows_52w', 0)
                
                # MA数据
                above_ma50 = result.get('above_ma50', 0)
                above_ma200 = result.get('above_ma200', 0)
                
                print(f"     {market}:")
                print(f"       涨跌: {advances}↑ {declines}↓ {unchanged}→ (总计{total})")
                print(f"       技术: RSI={rsi:.1f} 健康度={health:.1f}")
                print(f"       新高新低: {new_highs}新高 {new_lows}新低")
                print(f"       MA: {above_ma50}只在MA50上 {above_ma200}只在MA200上")
                print()
            
            # 验证数据保存
            if verify_realtime_data_saved():
                print(f"✅ 实时数据验证成功 - 15分钟数据已保存到数据库")
                return True
            else:
                print(f"❌ 实时数据验证失败 - 数据可能未正确保存")
                return False
        else:
            print(f"❌ 15分钟实时计算失败 - 没有返回结果")
            return False
            
    except Exception as e:
        calc_time = time.time() - start_time
        print(f"❌ 15分钟实时计算异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_realtime_data_saved():
    """验证实时数据是否成功保存到数据库"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查最新数据
        cursor.execute("""
        SELECT COUNT(*) as count, MAX(recorded_at) as latest, COUNT(DISTINCT market) as markets,
               MIN(recorded_at) as earliest
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        count, latest, markets, earliest = result
        
        # 检查数据质量 - 查看是否有实时计算的特征
        cursor.execute("""
        SELECT market, advances, declines, new_highs_52w, new_lows_52w, above_ma50, above_ma200
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m' 
        ORDER BY recorded_at DESC 
        LIMIT 5
        """)
        
        sample_data = cursor.fetchall()
        conn.close()
        
        if count > 0:
            print(f"   📊 数据库验证结果:")
            print(f"      总记录数: {count}")
            print(f"      市场数: {markets}")
            print(f"      时间范围: {earliest} 到 {latest}")
            
            print(f"   📋 样本数据验证:")
            for market, advances, declines, new_highs, new_lows, above_ma50, above_ma200 in sample_data:
                print(f"      {market}: {advances}↑{declines}↓ 新高{new_highs} 新低{new_lows} MA50上{above_ma50} MA200上{above_ma200}")
            
            return True
        else:
            print(f"   ❌ 数据库验证: 没有找到15分钟数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        return False

def show_final_summary():
    """显示最终总结"""
    print(f"\n🎯 15分钟实时市场广度计算总结")
    print("=" * 70)
    
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取详细统计
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT market) as total_markets,
            MIN(recorded_at) as earliest_time,
            MAX(recorded_at) as latest_time,
            AVG(advances) as avg_advances,
            AVG(declines) as avg_declines,
            AVG(internal_health) as avg_health,
            AVG(avg_rsi) as avg_rsi
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        total_records, total_markets, earliest_time, latest_time, avg_advances, avg_declines, avg_health, avg_rsi = result
        
        conn.close()
        
        print(f"📈 数据统计:")
        print(f"   总记录数: {total_records:,}")
        print(f"   市场数量: {total_markets}")
        print(f"   时间范围: {earliest_time} 到 {latest_time}")
        
        print(f"\n📊 市场平均指标:")
        print(f"   平均上涨股票数: {avg_advances:.1f}")
        print(f"   平均下跌股票数: {avg_declines:.1f}")
        print(f"   平均健康度: {avg_health:.1f}")
        print(f"   平均RSI: {avg_rsi:.1f}")
        
        print(f"\n💡 实时计算特点:")
        print(f"   ✅ 使用真实的股价数据计算涨跌")
        print(f"   ✅ 使用真实的52周新高新低数据")
        print(f"   ✅ 使用真实的MA50/MA200数据")
        print(f"   ✅ 使用真实的RSI技术指标")
        print(f"   ✅ 反映最近2个月的真实市场状况")
        
        print(f"\n🎯 下一步:")
        print(f"   1. Web界面现在显示真实的15分钟市场数据")
        print(f"   2. 可以进行精确的短期市场分析")
        print(f"   3. 建议继续计算其他时间框架的实时数据")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def main():
    """主函数"""
    success = calculate_15m_realtime_breadth()
    
    if success:
        show_final_summary()
        print(f"\n🎉 15分钟实时市场广度计算成功完成!")
        print(f"   现在您拥有基于真实数据的15分钟市场广度指标")
    else:
        print(f"\n❌ 15分钟实时市场广度计算失败")
        print(f"   请检查日志信息和网络连接")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
