#!/usr/bin/env python3
"""
高速并行MTF系统 - 解决pickle问题的独立实现
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import pymysql
import multiprocessing as mp
from multiprocessing import Pool
import time
import traceback

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'market-breadth-task'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

def setup_worker():
    """工作进程初始化函数"""
    import sys
    import os
    
    # 确保路径正确
    current_dir = os.getcwd()
    market_breadth_path = os.path.join(current_dir, 'market-breadth-task')
    config_path = os.path.join(current_dir, 'config')
    
    if market_breadth_path not in sys.path:
        sys.path.insert(0, market_breadth_path)
    if config_path not in sys.path:
        sys.path.insert(0, config_path)


def process_symbol_worker(symbol):
    """
    独立的工作函数 - 处理单个股票
    这个函数在模块级别，可以被pickle序列化
    """
    try:
        # 重新导入所有必要模块
        import sys
        import os
        import pandas as pd
        import numpy as np
        from datetime import datetime, timedelta
        import pymysql
        
        # 确保路径
        current_dir = os.getcwd()
        market_breadth_path = os.path.join(current_dir, 'market-breadth-task')
        config_path = os.path.join(current_dir, 'config')
        
        if market_breadth_path not in sys.path:
            sys.path.insert(0, market_breadth_path)
        if config_path not in sys.path:
            sys.path.insert(0, config_path)
        
        from utils import download_hist_price
        from db_settings import get_default_db_config
        from mtf_historical_config import get_mtf_config
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=600)  # 18个月 + 缓冲
        
        # 获取配置
        tf_config = get_mtf_config('1d')
        
        # 下载数据
        price_data = download_hist_price(
            symbols=[symbol],
            interval=tf_config['interval'],
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=1,
            verbose=False
        )
        
        if symbol not in price_data or price_data[symbol] is None or price_data[symbol].empty:
            return {'symbol': symbol, 'success': False, 'error': 'No data', 'records': 0}
        
        df = price_data[symbol]
        
        # 处理列名
        if 'h' in df.columns:
            df = df.rename(columns={'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'})
        
        df = df.sort_index()
        total_calculated = len(df)
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 保存数据
        success, records_saved = save_to_database(symbol, '1d', df)
        
        return {
            'symbol': symbol,
            'success': success,
            'records': records_saved,
            'total_calculated': total_calculated,
            'error': None
        }
        
    except Exception as e:
        import traceback
        error_detail = f"{str(e)}\n{traceback.format_exc()}"
        return {
            'symbol': symbol,
            'success': False,
            'error': error_detail,
            'records': 0,
            'total_calculated': 0
        }


def calculate_indicators(df):
    """计算技术指标"""
    
    # 基础移动平均
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma10'] = df['close'].rolling(10).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma200'] = df['close'].rolling(200).mean()
    
    # 布尔值指标
    df['above_ma5'] = df['close'] > df['ma5']
    df['above_ma10'] = df['close'] > df['ma10']
    df['above_ma20'] = df['close'] > df['ma20']
    df['above_ma50'] = df['close'] > df['ma50']
    df['above_ma200'] = df['close'] > df['ma200']
    
    # RSI计算
    if len(df) >= 14:
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi_14'] = 100 - (100 / (1 + rs))
    
    # 52周指标
    window_52w = 252
    if len(df) >= 20:
        actual_window = min(len(df), window_52w)
        df['high_52w'] = df['high'].rolling(window=actual_window, min_periods=20).max()
        df['low_52w'] = df['low'].rolling(window=actual_window, min_periods=20).min()
        df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
        df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
        df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])
    
    # 20日指标
    if len(df) >= 20:
        df['high_20d'] = df['high'].rolling(20).max()
        df['low_20d'] = df['low'].rolling(20).min()
        df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
        df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
        df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])
    
    return df


def save_to_database(symbol, timeframe, df):
    """保存到数据库"""
    
    try:
        from db_settings import get_default_db_config
        import pymysql
        import pandas as pd
        import numpy as np
        
        # 60天存储
        recent_records = 60
        df_recent = df.tail(recent_records)
        
        conn = pymysql.connect(**get_default_db_config())
        cursor = conn.cursor()
        
        # 删除旧数据
        cursor.execute("DELETE FROM mtf_precomputed_ma_indicators WHERE symbol = %s AND timeframe = %s", (symbol, timeframe))
        cursor.execute("DELETE FROM mtf_precomputed_52w_indicators WHERE symbol = %s AND timeframe = %s", (symbol, timeframe))
        
        # 准备数据
        ma_records = []
        w52_records = []
        
        for date, row in df_recent.iterrows():
            # MA记录
            ma_record = (
                symbol, timeframe, date,
                float(row['close']) if pd.notna(row['close']) else None,
                float(row['high']) if pd.notna(row['high']) else None,
                float(row['low']) if pd.notna(row['low']) else None,
                int(row['volume']) if pd.notna(row['volume']) else None,
                float(row['ma5']) if pd.notna(row['ma5']) else None,
                float(row['ma10']) if pd.notna(row['ma10']) else None,
                float(row['ma20']) if pd.notna(row['ma20']) else None,
                float(row['ma50']) if pd.notna(row['ma50']) else None,
                float(row['ma200']) if pd.notna(row['ma200']) else None,
                bool(row['above_ma5']) if pd.notna(row['above_ma5']) else False,
                bool(row['above_ma10']) if pd.notna(row['above_ma10']) else False,
                bool(row['above_ma20']) if pd.notna(row['above_ma20']) else False,
                bool(row['above_ma50']) if pd.notna(row['above_ma50']) else False,
                bool(row['above_ma200']) if pd.notna(row['above_ma200']) else False,
                float(row['rsi_14']) if pd.notna(row['rsi_14']) else None
            )
            ma_records.append(ma_record)
            
            # 52周记录
            w52_record = (
                symbol, timeframe, date,
                float(row.get('high_52w', 0)) if pd.notna(row.get('high_52w', np.nan)) else None,
                float(row.get('low_52w', 0)) if pd.notna(row.get('low_52w', np.nan)) else None,
                float(row.get('high_20d', 0)) if pd.notna(row.get('high_20d', np.nan)) else None,
                float(row.get('low_20d', 0)) if pd.notna(row.get('low_20d', np.nan)) else None,
                bool(row.get('is_new_high_52w', False)),
                bool(row.get('is_new_low_52w', False)),
                bool(row.get('is_new_high_20d', False)),
                bool(row.get('is_new_low_20d', False)),
                0, 0, 0, 0,  # days_from 字段
                float(row.get('price_position_52w', 0.5)) if pd.notna(row.get('price_position_52w', np.nan)) else 0.5,
                float(row.get('price_position_20d', 0.5)) if pd.notna(row.get('price_position_20d', np.nan)) else 0.5
            )
            w52_records.append(w52_record)
        
        # 批量插入
        if ma_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_ma_indicators
            (symbol, timeframe, datetime, close_price, high_price, low_price, volume,
             ma5, ma10, ma20, ma50, ma200, above_ma5, above_ma10, above_ma20, above_ma50, above_ma200, rsi_14)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, ma_records)
        
        if w52_records:
            cursor.executemany("""
            INSERT INTO mtf_precomputed_52w_indicators
            (symbol, timeframe, datetime, high_52w, low_52w, high_20d, low_20d,
             is_new_high_52w, is_new_low_52w, is_new_high_20d, is_new_low_20d,
             days_from_high_52w, days_from_low_52w, days_from_high_20d, days_from_low_20d,
             price_position_52w, price_position_20d)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, w52_records)
        
        # 更新状态表
        cursor.execute("""
        INSERT INTO mtf_precomputed_status (symbol, timeframe, last_calculated_datetime, total_records, ma_complete, w52_complete)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        last_calculated_datetime = VALUES(last_calculated_datetime),
        total_records = VALUES(total_records),
        ma_complete = VALUES(ma_complete),
        w52_complete = VALUES(w52_complete)
        """, (symbol, timeframe, df_recent.index[-1], len(df_recent), True, True))
        
        conn.commit()
        conn.close()
        
        return True, len(df_recent)
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False, 0


def get_all_symbols():
    """获取所有股票代码"""
    from db_settings import get_default_db_config
    import pymysql
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT DISTINCT company FROM index_company_mapping_gics ORDER BY company")
        symbols = [row[0] for row in cursor.fetchall()]
        return symbols
    finally:
        conn.close()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='高速并行MTF系统')
    parser.add_argument('--workers', type=int, default=48, help='并行进程数')
    parser.add_argument('--test', action='store_true', help='测试模式，只处理10只股票')
    
    args = parser.parse_args()
    
    print("🚀 高速并行MTF系统")
    print("=" * 50)
    print(f"并行进程数: {args.workers}")
    
    # 获取股票列表
    if args.test:
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'F', 'GM', 'BWA', 'ALV', 'AXL']
        print(f"测试模式: {len(symbols)}只股票")
    else:
        symbols = get_all_symbols()
        print(f"全市场模式: {len(symbols)}只股票")
    
    print(f"\n开始处理...")
    start_time = time.time()
    
    # 使用多进程池
    with Pool(processes=args.workers, initializer=setup_worker) as pool:
        results = pool.map(process_symbol_worker, symbols)
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    failed_count = len(results) - success_count
    total_records = sum(r['records'] for r in results if r['success'])
    
    elapsed = time.time() - start_time
    
    print(f"\n🎉 处理完成!")
    print(f"总耗时: {elapsed/60:.1f}分钟")
    print(f"成功: {success_count:,}")
    print(f"失败: {failed_count:,}")
    print(f"成功率: {success_count/len(symbols)*100:.1f}%")
    print(f"总记录: {total_records:,}")
    print(f"处理速度: {len(symbols)/(elapsed/60):.0f}股票/分钟")
    
    # 显示失败的股票
    if failed_count > 0:
        print(f"\n❌ 失败的股票:")
        for r in results:
            if not r['success']:
                error_msg = r.get('error', 'Unknown error')
                if error_msg:
                    # 只显示错误的第一行，避免太长
                    error_line = error_msg.split('\n')[0]
                    print(f"  {r['symbol']}: {error_line}")
                else:
                    print(f"  {r['symbol']}: Unknown error")


if __name__ == "__main__":
    main()
