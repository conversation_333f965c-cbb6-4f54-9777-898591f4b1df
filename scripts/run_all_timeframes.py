#!/usr/bin/env python3
"""
批量运行所有时间框架的市场广度计算
5分钟、15分钟、1天、1周、1月
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from market_breadth_calculator import calculate_all_markets_breadth
import pymysql
from db_settings import get_default_db_config

def check_database_connection():
    """检查数据库连接"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        conn.close()
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def run_timeframe_calculation(timeframe: str, description: str):
    """运行单个时间框架的计算"""
    print(f"\n🚀 运行{description}市场广度计算...")
    print(f"   时间框架: {timeframe}")
    print(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    start_time = time.time()

    try:
        # 特殊处理月度数据 - 使用聚合器
        if timeframe == '1M':
            from monthly_aggregator import generate_monthly_breadth_data
            success = generate_monthly_breadth_data(lookback_days=30)
            calc_time = time.time() - start_time

            if success:
                print(f"✅ {description}聚合完成!")
                print(f"   数据来源: 日线数据聚合")
                print(f"   聚合耗时: {calc_time:.1f} 秒")
                print(f"   聚合周期: 30天")

                # 验证聚合结果
                try:
                    config = get_default_db_config()
                    conn = pymysql.connect(**config)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM market_breadth_metrics_gics WHERE timeframe = '1M'")
                    count = cursor.fetchone()[0]
                    conn.close()

                    print(f"   聚合市场数: {count} 个")
                    return True, count, calc_time
                except:
                    return True, 0, calc_time
            else:
                print(f"❌ {description}聚合失败")
                return False, 0, calc_time
        else:
            # 其他时间框架使用正常计算
            results = calculate_all_markets_breadth(timeframe, save_to_db=True)
            calc_time = time.time() - start_time

            if results:
                print(f"✅ {description}计算完成!")
                print(f"   处理市场数: {len(results)} 个")
                print(f"   计算耗时: {calc_time:.1f} 秒")
                print(f"   平均每市场: {calc_time/len(results):.2f} 秒")

                # 显示样本结果
                print(f"   样本结果:")
                for i, result in enumerate(results[:3]):
                    market = result.get('market', 'Unknown')
                    advances = result.get('advances', 0)
                    declines = result.get('declines', 0)
                    total = result.get('total_stocks', 0)
                    health = result.get('internal_health', 0)
                    rsi = result.get('avg_rsi', 0)
                    print(f"     {market}: {advances}↑ {declines}↓ (总计{total}) RSI={rsi:.1f} 健康度={health:.1f}")

                return True, len(results), calc_time
            else:
                print(f"❌ {description}计算失败 - 没有返回结果")
                return False, 0, calc_time

    except Exception as e:
        calc_time = time.time() - start_time
        print(f"❌ {description}计算异常: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, calc_time

def verify_data_saved(timeframe: str):
    """验证数据是否成功保存到数据库"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查最新数据
        cursor.execute("""
        SELECT COUNT(*) as count, MAX(recorded_at) as latest
        FROM market_breadth_metrics_gics 
        WHERE timeframe = %s
        """, (timeframe,))
        
        result = cursor.fetchone()
        count, latest = result
        
        conn.close()
        
        if count > 0:
            print(f"   ✅ 数据库验证: {count} 条记录, 最新时间: {latest}")
            return True
        else:
            print(f"   ❌ 数据库验证: 没有找到数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 批量运行多时间框架市场广度计算")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 数据库连接失败，退出程序")
        return
    
    # 定义要运行的时间框架
    timeframes = [
        ('5m', '5分钟'),
        ('15m', '15分钟'),
        ('1h', '1小时'),
        ('1d', '1天'),
        ('1w', '1周'),
        ('1M', '1月')
    ]
    
    # 运行结果统计
    results_summary = []
    total_start_time = time.time()
    
    # 逐个运行时间框架
    for timeframe, description in timeframes:
        success, market_count, calc_time = run_timeframe_calculation(timeframe, description)
        
        # 验证数据保存
        if success:
            data_saved = verify_data_saved(timeframe)
        else:
            data_saved = False
        
        results_summary.append({
            'timeframe': timeframe,
            'description': description,
            'success': success,
            'market_count': market_count,
            'calc_time': calc_time,
            'data_saved': data_saved
        })
        
        # 短暂休息，避免过载
        if timeframe != timeframes[-1][0]:  # 不是最后一个
            print(f"   ⏸️ 休息5秒...")
            time.sleep(5)
    
    # 总结报告
    total_time = time.time() - total_start_time
    
    print(f"\n🎯 批量计算总结")
    print("=" * 80)
    print(f"总耗时: {total_time:.1f} 秒 ({total_time/60:.1f} 分钟)")
    
    successful_count = 0
    total_markets = 0
    
    print(f"\n📊 各时间框架结果:")
    for result in results_summary:
        status = "✅" if result['success'] else "❌"
        data_status = "✅" if result['data_saved'] else "❌"
        
        print(f"   {status} {result['description']} ({result['timeframe']}):")
        print(f"      计算: {'成功' if result['success'] else '失败'}")
        print(f"      市场数: {result['market_count']}")
        print(f"      耗时: {result['calc_time']:.1f}秒")
        print(f"      数据保存: {'成功' if result['data_saved'] else '失败'} {data_status}")
        
        if result['success']:
            successful_count += 1
            total_markets += result['market_count']
    
    print(f"\n📈 总体统计:")
    print(f"   成功时间框架: {successful_count}/{len(timeframes)}")
    print(f"   总处理市场数: {total_markets}")
    print(f"   成功率: {successful_count/len(timeframes):.1%}")
    
    if successful_count == len(timeframes):
        print(f"\n🎉 所有时间框架计算成功!")
        print(f"   现在数据库包含以下时间框架的数据:")
        for result in results_summary:
            if result['success']:
                print(f"     - {result['timeframe']} ({result['description']})")
        
        print(f"\n💡 下一步:")
        print(f"   1. 可以使用多时间框架分析功能")
        print(f"   2. Web界面将支持真正的多时间框架选择")
        print(f"   3. 运行 python web_interface.py 查看效果")
        
    else:
        print(f"\n⚠️ 部分时间框架计算失败")
        failed_timeframes = [r['description'] for r in results_summary if not r['success']]
        print(f"   失败的时间框架: {', '.join(failed_timeframes)}")
        print(f"   建议检查日志和网络连接")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
