#!/usr/bin/env python3
"""
高性能多时间框架计算启动脚本
针对64核心64G服务器优化
"""

import sys
import os
import argparse
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from high_performance_mtf_system import HighPerformanceMTFSystem
from high_performance_mtf_config import (
    HIGH_PERFORMANCE_PLANS,
    get_system_resource_status,
    estimate_computation_time
)

def main():
    parser = argparse.ArgumentParser(
        description='高性能多时间框架预计算系统 - 64核心64G优化版',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 极速计算 (3个月历史，全部时间框架)
  python scripts/run_high_performance_mtf.py --plan ultra_fast

  # 全功率计算 (12个月历史，全部时间框架)  
  python scripts/run_high_performance_mtf.py --plan full_power

  # 最大性能计算 (24个月历史，全部时间框架)
  python scripts/run_high_performance_mtf.py --plan maximum_performance

  # 查看系统状态
  python scripts/run_high_performance_mtf.py --status

  # 查看可用方案
  python scripts/run_high_performance_mtf.py --list-plans

  # 自定义股票计算
  python scripts/run_high_performance_mtf.py --plan ultra_fast --symbols AAPL MSFT GOOGL

  # 强制重新计算
  python scripts/run_high_performance_mtf.py --plan ultra_fast --force
        """
    )
    
    parser.add_argument('--plan', 
                       choices=['ultra_fast', 'full_power', 'maximum_performance'],
                       default='ultra_fast',
                       help='计算方案 (默认: ultra_fast)')
    
    parser.add_argument('--symbols', nargs='+', 
                       help='指定股票代码 (默认: 全部股票)')
    
    parser.add_argument('--force', action='store_true',
                       help='强制重新计算 (忽略已有数据)')
    
    parser.add_argument('--status', action='store_true',
                       help='查看计算状态')
    
    parser.add_argument('--list-plans', action='store_true',
                       help='列出所有可用方案')
    
    parser.add_argument('--estimate', action='store_true',
                       help='仅估算计算时间，不执行')
    
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不实际计算')
    
    args = parser.parse_args()
    
    # 显示系统信息
    print("🚀 高性能多时间框架预计算系统")
    print("=" * 60)
    
    # 获取系统资源状态
    system_status = get_system_resource_status()
    print(f"💻 系统资源:")
    print(f"   CPU: {system_status['cpu_count']}核心 (使用率: {system_status['cpu_usage_percent']:.1f}%)")
    print(f"   内存: {system_status['memory_total_gb']:.1f}GB (可用: {system_status['memory_available_gb']:.1f}GB)")
    print(f"   推荐最大进程数: {system_status['recommended_max_processes']}")
    print()
    
    # 列出可用方案
    if args.list_plans:
        print("📋 可用计算方案:")
        for plan_name, plan_config in HIGH_PERFORMANCE_PLANS.items():
            print(f"\n🎯 {plan_name}:")
            print(f"   名称: {plan_config['name']}")
            print(f"   描述: {plan_config['description']}")
            print(f"   时间框架: {', '.join(plan_config['timeframes'])}")
            print(f"   历史月数: {plan_config['history_months']}个月")
            print(f"   预计耗时: {plan_config['estimated_time_minutes']}分钟")
            print(f"   内存使用: {plan_config['memory_usage_gb']}GB")
            print(f"   最大进程: {plan_config['max_processes']}")
        return
    
    # 初始化系统
    try:
        system = HighPerformanceMTFSystem()
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return 1
    
    # 查看状态
    if args.status:
        print("📊 计算状态:")
        try:
            status = system.get_calculation_status()
            
            print(f"\n📈 数据统计:")
            print(f"   MA指标记录: {status['total_ma_records']:,}")
            print(f"   52周指标记录: {status['total_52w_records']:,}")
            
            if status['timeframe_status']:
                print(f"\n⏰ 各时间框架状态:")
                for tf_status in status['timeframe_status']:
                    print(f"   {tf_status['timeframe']:>4}: {tf_status['symbols']:>4}只股票, "
                          f"{tf_status['records']:>8,}条记录, 最新: {tf_status['latest_date']}")
            else:
                print(f"\n⚠️  尚未开始计算")
                
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
        return
    
    # 估算计算时间
    if args.estimate or args.dry_run:
        print("⏱️  计算时间估算:")
        
        # 获取股票总数
        try:
            if args.symbols:
                total_symbols = len(args.symbols)
                print(f"   指定股票数: {total_symbols}")
            else:
                symbols = system._get_all_symbols()
                total_symbols = len(symbols)
                print(f"   全部股票数: {total_symbols:,}")
            
            # 估算时间
            time_estimate = estimate_computation_time(args.plan, total_symbols)
            
            print(f"\n📊 方案: {time_estimate['plan_name']}")
            print(f"   时间框架: {', '.join(time_estimate['timeframes'])}")
            print(f"   历史月数: {time_estimate['history_months']}个月")
            print(f"   预计耗时: {time_estimate['estimated_minutes']}分钟 ({time_estimate['estimated_hours']:.1f}小时)")
            print(f"   最大进程: {time_estimate['max_processes']}")
            print(f"   内存使用: {time_estimate['memory_usage_gb']}GB")
            
            if args.dry_run:
                print(f"\n🔍 试运行模式 - 不执行实际计算")
                return
                
        except Exception as e:
            print(f"❌ 估算失败: {e}")
            return 1
    
    # 执行计算
    if not args.estimate:
        plan_config = HIGH_PERFORMANCE_PLANS[args.plan]
        
        print(f"🎯 执行计算方案: {plan_config['name']}")
        print(f"   时间框架: {', '.join(plan_config['timeframes'])}")
        print(f"   历史月数: {plan_config['history_months']}个月")
        print(f"   强制重算: {'是' if args.force else '否'}")
        
        if args.symbols:
            print(f"   指定股票: {len(args.symbols)}只 ({', '.join(args.symbols[:5])}{'...' if len(args.symbols) > 5 else ''})")
        else:
            print(f"   计算范围: 全部股票")
        
        # 确认执行
        if not args.force:
            confirm = input(f"\n⚠️  确认开始计算? (y/N): ")
            if confirm.lower() != 'y':
                print("❌ 取消计算")
                return
        
        print(f"\n🚀 开始计算... ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")
        
        try:
            results = system.calculate_all_timeframes_ultra_fast(
                plan_name=args.plan,
                symbols=args.symbols,
                force_recalculate=args.force
            )
            
            print(f"\n🎉 计算完成! ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")
            print(f"   方案: {results['plan_name']}")
            print(f"   总耗时: {results['total_time_minutes']:.1f}分钟")
            print(f"   股票数: {results['total_symbols']:,}")
            print(f"   成功: {results['total_success']:,}")
            print(f"   失败: {results['total_failed']:,}")
            print(f"   记录: {results['total_records']:,}")
            
            print(f"\n📊 各时间框架结果:")
            for tf, tf_result in results['timeframe_results'].items():
                if 'error' in tf_result:
                    print(f"   {tf:>4}: ❌ {tf_result['error']}")
                else:
                    print(f"   {tf:>4}: ✅ 成功{tf_result['success_count']:,}, "
                          f"记录{tf_result['total_records']:,}")
            
            # 性能统计
            if results['total_time_minutes'] > 0:
                records_per_minute = results['total_records'] / results['total_time_minutes']
                symbols_per_minute = results['total_success'] / results['total_time_minutes']
                print(f"\n⚡ 性能统计:")
                print(f"   处理速度: {records_per_minute:,.0f}条记录/分钟")
                print(f"   股票速度: {symbols_per_minute:,.0f}只股票/分钟")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  用户中断计算")
            return 1
        except Exception as e:
            print(f"\n❌ 计算失败: {e}")
            import traceback
            traceback.print_exc()
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code if exit_code else 0)
