#!/usr/bin/env python3
"""
准确的5分钟数据内存估算
基于实际交易时间计算
"""

import pandas as pd
import numpy as np
import psutil

def calculate_5min_data_memory():
    """计算5分钟数据的准确内存需求"""
    print("📊 5分钟数据内存使用准确估算")
    print("=" * 50)
    
    # 交易时间分析
    trading_hours_per_day = 6.5  # 9:30-16:00
    minutes_per_day = trading_hours_per_day * 60  # 390分钟
    bars_per_day = minutes_per_day / 5  # 78条5分钟K线
    trading_days_per_year = 252
    
    # 不同时间跨度的数据量
    periods = {
        '7天': 7 * bars_per_day,
        '30天': 30 * bars_per_day, 
        '90天': 90 * bars_per_day,
        '252天(1年)': 252 * bars_per_day,
        '430天': 430 * bars_per_day * (252/365)  # 考虑非交易日
    }
    
    print(f"美股交易时间: {trading_hours_per_day} 小时/天")
    print(f"5分钟K线数量: {bars_per_day} 条/天")
    print(f"年交易日数: {trading_days_per_year} 天")
    
    print(f"\n不同时间跨度的数据量:")
    for period, count in periods.items():
        print(f"  {period}: {count:.0f} 条数据")
    
    # 每条数据的字段和大小
    fields = {
        'timestamp': 8,     # datetime64[ns] = 8 bytes
        'open': 8,          # float64 = 8 bytes  
        'high': 8,          # float64 = 8 bytes
        'low': 8,           # float64 = 8 bytes
        'close': 8,         # float64 = 8 bytes
        'volume': 8,        # float64 = 8 bytes
    }
    
    bytes_per_row = sum(fields.values())
    pandas_overhead = bytes_per_row * 0.3  # pandas开销
    total_bytes_per_row = bytes_per_row + pandas_overhead
    
    print(f"\n数据结构分析:")
    print(f"  基础字段: {len(fields)} 个")
    print(f"  每行字节数: {bytes_per_row} bytes")
    print(f"  pandas开销: {pandas_overhead:.1f} bytes/row")
    print(f"  总计每行: {total_bytes_per_row:.1f} bytes")
    
    # 计算不同时间跨度的内存需求
    print(f"\n单只股票内存需求:")
    print(f"{'时间跨度':<15} {'数据条数':<10} {'内存(KB)':<12} {'内存(MB)':<12}")
    print("-" * 55)
    
    memory_results = {}
    for period, count in periods.items():
        memory_bytes = count * total_bytes_per_row
        memory_kb = memory_bytes / 1024
        memory_mb = memory_kb / 1024
        
        print(f"{period:<15} {count:<10.0f} {memory_kb:<12.1f} {memory_mb:<12.3f}")
        memory_results[period] = memory_bytes
    
    return memory_results

def calculate_ma_indicators_memory():
    """计算MA指标的内存需求"""
    print(f"\n📈 MA指标内存需求")
    print("=" * 50)
    
    # MA指标需要的额外字段
    ma_fields = {
        'ma50': 8,          # float64 = 8 bytes
        'ma200': 8,         # float64 = 8 bytes  
        'above_ma50': 1,    # bool = 1 byte
        'above_ma200': 1,   # bool = 1 byte
    }
    
    ma_bytes_per_row = sum(ma_fields.values())
    
    # 对于5分钟数据，MA50需要50*78=3900条5分钟数据(约50天)
    # MA200需要200*78=15600条5分钟数据(约200天)
    
    bars_per_day = 78
    ma50_days = 50
    ma200_days = 200
    
    ma50_data_points = ma50_days * bars_per_day
    ma200_data_points = ma200_days * bars_per_day
    
    print(f"MA50计算需要: {ma50_days} 天 × {bars_per_day} 条/天 = {ma50_data_points} 条数据")
    print(f"MA200计算需要: {ma200_days} 天 × {bars_per_day} 条/天 = {ma200_data_points} 条数据")
    
    # 计算MA指标的额外内存
    ma_memory_per_row = ma_bytes_per_row * 1.3  # 包含pandas开销
    
    print(f"\nMA指标额外内存:")
    print(f"  每行额外字节: {ma_memory_per_row:.1f} bytes")
    
    # 对于不同数据量的MA内存需求
    data_counts = [ma50_data_points, ma200_data_points]
    labels = ['MA50数据集', 'MA200数据集']
    
    for label, count in zip(labels, data_counts):
        memory_bytes = count * ma_memory_per_row
        memory_mb = memory_bytes / 1024 / 1024
        print(f"  {label}: {memory_mb:.2f} MB")
    
    return ma_memory_per_row

def calculate_52w_indicators_memory():
    """计算52周指标的内存需求"""
    print(f"\n📊 52周指标内存需求")
    print("=" * 50)
    
    # 52周指标需要的额外字段
    w52_fields = {
        'high_52w': 8,      # float64 = 8 bytes
        'low_52w': 8,       # float64 = 8 bytes
        'is_new_high': 1,   # bool = 1 byte
        'is_new_low': 1,    # bool = 1 byte
    }
    
    w52_bytes_per_row = sum(w52_fields.values())
    
    # 52周 = 252个交易日
    bars_per_day = 78
    trading_days_52w = 252
    w52_data_points = trading_days_52w * bars_per_day
    
    print(f"52周计算需要: {trading_days_52w} 天 × {bars_per_day} 条/天 = {w52_data_points} 条数据")
    
    # 计算52周指标的额外内存
    w52_memory_per_row = w52_bytes_per_row * 1.3  # 包含pandas开销
    memory_bytes = w52_data_points * w52_memory_per_row
    memory_mb = memory_bytes / 1024 / 1024
    
    print(f"\n52周指标内存:")
    print(f"  每行额外字节: {w52_memory_per_row:.1f} bytes")
    print(f"  52周数据集: {memory_mb:.2f} MB")
    
    return w52_memory_per_row

def calculate_total_memory_per_stock():
    """计算单只股票的总内存需求"""
    print(f"\n🎯 单只股票总内存需求")
    print("=" * 50)
    
    # 基础5分钟数据(1年)
    bars_per_day = 78
    trading_days = 252
    total_bars = trading_days * bars_per_day  # 19,656条
    
    # 基础数据字段
    base_bytes_per_row = (8*6 + 8*0.3)  # OHLCV + pandas开销
    
    # MA指标字段
    ma_bytes_per_row = (8*2 + 1*2) * 1.3  # MA50,MA200,above_ma50,above_ma200 + 开销
    
    # 52周指标字段
    w52_bytes_per_row = (8*2 + 1*2) * 1.3  # high_52w,low_52w,is_new_high,is_new_low + 开销
    
    total_bytes_per_row = base_bytes_per_row + ma_bytes_per_row + w52_bytes_per_row
    
    total_memory_bytes = total_bars * total_bytes_per_row
    total_memory_kb = total_memory_bytes / 1024
    total_memory_mb = total_memory_kb / 1024
    
    print(f"数据条数: {total_bars:,} 条 (252天 × 78条/天)")
    print(f"基础数据: {base_bytes_per_row:.1f} bytes/行")
    print(f"MA指标: {ma_bytes_per_row:.1f} bytes/行") 
    print(f"52周指标: {w52_bytes_per_row:.1f} bytes/行")
    print(f"总计: {total_bytes_per_row:.1f} bytes/行")
    print(f"\n单只股票总内存: {total_memory_bytes:,.0f} bytes = {total_memory_kb:.1f} KB = {total_memory_mb:.2f} MB")
    
    return total_memory_mb

def calculate_batch_processing_memory():
    """计算批量处理的内存需求"""
    print(f"\n⚡ 批量处理内存需求")
    print("=" * 50)
    
    memory_per_stock_mb = calculate_total_memory_per_stock()
    
    batch_sizes = [10, 50, 100, 200, 500, 1000]
    
    print(f"\n批量处理内存估算:")
    print(f"{'批量大小':<10} {'基础内存(MB)':<15} {'处理开销(MB)':<15} {'总内存(MB)':<15} {'总内存(GB)':<15}")
    print("-" * 80)
    
    for batch_size in batch_sizes:
        base_memory_mb = batch_size * memory_per_stock_mb
        processing_overhead_mb = base_memory_mb * 0.5  # 50%处理开销
        total_memory_mb = base_memory_mb + processing_overhead_mb
        total_memory_gb = total_memory_mb / 1024
        
        print(f"{batch_size:<10} {base_memory_mb:<15.1f} {processing_overhead_mb:<15.1f} {total_memory_mb:<15.1f} {total_memory_gb:<15.3f}")

def calculate_full_system_requirements():
    """计算完整系统需求"""
    print(f"\n🚀 完整系统需求 (6500只股票)")
    print("=" * 50)
    
    total_stocks = 6500
    memory_per_stock_mb = 2.0  # 从上面计算得出约2MB/股票
    
    # 不同批量大小的处理时间和内存
    batch_configs = [
        {'size': 50, 'memory_gb': 0.15, 'time_min': 65},
        {'size': 100, 'memory_gb': 0.3, 'time_min': 33},
        {'size': 200, 'memory_gb': 0.6, 'time_min': 17},
        {'size': 500, 'memory_gb': 1.5, 'time_min': 7},
        {'size': 1000, 'memory_gb': 3.0, 'time_min': 4}
    ]
    
    print(f"总股票数: {total_stocks:,}")
    print(f"单股票内存: {memory_per_stock_mb} MB")
    
    print(f"\n批量处理方案:")
    print(f"{'批量大小':<10} {'批次数':<10} {'内存需求(GB)':<15} {'预计时间(分钟)':<15} {'建议'}")
    print("-" * 70)
    
    for config in batch_configs:
        batch_count = (total_stocks + config['size'] - 1) // config['size']
        
        if config['memory_gb'] < 1:
            suggestion = "✅ 推荐"
        elif config['memory_gb'] < 4:
            suggestion = "✅ 可行"
        else:
            suggestion = "⚠️ 需大内存"
        
        print(f"{config['size']:<10} {batch_count:<10} {config['memory_gb']:<15.1f} {config['time_min']:<15} {suggestion}")

def main():
    """主函数"""
    print("🔍 5分钟数据准确内存估算")
    print("=" * 60)
    
    # 1. 基础5分钟数据内存
    base_memory = calculate_5min_data_memory()
    
    # 2. MA指标内存
    ma_memory = calculate_ma_indicators_memory()
    
    # 3. 52周指标内存  
    w52_memory = calculate_52w_indicators_memory()
    
    # 4. 单只股票总内存
    total_per_stock = calculate_total_memory_per_stock()
    
    # 5. 批量处理内存
    calculate_batch_processing_memory()
    
    # 6. 完整系统需求
    calculate_full_system_requirements()
    
    # 7. 当前系统检查
    memory = psutil.virtual_memory()
    available_gb = memory.available / 1024 / 1024 / 1024
    
    print(f"\n💻 当前系统状态")
    print("=" * 50)
    print(f"可用内存: {available_gb:.1f} GB")
    
    if available_gb > 4:
        print("✅ 内存充足，可以使用大批量处理")
        print("推荐批量大小: 1000")
    elif available_gb > 1:
        print("✅ 内存适中，建议中等批量处理")
        print("推荐批量大小: 200-500")
    else:
        print("⚠️ 内存较少，建议小批量处理")
        print("推荐批量大小: 50-100")
    
    print(f"\n📋 关键结论")
    print("=" * 50)
    print(f"1. 单只股票5分钟数据(1年): 约2MB内存")
    print(f"2. 5分钟数据量远小于日数据: 19,656条 vs 365条")
    print(f"3. 批量处理1000只股票: 约3GB内存")
    print(f"4. 完整6500只股票: 约4分钟完成(1000批量)")

if __name__ == "__main__":
    main()
