#!/usr/bin/env python3
"""
清理MTF数据库数据的脚本
"""

import sys
import os
import pymysql

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from db_settings import get_default_db_config

def check_database_status():
    """检查数据库当前状态"""
    print("🔍 检查数据库当前状态...")
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 检查各表的记录数
        tables = [
            'mtf_precomputed_ma_indicators',
            'mtf_precomputed_52w_indicators', 
            'mtf_precomputed_status'
        ]
        
        total_records = 0
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count:,} 条记录")
                total_records += count
            except Exception as e:
                print(f"   {table}: 表不存在或查询失败 ({e})")
        
        print(f"   总记录数: {total_records:,}")
        
        # 检查有多少只股票
        try:
            cursor.execute("SELECT COUNT(DISTINCT symbol) FROM mtf_precomputed_ma_indicators")
            symbol_count = cursor.fetchone()[0]
            print(f"   已处理股票数: {symbol_count:,} 只")
        except:
            print(f"   已处理股票数: 0 只")
        
        return total_records
        
    finally:
        conn.close()

def clear_mtf_tables():
    """清理MTF相关表"""
    print("\n🗑️ 开始清理MTF数据表...")
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 清理表的顺序很重要，避免外键约束问题
        tables_to_clear = [
            'mtf_precomputed_ma_indicators',
            'mtf_precomputed_52w_indicators',
            'mtf_precomputed_status'
        ]
        
        for table in tables_to_clear:
            try:
                print(f"   清理 {table}...")
                cursor.execute(f"DELETE FROM {table}")
                affected_rows = cursor.rowcount
                print(f"   ✅ 删除了 {affected_rows:,} 条记录")
            except Exception as e:
                print(f"   ❌ 清理 {table} 失败: {e}")
        
        # 重置自增ID
        print("\n🔄 重置自增ID...")
        for table in tables_to_clear:
            try:
                cursor.execute(f"ALTER TABLE {table} AUTO_INCREMENT = 1")
                print(f"   ✅ {table} 自增ID已重置")
            except Exception as e:
                print(f"   ⚠️ {table} 自增ID重置失败: {e}")
        
        conn.commit()
        print("\n✅ 数据库清理完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库清理失败: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def verify_cleanup():
    """验证清理结果"""
    print("\n🔍 验证清理结果...")
    
    total_records = check_database_status()
    
    if total_records == 0:
        print("✅ 数据库已完全清理")
        return True
    else:
        print(f"⚠️ 仍有 {total_records:,} 条记录未清理")
        return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='MTF数据库清理工具')
    parser.add_argument('--force', action='store_true', help='强制清理，不需要确认')
    args = parser.parse_args()

    print("🗑️ MTF数据库清理工具")
    print("=" * 60)

    # 检查当前状态
    initial_records = check_database_status()

    if initial_records == 0:
        print("\n💡 数据库已经是空的，无需清理")
        return

    # 确认清理
    if args.force:
        print(f"\n🚀 强制模式: 自动清理 {initial_records:,} 条记录")
        response = 'YES'
    else:
        print(f"\n⚠️ 警告: 即将删除 {initial_records:,} 条记录")
        print("这个操作不可逆转！")
        response = input("\n确认清理数据库？(输入 'YES' 确认): ").strip()

    if response != 'YES':
        print("❌ 用户取消操作")
        return
    
    # 执行清理
    success = clear_mtf_tables()
    
    if success:
        # 验证清理结果
        verify_cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 数据库清理完成！")
        print("现在可以开始全市场计算了:")
        print()
        print("# 推荐命令:")
        print("source .venv/bin/activate && python scripts/ultra_fast_mtf_setup.py --timeframes 1d --months 18 --auto-workers")
        print()
        print("# 或者使用原始脚本:")
        print("source .venv/bin/activate && python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 18 --workers 24")
    else:
        print("\n❌ 清理失败，请检查错误信息")

if __name__ == "__main__":
    main()
