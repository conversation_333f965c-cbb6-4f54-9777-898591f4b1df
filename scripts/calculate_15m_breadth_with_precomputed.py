#!/usr/bin/env python3
"""
使用预计算MTF数据计算最近2个月15分钟市场广度
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def check_precomputed_data():
    """检查预计算MTF数据的可用性"""
    print("🔍 检查预计算MTF数据...")
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 检查MA指标数据
        cursor.execute("""
        SELECT COUNT(*) as count, COUNT(DISTINCT symbol) as symbols,
               MIN(datetime) as earliest, MAX(datetime) as latest
        FROM mtf_precomputed_ma_indicators 
        WHERE timeframe = '1d'
        """)
        
        ma_result = cursor.fetchone()
        ma_count, ma_symbols, ma_earliest, ma_latest = ma_result
        
        # 检查52周指标数据
        cursor.execute("""
        SELECT COUNT(*) as count, COUNT(DISTINCT symbol) as symbols,
               MIN(datetime) as earliest, MAX(datetime) as latest
        FROM mtf_precomputed_52w_indicators 
        WHERE timeframe = '1d'
        """)
        
        w52_result = cursor.fetchone()
        w52_count, w52_symbols, w52_earliest, w52_latest = w52_result
        
        conn.close()
        
        print(f"   📊 MA指标数据:")
        print(f"      记录数: {ma_count:,}")
        print(f"      股票数: {ma_symbols:,}")
        print(f"      时间范围: {ma_earliest} 到 {ma_latest}")
        
        print(f"   📊 52周指标数据:")
        print(f"      记录数: {w52_count:,}")
        print(f"      股票数: {w52_symbols:,}")
        print(f"      时间范围: {w52_earliest} 到 {w52_latest}")
        
        # 检查数据是否足够
        two_months_ago = datetime.now() - timedelta(days=60)

        if ma_count > 0 and w52_count > 0:
            if ma_latest:
                # 处理日期类型转换
                if isinstance(ma_latest, datetime):
                    latest_date = ma_latest.date()
                else:
                    latest_date = ma_latest

                if latest_date >= two_months_ago.date():
                    print(f"   ✅ 预计算数据可用，可以进行市场广度计算")
                    return True
                else:
                    print(f"   ⚠️ 预计算数据过旧，最新数据: {ma_latest}")
                    return False
            else:
                print(f"   ❌ 没有找到预计算数据")
                return False
        else:
            print(f"   ❌ 预计算数据不足")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查预计算数据失败: {e}")
        return False

def calculate_15m_breadth_with_precomputed():
    """使用预计算数据计算15分钟市场广度"""
    print("🚀 使用预计算MTF数据计算15分钟市场广度")
    print("=" * 70)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📋 配置:")
    print("   - 数据源: 预计算MTF指标数据")
    print("   - 时间框架: 15分钟")
    print("   - 计算范围: 最近2个月")
    
    # 检查预计算数据
    if not check_precomputed_data():
        print("❌ 预计算数据不可用，请先运行MTF预计算")
        return False
    
    print(f"\n🚀 开始15分钟市场广度计算（使用预计算数据）...")
    start_time = time.time()
    
    try:
        # 强制启用预计算数据
        os.environ['USE_PRECOMPUTED'] = '1'
        
        # 导入市场广度计算器
        from market_breadth_calculator import calculate_all_markets_breadth
        
        # 使用较少的进程，因为使用预计算数据速度很快
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        
        if cpu_count >= 64:
            num_processes = 32  # 预计算数据可以使用更多进程
        elif cpu_count >= 32:
            num_processes = 24
        elif cpu_count >= 16:
            num_processes = 16
        else:
            num_processes = max(8, cpu_count - 2)
        
        print(f"   并行配置: {num_processes}个进程 (CPU核心数: {cpu_count})")
        print(f"   数据源: 预计算MTF指标数据")
        
        # 计算15分钟市场广度 - 使用预计算数据
        results = calculate_all_markets_breadth('15m', num_processes=num_processes, save_to_db=True)
        calc_time = time.time() - start_time
        
        if results:
            print(f"✅ 15分钟市场广度计算完成（使用预计算数据）!")
            print(f"   处理市场数: {len(results)} 个")
            print(f"   计算耗时: {calc_time:.1f} 秒")
            print(f"   平均每市场: {calc_time/len(results):.2f} 秒")
            
            # 显示详细结果
            print(f"\n📊 详细结果（基于预计算MTF数据）:")
            for i, result in enumerate(results):
                market = result.get('market', 'Unknown')
                advances = result.get('advances', 0)
                declines = result.get('declines', 0)
                unchanged = result.get('unchanged', 0)
                total = result.get('total_stocks', 0)
                health = result.get('internal_health', 0)
                rsi = result.get('avg_rsi', 0)
                
                # 新高新低数据（来自预计算）
                new_highs = result.get('new_highs_52w', 0)
                new_lows = result.get('new_lows_52w', 0)
                
                # MA数据（来自预计算）
                above_ma50 = result.get('above_ma50', 0)
                above_ma200 = result.get('above_ma200', 0)
                
                print(f"     {market}:")
                print(f"       涨跌: {advances}↑ {declines}↓ {unchanged}→ (总计{total})")
                print(f"       技术: RSI={rsi:.1f} 健康度={health:.1f}")
                print(f"       新高新低: {new_highs}新高 {new_lows}新低 (预计算)")
                print(f"       MA: {above_ma50}只在MA50上 {above_ma200}只在MA200上 (预计算)")
                print()
            
            # 验证数据保存
            if verify_precomputed_data_saved():
                print(f"✅ 预计算数据验证成功 - 15分钟数据已保存到数据库")
                return True
            else:
                print(f"❌ 预计算数据验证失败 - 数据可能未正确保存")
                return False
        else:
            print(f"❌ 15分钟计算失败 - 没有返回结果")
            return False
            
    except Exception as e:
        calc_time = time.time() - start_time
        print(f"❌ 15分钟计算异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_precomputed_data_saved():
    """验证基于预计算数据的结果是否保存成功"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查最新数据
        cursor.execute("""
        SELECT COUNT(*) as count, MAX(recorded_at) as latest, COUNT(DISTINCT market) as markets,
               MIN(recorded_at) as earliest
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        count, latest, markets, earliest = result
        
        # 检查数据质量 - 查看预计算数据的特征
        cursor.execute("""
        SELECT market, advances, declines, new_highs_52w, new_lows_52w, above_ma50, above_ma200
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m' 
        ORDER BY recorded_at DESC 
        LIMIT 5
        """)
        
        sample_data = cursor.fetchall()
        conn.close()
        
        if count > 0:
            print(f"   📊 数据库验证结果:")
            print(f"      总记录数: {count}")
            print(f"      市场数: {markets}")
            print(f"      时间范围: {earliest} 到 {latest}")
            
            print(f"   📋 样本数据验证（预计算指标）:")
            for market, advances, declines, new_highs, new_lows, above_ma50, above_ma200 in sample_data:
                print(f"      {market}: {advances}↑{declines}↓ 新高{new_highs} 新低{new_lows} MA50上{above_ma50} MA200上{above_ma200}")
            
            return True
        else:
            print(f"   ❌ 数据库验证: 没有找到15分钟数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        return False

def show_final_summary():
    """显示最终总结"""
    print(f"\n🎯 15分钟市场广度计算总结（基于预计算MTF数据）")
    print("=" * 70)
    
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取详细统计
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT market) as total_markets,
            MIN(recorded_at) as earliest_time,
            MAX(recorded_at) as latest_time,
            AVG(advances) as avg_advances,
            AVG(declines) as avg_declines,
            AVG(internal_health) as avg_health,
            AVG(avg_rsi) as avg_rsi,
            SUM(new_highs_52w) as total_new_highs,
            SUM(new_lows_52w) as total_new_lows,
            SUM(above_ma50) as total_above_ma50,
            SUM(above_ma200) as total_above_ma200
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        total_records, total_markets, earliest_time, latest_time, avg_advances, avg_declines, avg_health, avg_rsi, total_new_highs, total_new_lows, total_above_ma50, total_above_ma200 = result
        
        conn.close()
        
        print(f"📈 数据统计:")
        print(f"   总记录数: {total_records:,}")
        print(f"   市场数量: {total_markets}")
        print(f"   时间范围: {earliest_time} 到 {latest_time}")
        
        print(f"\n📊 市场平均指标:")
        print(f"   平均上涨股票数: {avg_advances:.1f}")
        print(f"   平均下跌股票数: {avg_declines:.1f}")
        print(f"   平均健康度: {avg_health:.1f}")
        print(f"   平均RSI: {avg_rsi:.1f}")
        
        print(f"\n📈 预计算技术指标统计:")
        print(f"   总新高股票数: {total_new_highs}")
        print(f"   总新低股票数: {total_new_lows}")
        print(f"   总MA50上方股票数: {total_above_ma50}")
        print(f"   总MA200上方股票数: {total_above_ma200}")
        
        print(f"\n💡 预计算数据优势:")
        print(f"   ✅ 使用统一标准的技术指标")
        print(f"   ✅ 数据一致性高")
        print(f"   ✅ 计算速度快")
        print(f"   ✅ 基于已验证的MTF系统")
        
        print(f"\n🎯 下一步:")
        print(f"   1. Web界面现在显示基于预计算MTF数据的15分钟市场广度")
        print(f"   2. 可以进行基于统一技术指标的市场分析")
        print(f"   3. 建议继续计算其他时间框架（5m, 1h等）")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def main():
    """主函数"""
    success = calculate_15m_breadth_with_precomputed()
    
    if success:
        show_final_summary()
        print(f"\n🎉 15分钟市场广度计算成功完成（基于预计算MTF数据）!")
        print(f"   现在您拥有基于预计算MTF指标的15分钟市场广度数据")
    else:
        print(f"\n❌ 15分钟市场广度计算失败")
        print(f"   请检查预计算MTF数据是否可用")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
