#!/usr/bin/env python3
"""
预计算系统监控脚本
提供系统状态监控、告警和自动恢复功能
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

class PrecomputedMonitor:
    """预计算系统监控器"""
    
    def __init__(self):
        self.project_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.log_file = os.path.join(self.project_path, 'logs', 'monitor.log')
        self.status_file = os.path.join(self.project_path, 'logs', 'monitor_status.json')
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
    
    def log(self, message: str, level: str = 'INFO'):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        print(log_entry)
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except Exception as e:
            print(f"写入日志失败: {e}")
    
    def check_system_status(self) -> Dict:
        """检查系统状态"""
        try:
            from precomputed_query import check_precomputed_data_status
            
            status = check_precomputed_data_status()
            
            # 添加额外检查
            current_time = datetime.now()
            
            # 检查数据新鲜度（更严格）
            data_fresh = True
            freshness_hours = 0
            
            if 'freshness' in status:
                if not status['freshness'].get('ma_data_fresh', False):
                    data_fresh = False
                if not status['freshness'].get('52w_data_fresh', False):
                    data_fresh = False
            
            # 计算数据延迟时间
            if 'coverage' in status:
                ma_latest = status['coverage']['ma_indicators'].get('latest_date')
                if ma_latest:
                    try:
                        latest_date = datetime.strptime(ma_latest, '%Y-%m-%d')
                        freshness_hours = (current_time - latest_date).total_seconds() / 3600
                    except:
                        freshness_hours = 999
            
            return {
                'timestamp': current_time.isoformat(),
                'overall_status': status.get('status', 'unknown'),
                'data_fresh': data_fresh,
                'freshness_hours': freshness_hours,
                'ma_coverage': status.get('coverage', {}).get('ma_indicators', {}).get('covered_symbols', 0),
                '52w_coverage': status.get('coverage', {}).get('52w_indicators', {}).get('covered_symbols', 0),
                'raw_status': status
            }
            
        except Exception as e:
            self.log(f"检查系统状态失败: {e}", 'ERROR')
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'error',
                'error': str(e),
                'data_fresh': False,
                'freshness_hours': 999
            }
    
    def save_status(self, status: Dict):
        """保存状态到文件"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log(f"保存状态失败: {e}", 'ERROR')
    
    def load_last_status(self) -> Optional[Dict]:
        """加载上次状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log(f"加载上次状态失败: {e}", 'WARNING')
        return None
    
    def analyze_status_change(self, current: Dict, previous: Optional[Dict]) -> List[str]:
        """分析状态变化"""
        alerts = []
        
        if not previous:
            return alerts
        
        # 检查整体状态变化
        if current['overall_status'] != previous.get('overall_status'):
            if current['overall_status'] == 'ready':
                alerts.append("✅ 系统状态恢复正常")
            else:
                alerts.append(f"❌ 系统状态异常: {current['overall_status']}")
        
        # 检查数据新鲜度变化
        if current['data_fresh'] != previous.get('data_fresh'):
            if current['data_fresh']:
                alerts.append("✅ 数据新鲜度恢复正常")
            else:
                alerts.append("⚠️ 数据不新鲜")
        
        # 检查覆盖率变化
        current_ma = current.get('ma_coverage', 0)
        previous_ma = previous.get('ma_coverage', 0)
        if abs(current_ma - previous_ma) > 100:  # 变化超过100只股票
            alerts.append(f"📊 MA数据覆盖率变化: {previous_ma} → {current_ma}")
        
        return alerts
    
    def should_trigger_recovery(self, status: Dict) -> bool:
        """判断是否需要触发自动恢复"""
        # 系统状态不正常
        if status['overall_status'] != 'ready':
            return True
        
        # 数据不新鲜且超过6小时
        if not status['data_fresh'] and status['freshness_hours'] > 6:
            return True
        
        # 覆盖率过低
        if status.get('ma_coverage', 0) < 1000:  # 少于1000只股票
            return True
        
        return False
    
    def attempt_recovery(self) -> bool:
        """尝试自动恢复"""
        self.log("开始自动恢复", 'INFO')
        
        try:
            import subprocess
            
            # 尝试增量更新
            self.log("执行增量更新", 'INFO')
            result = subprocess.run([
                sys.executable, 
                os.path.join(self.project_path, 'scripts', 'setup_precomputed_system.py'),
                '--update'
            ], capture_output=True, text=True, timeout=300)  # 5分钟超时
            
            if result.returncode == 0:
                self.log("增量更新成功", 'INFO')
                
                # 等待一下再检查状态
                time.sleep(10)
                
                # 检查恢复效果
                new_status = self.check_system_status()
                if new_status['overall_status'] == 'ready':
                    self.log("自动恢复成功", 'INFO')
                    return True
                else:
                    self.log("增量更新后状态仍异常，需要完整重建", 'WARNING')
                    return False
            else:
                self.log(f"增量更新失败: {result.stderr}", 'ERROR')
                return False
                
        except subprocess.TimeoutExpired:
            self.log("增量更新超时", 'ERROR')
            return False
        except Exception as e:
            self.log(f"自动恢复失败: {e}", 'ERROR')
            return False
    
    def send_notification(self, message: str, level: str = 'INFO'):
        """发送通知（可扩展为邮件、微信等）"""
        # 这里可以集成邮件、Slack、微信等通知方式
        self.log(f"[通知] {message}", level)
        
        # 示例：写入通知文件
        notification_file = os.path.join(self.project_path, 'logs', 'notifications.log')
        try:
            with open(notification_file, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now()}] [{level}] {message}\n")
        except:
            pass
    
    def run_check(self, auto_recovery: bool = True) -> Dict:
        """执行一次完整检查"""
        self.log("开始系统检查", 'INFO')
        
        # 检查当前状态
        current_status = self.check_system_status()
        
        # 加载上次状态
        previous_status = self.load_last_status()
        
        # 分析状态变化
        alerts = self.analyze_status_change(current_status, previous_status)
        
        # 记录状态变化
        for alert in alerts:
            self.log(alert, 'INFO')
            self.send_notification(alert)
        
        # 判断是否需要恢复
        recovery_attempted = False
        recovery_success = False
        
        if auto_recovery and self.should_trigger_recovery(current_status):
            self.log("检测到系统异常，尝试自动恢复", 'WARNING')
            self.send_notification("检测到系统异常，开始自动恢复", 'WARNING')
            
            recovery_attempted = True
            recovery_success = self.attempt_recovery()
            
            if recovery_success:
                self.send_notification("自动恢复成功", 'INFO')
                # 重新检查状态
                current_status = self.check_system_status()
            else:
                self.send_notification("自动恢复失败，需要人工干预", 'ERROR')
        
        # 保存当前状态
        current_status['recovery_attempted'] = recovery_attempted
        current_status['recovery_success'] = recovery_success
        self.save_status(current_status)
        
        # 生成检查报告
        report = {
            'check_time': current_status['timestamp'],
            'system_status': current_status['overall_status'],
            'data_fresh': current_status['data_fresh'],
            'freshness_hours': current_status['freshness_hours'],
            'coverage': {
                'ma_indicators': current_status.get('ma_coverage', 0),
                '52w_indicators': current_status.get('52w_coverage', 0)
            },
            'alerts': alerts,
            'recovery_attempted': recovery_attempted,
            'recovery_success': recovery_success,
            'recommendations': self.generate_recommendations(current_status)
        }
        
        self.log(f"检查完成: {current_status['overall_status']}", 'INFO')
        return report
    
    def generate_recommendations(self, status: Dict) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if status['overall_status'] != 'ready':
            recommendations.append("系统状态异常，建议检查数据库连接和网络状态")
        
        if not status['data_fresh']:
            if status['freshness_hours'] > 24:
                recommendations.append("数据超过24小时未更新，建议立即执行完整重建")
            else:
                recommendations.append("数据不新鲜，建议执行增量更新")
        
        if status.get('ma_coverage', 0) < 2000:
            recommendations.append("MA数据覆盖率偏低，建议检查股票列表和数据源")
        
        if status.get('52w_coverage', 0) < 2000:
            recommendations.append("52周数据覆盖率偏低，建议检查历史数据完整性")
        
        return recommendations

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='预计算系统监控工具')
    parser.add_argument('--no-recovery', action='store_true', help='禁用自动恢复')
    parser.add_argument('--continuous', action='store_true', help='持续监控模式')
    parser.add_argument('--interval', type=int, default=300, help='持续监控间隔（秒）')
    parser.add_argument('--json', action='store_true', help='输出JSON格式')
    
    args = parser.parse_args()
    
    monitor = PrecomputedMonitor()
    
    if args.continuous:
        print(f"🔄 启动持续监控模式，间隔 {args.interval} 秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                report = monitor.run_check(auto_recovery=not args.no_recovery)
                
                if args.json:
                    print(json.dumps(report, indent=2, ensure_ascii=False))
                else:
                    print(f"\n📊 监控报告 - {report['check_time']}")
                    print(f"   系统状态: {report['system_status']}")
                    print(f"   数据新鲜: {report['data_fresh']}")
                    print(f"   数据延迟: {report['freshness_hours']:.1f} 小时")
                    print(f"   MA覆盖: {report['coverage']['ma_indicators']} 只股票")
                    print(f"   52周覆盖: {report['coverage']['52w_indicators']} 只股票")
                    
                    if report['alerts']:
                        print("   告警:")
                        for alert in report['alerts']:
                            print(f"     {alert}")
                    
                    if report['recommendations']:
                        print("   建议:")
                        for rec in report['recommendations']:
                            print(f"     • {rec}")
                
                time.sleep(args.interval)
                
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
    
    else:
        # 单次检查
        report = monitor.run_check(auto_recovery=not args.no_recovery)
        
        if args.json:
            print(json.dumps(report, indent=2, ensure_ascii=False))
        else:
            print("📊 预计算系统监控报告")
            print("=" * 40)
            print(f"检查时间: {report['check_time']}")
            print(f"系统状态: {report['system_status']}")
            print(f"数据新鲜: {'✅' if report['data_fresh'] else '❌'}")
            print(f"数据延迟: {report['freshness_hours']:.1f} 小时")
            print(f"MA数据覆盖: {report['coverage']['ma_indicators']} 只股票")
            print(f"52周数据覆盖: {report['coverage']['52w_indicators']} 只股票")
            
            if report['alerts']:
                print("\n🚨 状态变化:")
                for alert in report['alerts']:
                    print(f"  {alert}")
            
            if report['recovery_attempted']:
                status = "成功" if report['recovery_success'] else "失败"
                print(f"\n🔧 自动恢复: {status}")
            
            if report['recommendations']:
                print("\n💡 建议:")
                for rec in report['recommendations']:
                    print(f"  • {rec}")

if __name__ == "__main__":
    main()
