#!/usr/bin/env python3
"""
调试MTF Worker问题的脚本
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

def test_single_symbol_worker():
    """测试单个股票的worker函数"""
    
    # 测试参数
    symbol = 'AAPL'
    timeframe = '1d'
    start_date = datetime.now() - timedelta(days=18*30)
    end_date = datetime.now()
    force_recalculate = False
    
    task_data = (symbol, timeframe, start_date, end_date, force_recalculate)
    
    print(f"🧪 测试单个股票worker: {symbol}")
    print(f"   时间框架: {timeframe}")
    print(f"   开始日期: {start_date.strftime('%Y-%m-%d')}")
    print(f"   结束日期: {end_date.strftime('%Y-%m-%d')}")
    print()
    
    try:
        # 导入worker函数
        from mtf_precomputed_system import process_single_symbol_mtf_worker
        
        # 执行worker
        result = process_single_symbol_mtf_worker(task_data)
        
        print("✅ Worker执行结果:")
        print(f"   股票: {result['symbol']}")
        print(f"   成功: {result['success']}")
        print(f"   记录数: {result['records']}")
        print(f"   总计算: {result['total_calculated']}")
        
        if not result['success']:
            print(f"   错误: {result.get('error', 'No error message')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Worker测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_module_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from utils import download_hist_price
        print("✅ utils.download_hist_price 导入成功")
    except Exception as e:
        print(f"❌ utils.download_hist_price 导入失败: {e}")
    
    try:
        from db_settings import get_default_db_config
        print("✅ db_settings.get_default_db_config 导入成功")
    except Exception as e:
        print(f"❌ db_settings.get_default_db_config 导入失败: {e}")
    
    try:
        from mtf_historical_config import get_mtf_config
        print("✅ mtf_historical_config.get_mtf_config 导入成功")
    except Exception as e:
        print(f"❌ mtf_historical_config.get_mtf_config 导入失败: {e}")
    
    print()

def test_data_download():
    """测试数据下载"""
    print("📊 测试数据下载...")
    
    try:
        from utils import download_hist_price
        
        # 下载AAPL的数据
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        price_data = download_hist_price(
            symbols=['AAPL'],
            interval='1d',
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=1,
            verbose=True
        )
        
        if 'AAPL' in price_data and price_data['AAPL'] is not None:
            df = price_data['AAPL']
            print(f"✅ 数据下载成功: {len(df)} 条记录")
            print(f"   列名: {list(df.columns)}")
            print(f"   日期范围: {df.index[0]} 到 {df.index[-1]}")
        else:
            print("❌ 数据下载失败: 无数据返回")
            
    except Exception as e:
        print(f"❌ 数据下载测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def test_database_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接...")
    
    try:
        from db_settings import get_default_db_config
        import pymysql
        
        db_config = get_default_db_config()
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        if result and result[0] == 1:
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败: 查询结果异常")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
    
    print()

def main():
    """主函数"""
    print("🔧 MTF Worker 调试工具")
    print("=" * 60)
    
    # 测试模块导入
    test_module_imports()
    
    # 测试数据库连接
    test_database_connection()
    
    # 测试数据下载
    test_data_download()
    
    # 测试worker函数
    result = test_single_symbol_worker()
    
    print("=" * 60)
    if result and result['success']:
        print("🎉 所有测试通过！")
    else:
        print("❌ 测试发现问题，请检查上述错误信息")

if __name__ == "__main__":
    main()
