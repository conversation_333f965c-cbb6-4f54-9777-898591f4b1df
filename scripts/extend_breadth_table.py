#!/usr/bin/env python3
"""
扩展 market_breadth_metrics_gics 表
添加 coherence_details 和增强版背离检测字段
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def extend_breadth_table():
    """扩展市场广度表"""
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    try:
        print("🔧 扩展 market_breadth_metrics_gics 表")
        print("=" * 60)
        
        # 1. 检查当前表结构
        print("\n📊 当前表结构:")
        cursor.execute("DESCRIBE market_breadth_metrics_gics")
        current_columns = cursor.fetchall()
        
        existing_fields = [col[0] for col in current_columns]
        print(f"当前字段数: {len(existing_fields)}")
        
        # 2. 定义要添加的新字段
        new_fields = [
            {
                'name': 'coherence_details',
                'definition': 'TEXT COMMENT "动量一致性详细信息(JSON格式)"',
                'description': '动量一致性详细分解信息'
            },
            {
                'name': 'enhanced_divergence_details', 
                'definition': 'TEXT COMMENT "增强版背离检测详细结果(JSON格式)"',
                'description': '增强版背离检测的详细分析结果'
            },
            {
                'name': 'divergence_confidence',
                'definition': 'DECIMAL(5,3) DEFAULT NULL COMMENT "背离检测置信度(0-1)"',
                'description': '背离检测结果的置信度'
            },
            {
                'name': 'divergence_risk_level',
                'definition': 'VARCHAR(20) DEFAULT NULL COMMENT "背离风险等级"',
                'description': '背离风险等级(low/medium/high/extreme)'
            }
        ]
        
        # 3. 检查哪些字段需要添加
        fields_to_add = []
        for field in new_fields:
            if field['name'] not in existing_fields:
                fields_to_add.append(field)
            else:
                print(f"  ✅ {field['name']} 字段已存在")
        
        if not fields_to_add:
            print("\n✅ 所有字段都已存在，无需添加")
            return True
        
        # 4. 添加新字段
        print(f"\n🔧 需要添加 {len(fields_to_add)} 个新字段:")
        
        for field in fields_to_add:
            print(f"\n添加字段: {field['name']}")
            print(f"  描述: {field['description']}")
            
            alter_sql = f"""
            ALTER TABLE market_breadth_metrics_gics 
            ADD COLUMN {field['name']} {field['definition']}
            """
            
            try:
                cursor.execute(alter_sql)
                print(f"  ✅ {field['name']} 添加成功")
            except Exception as e:
                print(f"  ❌ {field['name']} 添加失败: {e}")
                return False
        
        # 5. 提交更改
        conn.commit()
        print(f"\n✅ 表结构扩展完成!")
        
        # 6. 验证新表结构
        print(f"\n📊 验证新表结构:")
        cursor.execute("DESCRIBE market_breadth_metrics_gics")
        new_columns = cursor.fetchall()
        
        print(f"扩展后字段数: {len(new_columns)}")
        print(f"新增字段数: {len(new_columns) - len(existing_fields)}")
        
        # 显示新增的字段
        print(f"\n新增字段详情:")
        for col in new_columns:
            field_name = col[0]
            field_type = col[1]
            if field_name not in existing_fields:
                print(f"  ✅ {field_name}: {field_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 扩展表失败: {e}")
        conn.rollback()
        return False
        
    finally:
        cursor.close()
        conn.close()

def test_new_fields():
    """测试新字段的使用"""
    print(f"\n🧪 测试新字段的使用")
    print("=" * 60)
    
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    try:
        # 测试插入包含新字段的数据
        test_coherence_details = {
            "direction_coherence": 0.75,
            "magnitude_coherence": 0.68,
            "positive_stocks": 180,
            "negative_stocks": 120,
            "neutral_stocks": 5,
            "coherence_breakdown": {
                "strong_positive": 120,
                "weak_positive": 60,
                "strong_negative": 80,
                "weak_negative": 40
            }
        }
        
        test_divergence_details = {
            "base_severity": 0.025,
            "enhanced_severity": 0.038,
            "adjustments": {
                "nh_nl_confirmation": 1.3,
                "ma_divergence": 1.15,
                "rsi_divergence": 1.1
            },
            "supporting_evidence": [
                "新低多于新高(NH/NL=0.25)",
                "均线支撑弱(MA健康度=0.35)",
                "RSI超买但价格仍涨(RSI=75.2)"
            ],
            "recommendation": "立即减仓或清仓，上涨缺乏支撑"
        }
        
        import json
        
        # 测试更新现有记录
        cursor.execute("""
        SELECT market, recorded_at 
        FROM market_breadth_metrics_gics 
        ORDER BY recorded_at DESC 
        LIMIT 1
        """)
        
        latest_record = cursor.fetchone()
        if latest_record:
            market, recorded_at = latest_record
            
            update_sql = """
            UPDATE market_breadth_metrics_gics 
            SET coherence_details = %s,
                enhanced_divergence_details = %s,
                divergence_confidence = %s,
                divergence_risk_level = %s
            WHERE market = %s AND recorded_at = %s
            """
            
            cursor.execute(update_sql, (
                json.dumps(test_coherence_details, ensure_ascii=False),
                json.dumps(test_divergence_details, ensure_ascii=False),
                0.85,
                'high',
                market,
                recorded_at
            ))
            
            conn.commit()
            
            print(f"✅ 测试数据更新成功")
            print(f"  市场: {market}")
            print(f"  时间: {recorded_at}")
            print(f"  置信度: 0.85")
            print(f"  风险等级: high")
            
            # 验证数据
            cursor.execute("""
            SELECT coherence_details, enhanced_divergence_details, 
                   divergence_confidence, divergence_risk_level
            FROM market_breadth_metrics_gics 
            WHERE market = %s AND recorded_at = %s
            """, (market, recorded_at))
            
            result = cursor.fetchone()
            if result:
                coherence_details, enhanced_divergence_details, confidence, risk_level = result
                
                print(f"\n📊 验证结果:")
                print(f"  coherence_details: {'✅ 有数据' if coherence_details else '❌ 无数据'}")
                print(f"  enhanced_divergence_details: {'✅ 有数据' if enhanced_divergence_details else '❌ 无数据'}")
                print(f"  divergence_confidence: {confidence}")
                print(f"  divergence_risk_level: {risk_level}")
                
                # 测试JSON解析
                if coherence_details:
                    try:
                        parsed_coherence = json.loads(coherence_details)
                        print(f"  ✅ coherence_details JSON解析成功")
                        print(f"    方向一致性: {parsed_coherence.get('direction_coherence')}")
                    except:
                        print(f"  ❌ coherence_details JSON解析失败")
                
                if enhanced_divergence_details:
                    try:
                        parsed_divergence = json.loads(enhanced_divergence_details)
                        print(f"  ✅ enhanced_divergence_details JSON解析成功")
                        print(f"    增强严重度: {parsed_divergence.get('enhanced_severity')}")
                    except:
                        print(f"  ❌ enhanced_divergence_details JSON解析失败")
        else:
            print("❌ 没有找到可测试的记录")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        conn.rollback()
        
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数"""
    print("🚀 扩展 market_breadth_metrics_gics 表")
    print("=" * 80)
    
    # 1. 扩展表结构
    success = extend_breadth_table()
    
    if success:
        # 2. 测试新字段
        test_new_fields()
        
        print(f"\n🎉 表扩展完成!")
        print(f"新增字段:")
        print(f"  - coherence_details: 动量一致性详细信息")
        print(f"  - enhanced_divergence_details: 增强版背离检测详细结果")
        print(f"  - divergence_confidence: 背离检测置信度")
        print(f"  - divergence_risk_level: 背离风险等级")
    else:
        print(f"\n❌ 表扩展失败!")

if __name__ == "__main__":
    main()
