#!/usr/bin/env python3
"""
验证当前计算器的纯度值是否正确
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))

def verify_current_purity():
    """验证当前纯度计算"""
    from market_breadth_calculator import calculate_all_markets_breadth
    
    print("🧪 验证当前计算器的纯度值是否正确...")
    results = calculate_all_markets_breadth('1h', save_to_db=False)
    
    if results:
        print(f"✅ 计算成功，处理了 {len(results)} 个市场")
        print("\n前5个市场的纯度验证:")
        
        all_correct = True
        for i, result in enumerate(results[:5]):
            market = result.get('market', 'Unknown')
            advances = result.get('advances', 0)
            declines = result.get('declines', 0)
            total_stocks = result.get('total_stocks', 0)
            purity = result.get('purity', 0)
            
            # 手动验证纯度
            if total_stocks > 0:
                advance_ratio = advances / total_stocks
                decline_ratio = declines / total_stocks
                manual_purity = advance_ratio ** 2 + decline_ratio ** 2
            else:
                manual_purity = 0
            
            is_correct = abs(purity - manual_purity) < 0.001
            if not is_correct:
                all_correct = False
            
            print(f"  {market}:")
            print(f"    计算器纯度: {purity:.6f}")
            print(f"    手动验证: {manual_purity:.6f}")
            print(f"    差异: {abs(purity - manual_purity):.6f}")
            print(f"    状态: {'✅ 正确' if is_correct else '❌ 错误'}")
            print()
        
        if all_correct:
            print("🎉 所有纯度计算都是正确的！")
            return True
        else:
            print("⚠️  发现纯度计算错误")
            return False
    else:
        print("❌ 计算失败")
        return False

def main():
    """主函数"""
    print("🔍 验证纯度计算正确性")
    print("=" * 50)
    
    success = verify_current_purity()
    
    if success:
        print("\n✅ 结论: 当前的市场广度计算器纯度计算完全正确")
        print("✅ hist_data.py 中的纯度计算逻辑已经修复")
        print("✅ 历史数据中的错误值是由于之前版本的bug造成的")
        print("✅ 新生成的数据都是正确的")
    else:
        print("\n❌ 仍需要进一步修复纯度计算逻辑")

if __name__ == "__main__":
    main()
