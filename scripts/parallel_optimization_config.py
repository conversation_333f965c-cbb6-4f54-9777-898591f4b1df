#!/usr/bin/env python3
"""
并行优化配置 - 64核心64G内存服务器专用
根据硬件配置自动优化并行参数
"""

import multiprocessing
import psutil
from typing import Dict, Any

def get_hardware_info() -> Dict[str, Any]:
    """获取硬件信息"""
    return {
        'cpu_count': multiprocessing.cpu_count(),
        'memory_gb': psutil.virtual_memory().total / (1024**3),
        'memory_available_gb': psutil.virtual_memory().available / (1024**3),
        'cpu_freq_ghz': psutil.cpu_freq().current / 1000 if psutil.cpu_freq() else 0,
        'disk_io_counters': psutil.disk_io_counters()
    }

def get_optimal_parallel_config(timeframe: str = '1d') -> Dict[str, Any]:
    """获取最优并行配置"""
    hw_info = get_hardware_info()
    cpu_count = hw_info['cpu_count']
    memory_gb = hw_info['memory_gb']
    
    # 64核心64G内存服务器的激进优化配置
    if cpu_count >= 64 and memory_gb >= 60:
        return {
            'max_workers': 48,  # 保留16核心给系统
            'batch_size': 100,  # 大批次处理
            'memory_per_worker_mb': 1000,  # 每个worker 1GB内存
            'io_threads_per_worker': 4,  # 每个worker 4个IO线程
            'prefetch_batches': 3,  # 预取3个批次
            'optimization_level': 'ultra_aggressive'
        }
    
    # 32核心32G内存服务器配置
    elif cpu_count >= 32 and memory_gb >= 30:
        return {
            'max_workers': 24,
            'batch_size': 80,
            'memory_per_worker_mb': 800,
            'io_threads_per_worker': 3,
            'prefetch_batches': 2,
            'optimization_level': 'aggressive'
        }
    
    # 16核心16G内存服务器配置
    elif cpu_count >= 16 and memory_gb >= 15:
        return {
            'max_workers': 12,
            'batch_size': 50,
            'memory_per_worker_mb': 500,
            'io_threads_per_worker': 2,
            'prefetch_batches': 2,
            'optimization_level': 'moderate'
        }
    
    # 8核心8G内存服务器配置
    elif cpu_count >= 8 and memory_gb >= 7:
        return {
            'max_workers': 6,
            'batch_size': 30,
            'memory_per_worker_mb': 300,
            'io_threads_per_worker': 2,
            'prefetch_batches': 1,
            'optimization_level': 'conservative'
        }
    
    # 小型服务器保守配置
    else:
        return {
            'max_workers': max(2, cpu_count - 1),
            'batch_size': 20,
            'memory_per_worker_mb': 200,
            'io_threads_per_worker': 1,
            'prefetch_batches': 1,
            'optimization_level': 'minimal'
        }

def get_timeframe_specific_config(timeframe: str) -> Dict[str, Any]:
    """获取时间框架特定配置"""
    base_config = get_optimal_parallel_config(timeframe)
    
    # 根据时间框架调整配置
    timeframe_adjustments = {
        '5m': {
            'batch_size_multiplier': 0.5,  # 5分钟数据量大，减小批次
            'memory_multiplier': 1.5,      # 需要更多内存
            'io_intensity': 'high'
        },
        '15m': {
            'batch_size_multiplier': 0.7,
            'memory_multiplier': 1.3,
            'io_intensity': 'high'
        },
        '1h': {
            'batch_size_multiplier': 0.8,
            'memory_multiplier': 1.2,
            'io_intensity': 'medium'
        },
        '1d': {
            'batch_size_multiplier': 1.0,  # 基准配置
            'memory_multiplier': 1.0,
            'io_intensity': 'medium'
        },
        '1w': {
            'batch_size_multiplier': 1.2,
            'memory_multiplier': 0.8,
            'io_intensity': 'low'
        },
        '1M': {
            'batch_size_multiplier': 1.5,
            'memory_multiplier': 0.6,
            'io_intensity': 'low'
        }
    }
    
    if timeframe in timeframe_adjustments:
        adj = timeframe_adjustments[timeframe]
        base_config['batch_size'] = int(base_config['batch_size'] * adj['batch_size_multiplier'])
        base_config['memory_per_worker_mb'] = int(base_config['memory_per_worker_mb'] * adj['memory_multiplier'])
        base_config['io_intensity'] = adj['io_intensity']
    
    return base_config

def print_optimization_summary():
    """打印优化配置摘要"""
    hw_info = get_hardware_info()
    config = get_optimal_parallel_config()
    
    print("🔥 并行优化配置摘要")
    print("=" * 60)
    
    print(f"💻 硬件信息:")
    print(f"   CPU核心数: {hw_info['cpu_count']}")
    print(f"   总内存: {hw_info['memory_gb']:.1f}GB")
    print(f"   可用内存: {hw_info['memory_available_gb']:.1f}GB")
    if hw_info['cpu_freq_ghz'] > 0:
        print(f"   CPU频率: {hw_info['cpu_freq_ghz']:.1f}GHz")
    
    print(f"\n⚡ 优化配置:")
    print(f"   最大worker数: {config['max_workers']}")
    print(f"   批次大小: {config['batch_size']}")
    print(f"   每worker内存: {config['memory_per_worker_mb']}MB")
    print(f"   IO线程数: {config['io_threads_per_worker']}")
    print(f"   优化级别: {config['optimization_level']}")
    
    # 计算资源利用率
    cpu_utilization = config['max_workers'] / hw_info['cpu_count'] * 100
    memory_utilization = (config['max_workers'] * config['memory_per_worker_mb'] / 1024) / hw_info['memory_gb'] * 100
    
    print(f"\n📊 资源利用率:")
    print(f"   CPU利用率: {cpu_utilization:.1f}%")
    print(f"   内存利用率: {memory_utilization:.1f}%")
    
    # 性能预估
    estimated_throughput = config['max_workers'] * config['batch_size'] / 60  # 股票/分钟
    print(f"\n🚀 性能预估:")
    print(f"   预估吞吐量: {estimated_throughput:.1f} 股票/分钟")
    
    return config

def get_database_optimization_config() -> Dict[str, Any]:
    """获取数据库优化配置"""
    hw_info = get_hardware_info()
    
    if hw_info['cpu_count'] >= 64:
        return {
            'connection_pool_size': 100,
            'max_overflow': 50,
            'pool_timeout': 30,
            'pool_recycle': 3600,
            'batch_insert_size': 1000,
            'bulk_insert_method': 'executemany',
            'transaction_isolation': 'READ_COMMITTED'
        }
    elif hw_info['cpu_count'] >= 32:
        return {
            'connection_pool_size': 50,
            'max_overflow': 25,
            'pool_timeout': 30,
            'pool_recycle': 3600,
            'batch_insert_size': 500,
            'bulk_insert_method': 'executemany',
            'transaction_isolation': 'READ_COMMITTED'
        }
    else:
        return {
            'connection_pool_size': 20,
            'max_overflow': 10,
            'pool_timeout': 30,
            'pool_recycle': 3600,
            'batch_insert_size': 200,
            'bulk_insert_method': 'execute',
            'transaction_isolation': 'READ_COMMITTED'
        }

if __name__ == "__main__":
    print_optimization_summary()
    
    print("\n" + "=" * 60)
    print("📋 各时间框架优化配置:")
    
    timeframes = ['5m', '15m', '1h', '1d', '1w', '1M']
    for tf in timeframes:
        config = get_timeframe_specific_config(tf)
        print(f"\n{tf}:")
        print(f"   Worker数: {config['max_workers']}")
        print(f"   批次大小: {config['batch_size']}")
        print(f"   内存/Worker: {config['memory_per_worker_mb']}MB")
        print(f"   IO强度: {config['io_intensity']}")
