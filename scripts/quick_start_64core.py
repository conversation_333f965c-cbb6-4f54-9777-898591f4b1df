#!/usr/bin/env python3
"""
64核心服务器快速启动脚本
一键启动最优化的MTF历史数据计算
"""

import sys
import os
import subprocess
import multiprocessing

def main():
    """主函数"""
    print("🔥 64核心服务器MTF快速启动")
    print("=" * 60)
    
    # 检测硬件
    cpu_count = multiprocessing.cpu_count()
    print(f"检测到 {cpu_count} 个CPU核心")
    
    # 根据CPU核心数推荐配置
    if cpu_count >= 64:
        workers = 48
        print("🚀 检测到64+核心服务器，使用超高性能配置")
    elif cpu_count >= 32:
        workers = 24
        print("⚡ 检测到32+核心服务器，使用高性能配置")
    elif cpu_count >= 16:
        workers = 12
        print("💪 检测到16+核心服务器，使用中等性能配置")
    else:
        workers = max(4, cpu_count - 2)
        print(f"📱 检测到{cpu_count}核心服务器，使用保守配置")
    
    print(f"推荐worker数量: {workers}")
    print()
    
    # 显示可用选项
    print("📋 可用的快速启动选项:")
    print("1. 日线数据 18个月 (推荐)")
    print("2. 日线数据 12个月")
    print("3. 日线数据 6个月")
    print("4. 多时间框架 (1d, 1h) 18个月")
    print("5. 全时间框架 (5m, 15m, 1h, 1d, 1w, 1M) 18个月")
    print("6. 自定义配置")
    print("7. 使用新的超高速脚本")
    print()
    
    choice = input("请选择 (1-7): ").strip()
    
    if choice == "1":
        # 日线数据 18个月
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 18 --workers {workers}"
        
    elif choice == "2":
        # 日线数据 12个月
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 12 --workers {workers}"
        
    elif choice == "3":
        # 日线数据 6个月
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 6 --workers {workers}"
        
    elif choice == "4":
        # 多时间框架
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes 1d 1h --months 18 --workers {workers}"
        
    elif choice == "5":
        # 全时间框架
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes 5m 15m 1h 1d 1w 1M --months 18 --workers {workers}"
        
    elif choice == "6":
        # 自定义配置
        print("\n自定义配置:")
        timeframes = input("时间框架 (用空格分隔，如: 1d 1h): ").strip().split()
        months = int(input("历史月数 (如: 18): ").strip())
        custom_workers = input(f"Worker数量 (默认{workers}): ").strip()
        
        if custom_workers:
            workers = int(custom_workers)
        
        cmd = f"python scripts/setup_mtf_historical_system.py --custom --timeframes {' '.join(timeframes)} --months {months} --workers {workers}"
        
    elif choice == "7":
        # 使用新的超高速脚本
        print("\n使用超高速脚本:")
        timeframes = input("时间框架 (默认1d): ").strip() or "1d"
        months = input("历史月数 (默认18): ").strip() or "18"
        
        cmd = f"python scripts/ultra_fast_mtf_setup.py --timeframes {timeframes} --months {months} --auto-workers"
        
    else:
        print("❌ 无效选择")
        return
    
    print(f"\n🚀 执行命令: {cmd}")
    print("=" * 60)
    
    # 激活虚拟环境并执行命令
    full_cmd = f"source .venv/bin/activate && {cmd}"
    
    try:
        # 使用bash执行命令以支持source
        result = subprocess.run(full_cmd, shell=True, executable='/bin/bash')
        
        if result.returncode == 0:
            print("\n✅ 执行完成！")
        else:
            print(f"\n❌ 执行失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")

if __name__ == "__main__":
    main()
