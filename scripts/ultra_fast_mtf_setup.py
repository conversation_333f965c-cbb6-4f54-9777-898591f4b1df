#!/usr/bin/env python3
"""
超高速MTF历史数据计算脚本 - 64核心64G内存优化版
专为高性能服务器设计，充分利用多核心和大内存优势
"""

import sys
import os
import time
import multiprocessing
import psutil
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from mtf_precomputed_system import MTFPrecomputedSystem

class UltraFastMTFSetup:
    """超高速MTF设置系统"""
    
    def __init__(self):
        self.system = MTFPrecomputedSystem()
        self.logger = self.system.logger
        
        # 系统资源检测
        self.cpu_count = multiprocessing.cpu_count()
        self.memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # 根据硬件配置优化参数
        self.optimal_workers = self._calculate_optimal_workers()
        
        print(f"🔥 超高速MTF系统初始化")
        print(f"   CPU核心数: {self.cpu_count}")
        print(f"   内存容量: {self.memory_gb:.1f}GB")
        print(f"   推荐worker数: {self.optimal_workers}")
        print("=" * 60)
    
    def _calculate_optimal_workers(self):
        """计算最优worker数量"""
        if self.cpu_count >= 64:
            # 64核心：使用48个worker（保留16核心给系统）
            return 48
        elif self.cpu_count >= 32:
            # 32核心：使用24个worker
            return 24
        elif self.cpu_count >= 16:
            # 16核心：使用12个worker
            return 12
        elif self.cpu_count >= 8:
            # 8核心：使用6个worker
            return 6
        else:
            # 小型系统：保守方案
            return max(2, self.cpu_count - 1)
    
    def ultra_fast_calculate(self, timeframes=['1d'], months=18, symbols=None):
        """超高速计算"""
        print(f"🚀 开始超高速计算")
        print(f"   时间框架: {', '.join(timeframes)}")
        print(f"   历史月数: {months}个月")
        print(f"   并行worker: {self.optimal_workers}个")
        
        if symbols:
            print(f"   指定股票: {len(symbols)}只")
        else:
            symbols = self.system.get_all_symbols()
            print(f"   全部股票: {len(symbols)}只")
        
        # 预估时间
        estimated_minutes = self._estimate_time(len(symbols), timeframes, months)
        print(f"   预估耗时: {estimated_minutes:.1f}分钟")
        print()
        
        # 确认执行
        response = input("是否开始执行？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 用户取消执行")
            return False
        
        # 开始计算
        start_time = time.time()
        
        results = self.system.calculate_historical_data(
            symbols=symbols,
            timeframes=timeframes,
            history_months=months,
            force_recalculate=False,
            max_workers=self.optimal_workers
        )
        
        end_time = time.time()
        actual_minutes = (end_time - start_time) / 60
        
        # 显示结果
        self._show_results(results, actual_minutes, estimated_minutes)
        
        return True
    
    def _estimate_time(self, symbol_count, timeframes, months):
        """预估计算时间"""
        # 基于经验的时间估算公式
        base_time_per_symbol = 0.5  # 每只股票基础时间（秒）
        timeframe_multiplier = {
            '5m': 3.0,
            '15m': 2.5,
            '1h': 2.0,
            '1d': 1.0,
            '1w': 0.8,
            '1M': 0.6
        }

        total_time = 0
        for tf in timeframes:
            multiplier = timeframe_multiplier.get(tf, 1.0)
            total_time += symbol_count * base_time_per_symbol * multiplier * (months / 3)
        
        # 并行加速比（考虑I/O瓶颈）
        speedup_ratio = min(self.optimal_workers * 0.7, symbol_count)
        
        return total_time / speedup_ratio / 60  # 转换为分钟
    
    def _show_results(self, results, actual_minutes, estimated_minutes):
        """显示计算结果"""
        print("\n" + "=" * 60)
        print("🎉 计算完成！")
        print("=" * 60)
        
        print(f"📊 总体结果:")
        print(f"   成功股票: {results['success_count']:,}")
        print(f"   失败股票: {results['failed_count']:,}")
        print(f"   成功率: {results['success_count']/(results['success_count']+results['failed_count'])*100:.1f}%")
        
        print(f"\n⏱️ 时间统计:")
        print(f"   预估时间: {estimated_minutes:.1f}分钟")
        print(f"   实际时间: {actual_minutes:.1f}分钟")
        
        if estimated_minutes > 0:
            efficiency = estimated_minutes / actual_minutes
            print(f"   加速比: {efficiency:.1f}x")
        
        print(f"\n📈 各时间框架详情:")
        for tf, tf_result in results['timeframe_results'].items():
            print(f"   {tf}: 成功{tf_result['success_count']:,}, "
                  f"失败{tf_result['failed_count']}")
        
        # 性能统计
        total_symbols = results['success_count'] + results['failed_count']
        if actual_minutes > 0:
            symbols_per_minute = total_symbols / actual_minutes
            print(f"\n🔥 性能指标:")
            print(f"   处理速度: {symbols_per_minute:.1f} 股票/分钟")
            print(f"   平均每股票: {actual_minutes*60/total_symbols:.2f} 秒")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='超高速MTF历史数据计算系统')
    parser.add_argument('--timeframes', nargs='+', default=['1d'], 
                       help='时间框架列表 (默认: 1d)')
    parser.add_argument('--months', type=int, default=18, 
                       help='历史数据月数 (默认: 18)')
    parser.add_argument('--symbols', nargs='+', 
                       help='指定股票代码列表')
    parser.add_argument('--auto-workers', action='store_true', 
                       help='自动优化worker数量（推荐）')
    parser.add_argument('--workers', type=int, 
                       help='手动指定worker数量')
    parser.add_argument('--test', action='store_true', 
                       help='测试模式（只处理10只股票）')
    
    args = parser.parse_args()
    
    # 创建系统实例
    setup = UltraFastMTFSetup()
    
    # 处理worker数量设置
    if args.workers:
        setup.optimal_workers = args.workers
        print(f"🔧 手动设置worker数量: {args.workers}")
    elif args.auto_workers:
        print(f"🤖 自动优化worker数量: {setup.optimal_workers}")
    
    # 处理测试模式
    test_symbols = None
    print(f"🔍 调试信息: args.test = {args.test}")
    if args.test:
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA',
                       'F', 'GM', 'BWA', 'ALV', 'AXL']
        print(f"🧪 测试模式: 使用 {len(test_symbols)} 只股票")

    symbols = args.symbols or test_symbols
    print(f"🔍 调试信息: symbols = {symbols[:5] if symbols else None}...")
    
    # 执行计算
    setup.ultra_fast_calculate(
        timeframes=args.timeframes,
        months=args.months,
        symbols=symbols
    )

if __name__ == "__main__":
    main()
