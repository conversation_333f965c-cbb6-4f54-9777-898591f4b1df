#!/usr/bin/env python3
"""
单独计算15分钟时间框架的市场广度数据
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from market_breadth_calculator import calculate_all_markets_breadth
import pymysql
from db_settings import get_default_db_config

def check_database_connection():
    """检查数据库连接"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        conn.close()
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_existing_data():
    """检查现有的15分钟数据"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查15分钟数据
        cursor.execute("""
        SELECT COUNT(*) as count, MAX(recorded_at) as latest, MIN(recorded_at) as earliest
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        count, latest, earliest = result
        
        conn.close()
        
        print(f"📊 现有15分钟数据状态:")
        print(f"   记录数: {count}")
        if count > 0:
            print(f"   最早时间: {earliest}")
            print(f"   最新时间: {latest}")
        else:
            print(f"   状态: 无数据")
        
        return count > 0
        
    except Exception as e:
        print(f"❌ 检查现有数据失败: {e}")
        return False

def calculate_15m_breadth():
    """计算15分钟市场广度数据"""
    print("🚀 开始计算15分钟市场广度数据")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据库连接
    if not check_database_connection():
        print("❌ 数据库连接失败，退出程序")
        return False
    
    # 检查现有数据
    has_existing_data = check_existing_data()
    
    if has_existing_data:
        print("\n⚠️ 发现现有15分钟数据")
        response = input("是否继续计算（会添加新数据）？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 用户取消计算")
            return False
    
    print(f"\n🚀 开始15分钟市场广度计算...")
    print(f"   时间框架: 15m")
    print(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    
    try:
        # 使用64核心服务器的优化配置
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        
        if cpu_count >= 64:
            num_processes = 24  # 64核心使用24个进程（保守配置）
        elif cpu_count >= 32:
            num_processes = 16  # 32核心使用16个进程
        elif cpu_count >= 16:
            num_processes = 8   # 16核心使用8个进程
        else:
            num_processes = max(4, cpu_count - 2)
        
        print(f"   并行配置: {num_processes}个进程 (CPU核心数: {cpu_count})")
        
        # 计算15分钟市场广度
        results = calculate_all_markets_breadth('15m', num_processes=num_processes, save_to_db=True)
        calc_time = time.time() - start_time
        
        if results:
            print(f"✅ 15分钟计算完成!")
            print(f"   处理市场数: {len(results)} 个")
            print(f"   计算耗时: {calc_time:.1f} 秒")
            print(f"   平均每市场: {calc_time/len(results):.2f} 秒")
            
            # 显示样本结果
            print(f"\n📊 样本结果:")
            for i, result in enumerate(results[:5]):
                market = result.get('market', 'Unknown')
                advances = result.get('advances', 0)
                declines = result.get('declines', 0)
                total = result.get('total_stocks', 0)
                health = result.get('internal_health', 0)
                rsi = result.get('avg_rsi', 0)
                print(f"     {market}: {advances}↑ {declines}↓ (总计{total}) RSI={rsi:.1f} 健康度={health:.1f}")
            
            # 验证数据保存
            if verify_data_saved():
                print(f"\n✅ 数据验证成功 - 15分钟数据已保存到数据库")
                return True
            else:
                print(f"\n❌ 数据验证失败 - 数据可能未正确保存")
                return False
        else:
            print(f"❌ 15分钟计算失败 - 没有返回结果")
            return False
            
    except Exception as e:
        calc_time = time.time() - start_time
        print(f"❌ 15分钟计算异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_data_saved():
    """验证数据是否成功保存到数据库"""
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查最新数据
        cursor.execute("""
        SELECT COUNT(*) as count, MAX(recorded_at) as latest, COUNT(DISTINCT market) as markets
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        count, latest, markets = result
        
        conn.close()
        
        if count > 0:
            print(f"   📊 数据库验证结果:")
            print(f"      总记录数: {count}")
            print(f"      市场数: {markets}")
            print(f"      最新时间: {latest}")
            return True
        else:
            print(f"   ❌ 数据库验证: 没有找到15分钟数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        return False

def show_final_summary():
    """显示最终总结"""
    print(f"\n🎯 15分钟市场广度计算总结")
    print("=" * 60)
    
    try:
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取详细统计
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT market) as total_markets,
            MIN(recorded_at) as earliest_time,
            MAX(recorded_at) as latest_time
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        """)
        
        result = cursor.fetchone()
        total_records, total_markets, earliest_time, latest_time = result
        
        # 获取各市场的记录数
        cursor.execute("""
        SELECT market, COUNT(*) as records
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '15m'
        GROUP BY market
        ORDER BY records DESC
        """)
        
        market_stats = cursor.fetchall()
        conn.close()
        
        print(f"📈 数据统计:")
        print(f"   总记录数: {total_records:,}")
        print(f"   市场数量: {total_markets}")
        print(f"   时间范围: {earliest_time} 到 {latest_time}")
        
        print(f"\n📊 各市场记录数:")
        for market, records in market_stats:
            print(f"   {market}: {records:,} 条记录")
        
        print(f"\n💡 下一步:")
        print(f"   1. 可以在Web界面选择15分钟时间框架")
        print(f"   2. 15分钟数据可用于短期市场分析")
        print(f"   3. 建议继续计算其他时间框架（5m, 1h等）")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def main():
    """主函数"""
    success = calculate_15m_breadth()
    
    if success:
        show_final_summary()
        print(f"\n🎉 15分钟市场广度计算成功完成!")
    else:
        print(f"\n❌ 15分钟市场广度计算失败")
        print(f"   请检查日志信息和网络连接")
    
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
