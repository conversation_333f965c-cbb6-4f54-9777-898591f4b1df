#!/usr/bin/env python3
"""
对比数据库中的纯度值和手动计算结果
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def compare_purity_calculation():
    """对比纯度计算"""
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    print("=== 对比不同时间的纯度值 ===")
    cursor.execute("""
    SELECT market, purity, internal_health, advances, declines, total_stocks, recorded_at
    FROM market_breadth_metrics_gics 
    WHERE market = 'NASDAQ100'
    ORDER BY recorded_at DESC 
    LIMIT 5
    """)
    
    nasdaq_data = cursor.fetchall()
    print("NASDAQ100最新5条记录:")
    for row in nasdaq_data:
        market = row[0]
        purity = float(row[1])
        health = float(row[2])
        advances = row[3]
        declines = row[4]
        total_stocks = row[5] if row[5] else 0
        time = row[6]
        
        # 手动计算纯度
        if total_stocks > 0:
            advance_ratio = advances / total_stocks
            decline_ratio = declines / total_stocks
            manual_purity = advance_ratio ** 2 + decline_ratio ** 2
        else:
            manual_purity = 0
        
        print(f"  时间: {time}")
        print(f"    数据库纯度: {purity:.3f}, 手动计算: {manual_purity:.6f}")
        print(f"    上涨: {advances}, 下跌: {declines}, 总数: {total_stocks}")
        print(f"    健康度: {health:.1f}")
        
        # 检查是否是乘以100的问题
        if abs(purity - manual_purity * 100) < 0.001:
            print(f"    ⚠️  发现问题: 数据库中的纯度值是手动计算值的100倍!")
        elif abs(purity - manual_purity) < 0.001:
            print(f"    ✅ 纯度值正确")
        else:
            print(f"    ❓ 纯度值差异: {abs(purity - manual_purity):.6f}")
        print()
    
    # 检查其他市场
    print("\n=== 检查其他市场的纯度值 ===")
    cursor.execute("""
    SELECT market, purity, advances, declines, total_stocks
    FROM market_breadth_metrics_gics 
    WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    AND total_stocks IS NOT NULL
    ORDER BY purity DESC
    LIMIT 10
    """)
    
    other_markets = cursor.fetchall()
    print("最高纯度值的10个市场:")
    for row in other_markets:
        market = row[0]
        purity = float(row[1])
        advances = row[2]
        declines = row[3]
        total_stocks = row[4]
        
        if total_stocks > 0:
            advance_ratio = advances / total_stocks
            decline_ratio = declines / total_stocks
            manual_purity = advance_ratio ** 2 + decline_ratio ** 2
            
            print(f"  {market}: 数据库={purity:.3f}, 手动={manual_purity:.6f}")
            
            if abs(purity - manual_purity * 100) < 0.001:
                print(f"    ⚠️  问题确认: 数据库值是正确值的100倍")
    
    conn.close()

def main():
    """主函数"""
    print("🔍 对比纯度计算结果")
    print("=" * 50)
    
    compare_purity_calculation()

if __name__ == "__main__":
    main()
