#!/usr/bin/env python3
"""
多时间框架计算进度实时监控
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def monitor_progress(interval=30, duration=3600):
    """
    监控计算进度
    
    Args:
        interval: 检查间隔（秒）
        duration: 总监控时长（秒）
    """
    
    config = get_default_db_config()
    start_time = datetime.now()
    prev_counts = {}
    
    print("🔍 多时间框架计算进度监控")
    print("=" * 60)
    print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"监控间隔: {interval}秒")
    print(f"预计监控: {duration//60}分钟")
    print()
    
    iteration = 0
    
    while True:
        iteration += 1
        current_time = datetime.now()
        elapsed = (current_time - start_time).total_seconds()
        
        if elapsed > duration:
            print(f"\n⏰ 监控时间到达 {duration//60}分钟，结束监控")
            break
        
        try:
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            print(f"\n📊 第{iteration}次检查 ({current_time.strftime('%H:%M:%S')}) - 已运行{elapsed//60:.0f}分钟")
            print("-" * 50)
            
            # 检查各时间框架的数据量
            cursor.execute("""
            SELECT timeframe, COUNT(*) as records, COUNT(DISTINCT symbol) as symbols
            FROM mtf_precomputed_ma_indicators 
            GROUP BY timeframe
            ORDER BY timeframe
            """)
            
            current_counts = {}
            total_records = 0
            total_symbols = 0
            
            for tf, records, symbols in cursor.fetchall():
                current_counts[tf] = records
                total_records += records
                
                # 计算新增量
                prev_count = prev_counts.get(tf, 0)
                new_records = records - prev_count
                
                # 计算速度 (记录/分钟)
                speed = new_records / (interval / 60) if iteration > 1 else 0
                
                print(f"  {tf:>3}: {records:>8,}条记录, {symbols:>4}只股票, 新增{new_records:>6,}条, 速度{speed:>6.0f}/分钟")
                
                if symbols > total_symbols:
                    total_symbols = symbols
            
            # 总体统计
            total_new = sum(current_counts.values()) - sum(prev_counts.values())
            total_speed = total_new / (interval / 60) if iteration > 1 else 0
            
            print(f"\n📈 总计: {total_records:,}条记录, {total_symbols}只股票")
            print(f"   本轮新增: {total_new:,}条记录")
            print(f"   总体速度: {total_speed:,.0f}条记录/分钟")
            
            # 预估完成时间
            if total_speed > 0 and total_symbols > 0:
                # 估算总需求：6543只股票 × 6个时间框架 × 平均记录数
                estimated_total = 6543 * 6 * 1000  # 粗略估算
                remaining = max(0, estimated_total - total_records)
                eta_minutes = remaining / total_speed if total_speed > 0 else 0
                eta_time = current_time.replace(second=0, microsecond=0) + \
                          pd.Timedelta(minutes=eta_minutes) if eta_minutes < 1440 else None
                
                if eta_time:
                    print(f"   预计完成: {eta_time.strftime('%H:%M')} (还需{eta_minutes:.0f}分钟)")
            
            # 检查最新记录
            cursor.execute("""
            SELECT symbol, timeframe, datetime, created_at
            FROM mtf_precomputed_ma_indicators 
            ORDER BY created_at DESC 
            LIMIT 5
            """)
            
            latest_records = cursor.fetchall()
            if latest_records:
                print(f"\n🕐 最新计算完成:")
                for symbol, tf, dt, created in latest_records:
                    print(f"     {symbol} {tf} {dt.strftime('%Y-%m-%d %H:%M')} (于{created.strftime('%H:%M:%S')})")
            
            # 检查错误状态
            cursor.execute("""
            SELECT COUNT(*) FROM mtf_precomputed_status 
            WHERE ma_complete = 0 OR w52_complete = 0
            """)
            error_count = cursor.fetchone()[0]
            if error_count > 0:
                print(f"\n⚠️  发现 {error_count} 个计算错误")
            
            prev_counts = current_counts.copy()
            conn.close()
            
        except Exception as e:
            print(f"❌ 监控出错: {e}")
        
        # 等待下次检查
        if elapsed + interval < duration:
            time.sleep(interval)
        else:
            break
    
    print(f"\n✅ 监控结束")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='多时间框架计算进度监控')
    parser.add_argument('--interval', type=int, default=30, help='检查间隔（秒，默认30）')
    parser.add_argument('--duration', type=int, default=3600, help='监控时长（秒，默认3600=1小时）')
    
    args = parser.parse_args()
    
    try:
        monitor_progress(args.interval, args.duration)
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断监控")
    except Exception as e:
        print(f"\n❌ 监控异常: {e}")
        import traceback
        traceback.print_exc()
