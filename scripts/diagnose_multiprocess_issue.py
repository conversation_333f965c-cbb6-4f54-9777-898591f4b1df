#!/usr/bin/env python3
"""
诊断多进程问题的脚本
"""

import sys
import os
import time
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

def simple_worker_test(symbol):
    """简化的worker测试函数"""
    try:
        import time
        import random
        
        # 模拟一些工作
        time.sleep(random.uniform(0.1, 0.5))
        
        # 测试基本的pickle序列化
        result = {
            'symbol': symbol,
            'success': True,
            'message': f'Successfully processed {symbol}',
            'worker_pid': os.getpid()
        }
        
        return result
        
    except Exception as e:
        return {
            'symbol': symbol,
            'success': False,
            'error': str(e),
            'worker_pid': os.getpid()
        }

def database_worker_test(symbol):
    """测试数据库连接的worker函数"""
    try:
        # 在worker进程中导入模块
        import sys
        import os
        current_dir = os.getcwd()
        config_path = os.path.join(current_dir, 'config')
        if config_path not in sys.path:
            sys.path.insert(0, config_path)
        
        from db_settings import get_default_db_config
        import pymysql
        
        # 测试数据库连接
        db_config = get_default_db_config()
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 简单查询
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        conn.close()
        
        return {
            'symbol': symbol,
            'success': True,
            'message': f'Database connection OK for {symbol}',
            'worker_pid': os.getpid()
        }
        
    except Exception as e:
        return {
            'symbol': symbol,
            'success': False,
            'error': str(e),
            'worker_pid': os.getpid()
        }

def data_download_worker_test(symbol):
    """测试数据下载的worker函数"""
    try:
        # 在worker进程中导入模块
        import sys
        import os
        current_dir = os.getcwd()
        market_breadth_path = os.path.join(current_dir, 'market-breadth-task')
        config_path = os.path.join(current_dir, 'config')
        
        if market_breadth_path not in sys.path:
            sys.path.insert(0, market_breadth_path)
        if config_path not in sys.path:
            sys.path.insert(0, config_path)
        
        from utils import download_hist_price
        
        # 测试数据下载
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        price_data = download_hist_price(
            symbols=[symbol],
            interval='1d',
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=1,
            verbose=False
        )
        
        if symbol in price_data and price_data[symbol] is not None:
            df = price_data[symbol]
            return {
                'symbol': symbol,
                'success': True,
                'message': f'Downloaded {len(df)} records for {symbol}',
                'worker_pid': os.getpid(),
                'records': len(df)
            }
        else:
            return {
                'symbol': symbol,
                'success': False,
                'error': 'No data returned',
                'worker_pid': os.getpid()
            }
        
    except Exception as e:
        import traceback
        return {
            'symbol': symbol,
            'success': False,
            'error': f"{str(e)}\n{traceback.format_exc()}",
            'worker_pid': os.getpid()
        }

def test_multiprocess_basic():
    """测试基本多进程功能"""
    print("🧪 测试1: 基本多进程功能")
    print("-" * 40)
    
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    with ProcessPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(simple_worker_test, symbol) for symbol in symbols]
        
        for future in as_completed(futures):
            result = future.result()
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['symbol']}: PID={result['worker_pid']}")
            if not result['success']:
                print(f"      错误: {result['error']}")
    
    print()

def test_multiprocess_database():
    """测试多进程数据库连接"""
    print("🧪 测试2: 多进程数据库连接")
    print("-" * 40)
    
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    with ProcessPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(database_worker_test, symbol) for symbol in symbols]
        
        for future in as_completed(futures):
            result = future.result()
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['symbol']}: PID={result['worker_pid']}")
            if not result['success']:
                print(f"      错误: {result['error']}")
    
    print()

def test_multiprocess_data_download():
    """测试多进程数据下载"""
    print("🧪 测试3: 多进程数据下载")
    print("-" * 40)
    
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    with ProcessPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(data_download_worker_test, symbol) for symbol in symbols]
        
        for future in as_completed(futures):
            result = future.result()
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['symbol']}: PID={result['worker_pid']}")
            if result['success']:
                print(f"      记录数: {result.get('records', 0)}")
            else:
                # 只显示错误的第一行
                error_line = result['error'].split('\n')[0]
                print(f"      错误: {error_line}")
    
    print()

def test_high_concurrency():
    """测试高并发情况"""
    print("🧪 测试4: 高并发测试 (48个进程)")
    print("-" * 40)
    
    # 使用更多股票测试高并发
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'F', 'GM', 'BWA', 'ALV', 'AXL'] * 5  # 50只股票
    
    start_time = time.time()
    success_count = 0
    failed_count = 0
    
    with ProcessPoolExecutor(max_workers=48) as executor:
        futures = [executor.submit(simple_worker_test, symbol) for symbol in symbols]
        
        for future in as_completed(futures):
            result = future.result()
            if result['success']:
                success_count += 1
            else:
                failed_count += 1
    
    elapsed = time.time() - start_time
    
    print(f"   总股票数: {len(symbols)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {failed_count}")
    print(f"   成功率: {success_count/len(symbols)*100:.1f}%")
    print(f"   耗时: {elapsed:.2f}秒")
    print(f"   处理速度: {len(symbols)/elapsed:.1f} 股票/秒")
    
    print()

def main():
    """主函数"""
    print("🔧 多进程问题诊断工具")
    print("=" * 60)
    print(f"CPU核心数: {multiprocessing.cpu_count()}")
    print(f"当前进程PID: {os.getpid()}")
    print()
    
    # 运行各项测试
    test_multiprocess_basic()
    test_multiprocess_database()
    test_multiprocess_data_download()
    test_high_concurrency()
    
    print("=" * 60)
    print("🎯 诊断建议:")
    print("1. 如果测试1失败 -> 基本多进程环境有问题")
    print("2. 如果测试2失败 -> 数据库连接池配置问题")
    print("3. 如果测试3失败 -> 数据下载API限制或网络问题")
    print("4. 如果测试4失败 -> 高并发资源竞争问题")

if __name__ == "__main__":
    main()
