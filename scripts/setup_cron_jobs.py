#!/usr/bin/env python3
"""
预计算系统定时任务快速配置脚本
自动配置 crontab 定时任务
"""

import os
import sys
import subprocess
from datetime import datetime
import platform

def get_project_path():
    """获取项目路径"""
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def check_system():
    """检查系统环境"""
    system = platform.system().lower()
    print(f"🖥️  检测到系统: {system}")
    
    if system == 'windows':
        print("⚠️  Windows 系统请手动配置任务计划程序")
        print("   参考文档: 定时任务配置指南.md")
        return False
    
    # 检查 crontab 是否可用
    try:
        subprocess.run(['crontab', '-l'], capture_output=True, check=False)
        print("✅ crontab 可用")
        return True
    except FileNotFoundError:
        print("❌ crontab 不可用，请先安装 cron")
        return False

def detect_timezone():
    """检测系统时区"""
    try:
        import subprocess
        result = subprocess.run(['timedatectl', 'show', '--property=Timezone', '--value'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip()
    except:
        pass

    # 备用方法
    try:
        with open('/etc/timezone', 'r') as f:
            return f.read().strip()
    except:
        pass

    return "UTC"  # 默认假设为UTC

def generate_crontab_content(project_path, config_type='basic'):
    """生成 crontab 配置内容"""

    timezone = detect_timezone()
    is_utc = timezone == "UTC" or "UTC" in timezone

    # 根据时区选择时间
    if is_utc:
        morning_time = "21"  # UTC 21:30 = 北京时间 5:30
        morning_check = "22"  # UTC 22:00 = 北京时间 6:00
        afternoon_time = "6"   # UTC 6:30 = 北京时间 14:30
        afternoon_check = "7"  # UTC 7:00 = 北京时间 15:00
        rebuild_time = "18"    # UTC 18:00 = 北京时间周一 2:00
        time_note = f"UTC时间（检测到时区: {timezone}）"
    else:
        morning_time = "5"     # 北京时间 5:30
        morning_check = "6"    # 北京时间 6:00
        afternoon_time = "14"  # 北京时间 14:30
        afternoon_check = "15" # 北京时间 15:00
        rebuild_time = "2"     # 北京时间周日 2:00
        time_note = f"本地时间（检测到时区: {timezone}）"

    basic_config = f"""# ================================
# 预计算系统定时任务配置
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 时区配置: {time_note}
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="{project_path}"

# 每日美股收盘后更新（{time_note.split('（')[0]}）
30 {morning_time} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/daily_update.log 2>&1

# 每日状态检查
0 {morning_check} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/status_check.log 2>&1

# 每周日凌晨完整重建
0 {rebuild_time} * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
"""

    market_breadth_config = f"""# ================================
# 多时间框架市场广度计算配置
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 时区配置: {time_note}
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="{project_path}"

# 每日多时间框架市场广度计算（{time_note.split('（')[0]}）
# 计算：5分钟、15分钟、1小时、1天、1周、1月
30 {morning_time} * * * cd $PROJECT_PATH && python scripts/run_all_timeframes.py >> logs/market_breadth_daily.log 2>&1

# 每周日额外运行一次（数据完整性保障）
0 {rebuild_time} * * 0 cd $PROJECT_PATH && python scripts/run_all_timeframes.py >> logs/market_breadth_weekly.log 2>&1
"""

    afternoon_config = f"""# ================================
# 预计算系统定时任务配置（下午更新版）
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 时区配置: {time_note}
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="{project_path}"

# 每日下午更新（{time_note.split('（')[0]}）
30 {afternoon_time} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 每日状态检查（下午更新后）
0 {afternoon_check} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1

# 每周日凌晨完整重建
0 {rebuild_time} * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
"""

    dual_config = f"""# ================================
# 预计算系统定时任务配置（双重更新版）
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 时区配置: {time_note}
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="{project_path}"

# 主要更新：美股收盘后（{time_note.split('（')[0]}）
30 {morning_time} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/morning_update.log 2>&1

# 备用更新：下午时段（{time_note.split('（')[0]}）
30 {afternoon_time} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/afternoon_update.log 2>&1

# 状态检查：早上更新后
0 {morning_check} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/morning_status.log 2>&1

# 状态检查：下午更新后
0 {afternoon_check} * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/afternoon_status.log 2>&1

# 每周日凌晨完整重建
0 {rebuild_time} * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1
"""

    advanced_config = f"""# ================================
# 预计算系统高级定时任务配置
# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# ================================

# 设置环境变量
PATH=/usr/local/bin:/usr/bin:/bin
PROJECT_PATH="{project_path}"

# 1. 日常更新任务
# 每日美股收盘后更新
30 5 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/daily_update.log 2>&1

# 交易时间内每小时更新（夏令时：21:30-4:30）
30 21-23 * * 1-5 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1
30 0-4 * * 2-6 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --update >> logs/hourly_update.log 2>&1

# 2. 状态监控任务
# 每日状态检查
0 6 * * * cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --status >> logs/status_check.log 2>&1

# 每4小时检查数据新鲜度
0 */4 * * * cd $PROJECT_PATH && python -c "from core.precomputed_query import check_precomputed_data_status; status = check_precomputed_data_status(); print('Status:', status['status'])" >> logs/freshness_check.log 2>&1

# 3. 维护任务
# 每周日完整重建
0 2 * * 0 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --init >> logs/weekly_rebuild.log 2>&1

# 每周一测试系统功能
0 7 * * 1 cd $PROJECT_PATH && python scripts/setup_precomputed_system.py --test >> logs/weekly_test.log 2>&1

# 每月清理日志文件
0 3 1 * * find $PROJECT_PATH/logs -name "*.log" -mtime +30 -delete
"""

    if config_type == 'advanced':
        return advanced_config
    elif config_type == 'afternoon':
        return afternoon_config
    elif config_type == 'dual':
        return dual_config
    elif config_type == 'market_breadth':
        return market_breadth_config
    else:
        return basic_config

def backup_existing_crontab():
    """备份现有的 crontab"""
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            backup_file = f"crontab_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(backup_file, 'w') as f:
                f.write(result.stdout)
            print(f"✅ 现有 crontab 已备份到: {backup_file}")
            return result.stdout
        else:
            print("ℹ️  当前没有 crontab 配置")
            return ""
    except Exception as e:
        print(f"⚠️  备份 crontab 失败: {e}")
        return ""

def install_crontab(content, merge_existing=True):
    """安装 crontab 配置"""
    try:
        if merge_existing:
            existing = backup_existing_crontab()
            if existing and "预计算系统定时任务配置" not in existing:
                content = existing.rstrip() + "\n\n" + content
        
        # 写入临时文件
        temp_file = "/tmp/precomputed_crontab"
        with open(temp_file, 'w') as f:
            f.write(content)
        
        # 安装 crontab
        result = subprocess.run(['crontab', temp_file], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ crontab 配置安装成功")
            os.remove(temp_file)
            return True
        else:
            print(f"❌ crontab 配置安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装 crontab 失败: {e}")
        return False

def create_log_directory(project_path):
    """创建日志目录"""
    log_dir = os.path.join(project_path, 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        print(f"✅ 创建日志目录: {log_dir}")
    else:
        print(f"ℹ️  日志目录已存在: {log_dir}")

def show_crontab_status():
    """显示当前 crontab 状态"""
    try:
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            precomputed_jobs = [line for line in lines if 'precomputed' in line.lower()]
            
            print(f"\n📋 当前 crontab 状态:")
            print(f"   总任务数: {len([l for l in lines if l.strip() and not l.startswith('#')])}")
            print(f"   预计算相关任务: {len(precomputed_jobs)}")
            
            if precomputed_jobs:
                print("\n🕐 预计算任务列表:")
                for job in precomputed_jobs:
                    print(f"   {job}")
        else:
            print("ℹ️  当前没有 crontab 配置")
    except Exception as e:
        print(f"⚠️  查看 crontab 状态失败: {e}")

def main():
    """主函数"""
    print("🚀 预计算系统定时任务配置工具")
    print("=" * 50)
    
    # 检查系统环境
    if not check_system():
        return
    
    project_path = get_project_path()
    print(f"📁 项目路径: {project_path}")
    
    # 创建日志目录
    create_log_directory(project_path)
    
    # 显示当前状态
    show_crontab_status()
    
    print("\n📋 配置选项:")
    print("1. 基础配置 (每日5:30预计算更新 + 状态检查 + 周末重建)")
    print("2. 下午配置 (每日14:30预计算更新 + 状态检查 + 周末重建)")
    print("3. 双重配置 (5:30 + 14:30双重预计算更新保障)")
    print("4. 市宽计算配置 (每日多时间框架市场广度计算)")
    print("5. 高级配置 (包含盘中更新 + 完整监控)")
    print("6. 查看当前配置")
    print("7. 删除相关任务")
    print("8. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-8): ").strip()

            if choice == '1':
                print("\n🔧 安装基础配置...")
                content = generate_crontab_content(project_path, 'basic')
                if install_crontab(content):
                    print("\n✅ 基础配置安装完成")
                    print("📅 任务安排:")
                    print("   - 每日 5:30: 数据更新（美股收盘后）")
                    print("   - 每日 6:00: 状态检查")
                    print("   - 每周日 2:00: 完整重建")
                break

            elif choice == '2':
                print("\n🔧 安装下午配置...")
                content = generate_crontab_content(project_path, 'afternoon')
                if install_crontab(content):
                    print("\n✅ 下午配置安装完成")
                    print("📅 任务安排:")
                    print("   - 每日 14:30: 数据更新（下午时段）")
                    print("   - 每日 15:00: 状态检查")
                    print("   - 每周日 2:00: 完整重建")
                break

            elif choice == '3':
                print("\n🔧 安装双重配置...")
                content = generate_crontab_content(project_path, 'dual')
                if install_crontab(content):
                    print("\n✅ 双重配置安装完成")
                    print("📅 任务安排:")
                    print("   - 每日 5:30: 主要更新（美股收盘后）")
                    print("   - 每日 14:30: 备用更新（下午时段）")
                    print("   - 每日 6:00 & 15:00: 状态检查")
                    print("   - 每周日 2:00: 完整重建")
                break

            elif choice == '4':
                print("\n🔧 安装市宽计算配置...")
                content = generate_crontab_content(project_path, 'market_breadth')
                if install_crontab(content):
                    print("\n✅ 市宽计算配置安装完成")
                    print("📅 任务安排:")
                    print("   - 每日市宽计算: 自动计算6个时间框架的市场广度")
                    print("   - 时间框架: 5分钟、15分钟、1小时、1天、1周、1月")
                    print("   - 每周额外运行: 数据完整性保障")
                    print("\n💡 说明:")
                    print("   - 直接计算市场广度指标（涨跌家数、新高新低、RSI等）")
                    print("   - 结果保存到数据库供Web界面使用")
                    print("   - 支持所有市场的多时间框架分析")
                    print("   - 如需预计算加速，可额外配置预计算系统")
                break

            elif choice == '5':
                print("\n🔧 安装高级配置...")
                content = generate_crontab_content(project_path, 'advanced')
                if install_crontab(content):
                    print("\n✅ 高级配置安装完成")
                    print("📅 任务安排:")
                    print("   - 每日 5:30: 数据更新")
                    print("   - 交易时间: 每小时更新")
                    print("   - 每4小时: 数据新鲜度检查")
                    print("   - 每周日 2:00: 完整重建")
                    print("   - 每周一 7:00: 功能测试")
                    print("   - 每月1日 3:00: 日志清理")
                break

            elif choice == '6':
                print("\n📋 当前 crontab 配置:")
                subprocess.run(['crontab', '-l'])

            elif choice == '7':
                print("\n🗑️  删除预计算相关任务...")
                try:
                    result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        filtered_lines = []
                        skip_section = False

                        for line in lines:
                            if '预计算系统定时任务配置' in line or '预计算系统 + 多时间框架市宽计算配置' in line:
                                skip_section = True
                            elif skip_section and line.strip() == '':
                                skip_section = False
                            elif not skip_section and 'precomputed' not in line.lower() and 'run_all_timeframes' not in line:
                                filtered_lines.append(line)

                        new_content = '\n'.join(filtered_lines).strip()

                        if new_content:
                            temp_file = "/tmp/cleaned_crontab"
                            with open(temp_file, 'w') as f:
                                f.write(new_content + '\n')
                            subprocess.run(['crontab', temp_file])
                            os.remove(temp_file)
                        else:
                            subprocess.run(['crontab', '-r'])

                        print("✅ 预计算和市宽计算相关任务已删除")
                    else:
                        print("ℹ️  当前没有 crontab 配置")
                except Exception as e:
                    print(f"❌ 删除任务失败: {e}")

            elif choice == '8':
                print("👋 退出配置工具")
                break

            else:
                print("❌ 无效选择，请输入 1-8")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    print("\n📖 更多配置选项请参考:")
    print("   - 预计算系统运行文档.md")
    print("   - 定时任务配置指南.md")

if __name__ == "__main__":
    main()
