#!/usr/bin/env python3
"""
多时间框架预计算数据质量检查脚本
简单快速检查数据完整性和准确性
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

import pymysql
from db_settings import get_default_db_config

def check_data_quality():
    """检查多时间框架预计算数据质量"""
    
    print("🔍 多时间框架预计算数据质量检查")
    print("=" * 50)
    
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    try:
        # 1. 基础统计
        print("\n📊 基础数据统计:")
        
        # MA指标表
        cursor.execute("SELECT COUNT(*) FROM mtf_precomputed_ma_indicators")
        ma_count = cursor.fetchone()[0]
        print(f"   MA指标记录: {ma_count:,}")
        
        # 52周指标表
        cursor.execute("SELECT COUNT(*) FROM mtf_precomputed_52w_indicators")
        w52_count = cursor.fetchone()[0]
        print(f"   52周指标记录: {w52_count:,}")
        
        # 状态表
        cursor.execute("SELECT COUNT(*) FROM mtf_precomputed_status")
        status_count = cursor.fetchone()[0]
        print(f"   状态记录: {status_count:,}")
        
        # 2. 时间框架分布
        print("\n⏰ 各时间框架数据分布:")
        cursor.execute("""
        SELECT timeframe, COUNT(DISTINCT symbol) as symbols, COUNT(*) as records,
               MIN(datetime) as earliest, MAX(datetime) as latest
        FROM mtf_precomputed_ma_indicators 
        GROUP BY timeframe 
        ORDER BY timeframe
        """)
        
        for tf, symbols, records, earliest, latest in cursor.fetchall():
            days_span = (latest - earliest).days if earliest and latest else 0
            print(f"   {tf:>4}: {symbols:>4}只股票, {records:>10,}条记录, {days_span:>3}天 ({earliest} 到 {latest})")
        
        # 3. 数据新鲜度检查
        print("\n🕐 数据新鲜度:")
        today = datetime.now().date()
        
        cursor.execute("SELECT MAX(datetime) FROM mtf_precomputed_ma_indicators")
        latest_ma = cursor.fetchone()[0]
        if latest_ma:
            days_old = (today - latest_ma.date()).days
            print(f"   MA数据最新: {latest_ma.date()} (延迟{days_old}天)")
        
        cursor.execute("SELECT MAX(datetime) FROM mtf_precomputed_52w_indicators")
        latest_52w = cursor.fetchone()[0]
        if latest_52w:
            days_old = (today - latest_52w.date()).days
            print(f"   52周数据最新: {latest_52w.date()} (延迟{days_old}天)")
        
        # 4. 数据完整性检查
        print("\n✅ 数据完整性检查:")
        
        # 检查是否有缺失的时间框架
        expected_timeframes = ['5m', '15m', '1h', '1d', '1w', '1M']
        cursor.execute("SELECT DISTINCT timeframe FROM mtf_precomputed_ma_indicators")
        actual_timeframes = [row[0] for row in cursor.fetchall()]
        
        missing_tf = set(expected_timeframes) - set(actual_timeframes)
        if missing_tf:
            print(f"   ❌ 缺失时间框架: {missing_tf}")
        else:
            print(f"   ✅ 所有时间框架完整: {actual_timeframes}")
        
        # 5. 随机抽样验证
        print("\n🎲 随机抽样验证:")
        
        # 随机选择几只股票检查数据
        cursor.execute("""
        SELECT symbol, timeframe, datetime, close_price, ma50, ma200, above_ma50, above_ma200
        FROM mtf_precomputed_ma_indicators 
        WHERE symbol IN ('AAPL', 'MSFT', 'GOOGL') 
        AND timeframe = '1d' 
        AND datetime >= DATE_SUB(CURDATE(), INTERVAL 5 DAY)
        ORDER BY symbol, datetime DESC
        LIMIT 10
        """)
        
        samples = cursor.fetchall()
        if samples:
            print("   样本数据 (最近5天日线):")
            for symbol, tf, dt, price, ma50, ma200, above50, above200 in samples:
                print(f"     {symbol} {dt}: 价格={price:.2f}, MA50={ma50:.2f}, MA200={ma200:.2f}, "
                      f"上方MA50={above50}, 上方MA200={above200}")
        
        # 6. 数据一致性检查
        print("\n🔄 数据一致性检查:")
        
        # 检查MA和52周数据的匹配度
        cursor.execute("""
        SELECT 
            (SELECT COUNT(DISTINCT CONCAT(symbol, timeframe, DATE(datetime))) 
             FROM mtf_precomputed_ma_indicators) as ma_keys,
            (SELECT COUNT(DISTINCT CONCAT(symbol, timeframe, DATE(datetime))) 
             FROM mtf_precomputed_52w_indicators) as w52_keys
        """)
        
        ma_keys, w52_keys = cursor.fetchone()
        match_rate = (min(ma_keys, w52_keys) / max(ma_keys, w52_keys) * 100) if max(ma_keys, w52_keys) > 0 else 0
        print(f"   MA与52周数据匹配度: {match_rate:.1f}% ({ma_keys:,} vs {w52_keys:,})")
        
        # 7. 性能统计
        print("\n⚡ 计算性能统计:")
        
        # 计算总数据量
        total_records = ma_count + w52_count
        print(f"   总记录数: {total_records:,}")
        
        # 估算存储大小 (粗略估算)
        estimated_size_mb = total_records * 0.0001  # 假设每条记录约0.1KB
        print(f"   估算存储大小: {estimated_size_mb:.1f}MB")
        
        # 8. 市宽计算就绪度
        print("\n🎯 市宽计算就绪度:")
        
        # 检查是否有足够的股票数据进行市宽计算
        cursor.execute("""
        SELECT timeframe, COUNT(DISTINCT symbol) as symbols
        FROM mtf_precomputed_ma_indicators 
        WHERE datetime >= DATE_SUB(CURDATE(), INTERVAL 60 DAY)
        GROUP BY timeframe
        """)
        
        ready_for_breadth = True
        for tf, symbols in cursor.fetchall():
            if symbols >= 1000:  # 至少需要1000只股票
                print(f"   ✅ {tf}: {symbols:,}只股票 (可计算市宽)")
            else:
                print(f"   ⚠️ {tf}: {symbols:,}只股票 (数据不足)")
                ready_for_breadth = False
        
        if ready_for_breadth:
            print(f"\n🚀 系统就绪: 可以开始60天多时间框架市宽计算!")
        else:
            print(f"\n⚠️ 系统未就绪: 需要更多数据才能进行市宽计算")
        
        # 9. 建议的下一步操作
        print("\n💡 建议的下一步操作:")
        if ready_for_breadth:
            print("   1. 执行60天多时间框架市宽计算")
            print("   2. 设置定期数据更新任务")
            print("   3. 配置市宽指标监控")
        else:
            print("   1. 检查数据计算过程中的错误")
            print("   2. 重新运行预计算系统")
            print("   3. 验证数据源连接")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

def quick_sample_check():
    """快速抽样检查"""
    print("\n🔍 快速抽样检查")
    print("-" * 30)
    
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    try:
        # 检查几只知名股票的最新数据
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN']
        
        for symbol in test_symbols:
            cursor.execute("""
            SELECT timeframe, COUNT(*) as records, MAX(datetime) as latest
            FROM mtf_precomputed_ma_indicators 
            WHERE symbol = %s
            GROUP BY timeframe
            ORDER BY timeframe
            """, (symbol,))
            
            results = cursor.fetchall()
            if results:
                print(f"\n📈 {symbol}:")
                for tf, records, latest in results:
                    print(f"   {tf:>4}: {records:>6}条记录, 最新: {latest}")
            else:
                print(f"\n❌ {symbol}: 无数据")
    
    finally:
        conn.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='多时间框架预计算数据质量检查')
    parser.add_argument('--quick', action='store_true', help='快速检查模式')
    parser.add_argument('--sample', action='store_true', help='抽样检查')
    
    args = parser.parse_args()
    
    if args.quick:
        quick_sample_check()
    elif args.sample:
        quick_sample_check()
    else:
        check_data_quality()
        if input("\n是否进行快速抽样检查? (y/N): ").lower() == 'y':
            quick_sample_check()
