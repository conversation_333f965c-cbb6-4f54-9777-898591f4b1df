#!/usr/bin/env python3
"""
Windows任务计划程序配置脚本
为Windows系统配置多时间框架市场广度计算和预计算系统
"""

import os
import sys
import subprocess
from datetime import datetime
import platform

def check_windows_system():
    """检查是否为Windows系统"""
    return platform.system().lower() == 'windows'

def get_project_path():
    """获取项目路径"""
    return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def create_batch_file(script_name, python_script, log_file):
    """创建批处理文件"""
    project_path = get_project_path()
    batch_content = f"""@echo off
cd /d "{project_path}"
python {python_script} >> logs\\{log_file} 2>&1
"""
    
    batch_file = os.path.join(project_path, 'scripts', f'{script_name}.bat')
    
    with open(batch_file, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ 创建批处理文件: {batch_file}")
    return batch_file

def create_powershell_script(script_name, python_script, log_file):
    """创建PowerShell脚本"""
    project_path = get_project_path()
    ps_content = f"""# {script_name} PowerShell脚本
$ProjectPath = "{project_path}"
$LogPath = "$ProjectPath\\logs"

# 确保日志目录存在
if (!(Test-Path $LogPath)) {{
    New-Item -ItemType Directory -Path $LogPath
}}

# 切换到项目目录
Set-Location $ProjectPath

# 记录开始时间
$StartTime = Get-Date
Write-Output "[$StartTime] 开始执行 {script_name}" | Out-File -Append "$LogPath\\{log_file}"

try {{
    # 执行Python脚本
    python {python_script} 2>&1 | Out-File -Append "$LogPath\\{log_file}"
    
    $EndTime = Get-Date
    $Duration = $EndTime - $StartTime
    Write-Output "[$EndTime] 执行完成，耗时: $($Duration.TotalSeconds) 秒" | Out-File -Append "$LogPath\\{log_file}"
    
}} catch {{
    $ErrorTime = Get-Date
    Write-Output "[$ErrorTime] 执行失败: $($_.Exception.Message)" | Out-File -Append "$LogPath\\{log_file}"
}}
"""
    
    ps_file = os.path.join(project_path, 'scripts', f'{script_name}.ps1')
    
    with open(ps_file, 'w', encoding='utf-8') as f:
        f.write(ps_content)
    
    print(f"✅ 创建PowerShell脚本: {ps_file}")
    return ps_file

def create_task_xml(task_name, script_path, trigger_time, description):
    """创建任务计划程序XML配置"""
    xml_content = f"""<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>{datetime.now().strftime('%Y-%m-%dT%H:%M:%S')}</Date>
    <Author>BreadthPulse System</Author>
    <Description>{description}</Description>
  </RegistrationInfo>
  <Triggers>
    <CalendarTrigger>
      <StartBoundary>{datetime.now().strftime('%Y-%m-%d')}T{trigger_time}:00</StartBoundary>
      <Enabled>true</Enabled>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>LeastPrivilege</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>true</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT2H</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>powershell</Command>
      <Arguments>-ExecutionPolicy Bypass -File "{script_path}"</Arguments>
    </Exec>
  </Actions>
</Task>"""
    
    return xml_content

def setup_market_breadth_tasks():
    """配置市场广度计算任务"""
    print("\n🔧 配置市场广度计算任务...")
    
    # 创建PowerShell脚本
    ps_file = create_powershell_script(
        "market_breadth_daily",
        "scripts/run_all_timeframes.py",
        "market_breadth_daily.log"
    )
    
    # 创建批处理文件（备用）
    bat_file = create_batch_file(
        "market_breadth_daily",
        "scripts/run_all_timeframes.py", 
        "market_breadth_daily.log"
    )
    
    print("\n📋 任务配置完成！")
    print("📅 建议的运行时间:")
    print("   - 每日 5:30 AM: 市场广度计算")
    print("   - 每周日 2:00 AM: 额外保障运行")
    
    return ps_file, bat_file

def setup_precomputed_tasks():
    """配置预计算系统任务"""
    print("\n🔧 配置预计算系统任务...")
    
    # 创建PowerShell脚本
    ps_file = create_powershell_script(
        "precomputed_update",
        "scripts/setup_precomputed_system.py --update",
        "precomputed_update.log"
    )
    
    # 创建批处理文件（备用）
    bat_file = create_batch_file(
        "precomputed_update",
        "scripts/setup_precomputed_system.py --update",
        "precomputed_update.log"
    )
    
    print("\n📋 任务配置完成！")
    print("📅 建议的运行时间:")
    print("   - 每日 5:30 AM: 预计算数据更新")
    print("   - 每日 6:00 AM: 状态检查")
    
    return ps_file, bat_file

def generate_manual_setup_guide(ps_files):
    """生成手动配置指南"""
    guide_content = f"""# Windows任务计划程序手动配置指南

## 🎯 配置步骤

### 1. 打开任务计划程序
- 按 `Win + R`，输入 `taskschd.msc`
- 或在开始菜单搜索"任务计划程序"

### 2. 创建基本任务

#### 市场广度计算任务
1. 右键点击"任务计划程序库" → "创建基本任务"
2. **名称**: `市场广度计算-每日`
3. **描述**: `每日计算多时间框架市场广度指标`
4. **触发器**: 每天
5. **开始时间**: `05:30:00`
6. **操作**: 启动程序
   - **程序**: `powershell`
   - **参数**: `-ExecutionPolicy Bypass -File "{ps_files[0]}"`
   - **起始于**: `{get_project_path()}`

#### 预计算系统任务（可选）
1. 右键点击"任务计划程序库" → "创建基本任务"
2. **名称**: `预计算系统更新-每日`
3. **描述**: `每日更新预计算指标数据`
4. **触发器**: 每天
5. **开始时间**: `05:30:00`
6. **操作**: 启动程序
   - **程序**: `powershell`
   - **参数**: `-ExecutionPolicy Bypass -File "{ps_files[1] if len(ps_files) > 1 else ps_files[0]}"`
   - **起始于**: `{get_project_path()}`

## 🚀 快速测试

### 手动运行PowerShell脚本
```powershell
# 测试市场广度计算
powershell -ExecutionPolicy Bypass -File "{ps_files[0]}"

# 测试预计算更新（如果配置了）
{"powershell -ExecutionPolicy Bypass -File " + ps_files[1] if len(ps_files) > 1 else "# 未配置预计算任务"}
```

### 手动运行批处理文件
```cmd
# 测试市场广度计算
{get_project_path()}\\scripts\\market_breadth_daily.bat

# 测试预计算更新（如果配置了）
{"# " + get_project_path() + "\\scripts\\precomputed_update.bat" if len(ps_files) > 1 else "# 未配置预计算任务"}
```

## 📊 验证配置

### 检查日志文件
- 市场广度计算日志: `logs/market_breadth_daily.log`
- 预计算更新日志: `logs/precomputed_update.log`

### 检查数据库
```python
# 验证市场广度数据
python -c "
import pymysql
import sys, os
sys.path.insert(0, 'config')
from db_settings import get_default_db_config

config = get_default_db_config()
conn = pymysql.connect(**config)
cursor = conn.cursor()

cursor.execute('SELECT timeframe, COUNT(*) FROM market_breadth_metrics_gics GROUP BY timeframe')
results = cursor.fetchall()

print('市场广度数据统计:')
for timeframe, count in results:
    print(f'  {{timeframe}}: {{count}} 条记录')

conn.close()
"
```

## ⚠️ 注意事项

1. **执行策略**: 确保PowerShell执行策略允许运行脚本
2. **网络连接**: 任务需要网络连接获取股价数据
3. **运行权限**: 确保任务有足够权限访问项目目录和数据库
4. **时间设置**: 建议在美股收盘后运行（北京时间5:30）

## 🔧 故障排除

### 常见问题
1. **PowerShell执行策略错误**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **路径问题**: 确保所有路径使用绝对路径

3. **权限问题**: 以管理员身份运行任务计划程序

---

**配置完成后，系统将每日自动计算市场广度指标，为Web界面的轮动分析提供数据支持。**
"""
    
    guide_file = os.path.join(get_project_path(), "Windows任务配置指南.md")
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"📖 配置指南已保存到: {guide_file}")
    return guide_file

def main():
    """主函数"""
    print("🚀 Windows任务计划程序配置工具")
    print("=" * 50)
    
    if not check_windows_system():
        print("❌ 此脚本仅适用于Windows系统")
        return
    
    project_path = get_project_path()
    print(f"📁 项目路径: {project_path}")
    
    # 确保日志目录存在
    log_dir = os.path.join(project_path, 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        print(f"✅ 创建日志目录: {log_dir}")
    
    print("\n📋 配置选项:")
    print("1. 市场广度计算配置 (推荐)")
    print("2. 预计算系统配置")
    print("3. 完整配置 (市场广度 + 预计算)")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n🔧 配置市场广度计算...")
                ps_files = [setup_market_breadth_tasks()[0]]
                guide_file = generate_manual_setup_guide(ps_files)
                
                print("\n✅ 市场广度计算配置完成!")
                print("📊 功能: 每日计算6个时间框架的市场广度指标")
                print("🎯 用途: 为Web界面轮动分析提供数据基础")
                break
                
            elif choice == '2':
                print("\n🔧 配置预计算系统...")
                ps_files = [setup_precomputed_tasks()[0]]
                guide_file = generate_manual_setup_guide(ps_files)
                
                print("\n✅ 预计算系统配置完成!")
                print("⚡ 功能: 预计算MA指标和52周新高新低")
                print("🎯 用途: 加速Web界面响应速度")
                break
                
            elif choice == '3':
                print("\n🔧 配置完整系统...")
                mb_files = setup_market_breadth_tasks()
                pc_files = setup_precomputed_tasks()
                ps_files = [mb_files[0], pc_files[0]]
                guide_file = generate_manual_setup_guide(ps_files)
                
                print("\n✅ 完整系统配置完成!")
                print("📊 功能: 市场广度计算 + 预计算系统")
                print("🎯 用途: 完整的轮动分析系统")
                break
                
            elif choice == '4':
                print("👋 退出配置工具")
                return
                
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            return
        except Exception as e:
            print(f"❌ 操作失败: {e}")
    
    print(f"\n📖 下一步: 请参考配置指南手动设置Windows任务计划程序")
    print(f"📄 配置指南: {guide_file}")
    print("\n💡 快速测试:")
    print("1. 手动运行生成的PowerShell脚本测试功能")
    print("2. 配置Windows任务计划程序实现自动化")
    print("3. 启动Web界面查看轮动分析结果")

if __name__ == "__main__":
    main()
