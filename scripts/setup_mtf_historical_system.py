#!/usr/bin/env python3
"""
多时间框架历史数据预计算系统设置脚本
支持3个月历史数据的快速计算和管理
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from mtf_precomputed_system import MTFPrecomputedSystem
from mtf_historical_config import (
    get_preset_plan, list_preset_plans, calculate_optimal_history_period,
    get_timeframes_by_priority, get_incremental_update_config
)

class MTFHistoricalManager:
    """多时间框架历史数据管理器"""
    
    def __init__(self):
        self.system = MTFPrecomputedSystem()
        self.logger = self.system.logger
    
    def show_preset_plans(self):
        """显示预设计算方案"""
        print("📋 可用的历史数据计算方案:")
        print("=" * 60)
        
        plans = list_preset_plans()
        for i, plan in enumerate(plans, 1):
            config = plan['config']
            print(f"\n{i}. {config['name']}")
            print(f"   描述: {config['description']}")
            print(f"   时间框架: {', '.join(config['timeframes'])}")
            print(f"   历史月数: {config['history_months']}个月")
            print(f"   预计耗时: {config['estimated_time_minutes']}分钟")
        
        print(f"\n💡 推荐方案:")
        print(f"   • 首次使用: 选择 '1. 快速启动'")
        print(f"   • 生产环境: 选择 '2. 标准配置' 或 '3. 全面配置'")
        print(f"   • 日内交易: 选择 '4. 日内重点'")
        print(f"   • 长期投资: 选择 '5. 长期重点'")
    
    def execute_preset_plan(self, plan_name: str, symbols: list = None):
        """执行预设计算方案"""
        try:
            plan = get_preset_plan(plan_name)
            
            print(f"🚀 执行计算方案: {plan['name']}")
            print(f"   {plan['description']}")
            print("=" * 60)
            
            # 显示计算详情
            total_timeframes = len(plan['timeframes'])
            print(f"📊 计算详情:")
            print(f"   时间框架数量: {total_timeframes}")
            print(f"   历史数据月数: {plan['history_months']}")
            print(f"   预计总耗时: {plan['estimated_time_minutes']}分钟")
            print(f"   并行处理: {'是' if plan['parallel_processing'] else '否'}")
            
            if symbols:
                print(f"   指定股票: {len(symbols)}只")
            else:
                print(f"   股票范围: 全部股票")
            
            # 按优先级排序时间框架
            prioritized_timeframes = []
            for tf in get_timeframes_by_priority():
                if tf in plan['timeframes']:
                    prioritized_timeframes.append(tf)
            
            print(f"\n🔄 执行顺序: {' → '.join(prioritized_timeframes)}")
            
            # 确认执行
            if not self._confirm_execution(plan):
                print("❌ 用户取消执行")
                return False
            
            # 开始执行
            start_time = time.time()
            
            results = self.system.calculate_historical_data(
                symbols=symbols,
                timeframes=prioritized_timeframes,
                history_months=plan['history_months'],
                force_recalculate=False
            )
            
            end_time = time.time()
            actual_time = (end_time - start_time) / 60  # 转换为分钟
            
            # 显示结果
            self._show_execution_results(results, actual_time, plan['estimated_time_minutes'])
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行计算方案失败: {e}")
            print(f"❌ 执行失败: {e}")
            return False
    
    def calculate_custom_history(self, timeframes: list, months: int, symbols: list = None, max_workers: int = 1):
        """自定义历史数据计算"""
        print(f"🎯 自定义历史数据计算")
        print("=" * 60)
        
        # 显示计算计划
        print(f"📊 计算计划:")
        print(f"   时间框架: {', '.join(timeframes)}")
        print(f"   历史月数: {months}个月")
        
        total_estimated_time = 0
        
        print(f"\n📋 各时间框架详情:")
        for tf in timeframes:
            try:
                period_info = calculate_optimal_history_period(tf, months)
                print(f"   {tf:>4}: {period_info['optimal_days']:>3}天历史, "
                      f"约{period_info['expected_points']:>6,}个数据点")
                
                # 估算时间（粗略）
                if tf in ['5m', '15m']:
                    estimated_minutes = period_info['expected_points'] / 1000 * 0.1
                elif tf in ['1h']:
                    estimated_minutes = period_info['expected_points'] / 1000 * 0.2
                else:
                    estimated_minutes = period_info['expected_points'] / 1000 * 0.5
                
                total_estimated_time += estimated_minutes
                
            except Exception as e:
                print(f"   {tf:>4}: 配置错误 - {e}")
        
        print(f"\n⏱️  预计总耗时: {total_estimated_time:.1f}分钟")
        
        if symbols:
            print(f"📈 指定股票: {len(symbols)}只")
        else:
            print(f"📈 股票范围: 全部股票")
        
        # 确认执行
        if not self._confirm_execution({'estimated_time_minutes': total_estimated_time}):
            print("❌ 用户取消执行")
            return False
        
        # 执行计算
        start_time = time.time()
        
        results = self.system.calculate_historical_data(
            symbols=symbols,
            timeframes=timeframes,
            history_months=months,
            force_recalculate=False,
            max_workers=max_workers
        )
        
        end_time = time.time()
        actual_time = (end_time - start_time) / 60
        
        # 显示结果
        self._show_execution_results(results, actual_time, total_estimated_time)
        
        return True
    
    def show_system_status(self):
        """显示系统状态"""
        print("📊 多时间框架预计算系统状态")
        print("=" * 60)
        
        status = self.system.get_system_status()
        
        print(f"📈 总体统计:")
        print(f"   总股票数: {status['total_symbols']:,}")
        print(f"   总记录数: {status['total_records']:,}")
        print(f"   最后更新: {status['last_update']}")
        
        print(f"\n📋 各时间框架状态:")
        print(f"{'时间框架':>6} {'股票数':>8} {'记录数':>12} {'完成率':>8} {'最后更新':>20}")
        print("-" * 60)
        
        for tf in get_timeframes_by_priority():
            if tf in status['timeframes']:
                tf_status = status['timeframes'][tf]
                print(f"{tf:>6} {tf_status['symbols']:>8,} {tf_status['records']:>12,} "
                      f"{tf_status['completion_rate']:>7.1f}% {str(tf_status['last_update'])[:19]:>20}")
            else:
                print(f"{tf:>6} {'未初始化':>8} {'0':>12} {'0.0%':>8} {'无':>20}")
    
    def incremental_update(self, timeframes: list = None):
        """增量更新"""
        if timeframes is None:
            timeframes = get_timeframes_by_priority()
        
        print(f"🔄 增量更新")
        print("=" * 60)
        
        print(f"📋 更新时间框架: {', '.join(timeframes)}")
        
        total_updated = 0
        
        for tf in timeframes:
            print(f"\n🔄 更新 {tf}...")
            
            try:
                update_config = get_incremental_update_config(tf)
                
                # 计算更新时间范围
                lookback_hours = update_config['lookback_hours']
                start_time = datetime.now() - timedelta(hours=lookback_hours)
                
                print(f"   更新范围: {start_time.strftime('%Y-%m-%d %H:%M')} 至今")
                
                # 执行增量更新（这里简化为重新计算最近数据）
                results = self.system.calculate_historical_data(
                    symbols=None,
                    timeframes=[tf],
                    history_months=1,  # 只更新最近1个月
                    force_recalculate=True
                )
                
                tf_result = results['timeframe_results'][tf]
                print(f"   ✅ 更新完成: 成功{tf_result['success_count']}, 失败{tf_result['failed_count']}")
                total_updated += tf_result['success_count']
                
            except Exception as e:
                print(f"   ❌ 更新失败: {e}")
        
        print(f"\n✅ 增量更新完成，总计更新 {total_updated} 个股票时间框架")
    
    def _confirm_execution(self, plan: dict) -> bool:
        """确认执行"""
        estimated_time = plan.get('estimated_time_minutes', 0)
        
        if estimated_time > 60:
            print(f"\n⚠️  注意: 预计耗时较长 ({estimated_time:.1f}分钟)")
            print(f"   建议在系统空闲时执行")
        
        while True:
            response = input(f"\n❓ 确认执行? (y/n): ").strip().lower()
            if response in ['y', 'yes', '是']:
                return True
            elif response in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
    
    def _show_execution_results(self, results: dict, actual_time: float, estimated_time: float):
        """显示执行结果"""
        print(f"\n✅ 计算完成!")
        print("=" * 60)
        
        print(f"📊 总体结果:")
        print(f"   成功: {results['success_count']:,}")
        print(f"   失败: {results['failed_count']:,}")
        print(f"   实际耗时: {actual_time:.1f}分钟")
        print(f"   预计耗时: {estimated_time:.1f}分钟")
        
        if actual_time > 0 and estimated_time > 0:
            efficiency = estimated_time / actual_time * 100
            print(f"   时间效率: {efficiency:.1f}%")
        
        print(f"\n📋 各时间框架结果:")
        for tf, tf_result in results['timeframe_results'].items():
            print(f"   {tf:>4}: 成功{tf_result['success_count']:>4}, "
                  f"失败{tf_result['failed_count']:>3}, "
                  f"记录{tf_result['total_records']:>8,}")
        
        # 建议后续操作
        print(f"\n💡 后续建议:")
        if results['failed_count'] > 0:
            print(f"   • 检查失败原因，考虑重新计算失败的股票")
        
        print(f"   • 运行 --status 检查系统状态")
        print(f"   • 配置定时任务进行增量更新")
        print(f"   • 测试预计算数据的查询性能")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多时间框架历史数据预计算系统')
    parser.add_argument('--list-plans', action='store_true', help='列出预设计算方案')
    parser.add_argument('--plan', type=str, help='执行预设计算方案')
    parser.add_argument('--custom', action='store_true', help='自定义计算')
    parser.add_argument('--timeframes', nargs='+', help='指定时间框架')
    parser.add_argument('--months', type=int, default=3, help='历史数据月数')
    parser.add_argument('--symbols', nargs='+', help='指定股票代码')
    parser.add_argument('--status', action='store_true', help='查看系统状态')
    parser.add_argument('--update', action='store_true', help='增量更新')
    parser.add_argument('--workers', type=int, default=1, help='并行进程数 (默认1，单进程)')
    
    args = parser.parse_args()
    
    manager = MTFHistoricalManager()
    
    if args.list_plans:
        manager.show_preset_plans()
    
    elif args.plan:
        manager.execute_preset_plan(args.plan, args.symbols)
    
    elif args.custom:
        if not args.timeframes:
            print("❌ 自定义计算需要指定 --timeframes")
            return
        manager.calculate_custom_history(args.timeframes, args.months, args.symbols, args.workers)
    
    elif args.status:
        manager.show_system_status()
    
    elif args.update:
        manager.incremental_update(args.timeframes)
    
    else:
        print("🎯 多时间框架历史数据预计算系统")
        print("=" * 50)
        print()
        print("📋 可用操作:")
        print("  --list-plans     : 列出预设计算方案")
        print("  --plan <方案名>   : 执行预设方案")
        print("  --custom         : 自定义计算")
        print("  --status         : 查看系统状态")
        print("  --update         : 增量更新")
        print()
        print("📖 使用示例:")
        print("  # 查看可用方案")
        print("  python scripts/setup_mtf_historical_system.py --list-plans")
        print()
        print("  # 执行快速启动方案")
        print("  python scripts/setup_mtf_historical_system.py --plan quick_start")
        print()
        print("  # 自定义计算3个月的日线和小时线数据")
        print("  python scripts/setup_mtf_historical_system.py --custom --timeframes 1d 1h --months 3")
        print()
        print("  # 查看系统状态")
        print("  python scripts/setup_mtf_historical_system.py --status")


if __name__ == "__main__":
    main()
