#!/usr/bin/env python3
"""
运行市场广度计算并真正保存到数据库
"""

import sys
import os
import time

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from market_breadth_calculator import calculate_all_markets_breadth
import pymysql
from db_settings import get_default_db_config

def save_5m_data():
    """保存5分钟数据"""
    print("🚀 运行5分钟市场广度计算并保存到数据库...")
    
    start_time = time.time()
    results = calculate_all_markets_breadth('5m', save_to_db=True)
    calc_time = time.time() - start_time
    
    if results:
        print(f"✅ 5分钟计算完成，处理了 {len(results)} 个市场 (耗时: {calc_time:.1f}秒)")
        print("样本结果:")
        for i, result in enumerate(results[:3]):
            market = result.get('market', 'Unknown')
            advances = result.get('advances', 0)
            declines = result.get('declines', 0)
            health = result.get('internal_health', 0)
            print(f"  {market}: {advances}↑ {declines}↓ health={health:.1f}")
        return True
    else:
        print("❌ 5分钟计算失败")
        return False

def save_1h_data():
    """保存1小时数据"""
    print("\n🚀 运行1小时市场广度计算并保存到数据库...")
    
    start_time = time.time()
    results = calculate_all_markets_breadth('1h', save_to_db=True)
    calc_time = time.time() - start_time
    
    if results:
        print(f"✅ 1小时计算完成，处理了 {len(results)} 个市场 (耗时: {calc_time:.1f}秒)")
        print("样本结果:")
        for i, result in enumerate(results[:3]):
            market = result.get('market', 'Unknown')
            advances = result.get('advances', 0)
            declines = result.get('declines', 0)
            health = result.get('internal_health', 0)
            print(f"  {market}: {advances}↑ {declines}↓ health={health:.1f}")
        return True
    else:
        print("❌ 1小时计算失败")
        return False

def verify_data_saved():
    """验证数据是否真的保存了"""
    print("\n🔍 验证数据是否保存到数据库...")
    
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    try:
        # 检查5分钟数据
        cursor.execute("SELECT COUNT(*) FROM market_breadth_metrics_gics WHERE timeframe = '5m'")
        count_5m = cursor.fetchone()[0]
        
        # 检查1小时数据
        cursor.execute("SELECT COUNT(*) FROM market_breadth_metrics_gics WHERE timeframe = '1h'")
        count_1h = cursor.fetchone()[0]
        
        print(f"数据库中的数据:")
        print(f"  5分钟数据: {count_5m} 条")
        print(f"  1小时数据: {count_1h} 条")
        
        if count_5m > 0 or count_1h > 0:
            # 显示最新的几条数据
            cursor.execute("""
            SELECT market, timeframe, advances, declines, internal_health, recorded_at 
            FROM market_breadth_metrics_gics 
            ORDER BY recorded_at DESC 
            LIMIT 5
            """)
            recent_data = cursor.fetchall()
            
            print("\n最新保存的数据:")
            for row in recent_data:
                print(f"  {row[0]} ({row[1]}): {row[2]}↑ {row[3]}↓ health={row[4]:.1f} time={row[5]}")
            
            return True
        else:
            print("❌ 没有找到任何保存的数据")
            return False
            
    except Exception as e:
        print(f"❌ 验证数据时出错: {e}")
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("📊 市场广度数据保存测试")
    print("=" * 50)
    
    success_count = 0
    
    # 1. 保存5分钟数据
    if save_5m_data():
        success_count += 1
    
    # 2. 保存1小时数据
    if save_1h_data():
        success_count += 1
    
    # 3. 验证数据保存
    if verify_data_saved():
        print(f"\n🎉 数据保存成功！完成了 {success_count}/2 个时间框架的计算")
    else:
        print(f"\n❌ 数据保存可能有问题，请检查日志")
    
    print("\n📋 说明:")
    print("- 如果看到数据保存成功，说明预计算系统正常工作")
    print("- 之前的测试使用了 save_to_db=False，所以没有保存数据")
    print("- 现在的计算使用了 save_to_db=True，数据会真正保存到数据库")

if __name__ == "__main__":
    main()
