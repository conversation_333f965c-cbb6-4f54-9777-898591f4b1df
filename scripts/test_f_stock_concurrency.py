#!/usr/bin/env python3
"""
专门测试F股票的并发问题
"""

import sys
import os
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

def test_f_stock_worker():
    """测试F股票的worker函数"""
    try:
        from mtf_precomputed_system import process_single_symbol_mtf_worker
        
        # 使用与实际测试相同的参数
        symbol = 'F'
        timeframe = '1d'
        start_date = datetime.now() - timedelta(days=18*30)  # 18个月
        end_date = datetime.now()
        force_recalculate = False
        
        task_data = (symbol, timeframe, start_date, end_date, force_recalculate)
        
        # 执行worker
        result = process_single_symbol_mtf_worker(task_data)
        
        return result
        
    except Exception as e:
        import traceback
        return {
            'symbol': 'F',
            'timeframe': '1d',
            'success': False,
            'error': f"{str(e)}\n{traceback.format_exc()}",
            'records': 0,
            'total_calculated': 0
        }

def test_f_multiple_times(times=10):
    """多次测试F股票"""
    print(f"🧪 连续测试F股票 {times} 次")
    print("-" * 40)
    
    success_count = 0
    for i in range(times):
        print(f"   第 {i+1} 次测试...", end=" ")
        result = test_f_stock_worker()
        
        if result['success']:
            print(f"✅ 成功 ({result['records']}条记录)")
            success_count += 1
        else:
            print(f"❌ 失败")
            error_msg = result.get('error', 'Unknown error')
            error_line = error_msg.split('\n')[0]
            print(f"      错误: {error_line}")
    
    print(f"\n   连续测试结果: {success_count}/{times} 成功 ({success_count/times*100:.1f}%)")
    return success_count == times

def test_f_concurrent(workers=10, iterations=5):
    """并发测试F股票"""
    print(f"\n🧪 并发测试F股票 ({workers}个进程, {iterations}轮)")
    print("-" * 40)
    
    total_tests = 0
    total_success = 0
    
    for iteration in range(iterations):
        print(f"   第 {iteration+1} 轮并发测试...")
        
        start_time = time.time()
        results = []
        
        with ProcessPoolExecutor(max_workers=workers) as executor:
            # 提交多个F股票任务
            futures = [executor.submit(test_f_stock_worker) for _ in range(workers)]
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
        
        elapsed = time.time() - start_time
        success_count = sum(1 for r in results if r['success'])
        
        print(f"      结果: {success_count}/{workers} 成功, 耗时: {elapsed:.2f}秒")
        
        total_tests += workers
        total_success += success_count
        
        # 显示失败的详情
        failures = [r for r in results if not r['success']]
        if failures:
            for failure in failures:
                error_msg = failure.get('error', 'Unknown error')
                error_line = error_msg.split('\n')[0]
                print(f"         失败原因: {error_line}")
    
    print(f"\n   总体并发测试结果: {total_success}/{total_tests} 成功 ({total_success/total_tests*100:.1f}%)")
    return total_success == total_tests

def test_f_vs_other_stocks():
    """测试F股票与其他股票的并发表现"""
    print(f"\n🧪 F股票 vs 其他股票并发对比")
    print("-" * 40)
    
    # 测试股票列表：包含F和其他股票
    test_symbols = ['F', 'AAPL', 'MSFT', 'F', 'GOOGL', 'F', 'TSLA', 'F', 'NVDA', 'F']
    
    def test_single_stock(symbol):
        try:
            from mtf_precomputed_system import process_single_symbol_mtf_worker
            
            timeframe = '1d'
            start_date = datetime.now() - timedelta(days=18*30)
            end_date = datetime.now()
            force_recalculate = False
            
            task_data = (symbol, timeframe, start_date, end_date, force_recalculate)
            result = process_single_symbol_mtf_worker(task_data)
            
            return result
            
        except Exception as e:
            import traceback
            return {
                'symbol': symbol,
                'timeframe': '1d',
                'success': False,
                'error': f"{str(e)}\n{traceback.format_exc()}",
                'records': 0,
                'total_calculated': 0
            }
    
    start_time = time.time()
    results = []
    
    with ProcessPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(test_single_stock, symbol) for symbol in test_symbols]
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    elapsed = time.time() - start_time
    
    # 分析结果
    f_results = [r for r in results if r['symbol'] == 'F']
    other_results = [r for r in results if r['symbol'] != 'F']
    
    f_success = sum(1 for r in f_results if r['success'])
    other_success = sum(1 for r in other_results if r['success'])
    
    print(f"   F股票: {f_success}/{len(f_results)} 成功 ({f_success/len(f_results)*100:.1f}%)")
    print(f"   其他股票: {other_success}/{len(other_results)} 成功 ({other_success/len(other_results)*100:.1f}%)")
    print(f"   总耗时: {elapsed:.2f}秒")
    
    # 显示F股票的失败详情
    f_failures = [r for r in f_results if not r['success']]
    if f_failures:
        print(f"\n   F股票失败详情:")
        for failure in f_failures:
            error_msg = failure.get('error', 'Unknown error')
            error_line = error_msg.split('\n')[0]
            print(f"      {error_line}")

def main():
    """主函数"""
    print("🔧 F股票并发问题专项测试")
    print("=" * 60)
    
    # 测试1: 连续单独测试
    sequential_ok = test_f_multiple_times(10)
    
    # 测试2: 并发测试
    concurrent_ok = test_f_concurrent(workers=10, iterations=3)
    
    # 测试3: 与其他股票对比
    test_f_vs_other_stocks()
    
    print("\n" + "=" * 60)
    print("📊 测试结论:")
    
    if sequential_ok:
        print("✅ F股票单独测试100%成功 - 数据本身没问题")
    else:
        print("❌ F股票单独测试有问题 - 可能是数据问题")
    
    if not concurrent_ok:
        print("❌ F股票并发测试有问题 - 确认是并发竞争问题")
        print("💡 建议:")
        print("   1. 减少并发worker数量")
        print("   2. 添加重试机制")
        print("   3. 增加数据库连接池大小")
        print("   4. 添加随机延迟避免竞争")
    else:
        print("✅ F股票并发测试正常 - 可能是偶发问题")

if __name__ == "__main__":
    main()
