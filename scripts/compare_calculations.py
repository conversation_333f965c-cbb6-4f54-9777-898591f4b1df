#!/usr/bin/env python3
"""
对比市场广度计算器和hist_data.py的计算结果
"""

import sys
import os
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from market_breadth_calculator import calculate_all_markets_breadth
import pymysql
from db_settings import get_default_db_config

def get_hist_data_results():
    """获取hist_data.py的历史计算结果"""
    config = get_default_db_config()
    conn = pymysql.connect(**config)
    cursor = conn.cursor()
    
    # 获取最近的历史数据（1小时数据，因为hist_data.py通常计算历史数据）
    cursor.execute("""
    SELECT market, total_stocks, advances, declines, unchanged,
           internal_health, purity, momentum_coherence, recorded_at
    FROM market_breadth_metrics_gics 
    WHERE timeframe = '1h' 
    AND recorded_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
    ORDER BY recorded_at DESC, market
    LIMIT 10
    """)
    
    hist_results = cursor.fetchall()
    conn.close()
    
    return hist_results

def get_current_results():
    """获取当前计算器的结果"""
    print("🔄 运行当前市场广度计算器...")
    results = calculate_all_markets_breadth('1h', save_to_db=False)
    return results

def compare_results():
    """对比计算结果"""
    print("📊 对比市场广度计算结果")
    print("=" * 60)
    
    # 获取历史数据
    print("\n📈 获取hist_data.py的历史结果...")
    hist_results = get_hist_data_results()
    
    if not hist_results:
        print("❌ 没有找到hist_data.py的历史数据")
        return
    
    print(f"✅ 找到 {len(hist_results)} 条历史记录")
    
    # 获取当前计算结果
    current_results = get_current_results()
    
    if not current_results:
        print("❌ 当前计算器没有返回结果")
        return
    
    print(f"✅ 当前计算器返回 {len(current_results)} 个市场结果")
    
    # 创建当前结果的字典，便于查找
    current_dict = {result['market']: result for result in current_results}
    
    print("\n🔍 详细对比分析:")
    print("-" * 60)
    
    for hist_row in hist_results[:5]:  # 只对比前5个
        market = hist_row[0]
        hist_total_stocks = hist_row[1]
        hist_advances = hist_row[2]
        hist_declines = hist_row[3]
        hist_unchanged = hist_row[4]
        hist_health = float(hist_row[5])
        hist_purity = float(hist_row[6])
        hist_momentum = float(hist_row[7]) if hist_row[7] else 0.0
        hist_time = hist_row[8]
        
        if market in current_dict:
            current = current_dict[market]
            
            print(f"\n📍 市场: {market}")
            print(f"   历史数据时间: {hist_time}")
            print(f"   当前计算时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            print(f"   📊 基础指标对比:")
            print(f"      总股票数: 历史={hist_total_stocks}, 当前={current.get('total_stocks', 'N/A')}")
            print(f"      上涨数量: 历史={hist_advances}, 当前={current.get('advances', 'N/A')}")
            print(f"      下跌数量: 历史={hist_declines}, 当前={current.get('declines', 'N/A')}")
            print(f"      不变数量: 历史={hist_unchanged}, 当前={current.get('unchanged', 'N/A')}")
            
            print(f"   🏥 健康度对比:")
            current_health = current.get('internal_health', 0)
            health_diff = abs(hist_health - current_health)
            print(f"      内部健康度: 历史={hist_health:.1f}, 当前={current_health:.1f}, 差异={health_diff:.1f}")
            
            print(f"   📈 其他指标对比:")
            current_purity = current.get('purity', 0)
            purity_diff = abs(hist_purity - current_purity)
            print(f"      纯度指标: 历史={hist_purity:.3f}, 当前={current_purity:.3f}, 差异={purity_diff:.3f}")
            
            current_momentum = current.get('momentum_coherence', 0)
            momentum_diff = abs(hist_momentum - current_momentum)
            print(f"      动量一致性: 历史={hist_momentum:.3f}, 当前={current_momentum:.3f}, 差异={momentum_diff:.3f}")
            
            # 分析差异原因
            if health_diff > 5:
                print(f"   ⚠️  健康度差异较大 ({health_diff:.1f})")
                if hist_total_stocks != current.get('total_stocks', 0):
                    print(f"      可能原因: 股票数量不同")
                if abs(hist_advances - current.get('advances', 0)) > 10:
                    print(f"      可能原因: 涨跌数据不同")
            else:
                print(f"   ✅ 健康度差异在合理范围内")
        else:
            print(f"\n❌ 市场 {market} 在当前计算结果中未找到")

def main():
    """主函数"""
    compare_results()

if __name__ == "__main__":
    main()
