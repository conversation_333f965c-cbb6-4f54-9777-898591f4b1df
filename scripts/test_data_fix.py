#!/usr/bin/env python3
"""
测试数据修复的脚本
"""

import sys
import os
import pymysql

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

from db_settings import get_default_db_config

def check_data_before_fix():
    """检查修复前的数据"""
    print("🔍 检查修复前的数据...")
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 检查MA指标表
        cursor.execute("""
        SELECT symbol, close_price, high_price, low_price, volume, ma5, ma10, ma20
        FROM mtf_precomputed_ma_indicators 
        WHERE symbol = 'AAPL' 
        ORDER BY datetime DESC 
        LIMIT 5
        """)
        
        ma_results = cursor.fetchall()
        print(f"   MA指标表记录数: {len(ma_results)}")
        
        if ma_results:
            print("   前5条MA记录:")
            for i, row in enumerate(ma_results):
                symbol, close_price, high_price, low_price, volume, ma5, ma10, ma20 = row
                print(f"     {i+1}. close={close_price}, high={high_price}, low={low_price}, volume={volume}")
                print(f"        ma5={ma5}, ma10={ma10}, ma20={ma20}")
        
        # 检查52周指标表
        cursor.execute("""
        SELECT symbol, close_price, high_price, low_price, volume, high_52w, low_52w
        FROM mtf_precomputed_52w_indicators 
        WHERE symbol = 'AAPL' 
        ORDER BY datetime DESC 
        LIMIT 5
        """)
        
        w52_results = cursor.fetchall()
        print(f"   52周指标表记录数: {len(w52_results)}")
        
        if w52_results:
            print("   前5条52周记录:")
            for i, row in enumerate(w52_results):
                symbol, close_price, high_price, low_price, volume, high_52w, low_52w = row
                print(f"     {i+1}. close={close_price}, high={high_price}, low={low_price}, volume={volume}")
                print(f"        high_52w={high_52w}, low_52w={low_52w}")
        
    finally:
        conn.close()

def test_single_stock_fix():
    """测试单只股票的修复"""
    print("\n🧪 测试单只股票修复...")
    
    from mtf_precomputed_system import process_single_symbol_mtf_worker
    from datetime import datetime, timedelta
    
    # 测试AAPL
    symbol = 'AAPL'
    timeframe = '1d'
    start_date = datetime.now() - timedelta(days=90)  # 3个月数据
    end_date = datetime.now()
    force_recalculate = True  # 强制重新计算
    
    task_data = (symbol, timeframe, start_date, end_date, force_recalculate)
    
    print(f"   处理 {symbol}...")
    result = process_single_symbol_mtf_worker(task_data)
    
    if result['success']:
        print(f"   ✅ 成功: 保存了 {result['records']} 条记录")
    else:
        print(f"   ❌ 失败: {result.get('error', 'Unknown error')}")
    
    return result['success']

def check_data_after_fix():
    """检查修复后的数据"""
    print("\n🔍 检查修复后的数据...")
    
    conn = pymysql.connect(**get_default_db_config())
    cursor = conn.cursor()
    
    try:
        # 检查MA指标表
        cursor.execute("""
        SELECT symbol, close_price, high_price, low_price, volume, ma5, ma10, ma20
        FROM mtf_precomputed_ma_indicators 
        WHERE symbol = 'AAPL' 
        ORDER BY datetime DESC 
        LIMIT 5
        """)
        
        ma_results = cursor.fetchall()
        print(f"   MA指标表记录数: {len(ma_results)}")
        
        null_count = 0
        valid_count = 0
        
        if ma_results:
            print("   前5条MA记录:")
            for i, row in enumerate(ma_results):
                symbol, close_price, high_price, low_price, volume, ma5, ma10, ma20 = row
                print(f"     {i+1}. close={close_price}, high={high_price}, low={low_price}, volume={volume}")
                print(f"        ma5={ma5}, ma10={ma10}, ma20={ma20}")
                
                # 统计NULL值
                if close_price is None or high_price is None or low_price is None:
                    null_count += 1
                else:
                    valid_count += 1
        
        print(f"   MA表数据质量: 有效记录 {valid_count}, NULL记录 {null_count}")
        
        # 检查52周指标表
        cursor.execute("""
        SELECT symbol, close_price, high_price, low_price, volume, high_52w, low_52w
        FROM mtf_precomputed_52w_indicators 
        WHERE symbol = 'AAPL' 
        ORDER BY datetime DESC 
        LIMIT 5
        """)
        
        w52_results = cursor.fetchall()
        print(f"   52周指标表记录数: {len(w52_results)}")
        
        null_count_52w = 0
        valid_count_52w = 0
        
        if w52_results:
            print("   前5条52周记录:")
            for i, row in enumerate(w52_results):
                symbol, close_price, high_price, low_price, volume, high_52w, low_52w = row
                print(f"     {i+1}. close={close_price}, high={high_price}, low={low_price}, volume={volume}")
                print(f"        high_52w={high_52w}, low_52w={low_52w}")
                
                # 统计NULL值
                if close_price is None or high_price is None or low_price is None:
                    null_count_52w += 1
                else:
                    valid_count_52w += 1
        
        print(f"   52周表数据质量: 有效记录 {valid_count_52w}, NULL记录 {null_count_52w}")
        
        # 返回修复是否成功
        return valid_count > 0 and valid_count_52w > 0
        
    finally:
        conn.close()

def main():
    """主函数"""
    print("🔧 数据修复测试工具")
    print("=" * 60)
    
    # 检查修复前的数据
    check_data_before_fix()
    
    # 测试修复
    fix_success = test_single_stock_fix()
    
    if fix_success:
        # 检查修复后的数据
        data_valid = check_data_after_fix()
        
        print("\n" + "=" * 60)
        if data_valid:
            print("🎉 数据修复成功！")
            print("   ✅ 基础价格字段已正确保存")
            print("   ✅ 技术指标已正确计算")
            print("   ✅ 数据库记录完整")
        else:
            print("❌ 数据修复失败！")
            print("   仍然存在NULL值问题")
    else:
        print("\n❌ 修复测试失败，无法处理股票数据")

if __name__ == "__main__":
    main()
