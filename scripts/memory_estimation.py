#!/usr/bin/env python3
"""
预计算系统内存使用估算
分析单只股票和批量处理的内存需求
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import psutil
import os

def estimate_single_stock_memory():
    """估算单只股票430天数据的内存使用"""
    print("📊 单只股票内存使用估算")
    print("=" * 50)
    
    # 数据结构分析
    days = 430
    
    # 每个数据点的字段
    fields = {
        'date': 8,          # datetime64[ns] = 8 bytes
        'open': 8,          # float64 = 8 bytes  
        'high': 8,          # float64 = 8 bytes
        'low': 8,           # float64 = 8 bytes
        'close': 8,         # float64 = 8 bytes
        'volume': 8,        # float64 = 8 bytes
        'ma50': 8,          # float64 = 8 bytes
        'ma200': 8,         # float64 = 8 bytes
        'above_ma50': 1,    # bool = 1 byte
        'above_ma200': 1,   # bool = 1 byte
        'high_52w': 8,      # float64 = 8 bytes
        'low_52w': 8,       # float64 = 8 bytes
        'is_new_high': 1,   # bool = 1 byte
        'is_new_low': 1,    # bool = 1 byte
    }
    
    bytes_per_row = sum(fields.values())
    
    # DataFrame开销
    pandas_overhead = bytes_per_row * 0.3  # pandas大约30%开销
    
    total_bytes_per_stock = (bytes_per_row + pandas_overhead) * days
    
    print(f"数据天数: {days} 天")
    print(f"字段数量: {len(fields)} 个")
    print(f"每行字节数: {bytes_per_row} bytes")
    print(f"pandas开销: {pandas_overhead:.1f} bytes/row")
    print(f"单只股票总内存: {total_bytes_per_stock:.0f} bytes = {total_bytes_per_stock/1024:.1f} KB = {total_bytes_per_stock/1024/1024:.3f} MB")
    
    return total_bytes_per_stock

def estimate_batch_memory(num_stocks, memory_per_stock):
    """估算批量处理的内存使用"""
    print(f"\n📈 批量处理内存估算 ({num_stocks} 只股票)")
    print("=" * 50)
    
    # 基础数据内存
    base_memory = memory_per_stock * num_stocks
    
    # 计算过程中的临时变量
    temp_memory = base_memory * 0.5  # 计算过程大约需要50%额外内存
    
    # Python对象开销
    python_overhead = num_stocks * 1024  # 每只股票约1KB的Python对象开销
    
    # 数据库操作缓冲
    db_buffer = num_stocks * 512  # 每只股票约512B的数据库缓冲
    
    total_memory = base_memory + temp_memory + python_overhead + db_buffer
    
    print(f"基础数据内存: {base_memory/1024/1024:.1f} MB")
    print(f"计算临时内存: {temp_memory/1024/1024:.1f} MB")
    print(f"Python对象开销: {python_overhead/1024:.1f} KB")
    print(f"数据库缓冲: {db_buffer/1024:.1f} KB")
    print(f"总内存需求: {total_memory/1024/1024:.1f} MB = {total_memory/1024/1024/1024:.2f} GB")
    
    return total_memory

def estimate_full_system_memory():
    """估算完整系统的内存需求"""
    print(f"\n🚀 完整系统内存估算 (6500只股票)")
    print("=" * 50)
    
    total_stocks = 6500
    memory_per_stock = estimate_single_stock_memory()
    
    # 不同批量大小的内存需求
    batch_sizes = [10, 50, 100, 200, 500, 1000]
    
    print(f"\n批量大小对内存需求的影响:")
    print(f"{'批量大小':<10} {'内存需求(MB)':<15} {'内存需求(GB)':<15} {'建议'}")
    print("-" * 60)
    
    recommendations = []
    
    for batch_size in batch_sizes:
        batch_memory = estimate_batch_memory(batch_size, memory_per_stock)
        memory_mb = batch_memory / 1024 / 1024
        memory_gb = memory_gb = memory_mb / 1024
        
        # 建议
        if memory_gb < 1:
            suggestion = "✅ 适合小内存服务器"
        elif memory_gb < 4:
            suggestion = "✅ 适合普通服务器"
        elif memory_gb < 8:
            suggestion = "⚠️ 需要大内存服务器"
        else:
            suggestion = "❌ 内存需求过高"
        
        print(f"{batch_size:<10} {memory_mb:<15.1f} {memory_gb:<15.2f} {suggestion}")
        
        recommendations.append({
            'batch_size': batch_size,
            'memory_gb': memory_gb,
            'suggestion': suggestion
        })
    
    # 总处理时间估算
    print(f"\n⏱️ 处理时间估算:")
    print(f"假设每批处理时间: 30秒")
    
    for rec in recommendations:
        batch_size = rec['batch_size']
        total_batches = (total_stocks + batch_size - 1) // batch_size
        total_time_minutes = total_batches * 0.5  # 30秒 = 0.5分钟
        
        print(f"批量大小 {batch_size}: {total_batches} 批次, 约 {total_time_minutes:.1f} 分钟")
    
    return recommendations

def check_current_system():
    """检查当前系统资源"""
    print(f"\n💻 当前系统资源")
    print("=" * 50)
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / 1024 / 1024 / 1024:.1f} GB")
    print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU核心数: {cpu_count}")
    print(f"CPU使用率: {cpu_percent:.1f}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    print(f"磁盘总空间: {disk.total / 1024 / 1024 / 1024:.1f} GB")
    print(f"磁盘可用空间: {disk.free / 1024 / 1024 / 1024:.1f} GB")
    
    return memory.available / 1024 / 1024 / 1024

def recommend_batch_size(available_memory_gb):
    """根据可用内存推荐批量大小"""
    print(f"\n🎯 批量大小推荐")
    print("=" * 50)
    
    # 保守估计，使用70%的可用内存
    usable_memory_gb = available_memory_gb * 0.7
    
    print(f"可用内存: {available_memory_gb:.1f} GB")
    print(f"建议使用: {usable_memory_gb:.1f} GB (70%)")
    
    # 单只股票内存需求
    memory_per_stock = estimate_single_stock_memory()
    memory_per_stock_mb = memory_per_stock / 1024 / 1024
    
    # 考虑处理开销，实际每只股票需要约2倍内存
    effective_memory_per_stock_mb = memory_per_stock_mb * 2
    
    # 计算推荐批量大小
    recommended_batch_size = int(usable_memory_gb * 1024 / effective_memory_per_stock_mb)
    
    # 调整到合理范围
    if recommended_batch_size < 10:
        recommended_batch_size = 10
    elif recommended_batch_size > 1000:
        recommended_batch_size = 1000
    
    print(f"单只股票有效内存需求: {effective_memory_per_stock_mb:.2f} MB")
    print(f"推荐批量大小: {recommended_batch_size}")
    
    # 验证推荐
    estimated_usage = recommended_batch_size * effective_memory_per_stock_mb / 1024
    print(f"预计内存使用: {estimated_usage:.2f} GB")
    
    if estimated_usage <= usable_memory_gb:
        print("✅ 推荐批量大小安全")
    else:
        print("⚠️ 推荐批量大小可能过大，建议减小")
    
    return recommended_batch_size

def create_optimized_batch_strategy():
    """创建优化的批量处理策略"""
    print(f"\n⚡ 优化批量处理策略")
    print("=" * 50)
    
    strategies = [
        {
            'name': '内存优先策略',
            'batch_size': 50,
            'description': '适合内存受限的服务器，处理时间较长但稳定',
            'memory_gb': 0.5,
            'time_hours': 2.2
        },
        {
            'name': '平衡策略', 
            'batch_size': 200,
            'description': '内存和时间的平衡，适合大多数服务器',
            'memory_gb': 1.8,
            'time_hours': 0.55
        },
        {
            'name': '速度优先策略',
            'batch_size': 500,
            'description': '适合大内存服务器，处理速度最快',
            'memory_gb': 4.2,
            'time_hours': 0.22
        }
    ]
    
    for strategy in strategies:
        print(f"\n{strategy['name']}:")
        print(f"  批量大小: {strategy['batch_size']}")
        print(f"  内存需求: {strategy['memory_gb']} GB")
        print(f"  预计时间: {strategy['time_hours']} 小时")
        print(f"  描述: {strategy['description']}")
    
    return strategies

def main():
    """主函数"""
    print("🔍 预计算系统内存使用分析")
    print("=" * 60)
    
    # 1. 单只股票内存估算
    memory_per_stock = estimate_single_stock_memory()
    
    # 2. 完整系统内存估算
    recommendations = estimate_full_system_memory()
    
    # 3. 检查当前系统
    available_memory = check_current_system()
    
    # 4. 推荐批量大小
    recommended_batch = recommend_batch_size(available_memory)
    
    # 5. 优化策略
    strategies = create_optimized_batch_strategy()
    
    # 6. 总结建议
    print(f"\n📋 总结建议")
    print("=" * 50)
    print(f"1. 单只股票内存需求: {memory_per_stock/1024:.1f} KB")
    print(f"2. 当前系统可用内存: {available_memory:.1f} GB")
    print(f"3. 推荐批量大小: {recommended_batch}")
    print(f"4. 如果内存不足，建议使用云服务器或升级硬件")
    print(f"5. 首次计算预计需要 1-3 小时，后续增量更新只需几分钟")

if __name__ == "__main__":
    main()
