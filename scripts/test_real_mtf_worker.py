#!/usr/bin/env python3
"""
测试真实MTF worker的脚本
"""

import sys
import os
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'config'))

def test_real_mtf_worker_single(symbol):
    """测试真实的MTF worker函数 - 单个股票"""
    try:
        from mtf_precomputed_system import process_single_symbol_mtf_worker
        
        # 使用与实际测试相同的参数
        timeframe = '1d'
        start_date = datetime.now() - timedelta(days=18*30)  # 18个月
        end_date = datetime.now()
        force_recalculate = False
        
        task_data = (symbol, timeframe, start_date, end_date, force_recalculate)
        
        # 执行worker
        result = process_single_symbol_mtf_worker(task_data)
        
        return result
        
    except Exception as e:
        import traceback
        return {
            'symbol': symbol,
            'timeframe': '1d',
            'success': False,
            'error': f"{str(e)}\n{traceback.format_exc()}",
            'records': 0,
            'total_calculated': 0
        }

def test_sequential():
    """顺序测试所有股票"""
    print("🧪 顺序测试真实MTF worker")
    print("-" * 50)
    
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'F', 'GM', 'BWA', 'ALV', 'AXL']
    
    results = []
    for symbol in test_symbols:
        print(f"   处理 {symbol}...", end=" ")
        result = test_real_mtf_worker_single(symbol)
        
        if result['success']:
            print(f"✅ 成功 ({result['records']}条记录)")
        else:
            print(f"❌ 失败")
            # 只显示错误的第一行
            error_line = result['error'].split('\n')[0]
            print(f"      错误: {error_line}")
        
        results.append(result)
    
    success_count = sum(1 for r in results if r['success'])
    print(f"\n   顺序测试结果: {success_count}/{len(test_symbols)} 成功")
    
    return results

def test_parallel():
    """并行测试所有股票"""
    print("\n🧪 并行测试真实MTF worker (10个进程)")
    print("-" * 50)
    
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'F', 'GM', 'BWA', 'ALV', 'AXL']
    
    start_time = time.time()
    results = []
    
    with ProcessPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(test_real_mtf_worker_single, symbol) for symbol in test_symbols]
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            
            if result['success']:
                print(f"   ✅ {result['symbol']}: {result['records']}条记录")
            else:
                print(f"   ❌ {result['symbol']}: 失败")
                # 只显示错误的第一行
                error_msg = result.get('error', 'Unknown error')
                error_line = error_msg.split('\n')[0]
                print(f"      错误: {error_line}")
    
    elapsed = time.time() - start_time
    success_count = sum(1 for r in results if r['success'])
    
    print(f"\n   并行测试结果: {success_count}/{len(test_symbols)} 成功")
    print(f"   耗时: {elapsed:.2f}秒")
    
    return results

def test_high_parallel():
    """高并行测试"""
    print("\n🧪 高并行测试真实MTF worker (48个进程)")
    print("-" * 50)
    
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'F', 'GM', 'BWA', 'ALV', 'AXL']
    
    start_time = time.time()
    results = []
    
    with ProcessPoolExecutor(max_workers=48) as executor:
        futures = [executor.submit(test_real_mtf_worker_single, symbol) for symbol in test_symbols]
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            
            if result['success']:
                print(f"   ✅ {result['symbol']}: {result['records']}条记录")
            else:
                print(f"   ❌ {result['symbol']}: 失败")
                # 只显示错误的第一行
                error_msg = result.get('error', 'Unknown error')
                error_line = error_msg.split('\n')[0]
                print(f"      错误: {error_line}")
    
    elapsed = time.time() - start_time
    success_count = sum(1 for r in results if r['success'])
    
    print(f"\n   高并行测试结果: {success_count}/{len(test_symbols)} 成功")
    print(f"   耗时: {elapsed:.2f}秒")
    
    return results

def analyze_failures(results):
    """分析失败原因"""
    failures = [r for r in results if not r['success']]
    
    if not failures:
        print("\n🎉 没有失败的股票！")
        return
    
    print(f"\n🔍 失败分析 ({len(failures)}个失败)")
    print("-" * 50)
    
    # 按错误类型分组
    error_types = {}
    for failure in failures:
        error_msg = failure.get('error', 'Unknown error')
        error_line = error_msg.split('\n')[0]
        if error_line not in error_types:
            error_types[error_line] = []
        error_types[error_line].append(failure['symbol'])
    
    for error, symbols in error_types.items():
        print(f"   错误: {error}")
        print(f"   股票: {', '.join(symbols)}")
        print()

def main():
    """主函数"""
    print("🔧 真实MTF Worker测试工具")
    print("=" * 60)
    
    # 顺序测试
    sequential_results = test_sequential()
    
    # 并行测试
    parallel_results = test_parallel()
    
    # 高并行测试
    high_parallel_results = test_high_parallel()
    
    print("\n" + "=" * 60)
    print("📊 测试结果对比:")
    
    seq_success = sum(1 for r in sequential_results if r['success'])
    par_success = sum(1 for r in parallel_results if r['success'])
    high_success = sum(1 for r in high_parallel_results if r['success'])
    
    print(f"   顺序执行: {seq_success}/10 成功")
    print(f"   并行执行(10进程): {par_success}/10 成功")
    print(f"   高并行执行(48进程): {high_success}/10 成功")
    
    # 分析失败原因
    if seq_success < 10:
        print("\n🔍 顺序执行失败分析:")
        analyze_failures(sequential_results)
    
    if par_success < 10:
        print("\n🔍 并行执行失败分析:")
        analyze_failures(parallel_results)
    
    if high_success < 10:
        print("\n🔍 高并行执行失败分析:")
        analyze_failures(high_parallel_results)

if __name__ == "__main__":
    main()
