# 64核心64G内存服务器优化指南

## 🔥 快速开始

您的原始命令只使用了1个worker，严重浪费了64核心的强大性能。现在有以下几种优化方案：

### 方案1: 一键快速启动（推荐）
```bash
source .venv/bin/activate && python scripts/quick_start_64core.py
```

### 方案2: 使用优化后的原始脚本
```bash
# 自动优化worker数量（推荐）
source .venv/bin/activate && python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 18

# 手动指定48个worker（64核心推荐配置）
source .venv/bin/activate && python scripts/setup_mtf_historical_system.py --custom --timeframes 1d --months 18 --workers 48
```

### 方案3: 使用新的超高速脚本
```bash
# 自动优化配置
source .venv/bin/activate && python scripts/ultra_fast_mtf_setup.py --timeframes 1d --months 18 --auto-workers

# 测试模式（只处理10只股票）
source .venv/bin/activate && python scripts/ultra_fast_mtf_setup.py --timeframes 1d --months 18 --auto-workers --test
```

## ⚡ 性能优化详情

### 硬件配置检测
- **CPU核心数**: 64
- **推荐worker数**: 48 (保留16核心给系统)
- **内存配置**: 每worker约1GB内存
- **预估加速比**: 30-40倍

### 优化策略
1. **多进程并行**: 使用48个进程同时处理不同股票
2. **批量处理**: 每批处理100只股票
3. **内存优化**: 充分利用64GB内存进行缓存
4. **I/O优化**: 每个worker使用4个I/O线程

### 性能对比
| 配置 | Worker数 | 预估时间 | 加速比 |
|------|----------|----------|--------|
| 原始配置 | 1 | 20-30小时 | 1x |
| 优化配置 | 48 | 30-60分钟 | 30-40x |

## 📊 监控和调试

### 查看系统资源状态
```bash
source .venv/bin/activate && python scripts/parallel_optimization_config.py
```

### 实时监控
```bash
# 监控CPU使用率
htop

# 监控内存使用
free -h

# 监控进程
ps aux | grep python
```

## 🛠️ 故障排除

### 常见问题

1. **内存不足**
   - 减少worker数量: `--workers 32`
   - 减少批次大小

2. **数据库连接过多**
   - 检查数据库最大连接数配置
   - 适当减少worker数量

3. **磁盘I/O瓶颈**
   - 确保使用SSD存储
   - 检查磁盘空间

### 调试模式
```bash
# 测试模式，只处理少量股票
source .venv/bin/activate && python scripts/ultra_fast_mtf_setup.py --test --auto-workers
```

## 📈 预期结果

使用优化配置后，您应该看到：
- **CPU使用率**: 75-85%
- **内存使用率**: 60-70%
- **处理速度**: 50-100 股票/分钟
- **总耗时**: 30-60分钟（vs 原来的20-30小时）

## 🎯 推荐使用流程

1. **首次使用**: 运行测试模式验证配置
2. **正式计算**: 使用快速启动脚本
3. **监控进度**: 观察CPU和内存使用情况
4. **结果验证**: 检查计算完成的股票数量

立即开始优化：
```bash
source .venv/bin/activate && python scripts/quick_start_64core.py
```
