#!/usr/bin/env python3
"""
完整参考指南
包含数据库结构、文档要求、前端期望的数据格式
防止遗忘的完整参考手册

重要提醒：
1. 不要重新计算市场广度，直接使用 market_breadth_metrics_gics 表
2. 不要重新计算板块轮动，直接使用 sector_rotation_metrics_gics 表  
3. 不要重新计算MTF分析，直接使用 mtf_analysis_results 表
4. 不要使用任何模拟数据、测试数据、简化数据
5. 所有数据必须来自真实数据库表
6. 前端期望特定的数据结构，必须严格按照要求返回
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def show_database_structure():
    """显示数据库结构"""
    print("🗃️ === 数据库表结构参考 ===")
    print()
    
    tables_info = {
        'market_breadth_metrics_gics': {
            'purpose': '市场广度指标 (已计算好，不要重新计算)',
            'key_fields': [
                'recorded_at - 记录时间',
                'market - 市场名称 (如 Information Technology)',
                'total_stocks - 总股票数',
                'advances - 上涨股票数',
                'declines - 下跌股票数',
                'unchanged - 不变股票数',
                'advancing_volume - 上涨成交量',
                'declining_volume - 下跌成交量',
                'total_volume - 总成交量',
                'new_highs_52w - 52周新高数量',
                'new_lows_52w - 52周新低数量',
                'above_ma50 - 50日均线上方股票数',
                'above_ma200 - 200日均线上方股票数',
                'avg_rsi - 平均RSI',
                'market_cap_weighted_return - 市值加权收益率',
                'equal_weighted_return - 等权重收益率',
                'purity - 纯度指标',
                'internal_health - 内部健康度',
                'momentum_coherence - 动量一致性',
                'divergence_type - 背离类型',
                'divergence_severity - 背离严重程度',
                'divergence_confidence - 背离置信度',
                'divergence_risk_level - 背离风险等级'
            ]
        },
        
        'sector_rotation_metrics_gics': {
            'purpose': '板块轮动指标 (已计算好，不要重新计算)',
            'key_fields': [
                'recorded_at - 记录时间',
                'market - 市场名称',
                'unified_rii - 统一轮动强度指标',
                'rotation_stage - 轮动阶段',
                'price_dispersion - 价格离散度',
                'rank_velocity - 排名变化速度',
                'volume_concentration - 成交量集中度',
                'momentum_persistence - 动量持续性',
                'sector_momentum_json - 板块动量数据(JSON)',
                'relative_strength_json - 相对强度数据(JSON)',
                'optimal_weights_json - 最优权重数据(JSON)',
                'risk_level - 风险等级'
            ]
        },
        
        'mtf_analysis_results': {
            'purpose': 'MTF分析结果 (已计算好，不要重新计算)',
            'key_fields': [
                'recorded_at - 记录时间',
                'market_regime - 市场状态',
                'consensus_score - 共识分数',
                'signal_reliability - 信号可靠性',
                'suggested_position - 建议仓位',
                'operation_strategy - 操作策略',
                'top_sectors_json - 推荐板块(JSON)',
                'avoid_sectors_json - 避免板块(JSON)',
                'key_insights_json - 关键洞察(JSON)',
                'warnings_json - 警告信息(JSON)',
                'unified_rii - 统一轮动指标',
                'rotation_stage - 轮动阶段',
                'risk_level - 风险等级'
            ]
        },
        
        'market_config_gics': {
            'purpose': '板块配置信息',
            'key_fields': [
                'market - 市场名称',
                'display_name - 显示名称',
                'description - 描述',
                'is_active - 是否激活',
                'sort_order - 排序'
            ]
        },
        
        'index_company_mapping_gics': {
            'purpose': '板块股票映射关系',
            'key_fields': [
                'symbol - 股票代码',
                'company_name - 公司名称',
                'market - 所属市场/板块',
                'weight - 权重',
                'is_active - 是否激活'
            ]
        }
    }
    
    for table_name, info in tables_info.items():
        print(f"📋 {table_name}")
        print(f"   用途: {info['purpose']}")
        print(f"   关键字段:")
        for field in info['key_fields']:
            print(f"     - {field}")
        print()

def show_frontend_requirements():
    """显示前端期望的数据结构"""
    print("🖥️ === 前端期望的数据结构 ===")
    print()
    
    print("📝 executive_summary 必需字段:")
    print("  - main_conclusion: 主要结论")
    print("  - confidence_level: 置信水平")
    print("  - market_phase: 市场阶段")
    print("  - primary_driver: 主要驱动")
    print("  - action_required: 行动要求")
    print()
    
    print("📊 timeframe_analysis 每个时间框架必需字段:")
    print("  - signal: 信号 (buy/sell/hold)")
    print("  - strength: 强度 (0-1)")
    print("  - confidence: 置信度 (0-1)")
    print("  - rsi: RSI值")
    print("  - health: 健康度")
    print("  - divergence: {type: '类型', severity: 严重程度}")
    print("  - momentum_coherence: 动量一致性")
    print("  - recommendation: 建议")
    print()
    
    print("🎯 metadata 必需字段:")
    print("  - report_type: 报告类型")
    print("  - version: 版本")
    print("  - timestamp: 时间戳")
    print("  - timeframes_analyzed: 分析的时间框架数")
    print("  - sector: 板块")
    print("  - analysis_method: 分析方法")
    print()

def show_document_requirements():
    """显示文档要求"""
    print("📚 === 文档要求参考 ===")
    print()
    
    print("📄 多时间框架板块轮动优化.md 要求:")
    print("  1. LLM友好格式输出")
    print("  2. 增强的executive_summary")
    print("  3. 决策路径说明")
    print("  4. 风险场景分析")
    print("  5. 交互式问答预设")
    print()
    
    print("🔧 mtf-optimization-implementation.py 要求:")
    print("  1. LLM上下文生成")
    print("  2. 置信度解释")
    print("  3. 数据质量评估")
    print("  4. 市场理解增强")
    print("  5. 决策支持系统")
    print()

def show_correct_data_mapping():
    """显示正确的数据映射"""
    print("🔄 === 正确的数据映射 ===")
    print()
    
    print("从数据库到前端的字段映射:")
    print()
    
    print("📊 executive_summary 映射:")
    print("  main_conclusion <- 基于 market_regime + suggested_position 生成")
    print("  confidence_level <- signal_reliability * 100 + '%'")
    print("  market_phase <- rotation_stage")
    print("  primary_driver <- operation_strategy")
    print("  action_required <- operation_strategy")
    print()
    
    print("📈 timeframe_analysis 映射:")
    print("  signal <- 基于 suggested_position 判断 (>60=buy, <40=sell, else=hold)")
    print("  strength <- signal_reliability")
    print("  confidence <- signal_reliability")
    print("  rsi <- avg_rsi")
    print("  health <- internal_health")
    print("  divergence.type <- divergence_type")
    print("  divergence.severity <- divergence_severity")
    print("  momentum_coherence <- momentum_coherence")
    print("  recommendation <- operation_strategy")
    print()

def show_sql_examples():
    """显示SQL查询示例"""
    print("💾 === SQL查询示例 ===")
    print()
    
    print("🔍 获取市场广度数据:")
    print("""
SELECT * FROM market_breadth_metrics_gics 
WHERE market = 'Information Technology'
ORDER BY recorded_at DESC LIMIT 1;
""")
    
    print("🔍 获取板块轮动数据:")
    print("""
SELECT * FROM sector_rotation_metrics_gics 
WHERE market = 'Information Technology'
ORDER BY recorded_at DESC LIMIT 1;
""")
    
    print("🔍 获取MTF分析结果:")
    print("""
SELECT * FROM mtf_analysis_results 
ORDER BY recorded_at DESC LIMIT 1;
""")
    
    print("🔍 获取可用市场列表:")
    print("""
SELECT DISTINCT market FROM market_breadth_metrics_gics 
ORDER BY market;
""")

def show_common_mistakes():
    """显示常见错误"""
    print("⚠️ === 常见错误和解决方案 ===")
    print()
    
    print("❌ 常见错误:")
    print("  1. 返回数据缺少 executive_summary 字段")
    print("  2. timeframe_analysis 缺少 divergence 对象")
    print("  3. 使用模拟数据而不是真实数据库数据")
    print("  4. 重新计算已有的指标")
    print("  5. 数据结构层级错误")
    print()
    
    print("✅ 解决方案:")
    print("  1. 严格按照前端期望的数据结构返回")
    print("  2. 确保所有必需字段都有值，不能是 undefined")
    print("  3. 直接从数据库表获取真实数据")
    print("  4. 使用已计算好的指标，不要重新计算")
    print("  5. 正确映射数据库字段到前端字段")
    print()

def show_web_interface_fix_template():
    """显示web界面修复模板"""
    print("🔧 === Web界面修复模板 ===")
    print()
    
    print("正确的数据结构模板:")
    print("""
{
    "metadata": {
        "report_type": "增强版多时间框架分析",
        "version": "2.0",
        "timestamp": "2025-08-01T12:00:00",
        "timeframes_analyzed": 3,
        "sector": "Information Technology",
        "analysis_method": "enhanced_integrated"
    },
    "executive_summary": {
        "main_conclusion": "市场处于normal_market，建议稳健操作（50%仓位）",
        "confidence_level": "75%",
        "market_phase": "稳定期",
        "primary_driver": "市场广度和轮动分析",
        "action_required": "基于市场广度分析"
    },
    "timeframe_analysis": {
        "daily": {
            "signal": "hold",
            "strength": 0.75,
            "confidence": 0.75,
            "rsi": 55.5,
            "health": 65.2,
            "divergence": {
                "type": "none",
                "severity": 0
            },
            "momentum_coherence": 0.68,
            "recommendation": "正常关注"
        }
    },
    "market_overview": {...},
    "breadth_analysis": {...},
    "rotation_analysis": {...},
    "sector_analysis": {...},
    "recommendations": {...}
}
""")

def main():
    """主函数"""
    print("📚 完整参考指南")
    print("当你忘记数据库结构、文档要求或前端期望时，运行这个脚本！")
    print("=" * 80)
    
    show_database_structure()
    show_frontend_requirements()
    show_document_requirements()
    show_correct_data_mapping()
    show_sql_examples()
    show_common_mistakes()
    show_web_interface_fix_template()
    
    print("=" * 80)
    print("🎯 重要提醒:")
    print("1. 永远不要重新计算已有的指标")
    print("2. 永远不要使用模拟数据")
    print("3. 严格按照前端期望的数据结构返回")
    print("4. 确保所有必需字段都有值")
    print("5. 数据库已经计算好了所有指标")
    print("=" * 80)

if __name__ == "__main__":
    main()
