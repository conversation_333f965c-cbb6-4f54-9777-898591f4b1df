# 统一的板块轮动与Market Breadth计算系统

## 一、系统概述

本系统整合了Market Breadth（市场广度）和Sector Rotation（板块轮动）两个模块，消除重复计算，提供统一的分析框架。

### 核心设计原则

1. **模块职责清晰**：广度模块负责内部结构，轮动模块负责动态关系
2. **数据流单向**：原始数据 → 广度分析 → 轮动分析 → 投资决策
3. **指标不重复**：每个指标只在一个模块中计算
4. **结果可追溯**：保留中间计算结果，便于分析和调试

## 二、完整实现代码

```python
"""
统一的板块轮动与Market Breadth计算系统
Unified Sector Rotation and Market Breadth System

版本: 2.0
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import warnings


# ========================================
# 数据结构定义
# ========================================

@dataclass
class SectorBreadthData:
    """板块广度原始数据"""
    sector_name: str
    timestamp: datetime
    advances: int
    declines: int
    unchanged: int
    total_stocks: int
    advancing_volume: float
    declining_volume: float
    new_highs_52w: int
    new_lows_52w: int
    above_ma50: int
    above_ma200: int
    avg_rsi: float
    individual_returns: Optional[List[float]] = None


@dataclass
class BreadthMetrics:
    """广度分析结果"""
    sector_name: str
    timestamp: datetime
    # 基础指标
    ad_ratio: float
    purity: float
    volume_breadth: float
    nh_nl_ratio: float
    ma50_breadth: float
    ma200_breadth: float
    # 衍生指标
    internal_health: float
    participation_rate: float
    momentum_coherence: Optional[float] = None
    # 背离检测
    price_breadth_divergence: Optional[Dict] = None


@dataclass
class RotationMetrics:
    """轮动分析结果"""
    timestamp: datetime
    # 动量指标
    sector_momentum: pd.Series
    relative_strength: pd.Series
    # 轮动指标
    price_dispersion: float
    rank_velocity: float
    volume_concentration: float
    unified_rii: float
    # 阶段判定
    rotation_stage: str
    stage_probabilities: Dict[str, float]
    # 配置建议
    optimal_weights: pd.Series
    risk_level: str


@dataclass
class UnifiedAnalysisResult:
    """统一分析结果"""
    timestamp: datetime
    # 广度分析结果
    breadth_by_sector: Dict[str, BreadthMetrics]
    market_breadth_summary: Dict
    # 轮动分析结果
    rotation_metrics: RotationMetrics
    # 综合指标
    unified_health_scores: Dict[str, float]
    unified_velocity: float
    # 决策建议
    investment_signals: Dict
    risk_warnings: List[Dict]


# ========================================
# 广度分析模块（专注内部结构）
# ========================================

class MarketBreadthAnalyzer:
    """市场广度分析器 - 负责板块内部结构分析"""
    
    def __init__(self):
        self.min_stocks_threshold = 10
        
    def analyze_sector_breadth(self, sector_data: SectorBreadthData) -> BreadthMetrics:
        """
        分析单个板块的内部广度
        
        只计算内部结构相关指标，不涉及板块间比较
        """
        # 基础广度指标
        ad_ratio = self._calculate_ad_ratio(sector_data.advances, sector_data.declines)
        purity = self._calculate_purity(sector_data)
        volume_breadth = self._calculate_volume_breadth(sector_data)
        nh_nl_ratio = self._calculate_nh_nl_ratio(sector_data)
        
        # 均线广度
        ma50_breadth = sector_data.above_ma50 / sector_data.total_stocks if sector_data.total_stocks > 0 else 0.5
        ma200_breadth = sector_data.above_ma200 / sector_data.total_stocks if sector_data.total_stocks > 0 else 0.5
        
        # 参与率
        participation_rate = 1 - (sector_data.unchanged / sector_data.total_stocks) if sector_data.total_stocks > 0 else 0.5
        
        # 动量一致性（如果有个股数据）
        momentum_coherence = None
        if sector_data.individual_returns:
            momentum_coherence = self._calculate_momentum_coherence(sector_data.individual_returns)
        
        # 内部健康度评分
        internal_health = self._calculate_internal_health(
            ad_ratio, purity, volume_breadth, nh_nl_ratio, 
            ma50_breadth, ma200_breadth, participation_rate
        )
        
        return BreadthMetrics(
            sector_name=sector_data.sector_name,
            timestamp=sector_data.timestamp,
            ad_ratio=ad_ratio,
            purity=purity,
            volume_breadth=volume_breadth,
            nh_nl_ratio=nh_nl_ratio,
            ma50_breadth=ma50_breadth,
            ma200_breadth=ma200_breadth,
            internal_health=internal_health,
            participation_rate=participation_rate,
            momentum_coherence=momentum_coherence
        )
    
    def _calculate_ad_ratio(self, advances: int, declines: int) -> float:
        """计算涨跌比"""
        if declines == 0:
            return 10.0 if advances > 0 else 1.0
        return min(advances / declines, 10.0)  # 限制最大值为10
    
    def _calculate_purity(self, data: SectorBreadthData) -> float:
        """计算板块纯度"""
        if data.total_stocks == 0:
            return 0.5
        
        advance_ratio = data.advances / data.total_stocks
        decline_ratio = data.declines / data.total_stocks
        
        purity = advance_ratio**2 + decline_ratio**2
        return max(0, min(1, purity))
    
    def _calculate_volume_breadth(self, data: SectorBreadthData) -> float:
        """计算成交量广度"""
        total_volume = data.advancing_volume + data.declining_volume
        if total_volume == 0:
            return 0.0
        
        return (data.advancing_volume - data.declining_volume) / total_volume
    
    def _calculate_nh_nl_ratio(self, data: SectorBreadthData) -> float:
        """计算新高新低比率"""
        if data.new_lows_52w == 0:
            return 10.0 if data.new_highs_52w > 0 else 1.0
        return min(data.new_highs_52w / data.new_lows_52w, 10.0)
    
    def _calculate_momentum_coherence(self, returns: List[float]) -> float:
        """计算动量一致性"""
        if not returns or len(returns) < 2:
            return 0.5
        
        returns_array = np.array(returns)
        mean_return = np.mean(returns_array)
        
        if abs(mean_return) < 0.0001:
            return 0.5
        
        std_return = np.std(returns_array)
        coherence = 1 - std_return / abs(mean_return)
        
        return max(0, min(1, coherence))
    
    def _calculate_internal_health(self, ad_ratio: float, purity: float, 
                                 volume_breadth: float, nh_nl_ratio: float,
                                 ma50_breadth: float, ma200_breadth: float,
                                 participation_rate: float) -> float:
        """计算内部健康度（0-100）"""
        # 各指标评分
        scores = {
            'ad_score': min(ad_ratio * 20, 100),  # AD比率得分
            'purity_score': purity * 100,  # 纯度得分
            'volume_score': (volume_breadth + 1) * 50,  # 成交量得分
            'nh_nl_score': min(nh_nl_ratio * 15, 100),  # 新高新低得分
            'ma_score': (ma50_breadth * 0.6 + ma200_breadth * 0.4) * 100,  # 均线得分
            'participation_score': participation_rate * 100  # 参与度得分
        }
        
        # 权重分配
        weights = {
            'ad_score': 0.20,
            'purity_score': 0.15,
            'volume_score': 0.15,
            'nh_nl_score': 0.10,
            'ma_score': 0.25,
            'participation_score': 0.15
        }
        
        # 加权计算
        health_score = sum(scores[k] * weights[k] for k in scores)
        
        return round(health_score, 2)
    
    def detect_price_breadth_divergence(self, price_change: float, 
                                      breadth_metrics: BreadthMetrics) -> Optional[Dict]:
        """检测价格-广度背离"""
        divergence = None
        
        # 价格上涨但广度恶化
        if price_change > 0.01 and breadth_metrics.ad_ratio < 1.0:
            divergence = {
                'type': 'negative',
                'severity': abs(price_change * (1 - breadth_metrics.ad_ratio)),
                'message': '价格上涨但内部结构恶化'
            }
        # 价格下跌但广度改善
        elif price_change < -0.01 and breadth_metrics.ad_ratio > 1.0:
            divergence = {
                'type': 'positive',
                'severity': abs(price_change * breadth_metrics.ad_ratio),
                'message': '价格下跌但内部结构改善'
            }
        
        return divergence
    
    def calculate_market_breadth_summary(self, 
                                       sector_breadth_list: List[BreadthMetrics]) -> Dict:
        """计算市场整体广度摘要"""
        if not sector_breadth_list:
            return {}
        
        # 汇总统计
        avg_internal_health = np.mean([b.internal_health for b in sector_breadth_list])
        healthy_sectors = sum(1 for b in sector_breadth_list if b.internal_health > 70)
        weak_sectors = sum(1 for b in sector_breadth_list if b.internal_health < 40)
        
        # 广度指标分布
        ad_ratios = [b.ad_ratio for b in sector_breadth_list if b.ad_ratio < 10]
        breadth_dispersion = np.std(ad_ratios) if ad_ratios else 0
        
        return {
            'market_internal_health': round(avg_internal_health, 2),
            'healthy_sectors_count': healthy_sectors,
            'weak_sectors_count': weak_sectors,
            'total_sectors': len(sector_breadth_list),
            'breadth_dispersion': round(breadth_dispersion, 3),
            'market_participation': round(np.mean([b.participation_rate for b in sector_breadth_list]), 3)
        }


# ========================================
# 轮动分析模块（专注动态关系）
# ========================================

class SectorRotationAnalyzer:
    """板块轮动分析器 - 负责板块间动态关系分析"""
    
    def __init__(self):
        self.lookback_short = 5
        self.lookback_medium = 20
        self.lookback_long = 60
        
    def analyze_rotation(self, 
                        prices: pd.DataFrame,
                        volumes: pd.DataFrame,
                        breadth_metrics: Dict[str, BreadthMetrics]) -> RotationMetrics:
        """
        分析板块轮动，使用广度模块提供的内部指标
        
        专注于板块间的动态关系，不重复计算内部指标
        """
        # 1. 动量分析（板块间比较）
        momentum = self._calculate_momentum(prices)
        relative_strength = self._calculate_relative_strength(momentum)
        
        # 2. 离散度（只计算价格离散度）
        price_dispersion = self._calculate_price_dispersion(relative_strength)
        
        # 3. 轮动速度（基于排名变化）
        rank_velocity = self._calculate_rank_velocity(momentum)
        
        # 4. 成交量集中度
        volume_concentration = self._calculate_volume_concentration(volumes)
        
        # 5. 统一RII（结合广度信息）
        unified_rii = self._calculate_unified_rii(
            price_dispersion, rank_velocity, volume_concentration, breadth_metrics
        )
        
        # 6. 轮动阶段判定（考虑广度健康度）
        stage, stage_probs = self._identify_rotation_stage(
            unified_rii, rank_velocity, breadth_metrics
        )
        
        # 7. 风险等级
        risk_level = self._assess_risk_level(unified_rii, stage, breadth_metrics)
        
        # 8. 最优权重（基于综合分析）
        optimal_weights = self._calculate_optimal_weights(
            relative_strength, stage, breadth_metrics, risk_level
        )
        
        return RotationMetrics(
            timestamp=prices.index[-1],
            sector_momentum=momentum.iloc[-1],
            relative_strength=relative_strength.iloc[-1],
            price_dispersion=price_dispersion,
            rank_velocity=rank_velocity,
            volume_concentration=volume_concentration,
            unified_rii=unified_rii,
            rotation_stage=stage,
            stage_probabilities=stage_probs,
            optimal_weights=optimal_weights,
            risk_level=risk_level
        )
    
    def _calculate_momentum(self, prices: pd.DataFrame) -> pd.DataFrame:
        """计算动量"""
        return prices.pct_change(self.lookback_short) * 100
    
    def _calculate_relative_strength(self, momentum: pd.DataFrame) -> pd.DataFrame:
        """计算相对强度"""
        market_momentum = momentum.mean(axis=1)
        relative_strength = momentum.div(market_momentum, axis=0)
        
        # 处理特殊值
        relative_strength = relative_strength.replace([np.inf, -np.inf], np.nan)
        mask = abs(market_momentum) < 0.01
        for col in relative_strength.columns:
            relative_strength.loc[mask, col] = 1.0
        
        return relative_strength.fillna(1.0)
    
    def _calculate_price_dispersion(self, relative_strength: pd.DataFrame) -> float:
        """计算价格离散度"""
        return relative_strength.iloc[-1].std()
    
    def _calculate_rank_velocity(self, momentum: pd.DataFrame) -> float:
        """计算排名变化速度"""
        if len(momentum) < 2:
            return 0.0
        
        # 最近N天的排名
        lookback = min(5, len(momentum))
        recent_ranks = pd.DataFrame(index=momentum.index[-lookback:], 
                                  columns=momentum.columns)
        
        for idx in recent_ranks.index:
            recent_ranks.loc[idx] = momentum.loc[idx].rank(ascending=False)
        
        # 计算排名变化
        rank_changes = recent_ranks.diff().abs().mean(axis=1)
        avg_velocity = rank_changes.mean() / len(momentum.columns)
        
        return avg_velocity
    
    def _calculate_volume_concentration(self, volumes: pd.DataFrame) -> float:
        """计算成交量集中度（HHI）"""
        latest_volumes = volumes.iloc[-1]
        total_volume = latest_volumes.sum()
        
        if total_volume == 0:
            return 0.1
        
        market_shares = latest_volumes / total_volume
        hhi = (market_shares ** 2).sum()
        
        return hhi
    
    def _calculate_unified_rii(self, price_dispersion: float, rank_velocity: float,
                             volume_concentration: float, 
                             breadth_metrics: Dict[str, BreadthMetrics]) -> float:
        """
        计算统一的轮动强度指数
        结合价格指标和广度指标
        """
        # 价格维度RII（60%权重）
        price_rii = 0.5 * price_dispersion + 0.3 * rank_velocity + 0.2 * volume_concentration
        
        # 广度维度调整因子（40%权重）
        if breadth_metrics:
            # 计算广度健康度的标准差（反映内部结构分化）
            health_scores = [b.internal_health for b in breadth_metrics.values()]
            breadth_dispersion = np.std(health_scores) / 100  # 归一化
            
            # 计算平均内部健康度的反向指标（健康度低时轮动风险高）
            avg_health = np.mean(health_scores)
            health_risk = (100 - avg_health) / 100
            
            breadth_factor = 0.6 * breadth_dispersion + 0.4 * health_risk
        else:
            breadth_factor = 0.5
        
        # 统一RII
        unified_rii = 0.6 * price_rii + 0.4 * breadth_factor
        
        return unified_rii
    
    def _identify_rotation_stage(self, rii: float, velocity: float,
                               breadth_metrics: Dict[str, BreadthMetrics]) -> Tuple[str, Dict[str, float]]:
        """识别轮动阶段"""
        # 获取平均内部健康度
        if breadth_metrics:
            avg_health = np.mean([b.internal_health for b in breadth_metrics.values()])
        else:
            avg_health = 70
        
        # 阶段概率计算
        probs = {
            '稳定期': 0,
            '启动期': 0,
            '加速期': 0,
            '混乱期': 0,
            '收敛期': 0
        }
        
        # 基于规则的概率分配
        if rii < 0.3 and avg_health > 70:
            probs['稳定期'] = 0.8
            probs['收敛期'] = 0.2
        elif 0.3 <= rii < 0.6 and velocity > 0.1:
            probs['启动期'] = 0.6
            probs['稳定期'] = 0.2
            probs['加速期'] = 0.2
        elif 0.6 <= rii < 0.8 and avg_health > 50:
            probs['加速期'] = 0.7
            probs['启动期'] = 0.2
            probs['混乱期'] = 0.1
        elif rii >= 0.8 or (rii > 0.6 and avg_health < 40):
            probs['混乱期'] = 0.8
            probs['加速期'] = 0.2
        else:
            probs['收敛期'] = 0.5
            probs['稳定期'] = 0.3
            probs['启动期'] = 0.2
        
        # 选择最高概率的阶段
        stage = max(probs.keys(), key=lambda k: probs[k])
        
        return stage, probs
    
    def _assess_risk_level(self, rii: float, stage: str, 
                         breadth_metrics: Dict[str, BreadthMetrics]) -> str:
        """评估风险等级"""
        # 基础风险评估
        if rii < 0.4:
            base_risk = 'low'
        elif rii < 0.7:
            base_risk = 'medium'
        else:
            base_risk = 'high'
        
        # 阶段调整
        if stage == '混乱期':
            base_risk = 'high'
        elif stage == '稳定期' and base_risk == 'high':
            base_risk = 'medium'
        
        # 广度调整
        if breadth_metrics:
            weak_sectors = sum(1 for b in breadth_metrics.values() if b.internal_health < 40)
            if weak_sectors > len(breadth_metrics) * 0.5:
                if base_risk == 'low':
                    base_risk = 'medium'
                elif base_risk == 'medium':
                    base_risk = 'high'
        
        return base_risk
    
    def _calculate_optimal_weights(self, relative_strength: pd.Series, stage: str,
                                 breadth_metrics: Dict[str, BreadthMetrics],
                                 risk_level: str) -> pd.Series:
        """计算最优权重"""
        sectors = relative_strength.index
        weights = pd.Series(0, index=sectors)
        
        # 获取内部健康度
        health_scores = {}
        for sector in sectors:
            if sector in breadth_metrics:
                health_scores[sector] = breadth_metrics[sector].internal_health
            else:
                health_scores[sector] = 50  # 默认值
        
        # 综合得分 = 相对强度 × 内部健康度
        combined_scores = pd.Series(index=sectors)
        for sector in sectors:
            rs_score = max(0, relative_strength[sector])
            health_score = health_scores[sector] / 100
            combined_scores[sector] = rs_score * (0.5 + 0.5 * health_score)
        
        # 根据阶段和风险等级分配权重
        if stage == '稳定期' and risk_level != 'high':
            # 可以集中配置
            top_3 = combined_scores.nlargest(3)
            if len(top_3) >= 3:
                weights[top_3.index[0]] = 0.4
                weights[top_3.index[1]] = 0.3
                weights[top_3.index[2]] = 0.2
                remaining = 0.1
                other_sectors = [s for s in sectors if s not in top_3.index]
                if other_sectors:
                    weights[other_sectors] = remaining / len(other_sectors)
        
        elif stage in ['混乱期', '加速期'] or risk_level == 'high':
            # 必须分散配置
            positive_sectors = combined_scores[combined_scores > 0]
            if len(positive_sectors) > 0:
                # 限制最大权重
                max_weight = 0.15 if stage == '混乱期' else 0.2
                
                # 按得分比例分配
                raw_weights = positive_sectors / positive_sectors.sum()
                raw_weights = raw_weights.clip(upper=max_weight)
                
                # 重新归一化
                weights[positive_sectors.index] = raw_weights / raw_weights.sum()
        
        else:
            # 温和配置
            positive_sectors = combined_scores[combined_scores > 0]
            if len(positive_sectors) > 0:
                weights[positive_sectors.index] = positive_sectors / positive_sectors.sum()
        
        # 确保权重和为1
        if weights.sum() > 0:
            weights = weights / weights.sum()
        else:
            weights[:] = 1 / len(weights)
        
        return weights


# ========================================
# 统一分析系统
# ========================================

class UnifiedRotationSystem:
    """统一的轮动分析系统"""
    
    def __init__(self):
        self.breadth_analyzer = MarketBreadthAnalyzer()
        self.rotation_analyzer = SectorRotationAnalyzer()
        
    def analyze(self, 
                prices: pd.DataFrame,
                volumes: pd.DataFrame,
                sectors_breadth_data: Dict[str, SectorBreadthData]) -> UnifiedAnalysisResult:
        """
        执行完整的统一分析
        
        参数:
            prices: 板块价格时间序列
            volumes: 板块成交量时间序列
            sectors_breadth_data: 各板块的广度原始数据
            
        返回:
            UnifiedAnalysisResult: 完整的分析结果
        """
        # Step 1: 广度分析（内部结构）
        breadth_metrics = {}
        for sector, data in sectors_breadth_data.items():
            breadth_metrics[sector] = self.breadth_analyzer.analyze_sector_breadth(data)
            
            # 检测背离
            if sector in prices.columns:
                price_change = prices[sector].pct_change().iloc[-1]
                divergence = self.breadth_analyzer.detect_price_breadth_divergence(
                    price_change, breadth_metrics[sector]
                )
                breadth_metrics[sector].price_breadth_divergence = divergence
        
        # 市场广度汇总
        breadth_list = list(breadth_metrics.values())
        market_breadth_summary = self.breadth_analyzer.calculate_market_breadth_summary(breadth_list)
        
        # Step 2: 轮动分析（动态关系）
        rotation_metrics = self.rotation_analyzer.analyze_rotation(
            prices, volumes, breadth_metrics
        )
        
        # Step 3: 计算统一指标
        unified_health_scores = self._calculate_unified_health(
            rotation_metrics.relative_strength,
            breadth_metrics
        )
        
        unified_velocity = self._calculate_unified_velocity(
            rotation_metrics.rank_velocity,
            market_breadth_summary.get('breadth_dispersion', 0)
        )
        
        # Step 4: 生成投资信号
        investment_signals = self._generate_investment_signals(
            rotation_metrics, breadth_metrics, market_breadth_summary
        )
        
        # Step 5: 风险预警
        risk_warnings = self._generate_risk_warnings(
            rotation_metrics, breadth_metrics, unified_health_scores
        )
        
        # 返回统一结果
        return UnifiedAnalysisResult(
            timestamp=datetime.now(),
            breadth_by_sector=breadth_metrics,
            market_breadth_summary=market_breadth_summary,
            rotation_metrics=rotation_metrics,
            unified_health_scores=unified_health_scores,
            unified_velocity=unified_velocity,
            investment_signals=investment_signals,
            risk_warnings=risk_warnings
        )
    
    def _calculate_unified_health(self, relative_strength: pd.Series,
                                breadth_metrics: Dict[str, BreadthMetrics]) -> Dict[str, float]:
        """计算统一健康度（结合内外部）"""
        unified_health = {}
        
        for sector in relative_strength.index:
            # 外部健康度（基于相对强度）
            external_health = min(100, max(0, relative_strength[sector] * 50))
            
            # 内部健康度
            if sector in breadth_metrics:
                internal_health = breadth_metrics[sector].internal_health
            else:
                internal_health = 50
            
            # 统一健康度（内外结合）
            unified_health[sector] = round(0.5 * internal_health + 0.5 * external_health, 2)
        
        return unified_health
    
    def _calculate_unified_velocity(self, rank_velocity: float, 
                                  breadth_dispersion: float) -> float:
        """计算统一轮动速度"""
        # 价格维度速度（70%权重）
        price_velocity_component = rank_velocity * 0.7
        
        # 广度维度速度（30%权重）
        breadth_velocity_component = breadth_dispersion * 0.3
        
        return round(price_velocity_component + breadth_velocity_component, 3)
    
    def _generate_investment_signals(self, rotation: RotationMetrics,
                                   breadth: Dict[str, BreadthMetrics],
                                   market_summary: Dict) -> Dict:
        """生成投资信号"""
        signals = {
            'overall_signal': 'neutral',
            'rotation_opportunity': False,
            'recommended_sectors': [],
            'avoid_sectors': [],
            'position_adjustment': 'maintain'
        }
        
        # 判断整体信号
        if rotation.rotation_stage == '稳定期' and market_summary['market_internal_health'] > 70:
            signals['overall_signal'] = 'bullish'
            signals['position_adjustment'] = 'increase'
        elif rotation.rotation_stage == '混乱期' or market_summary['market_internal_health'] < 40:
            signals['overall_signal'] = 'bearish'
            signals['position_adjustment'] = 'decrease'
        
        # 轮动机会
        if rotation.rotation_stage in ['启动期', '加速期'] and rotation.risk_level != 'high':
            signals['rotation_opportunity'] = True
        
        # 推荐板块（权重前3且健康度>60）
        top_weights = rotation.optimal_weights.nlargest(5)
        for sector in top_weights.index:
            if sector in breadth and breadth[sector].internal_health > 60:
                signals['recommended_sectors'].append({
                    'sector': sector,
                    'weight': round(top_weights[sector], 3),
                    'health': breadth[sector].internal_health
                })
        
        # 避免板块（健康度<40或有负背离）
        for sector, metrics in breadth.items():
            if metrics.internal_health < 40:
                signals['avoid_sectors'].append({
                    'sector': sector,
                    'reason': '内部结构差',
                    'health': metrics.internal_health
                })
            elif metrics.price_breadth_divergence and metrics.price_breadth_divergence['type'] == 'negative':
                signals['avoid_sectors'].append({
                    'sector': sector,
                    'reason': '负背离',
                    'severity': round(metrics.price_breadth_divergence['severity'], 3)
                })
        
        return signals
    
    def _generate_risk_warnings(self, rotation: RotationMetrics,
                              breadth: Dict[str, BreadthMetrics],
                              unified_health: Dict[str, float]) -> List[Dict]:
        """生成风险预警"""
        warnings = []
        
        # 轮动风险
        if rotation.unified_rii > 0.8:
            warnings.append({
                'type': 'rotation_risk',
                'level': 'high',
                'message': f'轮动强度过高({rotation.unified_rii:.2f})，市场不稳定',
                'action': '降低仓位，避免追涨'
            })
        
        # 阶段风险
        if rotation.rotation_stage == '混乱期':
            warnings.append({
                'type': 'stage_risk',
                'level': 'high',
                'message': '市场处于混乱期，方向不明',
                'action': '大幅降低仓位或观望'
            })
        
        # 广度恶化风险
        weak_sectors = [s for s, h in unified_health.items() if h < 40]
        if len(weak_sectors) > len(unified_health) * 0.5:
            warnings.append({
                'type': 'breadth_risk',
                'level': 'medium',
                'message': f'超过50%板块健康度低于40',
                'action': '谨慎操作，关注防御性板块'
            })
        
        # 背离风险
        divergence_sectors = []
        for sector, metrics in breadth.items():
            if metrics.price_breadth_divergence and metrics.price_breadth_divergence['type'] == 'negative':
                divergence_sectors.append(sector)
        
        if len(divergence_sectors) >= 3:
            warnings.append({
                'type': 'divergence_risk',
                'level': 'medium',
                'message': f'{len(divergence_sectors)}个板块出现负背离',
                'sectors': divergence_sectors,
                'action': '关注价格与内部结构的背离'
            })
        
        # 集中度风险
        if rotation.volume_concentration > 0.3:
            warnings.append({
                'type': 'concentration_risk',
                'level': 'low',
                'message': f'成交量过度集中(HHI={rotation.volume_concentration:.2f})',
                'action': '注意流动性风险'
            })
        
        return warnings


# ========================================
# 便捷使用函数
# ========================================

def quick_analysis(prices: pd.DataFrame, 
                  volumes: pd.DataFrame,
                  sectors_data: Dict[str, Dict]) -> Dict:
    """
    快速分析函数，简化数据准备过程
    
    参数:
        prices: 板块价格DataFrame
        volumes: 板块成交量DataFrame
        sectors_data: 板块广度数据字典
        
    返回:
        分析结果摘要
    """
    # 转换数据格式
    sectors_breadth_data = {}
    timestamp = datetime.now()
    
    for sector, data in sectors_data.items():
        sectors_breadth_data[sector] = SectorBreadthData(
            sector_name=sector,
            timestamp=timestamp,
            advances=data.get('advances', 0),
            declines=data.get('declines', 0),
            unchanged=data.get('unchanged', 0),
            total_stocks=data.get('total_stocks', 0),
            advancing_volume=data.get('advancing_volume', 0),
            declining_volume=data.get('declining_volume', 0),
            new_highs_52w=data.get('new_highs_52w', 0),
            new_lows_52w=data.get('new_lows_52w', 0),
            above_ma50=data.get('above_ma50', 0),
            above_ma200=data.get('above_ma200', 0),
            avg_rsi=data.get('avg_rsi', 50),
            individual_returns=data.get('individual_returns')
        )
    
    # 执行分析
    system = UnifiedRotationSystem()
    result = system.analyze(prices, volumes, sectors_breadth_data)
    
    # 生成摘要
    summary = {
        'timestamp': result.timestamp,
        'market_health': result.market_breadth_summary['market_internal_health'],
        'rotation_stage': result.rotation_metrics.rotation_stage,
        'rotation_intensity': round(result.rotation_metrics.unified_rii, 3),
        'unified_velocity': result.unified_velocity,
        'risk_level': result.rotation_metrics.risk_level,
        'top_sectors': [],
        'investment_signal': result.investment_signals['overall_signal'],
        'warnings_count': len(result.risk_warnings)
    }
    
    # 添加Top3板块
    for sector, weight in result.rotation_metrics.optimal_weights.nlargest(3).items():
        summary['top_sectors'].append({
            'sector': sector,
            'weight': round(weight, 3),
            'unified_health': result.unified_health_scores[sector]
        })
    
    return summary


# ========================================
# 使用示例
# ========================================

def example_usage():
    """完整的使用示例"""
    
    # 1. 准备数据
    dates = pd.date_range('2025-01-01', periods=100, freq='D')
    
    # 模拟价格数据
    sectors = ['Technology', 'Healthcare', 'Financials', 'Energy', 'Consumer']
    np.random.seed(42)
    
    price_data = {}
    volume_data = {}
    
    for i, sector in enumerate(sectors):
        # 价格趋势
        trend = 100 + np.cumsum(np.random.randn(100) * 0.5 + i * 0.01)
        price_data[sector] = trend
        
        # 成交量
        volume = np.random.uniform(50, 150, 100) * (1 + i * 0.1)
        volume_data[sector] = volume
    
    prices = pd.DataFrame(price_data, index=dates)
    volumes = pd.DataFrame(volume_data, index=dates)
    
    # 模拟广度数据
    sectors_breadth = {}
    for sector in sectors:
        total = 100
        advances = np.random.randint(20, 80)
        declines = np.random.randint(20, 80)
        unchanged = total - advances - declines
        
        sectors_breadth[sector] = {
            'advances': max(0, advances),
            'declines': max(0, declines),
            'unchanged': max(0, unchanged),
            'total_stocks': total,
            'advancing_volume': np.random.uniform(30, 70),
            'declining_volume': np.random.uniform(30, 70),
            'new_highs_52w': np.random.randint(0, 20),
            'new_lows_52w': np.random.randint(0, 10),
            'above_ma50': np.random.randint(30, 80),
            'above_ma200': np.random.randint(20, 70),
            'avg_rsi': np.random.uniform(40, 60),
            'individual_returns': np.random.randn(20).tolist()
        }
    
    # 2. 执行分析
    print("=== 统一板块轮动分析系统 ===\n")
    
    # 使用快速分析
    summary = quick_analysis(prices, volumes, sectors_breadth)
    
    print(f"分析时间: {summary['timestamp']}")
    print(f"\n市场概况:")
    print(f"  整体健康度: {summary['market_health']:.1f}")
    print(f"  轮动阶段: {summary['rotation_stage']}")
    print(f"  轮动强度: {summary['rotation_intensity']}")
    print(f"  统一速度: {summary['unified_velocity']}")
    print(f"  风险等级: {summary['risk_level']}")
    print(f"  投资信号: {summary['investment_signal']}")
    
    print(f"\n推荐配置:")
    for sector_info in summary['top_sectors']:
        print(f"  {sector_info['sector']}: {sector_info['weight']:.1%} (健康度: {sector_info['unified_health']})")
    
    if summary['warnings_count'] > 0:
        print(f"\n⚠️ 有{summary['warnings_count']}个风险预警")
    
    # 3. 详细分析
    system = UnifiedRotationSystem()
    
    # 准备详细数据
    sectors_breadth_data = {}
    for sector, data in sectors_breadth.items():
        sectors_breadth_data[sector] = SectorBreadthData(
            sector_name=sector,
            timestamp=datetime.now(),
            **data
        )
    
    # 执行详细分析
    detailed_result = system.analyze(prices, volumes, sectors_breadth_data)
    
    print(f"\n详细分析结果:")
    print(f"\n板块内部健康度:")
    for sector, metrics in detailed_result.breadth_by_sector.items():
        print(f"  {sector}:")
        print(f"    内部健康度: {metrics.internal_health:.1f}")
        print(f"    涨跌比: {metrics.ad_ratio:.2f}")
        print(f"    纯度: {metrics.purity:.2f}")
        if metrics.price_breadth_divergence:
            print(f"    背离: {metrics.price_breadth_divergence['type']}")
    
    print(f"\n投资建议:")
    signals = detailed_result.investment_signals
    print(f"  整体信号: {signals['overall_signal']}")
    print(f"  仓位调整: {signals['position_adjustment']}")
    if signals['rotation_opportunity']:
        print(f"  ✓ 存在轮动机会")
    
    if signals['recommended_sectors']:
        print(f"  推荐板块:")
        for rec in signals['recommended_sectors']:
            print(f"    - {rec['sector']} (权重: {rec['weight']:.1%}, 健康度: {rec['health']})")
    
    if detailed_result.risk_warnings:
        print(f"\n风险预警:")
        for warning in detailed_result.risk_warnings:
            print(f"  [{warning['level'].upper()}] {warning['message']}")
            print(f"    建议: {warning['action']}")
    
    return detailed_result


if __name__ == "__main__":
    # 运行示例
    result = example_usage()
```

## 三、系统特点

### 3.1 消除重复计算

- **广度模块**：只计算板块内部指标（AD比率、纯度、内部健康度等）
- **轮动模块**：只计算板块间指标（相对强度、排名变化、轮动阶段等）
- **统一指标**：在系统层面整合两个模块的结果

### 3.2 清晰的数据流

```
原始数据 → MarketBreadthAnalyzer（内部分析）
         ↓
     BreadthMetrics
         ↓
prices/volumes + BreadthMetrics → SectorRotationAnalyzer（动态分析）
         ↓
    RotationMetrics
         ↓
   UnifiedRotationSystem（整合）
         ↓
   UnifiedAnalysisResult（最终结果）
```

### 3.3 统一的关键指标

| 指标       | 计算方式           | 负责模块 |
| ---------- | ------------------ | -------- |
| 内部健康度 | 基于AD比率、纯度等 | 广度模块 |
| 相对强度   | 基于价格动量       | 轮动模块 |
| 统一健康度 | 50%内部 + 50%外部  | 系统层   |
| 轮动速度   | 排名变化速度       | 轮动模块 |
| 统一速度   | 70%价格 + 30%广度  | 系统层   |
| RII指数    | 60%价格 + 40%广度  | 轮动模块 |

### 3.4 投资决策支持

- 综合内外部因素的板块权重配置
- 基于广度验证的轮动信号
- 多维度的风险预警机制

---

**文档版本**: 2.0  
**最后更新**: 2025-01-07  
**主要改进**: 消除重复计算，建立清晰的模块协作关系