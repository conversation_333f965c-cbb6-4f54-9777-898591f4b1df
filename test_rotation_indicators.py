#!/usr/bin/env python3
"""
测试板块轮动指标的生成和显示
"""

import sys
import os
import json
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def test_rotation_indicators():
    """测试板块轮动指标"""
    print("🧪 测试板块轮动指标生成...")
    
    try:
        from multi_timeframe_analyzer import analyze_market_mtf
        
        # 测试Technology板块的多时间框架分析
        print("📊 分析Technology板块...")
        
        result = analyze_market_mtf(
            sector="Technology",
            timeframes=['5m', '1h', '1d'],
            output_format='full'
        )
        
        print("✅ 分析完成!")
        
        # 检查是否包含轮动指标
        if 'rotation_analysis' in result:
            rotation = result['rotation_analysis']
            print("\n🔄 板块轮动指标:")
            print(f"  统一轮动强度指数 (RII): {rotation.get('unified_rii', 'N/A')}")
            print(f"  价格离散度: {rotation.get('price_dispersion', 'N/A')}")
            print(f"  排名变化速度: {rotation.get('rank_velocity', 'N/A')}")
            print(f"  成交量集中度: {rotation.get('volume_concentration', 'N/A')}")
            print(f"  轮动阶段: {rotation.get('rotation_stage', 'N/A')}")
            print(f"  风险等级: {rotation.get('risk_level', 'N/A')}")
            
            if 'stage_probabilities' in rotation:
                print("  阶段概率:")
                for stage, prob in rotation['stage_probabilities'].items():
                    print(f"    {stage}: {prob:.1%}")
            
            if 'momentum_analysis' in rotation:
                momentum = rotation['momentum_analysis']
                print("  动量分析:")
                print(f"    板块动量: {momentum.get('sector_momentum', 'N/A')}")
                print(f"    相对强度: {momentum.get('relative_strength', 'N/A')}")
                print(f"    动量一致性: {momentum.get('momentum_coherence', 'N/A')}")
        else:
            print("❌ 未找到rotation_analysis部分")
        
        # 检查统一决策
        if 'unified_decision' in result:
            decision = result['unified_decision']
            print("\n🎯 统一决策:")
            if 'position_recommendation' in decision:
                pos = decision['position_recommendation']
                print(f"  仓位建议: {pos.get('suggested', 'N/A')}")
                print(f"  置信度: {pos.get('confidence', 'N/A'):.1%}")
                print(f"  决策理由: {pos.get('reasoning', 'N/A')}")
        
        # 检查风险评估
        if 'risk_assessment' in result:
            risk = result['risk_assessment']
            print("\n⚠️ 风险评估:")
            if 'overall_risk' in risk:
                overall = risk['overall_risk']
                print(f"  整体风险: {overall.get('level', 'N/A')}")
                print(f"  风险描述: {overall.get('description', 'N/A')}")
                if 'factors' in overall:
                    print(f"  风险因素: {', '.join(overall['factors'][:3])}")
        
        # 保存测试结果到文件
        with open('test_rotation_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 完整结果已保存到: test_rotation_result.json")
        print(f"📊 报告包含 {len(result)} 个主要部分")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface():
    """测试Web界面的轮动指标显示"""
    print("\n🌐 测试Web界面轮动指标显示...")
    
    try:
        # 模拟Web界面的数据结构
        mock_rotation_data = {
            'unified_rii': 0.65,
            'price_dispersion': 0.42,
            'rank_velocity': 0.28,
            'volume_concentration': 0.73,
            'rotation_stage': 'acceleration',
            'risk_level': 'medium',
            'stage_probabilities': {
                'stable': 0.1,
                'convergence': 0.2,
                'startup': 0.15,
                'acceleration': 0.45,
                'chaos': 0.1
            },
            'momentum_analysis': {
                'sector_momentum': 0.68,
                'relative_strength': 0.72,
                'momentum_coherence': 0.58
            }
        }
        
        print("✅ 模拟轮动数据生成成功")
        print("📊 轮动指标数据:")
        for key, value in mock_rotation_data.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 板块轮动指标测试开始")
    print("=" * 50)
    
    # 测试1: 轮动指标生成
    test1_success = test_rotation_indicators()
    
    # 测试2: Web界面显示
    test2_success = test_web_interface()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"  轮动指标生成: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  Web界面显示: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过! 板块轮动指标系统已就绪")
        print("\n💡 使用说明:")
        print("1. 启动Web界面: python web_interface.py")
        print("2. 选择板块和多时间框架组合")
        print("3. 查看完整的板块轮动指标分析")
        print("4. 轮动指标包括: RII指数、轮动阶段、价格离散度等")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置")
    
    return test1_success and test2_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
