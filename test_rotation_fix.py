#!/usr/bin/env python3
"""
测试板块轮动数据修复
验证数据类型转换和日期范围是否正确
"""

import sys
import os
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def test_data_types():
    """测试数据类型转换"""
    print("🧪 测试数据类型转换...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        from decimal import Decimal
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取一条市场广度数据测试
        cursor.execute("""
        SELECT market, advances, declines, total_stocks, 
               new_highs_52w, new_lows_52w, avg_rsi, internal_health
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d'
        ORDER BY recorded_at DESC
        LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            market, advances, declines, total_stocks, new_highs, new_lows, avg_rsi, internal_health = result
            
            print(f"📊 原始数据类型:")
            print(f"   advances: {type(advances)} = {advances}")
            print(f"   avg_rsi: {type(avg_rsi)} = {avg_rsi}")
            print(f"   internal_health: {type(internal_health)} = {internal_health}")
            
            # 测试类型转换
            advances = float(advances) if advances else 0
            declines = float(declines) if declines else 0
            total_stocks = float(total_stocks) if total_stocks else 1
            avg_rsi = float(avg_rsi) if avg_rsi else 50.0
            internal_health = float(internal_health) if internal_health else 50.0
            
            print(f"\n✅ 转换后数据类型:")
            print(f"   advances: {type(advances)} = {advances}")
            print(f"   avg_rsi: {type(avg_rsi)} = {avg_rsi}")
            print(f"   internal_health: {type(internal_health)} = {internal_health}")
            
            # 测试计算
            advance_ratio = advances / total_stocks
            rii = abs(advance_ratio - (declines / total_stocks))
            price_dispersion = abs(avg_rsi - 50.0) / 50.0
            volume_concentration = internal_health / 100.0
            rank_velocity = min(rii * 0.5, 1.0)
            composite_score = (rii * 0.3 + price_dispersion * 0.25 + 
                             volume_concentration * 0.25 + rank_velocity * 0.2)
            
            print(f"\n🔢 计算结果:")
            print(f"   RII: {rii:.4f}")
            print(f"   价格离散度: {price_dispersion:.4f}")
            print(f"   成交量集中度: {volume_concentration:.4f}")
            print(f"   排名变化速度: {rank_velocity:.4f}")
            print(f"   综合得分: {composite_score:.4f}")
            
            print("✅ 数据类型转换测试通过")
            return True
        else:
            print("❌ 未找到测试数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_range():
    """测试日期范围检查"""
    print("\n📅 测试日期范围检查...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查市场广度数据的实际日期范围
        cursor.execute("""
        SELECT MIN(DATE(recorded_at)) as earliest, 
               MAX(DATE(recorded_at)) as latest,
               COUNT(DISTINCT DATE(recorded_at)) as total_days
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d'
        """)
        
        breadth_result = cursor.fetchone()
        
        # 检查轮动数据的日期范围
        cursor.execute("""
        SELECT MIN(DATE(recorded_at)) as earliest,
               MAX(DATE(recorded_at)) as latest,
               COUNT(DISTINCT DATE(recorded_at)) as total_days
        FROM sector_rotation_metrics_gics
        """)
        
        rotation_result = cursor.fetchone()
        
        # 检查最近30天的缺失日期
        cursor.execute("""
        SELECT DISTINCT DATE(mb.recorded_at) as date,
               COUNT(DISTINCT mb.market) as market_count
        FROM market_breadth_metrics_gics mb
        LEFT JOIN sector_rotation_metrics sr ON DATE(mb.recorded_at) = DATE(sr.recorded_at)
        WHERE mb.recorded_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND sr.recorded_at IS NULL
        AND mb.timeframe = '1d'
        GROUP BY DATE(mb.recorded_at)
        HAVING COUNT(DISTINCT mb.market) >= 5
        ORDER BY date DESC
        """)
        
        missing_results = cursor.fetchall()
        
        conn.close()
        
        print("📊 数据范围统计:")
        if breadth_result:
            earliest, latest, total_days = breadth_result
            print(f"   市场广度数据: {earliest} 到 {latest} ({total_days} 天)")
        
        if rotation_result and rotation_result[0]:
            earliest, latest, total_days = rotation_result
            print(f"   板块轮动数据: {earliest} 到 {latest} ({total_days} 天)")
        else:
            print(f"   板块轮动数据: 无数据")
        
        print(f"\n📅 最近30天缺失的轮动数据:")
        if missing_results:
            for date, market_count in missing_results:
                print(f"   {date}: {market_count} 个市场有广度数据但缺少轮动数据")
        else:
            print("   无缺失数据")
        
        # 检查今天的日期
        today = datetime.now().date()
        print(f"\n📆 当前日期: {today}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日期范围检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_date_calculation():
    """测试单个日期的轮动计算"""
    print("\n🔄 测试单个日期的轮动计算...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 找一个有市场广度数据但没有轮动数据的日期
        cursor.execute("""
        SELECT DISTINCT DATE(mb.recorded_at) as date
        FROM market_breadth_metrics_gics mb
        LEFT JOIN sector_rotation_metrics_gics sr ON DATE(mb.recorded_at) = DATE(sr.recorded_at)
        WHERE sr.recorded_at IS NULL
        AND mb.timeframe = '1d'
        GROUP BY DATE(mb.recorded_at)
        HAVING COUNT(DISTINCT mb.market) >= 3
        ORDER BY date DESC
        LIMIT 1
        """)
        
        result = cursor.fetchone()
        
        if result:
            test_date = result[0]
            print(f"📅 测试日期: {test_date}")
            
            # 获取该日期的市场广度数据
            cursor.execute("""
            SELECT market, advances, declines, total_stocks, 
                   new_highs_52w, new_lows_52w, avg_rsi, internal_health
            FROM market_breadth_metrics_gics 
            WHERE DATE(recorded_at) = %s AND timeframe = '1d'
            ORDER BY market
            LIMIT 3
            """, (test_date,))
            
            breadth_data = cursor.fetchall()
            
            print(f"📊 找到 {len(breadth_data)} 条市场广度数据")
            
            # 测试计算前3个市场
            for i, row in enumerate(breadth_data, 1):
                market, advances, declines, total_stocks, new_highs, new_lows, avg_rsi, internal_health = row
                
                # 类型转换
                advances = float(advances) if advances else 0
                declines = float(declines) if declines else 0
                total_stocks = float(total_stocks) if total_stocks else 1
                avg_rsi = float(avg_rsi) if avg_rsi else 50.0
                internal_health = float(internal_health) if internal_health else 50.0
                
                # 计算指标
                advance_ratio = advances / total_stocks
                decline_ratio = declines / total_stocks
                rii = abs(advance_ratio - decline_ratio)
                price_dispersion = abs(avg_rsi - 50.0) / 50.0
                volume_concentration = internal_health / 100.0
                
                print(f"   {i}. {market}:")
                print(f"      RII: {rii:.4f}")
                print(f"      价格离散度: {price_dispersion:.4f}")
                print(f"      成交量集中度: {volume_concentration:.4f}")
            
            print("✅ 单日计算测试通过")
            return True
        else:
            print("⚠️  所有日期的轮动数据都已存在")
            return True
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 单日计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 板块轮动数据修复测试")
    print("=" * 50)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试1: 数据类型转换
    test1_ok = test_data_types()
    
    # 测试2: 日期范围检查
    test2_ok = test_date_range()
    
    # 测试3: 单日计算
    test3_ok = test_single_date_calculation()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"   数据类型转换: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"   日期范围检查: {'✅ 通过' if test2_ok else '❌ 失败'}")
    print(f"   单日计算测试: {'✅ 通过' if test3_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok and test3_ok:
        print("\n🎉 所有测试通过!")
        print("\n💡 现在可以安全运行:")
        print("   python backfill_rotation_data.py")
    else:
        print("\n⚠️  部分测试失败，请检查问题")
    
    return test1_ok and test2_ok and test3_ok

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
