#!/usr/bin/env python3
"""
调试并行处理失败的脚本
"""

import sys
import os
import traceback
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor

# 添加路径
sys.path.insert(0, 'market-breadth-task')
sys.path.insert(0, 'config')

def test_single_symbol_detailed(symbol):
    """详细测试单个股票的处理过程"""
    
    print(f"\n🔍 详细测试 {symbol}...")
    
    try:
        # 步骤1: 导入模块
        print("  步骤1: 导入模块...")
        from utils import download_hist_price
        from db_settings import get_default_db_config
        from mtf_historical_config import get_mtf_config
        print("  ✅ 模块导入成功")
        
        # 步骤2: 获取配置
        print("  步骤2: 获取配置...")
        tf_config = get_mtf_config('1d')
        db_config = get_default_db_config()
        print("  ✅ 配置获取成功")
        
        # 步骤3: 计算时间范围
        print("  步骤3: 计算时间范围...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=600)
        print(f"  ✅ 时间范围: {start_date.date()} 到 {end_date.date()}")
        
        # 步骤4: 下载数据
        print("  步骤4: 下载数据...")
        price_data = download_hist_price(
            symbols=[symbol],
            interval=tf_config['interval'],
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            columns=['h', 'l', 'c', 'v'],
            threads=1,
            verbose=False
        )
        
        if symbol not in price_data or price_data[symbol] is None or price_data[symbol].empty:
            print(f"  ❌ 数据下载失败: 无数据")
            return False
            
        df = price_data[symbol]
        print(f"  ✅ 数据下载成功: {len(df)}条记录")
        print(f"      列名: {list(df.columns)}")
        
        # 步骤5: 处理列名
        print("  步骤5: 处理列名...")
        if 'h' in df.columns:
            df = df.rename(columns={'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'})
            print("  ✅ 列名重命名完成")
        else:
            print("  ✅ 列名已正确")
        
        df = df.sort_index()
        total_calculated = len(df)
        print(f"  ✅ 数据排序完成: {total_calculated}条")
        
        # 步骤6: 计算技术指标
        print("  步骤6: 计算技术指标...")
        
        # 基础移动平均
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()
        df['ma200'] = df['close'].rolling(200).mean()
        print("  ✅ 移动平均计算完成")
        
        # 布尔值指标
        df['above_ma5'] = df['close'] > df['ma5']
        df['above_ma10'] = df['close'] > df['ma10']
        df['above_ma20'] = df['close'] > df['ma20']
        df['above_ma50'] = df['close'] > df['ma50']
        df['above_ma200'] = df['close'] > df['ma200']
        print("  ✅ 布尔指标计算完成")
        
        # RSI计算
        if len(df) >= 14:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi_14'] = 100 - (100 / (1 + rs))
            print("  ✅ RSI计算完成")
        
        # 52周指标
        if tf_config.get('supports_52w', False):
            window_52w = 252
            if len(df) >= 20:
                actual_window = min(len(df), window_52w)
                df['high_52w'] = df['high'].rolling(window=actual_window, min_periods=20).max()
                df['low_52w'] = df['low'].rolling(window=actual_window, min_periods=20).min()
                df['is_new_high_52w'] = df['high'] >= df['high_52w'] * 0.999
                df['is_new_low_52w'] = df['low'] <= df['low_52w'] * 1.001
                df['price_position_52w'] = (df['close'] - df['low_52w']) / (df['high_52w'] - df['low_52w'])
                print("  ✅ 52周指标计算完成")
        
        # 20日指标
        if len(df) >= 20:
            df['high_20d'] = df['high'].rolling(20).max()
            df['low_20d'] = df['low'].rolling(20).min()
            df['is_new_high_20d'] = df['high'] >= df['high_20d'] * 0.999
            df['is_new_low_20d'] = df['low'] <= df['low_20d'] * 1.001
            df['price_position_20d'] = (df['close'] - df['low_20d']) / (df['high_20d'] - df['low_20d'])
            print("  ✅ 20日指标计算完成")
        
        # 步骤7: 准备存储数据
        print("  步骤7: 准备存储数据...")
        
        # 60天存储
        recent_records = 60
        df_recent = df.tail(recent_records)
        print(f"  ✅ 准备存储 {len(df_recent)} 条记录")
        
        # 步骤8: 数据库连接测试
        print("  步骤8: 测试数据库连接...")
        import pymysql
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"  ✅ 数据库连接成功: {result}")
        
        conn.close()
        
        print(f"  🎉 {symbol} 所有步骤测试成功!")
        return True
        
    except Exception as e:
        print(f"  ❌ {symbol} 失败在某个步骤:")
        print(f"     错误: {str(e)}")
        print(f"     详细:")
        traceback.print_exc()
        return False


def test_parallel_processing():
    """测试并行处理"""
    
    print("\n🔄 测试并行处理...")
    
    # 测试股票列表
    test_symbols = ['F', 'GM', 'MSFT', 'AAPL']
    
    # 单进程测试
    print("\n📋 单进程测试:")
    single_results = {}
    for symbol in test_symbols:
        single_results[symbol] = test_single_symbol_detailed(symbol)
    
    print(f"\n📊 单进程结果:")
    for symbol, success in single_results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {symbol}: {status}")
    
    # 多进程测试
    print(f"\n🔄 多进程测试 (2个进程):")
    
    def simple_test_worker(symbol):
        """简化的工作函数"""
        try:
            # 重新导入模块
            import sys
            import os
            current_dir = os.getcwd()
            sys.path.insert(0, os.path.join(current_dir, 'market-breadth-task'))
            sys.path.insert(0, os.path.join(current_dir, 'config'))
            
            from utils import download_hist_price
            from datetime import datetime, timedelta
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            
            price_data = download_hist_price(
                symbols=[symbol],
                interval='1d',
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                columns=['h', 'l', 'c', 'v'],
                threads=1,
                verbose=False
            )
            
            if symbol in price_data and price_data[symbol] is not None:
                df = price_data[symbol]
                return {'symbol': symbol, 'success': True, 'records': len(df)}
            else:
                return {'symbol': symbol, 'success': False, 'error': 'No data'}
                
        except Exception as e:
            return {'symbol': symbol, 'success': False, 'error': str(e)}
    
    # 并行执行
    with ProcessPoolExecutor(max_workers=2) as executor:
        futures = [executor.submit(simple_test_worker, symbol) for symbol in test_symbols]
        
        parallel_results = {}
        for future in futures:
            try:
                result = future.result()
                parallel_results[result['symbol']] = result['success']
                if not result['success']:
                    print(f"  {result['symbol']}: ❌ 失败 - {result.get('error', 'Unknown')}")
                else:
                    print(f"  {result['symbol']}: ✅ 成功 - {result['records']}条记录")
            except Exception as e:
                print(f"  未知股票: ❌ 执行失败 - {e}")
    
    print(f"\n📊 对比结果:")
    print(f"{'股票':<8} {'单进程':<8} {'多进程':<8} {'状态'}")
    print("-" * 35)
    for symbol in test_symbols:
        single = "✅" if single_results.get(symbol, False) else "❌"
        parallel = "✅" if parallel_results.get(symbol, False) else "❌"
        
        if single_results.get(symbol, False) == parallel_results.get(symbol, False):
            status = "一致"
        else:
            status = "不一致 ⚠️"
        
        print(f"{symbol:<8} {single:<8} {parallel:<8} {status}")


if __name__ == "__main__":
    print("🔍 并行处理失败调试工具")
    print("=" * 50)
    
    test_parallel_processing()
