#!/usr/bin/env python3
"""
使用系统现有的MarketBreadthCalculator计算40天真实市场广度数据
直接调用正确的计算逻辑，不再使用简化指标
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def calculate_all_timeframes_breadth():
    """使用系统现有的MarketBreadthCalculator计算所有时间框架的市场广度数据"""
    print("🚀 使用系统现有的MarketBreadthCalculator计算市场广度数据")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 您要求的6个时间框架
    timeframes = ['5m', '15m', '1h', '1d', '1w']
    print("📊 将计算以下时间框架的市场广度数据:")
    for tf in timeframes:
        print(f"   ✅ {tf}")
    print("   ✅ 1M (从1d数据聚合)")
    print("💡 使用系统现有的完整计算逻辑，包括真实的RSI、MA、新高新低等")
    print()
    
    try:
        from market_breadth_calculator import calculate_all_markets_breadth
        
        # 确认执行
        confirm = input(f"\n是否开始计算所有时间框架的市场广度数据? (y/N): ").strip().lower()
        if confirm != 'y':
            print("👋 用户取消操作")
            return False
        
        print("\n🔄 开始计算市场广度数据...")
        print("💡 使用系统现有的MarketBreadthCalculator完整计算逻辑...")
        
        start_time = time.time()
        total_success = 0
        
        # 为每个时间框架计算市场广度数据
        for timeframe in timeframes:
            print(f"\n📈 计算 {timeframe} 时间框架...")
            
            try:
                # 使用系统现有的calculate_all_markets_breadth函数
                # 这个函数包含了所有正确的计算逻辑：
                # - 真实的涨跌家数计算
                # - 真实的52周新高新低计算
                # - 真实的MA50/MA200计算
                # - 真实的RSI计算
                # - 真实的成交量统计
                result = calculate_all_markets_breadth(timeframe, num_processes=6, save_to_db=True)
                
                if result and len(result) > 0:
                    print(f"✅ {timeframe} 完成，计算了 {len(result)} 个市场")
                    total_success += len(result)
                else:
                    print(f"⚠️  {timeframe} 计算返回空结果")
                
                # 避免过快请求
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ {timeframe} 时间框架计算失败: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        duration = time.time() - start_time
        
        print(f"\n🎉 市场广度计算完成!")
        print(f"📊 总成功次数: {total_success}")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        
        # 聚合月度数据
        if total_success > 0:
            print("\n📅 开始聚合月度数据...")
            monthly_success = aggregate_monthly_data_from_daily()
            if monthly_success:
                print("✅ 月度数据聚合完成")
            else:
                print("⚠️  月度数据聚合失败")
        
        # 验证计算结果
        verify_calculation_results()
        
        return total_success > 0
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def aggregate_monthly_data_from_daily():
    """从日数据聚合月度数据"""
    print("📊 从日数据聚合月度市场广度数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取需要聚合的月份（最近3个月即可）
        cursor.execute("""
        SELECT DISTINCT YEAR(recorded_at) as year, MONTH(recorded_at) as month
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d'
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        ORDER BY year DESC, month DESC
        """)
        
        months_to_aggregate = cursor.fetchall()
        
        if not months_to_aggregate:
            print("⚠️  没有找到可聚合的日数据")
            conn.close()
            return False
        
        print(f"📅 找到 {len(months_to_aggregate)} 个月需要聚合")
        
        success_count = 0
        
        for year, month in months_to_aggregate:
            print(f"📊 聚合 {year}-{month:02d} 月度数据...")
            
            # 删除该月的旧月度数据
            cursor.execute("""
            DELETE FROM market_breadth_metrics_gics 
            WHERE timeframe = '1M' 
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            """, (year, month))
            
            # 聚合该月的日数据
            cursor.execute("""
            SELECT market,
                   AVG(total_stocks) as avg_total_stocks,
                   AVG(advances) as avg_advances,
                   AVG(declines) as avg_declines,
                   AVG(unchanged) as avg_unchanged,
                   AVG(new_highs_52w) as avg_new_highs,
                   AVG(new_lows_52w) as avg_new_lows,
                   AVG(above_ma50) as avg_above_ma50,
                   AVG(above_ma200) as avg_above_ma200,
                   AVG(avg_rsi) as avg_rsi,
                   AVG(internal_health) as avg_internal_health,
                   COUNT(*) as trading_days,
                   MAX(recorded_at) as latest_date
            FROM market_breadth_metrics_gics 
            WHERE timeframe = '1d'
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            GROUP BY market
            HAVING COUNT(*) >= 5  -- 至少5个交易日
            """, (year, month))
            
            monthly_data = cursor.fetchall()
            
            if monthly_data:
                # 插入月度聚合数据
                insert_sql = """
                INSERT INTO market_breadth_metrics_gics (
                    market, timeframe, total_stocks, advances, declines, unchanged,
                    new_highs_52w, new_lows_52w, above_ma50, above_ma200,
                    avg_rsi, internal_health, recorded_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for row in monthly_data:
                    market, avg_total, avg_adv, avg_dec, avg_unch, avg_highs, avg_lows, avg_ma50, avg_ma200, avg_rsi, avg_health, trading_days, latest_date = row
                    
                    # 使用该月最后一个交易日作为记录时间
                    record_time = latest_date.replace(hour=16, minute=0, second=0)
                    
                    cursor.execute(insert_sql, (
                        market, '1M', 
                        round(avg_total) if avg_total else 0,
                        round(avg_adv) if avg_adv else 0, 
                        round(avg_dec) if avg_dec else 0, 
                        round(avg_unch) if avg_unch else 0,
                        round(avg_highs) if avg_highs else 0, 
                        round(avg_lows) if avg_lows else 0,
                        round(avg_ma50) if avg_ma50 else 0,
                        round(avg_ma200) if avg_ma200 else 0,
                        round(avg_rsi, 2) if avg_rsi else 50.0, 
                        round(avg_health, 2) if avg_health else 50.0,
                        record_time
                    ))
                
                conn.commit()
                print(f"✅ {year}-{month:02d} 聚合完成，{len(monthly_data)} 个市场")
                success_count += 1
            else:
                print(f"⚠️  {year}-{month:02d} 没有足够的日数据进行聚合")
        
        conn.close()
        
        print(f"\n📊 月度数据聚合总结:")
        print(f"   成功聚合: {success_count}/{len(months_to_aggregate)} 个月")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 月度数据聚合失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_calculation_results():
    """验证计算结果，检查是否还有简化指标"""
    print("\n🔍 验证计算结果，检查是否还有简化指标...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查各时间框架的数据质量
        cursor.execute("""
        SELECT timeframe,
               COUNT(*) as total_records,
               COUNT(DISTINCT market) as unique_markets,
               AVG(CASE WHEN advances > 0 OR declines > 0 THEN 1 ELSE 0 END) * 100 as real_ad_ratio,
               AVG(CASE WHEN new_highs_52w > 0 OR new_lows_52w > 0 THEN 1 ELSE 0 END) * 100 as real_hl_ratio,
               AVG(CASE WHEN above_ma50 > 0 OR above_ma200 > 0 THEN 1 ELSE 0 END) * 100 as real_ma_ratio,
               AVG(CASE WHEN avg_rsi != 50.0 THEN 1 ELSE 0 END) * 100 as real_rsi_ratio
        FROM market_breadth_metrics_gics 
        WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 WHEN '15m' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        print("📊 数据质量验证结果:")
        print("-" * 80)
        print(f"{'时间框架':<8} {'记录数':<8} {'市场数':<8} {'真实涨跌%':<12} {'真实新高低%':<14} {'真实MA%':<10} {'真实RSI%':<10}")
        print("-" * 80)
        
        all_good = True
        for row in results:
            tf, total, markets, ad_ratio, hl_ratio, ma_ratio, rsi_ratio = row
            
            # 检查是否有简化指标
            if ad_ratio < 50:  # 涨跌家数应该大部分有真实值
                print(f"{tf:<8} {total:<8} {markets:<8} {ad_ratio:<12.1f}% ❌ 涨跌计算异常")
                all_good = False
            elif rsi_ratio < 50:  # RSI不应该都是50.0
                print(f"{tf:<8} {total:<8} {markets:<8} {ad_ratio:<12.1f}% {hl_ratio:<14.1f}% {ma_ratio:<10.1f}% {rsi_ratio:<10.1f}% ❌ RSI异常")
                all_good = False
            else:
                print(f"{tf:<8} {total:<8} {markets:<8} {ad_ratio:<12.1f}% {hl_ratio:<14.1f}% {ma_ratio:<10.1f}% {rsi_ratio:<10.1f}% ✅")
        
        conn.close()
        
        if all_good:
            print("\n✅ 所有指标都是真实计算的，没有简化指标！")
        else:
            print("\n❌ 发现简化指标，需要检查计算逻辑")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 使用系统现有MarketBreadthCalculator的完整市场广度数据计算工具")
    print("不再使用任何简化指标，所有计算都基于真实股价数据")
    print()
    
    success = calculate_all_timeframes_breadth()
    
    if success:
        print("\n🎉 市场广度数据计算成功!")
        print("\n💡 下一步操作:")
        print("1. 检查数据库中的指标是否都是真实值")
        print("2. 运行: python calculate_30day_rotation.py")
        print("3. 启动Web界面查看完整轮动分析")
        print("4. 现在所有指标都应该有真实数据了!")
    else:
        print("\n❌ 市场广度数据计算失败")
        print("💡 故障排除:")
        print("1. 检查MarketBreadthCalculator是否正常工作")
        print("2. 确认时间框架配置正确")
        print("3. 查看错误日志信息")
    
    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
