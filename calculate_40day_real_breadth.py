#!/usr/bin/env python3
"""
使用现有的MarketBreadthCalculator计算40天真实市场广度数据
支持所有时间框架，使用完整的计算逻辑
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def calculate_all_timeframes_breadth():
    """计算所有时间框架的市场广度数据"""
    print("🚀 使用MarketBreadthCalculator计算所有时间框架的市场广度数据")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 您要求的6个时间框架 - 使用配置文件中的正确格式
    timeframes = ['5m', '15m', '1h', '1d', '1w']
    print("📊 将计算以下时间框架的40天历史数据:")
    for tf in timeframes:
        print(f"   ✅ {tf}")
    print("   ✅ 1M (从1d数据聚合)")
    print("💡 使用完整的市场广度计算逻辑，包括RSI、MA、新高新低等")
    print()
    
    try:
        from market_breadth_calculator import calculate_all_markets_breadth
        
        # 确认执行
        confirm = input(f"\n是否开始计算所有时间框架的市场广度数据? (y/N): ").strip().lower()
        if confirm != 'y':
            print("👋 用户取消操作")
            return False
        
        print("\n🔄 开始计算市场广度数据...")
        print("💡 使用现有的完整计算逻辑...")
        
        start_time = time.time()
        total_success = 0
        
        # 为每个时间框架计算40天历史数据
        for timeframe in timeframes:
            print(f"\n📈 计算 {timeframe} 时间框架的40天历史数据...")

            try:
                # 计算40天的历史数据
                tf_success = calculate_timeframe_historical_data(timeframe, days=40)

                if tf_success > 0:
                    print(f"✅ {timeframe} 完成，成功计算 {tf_success} 天")
                    total_success += tf_success
                else:
                    print(f"⚠️  {timeframe} 计算失败")

                # 避免过快请求
                time.sleep(2)

            except Exception as e:
                print(f"❌ {timeframe} 时间框架计算失败: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        duration = time.time() - start_time
        
        print(f"\n🎉 市场广度计算完成!")
        print(f"📊 总成功次数: {total_success}")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        
        # 聚合月度数据
        if total_success > 0:
            print("\n📅 开始聚合月度数据...")
            monthly_success = aggregate_monthly_data_from_daily()
            if monthly_success:
                print("✅ 月度数据聚合完成")
            else:
                print("⚠️  月度数据聚合失败")
        
        # 验证计算结果
        verify_historical_data()
        
        return total_success > 0
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_timeframe_historical_data(timeframe, days=40):
    """使用系统现有的MarketBreadthCalculator计算指定时间框架的历史数据"""
    print(f"🔄 使用MarketBreadthCalculator计算 {timeframe} 的历史数据...")

    try:
        from market_breadth_calculator import calculate_all_markets_breadth

        print(f"💡 使用系统现有的完整计算逻辑，包括真实的RSI、MA、52周新高新低等指标")

        # 直接使用系统现有的calculate_all_markets_breadth函数
        # 这个函数包含了所有正确的计算逻辑
        result = calculate_all_markets_breadth(timeframe, num_processes=6, save_to_db=True)

        if result and len(result) > 0:
            print(f"✅ {timeframe} 计算完成，成功计算 {len(result)} 个市场")
            return len(result)
        else:
            print(f"⚠️  {timeframe} 计算返回空结果")
            return 0

    except Exception as e:
        print(f"❌ {timeframe} 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

# 使用系统现有的MarketBreadthCalculator，不需要自定义计算函数

def aggregate_monthly_data_from_daily():
    """多进程工作函数：计算单个市场的广度指标"""
    market, timeframe, price_data, daily_data, days = task_data

    try:
        import pymysql
        from db_settings import get_default_db_config
        from datetime import datetime, timedelta
        import pandas as pd

        # 创建数据库连接（每个进程独立连接）
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        # 获取该市场的股票列表
        cursor.execute(f"SELECT company FROM index_company_mapping_gics WHERE market='{market}'")
        market_companies = [row[0] for row in cursor.fetchall()]

        if not market_companies:
            conn.close()
            return 0

        print(f"🔄 计算 {market} 的 {timeframe} 广度指标...")

        # 获取所有时间点
        all_timestamps = set()
        for company in market_companies:
            if company in price_data and price_data[company] is not None:
                if hasattr(price_data[company], 'index'):
                    all_timestamps.update(price_data[company].index)

        if not all_timestamps:
            print(f"⚠️  {market} 没有找到有效的时间点数据")
            conn.close()
            return 0

        # 只取最近N天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        recent_timestamps = [ts for ts in all_timestamps if ts >= start_date]
        recent_timestamps = sorted(recent_timestamps)

        success_count = 0

        # 为每个时间点计算广度指标
        for timestamp in recent_timestamps:
            try:
                # 计算该时间点的广度指标
                breadth_metrics = calculate_advanced_breadth_metrics(
                    timestamp, market, market_companies, price_data, daily_data
                )

                if breadth_metrics:
                    # 保存到数据库
                    save_breadth_to_db(cursor, market, timeframe, timestamp, breadth_metrics)
                    success_count += 1

            except Exception as e:
                continue

        conn.commit()
        conn.close()

        print(f"✅ {market} {timeframe} 完成，成功 {success_count} 条记录")
        return success_count

    except Exception as e:
        print(f"❌ {market} {timeframe} 计算失败: {e}")
        return 0

def calculate_breadth_from_price_data(timeframe, price_data, markets, days):
    """从价格数据计算市场广度指标"""
    try:
        import pymysql
        from db_settings import get_default_db_config
        import pandas as pd
        from datetime import datetime, timedelta

        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        success_count = 0

        # 获取所有时间点
        all_timestamps = set()
        for symbol, data in price_data.items():
            if data is not None and hasattr(data, 'index'):
                all_timestamps.update(data.index)

        # 只取最近N天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        recent_timestamps = [ts for ts in all_timestamps if ts >= start_date]
        recent_timestamps = sorted(recent_timestamps)

        print(f"📊 {timeframe} 找到 {len(recent_timestamps)} 个时间点")

        # 为每个市场计算每个时间点的广度指标
        for market in markets:
            # 获取该市场的股票列表
            cursor.execute(f"SELECT company FROM index_company_mapping_gics WHERE market='{market}'")
            market_companies = [row[0] for row in cursor.fetchall()]

            if not market_companies:
                continue

            print(f"🔄 计算 {market} 的 {timeframe} 广度指标...")

            for timestamp in recent_timestamps:
                try:
                    # 计算该时间点该市场的广度指标
                    breadth_metrics = calculate_market_breadth_at_time(
                        timestamp, market, market_companies, price_data
                    )

                    if breadth_metrics:
                        # 保存到数据库
                        save_breadth_to_db(cursor, market, timeframe, timestamp, breadth_metrics)
                        success_count += 1

                except Exception as e:
                    continue

        conn.commit()
        conn.close()

        print(f"✅ {timeframe} 完成，成功计算 {success_count} 条记录")
        return success_count

    except Exception as e:
        print(f"❌ 广度指标计算失败: {e}")
        return 0

def calculate_advanced_breadth_metrics(timestamp, market, companies, price_data, daily_data):
    """计算高级市场广度指标，包括真实的52周新高新低和MA指标"""
    try:
        valid_stocks = 0
        advances = 0
        declines = 0
        unchanged = 0
        total_volume = 0
        advancing_volume = 0
        declining_volume = 0

        # 52周和MA指标
        new_highs_52w = 0
        new_lows_52w = 0
        above_ma50 = 0
        above_ma200 = 0
        rsi_values = []

        for company in companies:
            if company not in price_data:
                continue

            data = price_data[company]
            if data is None or not hasattr(data, 'index'):
                continue

            if timestamp not in data.index:
                continue

            valid_stocks += 1

            # 获取当前价格数据
            current_row = data.loc[timestamp]
            current_price = current_row.get('c', current_row.get('close', 0))
            current_volume = current_row.get('v', current_row.get('volume', 0))

            # 计算涨跌（比较前一期价格）
            try:
                data_sorted = data.sort_index()
                current_idx = data_sorted.index.get_loc(timestamp)
                if current_idx > 0:
                    prev_price = data_sorted.iloc[current_idx - 1].get('c', current_price)

                    if current_price > prev_price:
                        advances += 1
                        advancing_volume += current_volume
                    elif current_price < prev_price:
                        declines += 1
                        declining_volume += current_volume
                    else:
                        unchanged += 1
                else:
                    unchanged += 1
            except:
                unchanged += 1

            total_volume += current_volume

            # 计算52周新高新低（使用365天的主数据，足够52周计算）
            try:
                # 获取52周的数据
                from datetime import timedelta
                week_52_ago = timestamp - timedelta(weeks=52)

                # 从主数据中获取52周数据
                stock_52w_data = data[data.index >= week_52_ago]

                if len(stock_52w_data) > 0:
                    high_52w = stock_52w_data['h'].max() if 'h' in stock_52w_data.columns else stock_52w_data.get('high', pd.Series()).max()
                    low_52w = stock_52w_data['l'].min() if 'l' in stock_52w_data.columns else stock_52w_data.get('low', pd.Series()).min()

                    # 判断是否创新高新低
                    current_high = current_row.get('h', current_row.get('high', current_price))
                    current_low = current_row.get('l', current_row.get('low', current_price))

                    if pd.notna(high_52w) and current_high >= high_52w:
                        new_highs_52w += 1
                    if pd.notna(low_52w) and current_low <= low_52w:
                        new_lows_52w += 1
            except:
                pass

            # 计算MA指标（使用365天的主数据，足够MA200计算）
            try:
                # 计算MA50和MA200
                data_sorted = data.sort_index()
                close_prices = data_sorted.get('c', data_sorted.get('close', pd.Series()))

                if len(close_prices) >= 50:
                    # 计算到当前时间点的MA50
                    current_idx = data_sorted.index.get_loc(timestamp)
                    if current_idx >= 49:  # 至少需要50个数据点
                        ma50_data = close_prices.iloc[max(0, current_idx-49):current_idx+1]
                        ma50 = ma50_data.mean()
                        if pd.notna(ma50) and current_price > ma50:
                            above_ma50 += 1

                if len(close_prices) >= 200:
                    # 计算到当前时间点的MA200
                    current_idx = data_sorted.index.get_loc(timestamp)
                    if current_idx >= 199:  # 至少需要200个数据点
                        ma200_data = close_prices.iloc[max(0, current_idx-199):current_idx+1]
                        ma200 = ma200_data.mean()
                        if pd.notna(ma200) and current_price > ma200:
                            above_ma200 += 1

                # 计算RSI
                if len(close_prices) >= 14:
                    current_idx = data_sorted.index.get_loc(timestamp)
                    if current_idx >= 13:  # 至少需要14个数据点
                        rsi_data = close_prices.iloc[max(0, current_idx-13):current_idx+1]
                        rsi = calculate_rsi(rsi_data, period=14)
                        if not pd.isna(rsi):
                            rsi_values.append(rsi)
            except:
                pass

        if valid_stocks == 0:
            return None

        # 计算平均RSI
        avg_rsi = sum(rsi_values) / len(rsi_values) if rsi_values else 50.0

        # 计算内部健康度
        advance_ratio = advances / valid_stocks if valid_stocks > 0 else 0.5
        internal_health = advance_ratio * 100

        return {
            'total_stocks': valid_stocks,
            'advances': advances,
            'declines': declines,
            'unchanged': unchanged,
            'total_volume': total_volume,
            'advancing_volume': advancing_volume,
            'declining_volume': declining_volume,
            'new_highs_52w': new_highs_52w,
            'new_lows_52w': new_lows_52w,
            'above_ma50': above_ma50,
            'above_ma200': above_ma200,
            'avg_rsi': avg_rsi,
            'internal_health': internal_health
        }

    except Exception as e:
        return None

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    try:
        import pandas as pd

        if len(prices) < period + 1:
            return 50.0

        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    except:
        return 50.0

def batch_save_to_db(cursor, batch_data):
    """批量保存广度指标到数据库"""
    try:
        insert_sql = """
        INSERT INTO market_breadth_metrics_gics (
            market, timeframe, total_stocks, advances, declines, unchanged,
            total_volume, advancing_volume, declining_volume,
            new_highs_52w, new_lows_52w, above_ma50, above_ma200,
            avg_rsi, internal_health, recorded_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            total_stocks = VALUES(total_stocks),
            advances = VALUES(advances),
            declines = VALUES(declines),
            unchanged = VALUES(unchanged),
            total_volume = VALUES(total_volume),
            advancing_volume = VALUES(advancing_volume),
            declining_volume = VALUES(declining_volume),
            new_highs_52w = VALUES(new_highs_52w),
            new_lows_52w = VALUES(new_lows_52w),
            above_ma50 = VALUES(above_ma50),
            above_ma200 = VALUES(above_ma200),
            avg_rsi = VALUES(avg_rsi),
            internal_health = VALUES(internal_health)
        """

        # 准备批量数据
        batch_values = []
        for market, timeframe, timestamp, metrics in batch_data:
            batch_values.append((
                market, timeframe,
                metrics['total_stocks'], metrics['advances'], metrics['declines'], metrics['unchanged'],
                metrics['total_volume'], metrics['advancing_volume'], metrics['declining_volume'],
                metrics['new_highs_52w'], metrics['new_lows_52w'],
                metrics['above_ma50'], metrics['above_ma200'],
                metrics['avg_rsi'], metrics['internal_health'],
                timestamp
            ))

        # 批量执行
        cursor.executemany(insert_sql, batch_values)
        return True

    except Exception as e:
        print(f"❌ 批量保存失败: {e}")
        return False

def save_breadth_to_db(cursor, market, timeframe, timestamp, metrics):
    """保存完整的广度指标到数据库"""
    try:
        insert_sql = """
        INSERT INTO market_breadth_metrics_gics (
            market, timeframe, total_stocks, advances, declines, unchanged,
            total_volume, advancing_volume, declining_volume,
            new_highs_52w, new_lows_52w, above_ma50, above_ma200,
            avg_rsi, internal_health, recorded_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            total_stocks = VALUES(total_stocks),
            advances = VALUES(advances),
            declines = VALUES(declines),
            unchanged = VALUES(unchanged),
            total_volume = VALUES(total_volume),
            advancing_volume = VALUES(advancing_volume),
            declining_volume = VALUES(declining_volume),
            new_highs_52w = VALUES(new_highs_52w),
            new_lows_52w = VALUES(new_lows_52w),
            above_ma50 = VALUES(above_ma50),
            above_ma200 = VALUES(above_ma200),
            avg_rsi = VALUES(avg_rsi),
            internal_health = VALUES(internal_health)
        """

        cursor.execute(insert_sql, (
            market, timeframe,
            metrics['total_stocks'], metrics['advances'], metrics['declines'], metrics['unchanged'],
            metrics['total_volume'], metrics['advancing_volume'], metrics['declining_volume'],
            metrics['new_highs_52w'], metrics['new_lows_52w'],
            metrics['above_ma50'], metrics['above_ma200'],
            metrics['avg_rsi'], metrics['internal_health'],
            timestamp
        ))

        return True

    except Exception as e:
        return False

def aggregate_monthly_data_from_daily():
    """从日数据聚合月度数据"""
    print("📊 从日数据聚合月度市场广度数据...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取需要聚合的月份（最近3个月即可）
        cursor.execute("""
        SELECT DISTINCT YEAR(recorded_at) as year, MONTH(recorded_at) as month
        FROM market_breadth_metrics_gics 
        WHERE timeframe = '1d'
        AND recorded_at >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
        ORDER BY year DESC, month DESC
        """)
        
        months_to_aggregate = cursor.fetchall()
        
        if not months_to_aggregate:
            print("⚠️  没有找到可聚合的日数据")
            conn.close()
            return False
        
        print(f"📅 找到 {len(months_to_aggregate)} 个月需要聚合")
        
        success_count = 0
        
        for year, month in months_to_aggregate:
            print(f"📊 聚合 {year}-{month:02d} 月度数据...")
            
            # 删除该月的旧月度数据
            cursor.execute("""
            DELETE FROM market_breadth_metrics_gics 
            WHERE timeframe = '1M' 
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            """, (year, month))
            
            # 聚合该月的日数据
            cursor.execute("""
            SELECT market,
                   AVG(total_stocks) as avg_total_stocks,
                   AVG(advances) as avg_advances,
                   AVG(declines) as avg_declines,
                   AVG(unchanged) as avg_unchanged,
                   AVG(new_highs_52w) as avg_new_highs,
                   AVG(new_lows_52w) as avg_new_lows,
                   AVG(above_ma50) as avg_above_ma50,
                   AVG(above_ma200) as avg_above_ma200,
                   AVG(avg_rsi) as avg_rsi,
                   AVG(internal_health) as avg_internal_health,
                   COUNT(*) as trading_days,
                   MAX(recorded_at) as latest_date
            FROM market_breadth_metrics_gics 
            WHERE timeframe = '1d'
            AND YEAR(recorded_at) = %s AND MONTH(recorded_at) = %s
            GROUP BY market
            HAVING COUNT(*) >= 5  -- 至少5个交易日
            """, (year, month))
            
            monthly_data = cursor.fetchall()
            
            if monthly_data:
                # 插入月度聚合数据
                insert_sql = """
                INSERT INTO market_breadth_metrics_gics (
                    market, timeframe, total_stocks, advances, declines, unchanged,
                    new_highs_52w, new_lows_52w, above_ma50, above_ma200,
                    avg_rsi, internal_health, recorded_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                for row in monthly_data:
                    market, avg_total, avg_adv, avg_dec, avg_unch, avg_highs, avg_lows, avg_ma50, avg_ma200, avg_rsi, avg_health, trading_days, latest_date = row
                    
                    # 使用该月最后一个交易日作为记录时间
                    record_time = latest_date.replace(hour=16, minute=0, second=0)
                    
                    cursor.execute(insert_sql, (
                        market, '1M', 
                        round(avg_total) if avg_total else 0,
                        round(avg_adv) if avg_adv else 0, 
                        round(avg_dec) if avg_dec else 0, 
                        round(avg_unch) if avg_unch else 0,
                        round(avg_highs) if avg_highs else 0, 
                        round(avg_lows) if avg_lows else 0,
                        round(avg_ma50) if avg_ma50 else 0,
                        round(avg_ma200) if avg_ma200 else 0,
                        round(avg_rsi, 2) if avg_rsi else 50.0, 
                        round(avg_health, 2) if avg_health else 50.0,
                        record_time
                    ))
                
                conn.commit()
                print(f"✅ {year}-{month:02d} 聚合完成，{len(monthly_data)} 个市场")
                success_count += 1
            else:
                print(f"⚠️  {year}-{month:02d} 没有足够的日数据进行聚合")
        
        conn.close()
        
        print(f"\n📊 月度数据聚合总结:")
        print(f"   成功聚合: {success_count}/{len(months_to_aggregate)} 个月")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 月度数据聚合失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_historical_data():
    """验证历史数据计算结果"""
    print("\n🔍 验证历史数据计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查各时间框架的数据
        cursor.execute("""
        SELECT timeframe,
               COUNT(DISTINCT DATE(recorded_at)) as unique_dates,
               COUNT(DISTINCT market) as unique_markets,
               COUNT(*) as total_records,
               MIN(recorded_at) as earliest,
               MAX(recorded_at) as latest
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        ORDER BY 
            CASE timeframe 
                WHEN '5min' THEN 1 WHEN '15min' THEN 2 WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 WHEN '1w' THEN 5 WHEN '1M' THEN 6 
            END
        """)
        
        results = cursor.fetchall()
        
        print("📊 历史数据统计:")
        print("-" * 80)
        print(f"{'时间框架':<8} {'天数':<6} {'市场数':<8} {'总记录':<8} {'最早':<12} {'最新':<12}")
        print("-" * 80)
        
        for row in results:
            tf, days, markets, total, earliest, latest = row
            earliest_str = earliest.strftime('%m-%d') if earliest else 'N/A'
            latest_str = latest.strftime('%m-%d') if latest else 'N/A'
            status = " (聚合)" if tf == '1M' else ""
            print(f"{tf:<8} {days:<6} {markets:<8} {total:<8} {earliest_str:<12} {latest_str:<12}{status}")
        
        # 检查数据质量
        cursor.execute("""
        SELECT timeframe,
               AVG(CASE WHEN above_ma50 IS NOT NULL THEN 1 ELSE 0 END) * 100 as ma50_coverage,
               AVG(CASE WHEN above_ma200 IS NOT NULL THEN 1 ELSE 0 END) * 100 as ma200_coverage,
               AVG(CASE WHEN avg_rsi IS NOT NULL THEN 1 ELSE 0 END) * 100 as rsi_coverage
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe
        """)
        
        quality_results = cursor.fetchall()
        
        print("\n📈 数据质量统计:")
        print("-" * 60)
        print(f"{'时间框架':<8} {'MA50覆盖':<10} {'MA200覆盖':<11} {'RSI覆盖':<10}")
        print("-" * 60)
        
        for row in quality_results:
            tf, ma50_cov, ma200_cov, rsi_cov = row
            print(f"{tf:<8} {ma50_cov:<10.1f}% {ma200_cov:<11.1f}% {rsi_cov:<10.1f}%")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 完整市场广度数据计算工具")
    print("使用现有的MarketBreadthCalculator计算完整的市场广度指标")
    print("包括RSI、MA50/MA200、新高新低、涨跌家数等所有指标")
    print()
    
    success = calculate_all_timeframes_breadth()
    
    if success:
        print("\n🎉 市场广度数据计算成功!")
        print("\n💡 下一步操作:")
        print("1. 运行: python calculate_30day_rotation.py")
        print("2. 计算30天板块轮动指标")
        print("3. 启动Web界面查看完整轮动分析")
        print("4. 现在所有指标都应该有真实数据了!")
    else:
        print("\n❌ 市场广度数据计算失败")
        print("💡 故障排除:")
        print("1. 检查数据库API连接")
        print("2. 确认MarketBreadthCalculator正常工作")
        print("3. 查看错误日志信息")
    
    return success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
