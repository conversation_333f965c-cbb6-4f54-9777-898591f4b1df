# 市场广度指标处理逻辑详解

## 📊 指标分类与处理策略

### 1. 52周新高新低 (new_highs_52w, new_lows_52w)

#### 计算原理
- **定义**: 当前价格是否达到过去252个交易日的最高/最低点
- **数据需求**: 至少252个交易日（约1年）的历史数据
- **计算方法**: 比较当前收盘价与过去252天的最高价/最低价

#### 时间框架支持策略
```
✅ 支持的时间框架:
- 1d (日度): 直接从日度数据计算
- 1w (周度): 从日度数据获取，因为周度分析仍关注长期趋势
- 1M (月度): 从日度数据获取，月度分析更需要长期视角

❌ 不支持的时间框架:
- 5m (5分钟): 只有7天数据，无法计算52周
- 15m (15分钟): 只有14天数据，无法计算52周  
- 1h (1小时): 只有30天数据，无法计算52周
```

#### 为什么小时数据不用日数据计算？
1. **性能考虑**: 每次计算都要获取所有股票1年的日度数据，计算量巨大
2. **实时性需求**: 小时级分析更关注短期波动，52周新高新低参考价值有限
3. **频率不匹配**: 小时级别的交易决策不需要52周的长期指标

### 2. 移动平均线 (above_ma50, above_ma200)

#### 计算原理
- **MA50**: 过去50个周期的平均价格
- **MA200**: 过去200个周期的平均价格
- **判断标准**: 当前价格是否高于移动平均线

#### 数据需求分析
```
时间框架 | MA50需求 | MA200需求 | 历史数据 | 支持情况
--------|---------|----------|---------|----------
5分钟    | 250分钟  | 1000分钟  | 7天     | ❌ 数据不足
15分钟   | 12.5小时 | 50小时    | 14天    | ❌ 数据不足
1小时    | 50小时   | 200小时   | 30天    | ⚠️ 仅MA50
1天      | 50天     | 200天     | 365天   | ✅ 全支持
1周      | 50周     | 200周     | -       | ✅ 从日度计算
1月      | 50月     | 200月     | -       | ✅ 从日度计算
```

#### 为什么周度/月度可以用日数据？
1. **分析目的一致**: 周度/月度分析本身就是长期趋势分析
2. **计算频率低**: 周度/月度计算频率低，性能影响小
3. **指标意义**: 长期时间框架需要长期技术指标支撑

### 3. RSI指标 (avg_rsi)

#### 计算原理
- **定义**: 相对强弱指数，衡量价格变动的速度和幅度
- **计算**: RSI = 100 - (100 / (1 + RS))，其中RS = 平均涨幅/平均跌幅
- **周期**: 通常使用14个周期

#### 支持策略
```
所有时间框架都支持RSI计算，但策略不同:

短期框架 (5m, 15m, 1h):
- 使用对应时间框架的数据
- 计算短期RSI，反映短期超买超卖

长期框架 (1d, 1w, 1M):
- 使用对应时间框架的数据  
- 计算长期RSI，反映长期趋势强度
```

## 🎯 设计原则总结

### 1. 数据可用性原则
- 只在有足够历史数据的时间框架计算相应指标
- 避免数据不足导致的计算错误

### 2. 性能优化原则
- 高频时间框架(5m, 15m, 1h)避免大量历史数据查询
- 低频时间框架(1w, 1M)可以承受更多计算成本

### 3. 分析意义原则
- 短期分析关注短期指标
- 长期分析需要长期指标支撑

### 4. 一致性原则
- 同类指标在不同时间框架保持计算逻辑一致
- 不支持的指标统一设为默认值

## 🔧 实现建议

### 当前实现 (简化版)
```python
# 不支持的指标设为默认值
'new_highs_52w': 0,
'new_lows_52w': 0,
'above_ma50': 0,
'above_ma200': 0,
'avg_rsi': 50.0
```

### 增强实现 (可选)
```python
# 为长期时间框架计算实际值
if timeframe in ['1w', '1M']:
    # 从日度数据计算52周新高新低和移动平均线
    highs_lows = calculate_from_daily_data(companies)
    ma_metrics = calculate_ma_from_daily_data(companies)
```

这种设计既保证了计算效率，又保持了指标的合理性。
