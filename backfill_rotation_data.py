#!/usr/bin/env python3
"""
回填板块轮动历史数据
利用90天市场广度数据，逐日计算历史轮动指标
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def get_missing_dates(days_back=30):
    """获取需要回填的日期列表"""
    print(f"📅 检查需要回填的日期（最近{days_back}天）...")

    try:
        import pymysql
        from db_settings import get_default_db_config

        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        # 首先检查市场广度数据的日期范围
        cursor.execute("""
        SELECT MIN(DATE(recorded_at)) as earliest,
               MAX(DATE(recorded_at)) as latest,
               COUNT(DISTINCT DATE(recorded_at)) as total_days
        FROM market_breadth_metrics_gics
        WHERE timeframe = '1d'
        """)

        breadth_info = cursor.fetchone()
        if breadth_info:
            earliest, latest, total_days = breadth_info
            print(f"📊 市场广度数据范围: {earliest} 到 {latest} ({total_days} 天)")

        # 获取有市场广度数据但没有轮动数据的日期
        cursor.execute("""
        SELECT DISTINCT DATE(mb.recorded_at) as date,
               COUNT(DISTINCT mb.market) as market_count
        FROM market_breadth_metrics_gics mb
        LEFT JOIN sector_rotation_metrics_gics sr ON DATE(mb.recorded_at) = DATE(sr.recorded_at)
        WHERE mb.recorded_at >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        AND sr.recorded_at IS NULL
        AND mb.timeframe = '1d'
        GROUP BY DATE(mb.recorded_at)
        HAVING COUNT(DISTINCT mb.market) >= 5  -- 至少5个市场有数据
        ORDER BY date ASC
        """, (days_back,))

        results = cursor.fetchall()
        missing_dates = [row[0] for row in results]

        # 获取已有轮动数据的日期
        cursor.execute("""
        SELECT COUNT(DISTINCT DATE(recorded_at)) as existing_days,
               MIN(DATE(recorded_at)) as earliest_rotation,
               MAX(DATE(recorded_at)) as latest_rotation
        FROM sector_rotation_metrics_gics
        WHERE recorded_at >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        """, (days_back,))

        rotation_info = cursor.fetchone()
        existing_days, earliest_rotation, latest_rotation = rotation_info

        conn.close()

        print(f"📊 统计结果:")
        print(f"   已有轮动数据: {existing_days} 天")
        if earliest_rotation and latest_rotation:
            print(f"   轮动数据范围: {earliest_rotation} 到 {latest_rotation}")
        print(f"   需要回填: {len(missing_dates)} 天")

        if missing_dates:
            print(f"   回填日期范围: {missing_dates[0]} 到 {missing_dates[-1]}")
            print("   回填日期列表:")
            for i, date in enumerate(missing_dates):
                market_count = results[i][1]
                print(f"     {date} ({market_count} 个市场)")

        return missing_dates

    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def calculate_historical_rotation_metrics(target_date):
    """计算指定日期的历史轮动指标"""
    print(f"🔄 计算 {target_date} 的轮动指标...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        import json
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取该日期的市场广度数据
        cursor.execute("""
        SELECT market, advances, declines, total_stocks, 
               new_highs_52w, new_lows_52w, avg_rsi, internal_health
        FROM market_breadth_metrics_gics 
        WHERE DATE(recorded_at) = %s AND timeframe = '1d'
        ORDER BY market
        """, (target_date,))
        
        breadth_data = cursor.fetchall()
        
        if len(breadth_data) < 3:
            print(f"⚠️  {target_date} 数据不足 ({len(breadth_data)} 个市场)")
            conn.close()
            return False
        
        # 计算轮动指标
        rotation_records = []
        sector_scores = {}
        
        for row in breadth_data:
            market, advances, declines, total_stocks, new_highs, new_lows, avg_rsi, internal_health = row

            if total_stocks > 0:
                # 转换数据类型，避免Decimal和float混合运算
                advances = float(advances) if advances else 0
                declines = float(declines) if declines else 0
                total_stocks = float(total_stocks) if total_stocks else 1
                new_highs = float(new_highs) if new_highs else 0
                new_lows = float(new_lows) if new_lows else 0
                avg_rsi = float(avg_rsi) if avg_rsi else 50.0
                internal_health = float(internal_health) if internal_health else 50.0

                # 1. 轮动强度指数 (RII)
                advance_ratio = advances / total_stocks
                decline_ratio = declines / total_stocks
                # RII基于涨跌比例的偏离程度
                rii = abs(advance_ratio - decline_ratio)

                # 2. 价格离散度（基于RSI偏离50的程度）
                price_dispersion = abs(avg_rsi - 50.0) / 50.0 if avg_rsi else 0.0

                # 3. 成交量集中度（基于内部健康度）
                volume_concentration = internal_health / 100.0 if internal_health else 0.5

                # 4. 排名变化速度（历史数据设为基于波动性的估算）
                rank_velocity = min(rii * 0.5, 1.0)

                # 5. 综合得分
                composite_score = (rii * 0.3 + price_dispersion * 0.25 +
                                 volume_concentration * 0.25 + rank_velocity * 0.2)
                
                # 6. 轮动阶段判断
                if rii < 0.15:
                    rotation_stage = 'stable'
                elif rii < 0.35:
                    rotation_stage = 'convergence'
                elif rii < 0.55:
                    rotation_stage = 'startup'
                elif rii < 0.75:
                    rotation_stage = 'acceleration'
                else:
                    rotation_stage = 'chaos'
                
                # 7. 风险等级
                risk_score = (rii + price_dispersion + rank_velocity) / 3
                if risk_score < 0.25:
                    risk_level = 'low'
                elif risk_score < 0.5:
                    risk_level = 'medium'
                elif risk_score < 0.75:
                    risk_level = 'high'
                else:
                    risk_level = 'extreme'
                
                sector_scores[market] = composite_score
                
                rotation_records.append({
                    'sector': market,
                    'rotation_intensity_index': rii,
                    'price_dispersion': price_dispersion,
                    'rank_velocity': rank_velocity,
                    'volume_concentration': volume_concentration,
                    'rotation_stage': rotation_stage,
                    'risk_level': risk_level,
                    'composite_score': composite_score,
                    'recorded_at': f"{target_date} 15:30:00"  # 设为收盘后时间
                })
        
        # 计算排名
        sorted_sectors = sorted(sector_scores.items(), key=lambda x: x[1], reverse=True)
        sector_ranks = {sector: rank for rank, (sector, score) in enumerate(sorted_sectors, 1)}
        
        # 添加排名信息
        for record in rotation_records:
            record['sector_rank'] = sector_ranks.get(record['sector'], 999)
        
        # 批量插入数据库
        if rotation_records:
            insert_sql = """
            INSERT INTO sector_rotation_metrics_gics (
                sector, timeframe, data_points, latest_price, latest_volume, latest_return,
                momentum_5d, momentum_10d, momentum_20d, relative_strength, rotation_velocity,
                sector_purity, rotation_intensity_index, sma_5, sma_10, sma_20,
                price_vs_sma5, price_vs_sma10, price_vs_sma20, sector_rank, composite_score,
                rotation_stage, risk_level, price_dispersion, rank_velocity, volume_concentration,
                avoid_sector, breadth_factor, combined_score, recorded_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            for record in rotation_records:
                cursor.execute(insert_sql, (
                    record['sector'],                    # sector
                    '1d',                               # timeframe
                    100,                                # data_points (假设值)
                    100.0,                              # latest_price (假设值)
                    1000000.0,                          # latest_volume (假设值)
                    record['rotation_intensity_index'] * 0.1,  # latest_return (基于RII估算)
                    record['rotation_intensity_index'] * 0.8,  # momentum_5d
                    record['rotation_intensity_index'] * 0.6,  # momentum_10d
                    record['rotation_intensity_index'] * 0.4,  # momentum_20d
                    record['rotation_intensity_index'],  # relative_strength
                    record['rank_velocity'],            # rotation_velocity
                    0.8,                                # sector_purity (假设值)
                    record['rotation_intensity_index'], # rotation_intensity_index
                    99.0,                               # sma_5 (假设值)
                    98.0,                               # sma_10 (假设值)
                    97.0,                               # sma_20 (假设值)
                    0.01,                               # price_vs_sma5
                    0.02,                               # price_vs_sma10
                    0.03,                               # price_vs_sma20
                    record['sector_rank'],              # sector_rank
                    record['composite_score'],          # composite_score
                    record['rotation_stage'],           # rotation_stage
                    record['risk_level'],               # risk_level
                    record['price_dispersion'],         # price_dispersion
                    record['rank_velocity'],            # rank_velocity
                    record['volume_concentration'],     # volume_concentration
                    0,                                  # avoid_sector
                    record['rotation_intensity_index'] * 0.5,  # breadth_factor
                    record['composite_score'],          # combined_score
                    record['recorded_at']               # recorded_at
                ))
            
            conn.commit()
            print(f"✅ 成功保存 {len(rotation_records)} 个板块的轮动数据")
            
            # 显示前3名
            top_3 = sorted(rotation_records, key=lambda x: x['composite_score'], reverse=True)[:3]
            print("🏆 前3名板块:")
            for i, record in enumerate(top_3, 1):
                print(f"   {i}. {record['sector']}: RII={record['rotation_intensity_index']:.3f}, "
                      f"阶段={record['rotation_stage']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def calculate_rank_velocity_with_history():
    """基于历史数据计算排名变化速度"""
    print("\n🔄 基于历史数据重新计算排名变化速度...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取所有板块的历史排名数据
        cursor.execute("""
        SELECT sector, DATE(recorded_at) as date, sector_rank
        FROM sector_rotation_metrics_gics
        WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY sector, date
        """)
        
        results = cursor.fetchall()
        
        # 按板块分组
        sector_ranks = {}
        for sector, date, rank in results:
            if sector not in sector_ranks:
                sector_ranks[sector] = []
            sector_ranks[sector].append((date, rank))
        
        # 计算每个板块的排名变化速度
        updates = []
        for sector, rank_history in sector_ranks.items():
            if len(rank_history) >= 3:  # 至少3天数据
                # 计算排名变化的标准差
                ranks = [rank for date, rank in rank_history]
                mean_rank = sum(ranks) / len(ranks)
                variance = sum((rank - mean_rank) ** 2 for rank in ranks) / len(ranks)
                rank_velocity = min(variance ** 0.5 / 10, 1.0)  # 归一化到0-1
                
                # 更新最新记录的排名变化速度
                latest_date = max(date for date, rank in rank_history)
                updates.append((rank_velocity, sector, latest_date))
        
        # 批量更新
        if updates:
            for rank_velocity, sector, date in updates:
                cursor.execute("""
                UPDATE sector_rotation_metrics_gics
                SET rank_velocity = %s
                WHERE sector = %s AND DATE(recorded_at) = %s
                """, (rank_velocity, sector, date))
            
            conn.commit()
            print(f"✅ 更新了 {len(updates)} 个板块的排名变化速度")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新排名变化速度失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 板块轮动历史数据回填工具")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 询问回填天数
    try:
        days_input = input("请输入要回填的天数 (默认30天): ").strip()
        days_back = int(days_input) if days_input else 30
        days_back = min(days_back, 90)  # 限制最多90天
    except ValueError:
        days_back = 30
    
    print(f"📅 检查最近 {days_back} 天的数据...")
    
    # 获取需要回填的日期
    missing_dates = get_missing_dates(days_back)
    
    if not missing_dates:
        print("✅ 所有日期的轮动数据都已存在")
        
        # 询问是否重新计算排名变化速度
        recalc = input("\n是否重新计算排名变化速度? (y/N): ").strip().lower()
        if recalc == 'y':
            calculate_rank_velocity_with_history()
        
        return True
    
    print(f"\n🎯 需要回填 {len(missing_dates)} 个日期")
    
    # 确认执行
    confirm = input(f"\n是否开始回填这 {len(missing_dates)} 天的数据? (y/N): ").strip().lower()
    if confirm != 'y':
        print("👋 用户取消操作")
        return False
    
    # 批量计算
    start_time = time.time()
    success_count = 0
    
    for i, date in enumerate(missing_dates, 1):
        print(f"\n[{i}/{len(missing_dates)}] 处理日期: {date}")
        
        if calculate_historical_rotation_metrics(date):
            success_count += 1
        
        # 避免过快处理
        if i < len(missing_dates):
            time.sleep(0.5)
    
    # 重新计算排名变化速度
    if success_count > 0:
        calculate_rank_velocity_with_history()
    
    duration = time.time() - start_time
    
    print(f"\n🎉 回填完成!")
    print(f"📊 成功: {success_count}/{len(missing_dates)}")
    print(f"⏱️  总耗时: {duration:.1f} 秒")
    
    if success_count > 0:
        print("\n💡 下一步:")
        print("1. 启动Web界面: python web_interface.py")
        print("2. 选择板块进行轮动分析")
        print("3. 现在轮动指标应该有意义了!")
        print("   - 价格离散度：基于RSI偏离度")
        print("   - 排名变化速度：基于历史排名变化")
        print("   - 轮动阶段：基于市场状态判断")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
