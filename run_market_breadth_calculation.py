#!/usr/bin/env python3
"""
市场广度计算 - 手动运行脚本
为Windows用户提供简单的市场广度计算方式
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def run_market_breadth_calculation():
    """运行市场广度计算"""
    print("🚀 开始多时间框架市场广度计算")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 导入并运行市场广度计算
        from scripts.run_all_timeframes import main as run_all_timeframes_main
        
        print("📊 正在计算6个时间框架的市场广度指标...")
        print("   - 5分钟时间框架")
        print("   - 15分钟时间框架") 
        print("   - 1小时时间框架")
        print("   - 日线时间框架")
        print("   - 周线时间框架")
        print("   - 月线时间框架")
        print()
        
        # 执行计算
        start_time = time.time()
        run_all_timeframes_main()
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"\n✅ 计算完成!")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print(f"🏁 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_calculation_results():
    """验证计算结果"""
    print("\n🔍 验证计算结果...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 查询各时间框架的数据统计
        cursor.execute("""
        SELECT timeframe, COUNT(*) as count, MAX(recorded_at) as latest_time
        FROM market_breadth_metrics_gics 
        GROUP BY timeframe 
        ORDER BY 
            CASE timeframe 
                WHEN '5m' THEN 1 
                WHEN '15m' THEN 2 
                WHEN '1h' THEN 3 
                WHEN '1d' THEN 4 
                WHEN '1w' THEN 5 
                WHEN '1M' THEN 6 
                ELSE 7 
            END
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("📊 市场广度数据统计:")
            for timeframe, count, latest_time in results:
                print(f"   {timeframe:>4}: {count:>4} 条记录, 最新时间: {latest_time}")
            
            # 检查今日数据
            cursor.execute("""
            SELECT timeframe, COUNT(*) as count
            FROM market_breadth_metrics_gics 
            WHERE DATE(recorded_at) = CURDATE()
            GROUP BY timeframe
            """)
            
            today_results = cursor.fetchall()
            if today_results:
                print("\n📅 今日新增数据:")
                for timeframe, count in today_results:
                    print(f"   {timeframe:>4}: {count:>4} 条记录")
            else:
                print("\n⚠️  今日暂无新增数据")
        else:
            print("❌ 未找到市场广度数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_web_interface_data():
    """测试Web界面数据可用性"""
    print("\n🌐 测试Web界面数据可用性...")
    
    try:
        from multi_timeframe_analyzer import analyze_market_mtf
        
        print("📊 测试Technology板块分析...")
        
        # 测试简单的分析
        result = analyze_market_mtf(
            sector="Technology",
            timeframes=['1d'],  # 只测试日线数据
            output_format='summary'
        )
        
        if result and 'rotation_analysis' in result:
            rotation = result['rotation_analysis']
            print("✅ 轮动指标计算正常:")
            print(f"   RII指数: {rotation.get('unified_rii', 'N/A')}")
            print(f"   轮动阶段: {rotation.get('rotation_stage', 'N/A')}")
            print(f"   风险等级: {rotation.get('risk_level', 'N/A')}")
            return True
        else:
            print("⚠️  轮动指标计算异常，但基础功能可能正常")
            return False
            
    except Exception as e:
        print(f"❌ Web界面数据测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 市场广度计算系统")
    print("适用于Windows系统的手动运行方式")
    print()
    
    # 步骤1: 运行市场广度计算
    calc_success = run_market_breadth_calculation()
    
    if calc_success:
        # 步骤2: 验证计算结果
        verify_success = verify_calculation_results()
        
        # 步骤3: 测试Web界面数据
        web_success = test_web_interface_data()
        
        print("\n" + "=" * 50)
        print("📋 运行结果总结:")
        print(f"   市场广度计算: {'✅ 成功' if calc_success else '❌ 失败'}")
        print(f"   数据验证: {'✅ 通过' if verify_success else '❌ 失败'}")
        print(f"   Web界面测试: {'✅ 正常' if web_success else '⚠️ 异常'}")
        
        if calc_success and verify_success:
            print("\n🎉 系统运行成功!")
            print("\n💡 下一步操作:")
            print("1. 启动Web界面: python web_interface.py")
            print("2. 访问 http://localhost:5000")
            print("3. 选择板块进行轮动分析")
            print("4. 查看完整的轮动指标和决策建议")
            
            print("\n⏰ 自动化建议:")
            print("- 每日运行此脚本更新数据")
            print("- 或配置Windows任务计划程序自动运行")
            print("- 参考: python scripts/setup_windows_tasks.py")
        else:
            print("\n⚠️ 部分功能异常，请检查系统配置")
    
    else:
        print("\n❌ 市场广度计算失败")
        print("💡 故障排除建议:")
        print("1. 检查数据库连接配置")
        print("2. 确认网络连接正常")
        print("3. 查看错误日志信息")
        print("4. 尝试运行: python scripts/run_all_timeframes.py")
    
    print(f"\n📝 运行日志可查看: logs/market_breadth_daily.log")
    
    return calc_success

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
