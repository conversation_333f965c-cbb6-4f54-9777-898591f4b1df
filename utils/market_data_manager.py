"""
市场数据工具模块

提供通用的市场分组和股票列表获取功能，避免重复代码。
被 get_history_price.py、get_realtime_price.py、market_breadth.py 等脚本使用。

功能：
1. 获取所有市场分组
2. 根据市场分组获取股票列表
3. 获取股票的vhcid映射
4. 统一的错误处理和日志记录
"""

import os
import logging
from typing import List, Dict, Tuple, Optional
import pymysql

# 智能查找并添加config目录到Python路径
import sys
import os

def find_config_path():
    """智能查找config目录"""
    current_file = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file)

    # 尝试不同的路径组合
    possible_paths = [
        # 从utils目录向上查找 (当前在utils目录)
        os.path.join(os.path.dirname(current_dir), 'config'),
        # 从data_source_tool/utils向上查找
        os.path.join(os.path.dirname(os.path.dirname(current_dir)), 'config'),
        # 直接在当前目录的config子目录
        os.path.join(current_dir, 'config'),
        # 在上级目录的config子目录
        os.path.join(os.path.dirname(current_dir), 'data_source_tool', 'config')
    ]

    for path in possible_paths:
        if os.path.exists(os.path.join(path, 'db_settings.py')):
            return path

    # 如果都找不到，返回默认路径
    return os.path.join(os.path.dirname(current_dir), 'config')

config_path = find_config_path()
if config_path not in sys.path:
    sys.path.insert(0, config_path)

try:
    from db_settings import get_default_db_config, get_security_db_config
except ImportError as e:
    print(f"警告: 无法导入数据库配置: {e}")
    print(f"尝试的config路径: {config_path}")
    # 提供默认配置作为后备
    def get_default_db_config():
        return {
            'host': '*************',
            'port': 3306,
            'user': 'zhuxun',
            'password': '123456',
            'database': 'trend_asset_screener',
            'charset': 'utf8mb4'
        }

    def get_security_db_config():
        return {
            'host': '************',
            'port': 3306,
            'user': 'algoa01',
            'password': 'Xjmn6D54',
            'database': 'security_master',
            'charset': 'utf8mb4'
        }

# 设置日志记录器
logger = logging.getLogger(__name__)


class MarketDataManager:
    """市场数据管理器 - 统一处理市场分组和股票数据获取"""
    
    def __init__(self, db_config: Optional[Dict] = None, use_env: bool = False):
        """
        初始化市场数据管理器

        :param db_config: 数据库配置字典，包含 host, port, user, password, database
        :param use_env: 是否使用环境变量，优先级高于配置文件
        """
        if use_env and self._check_env_vars():
            # 兼容模式：使用环境变量
            self.db_config = {
                'host': os.environ['DEFAULT_DB_HOST'],
                'port': int(os.environ['DEFAULT_DB_PORT']),
                'user': os.environ['DEFAULT_DB_USER'],
                'password': os.environ['DEFAULT_DB_PASSWORD'],
                'database': os.environ['DEFAULT_DB_NAME']
            }
        elif db_config:
            # 自定义配置
            self.db_config = db_config
        else:
            # 默认使用配置文件
            self.db_config = get_default_db_config()

        # vhcid映射数据库配置（使用配置文件）
        self.vhcid_db_config = get_security_db_config()
    
    def _check_env_vars(self) -> bool:
        """检查必要的环境变量是否存在"""
        required_vars = ['DEFAULT_DB_HOST', 'DEFAULT_DB_PORT', 'DEFAULT_DB_USER', 
                        'DEFAULT_DB_PASSWORD', 'DEFAULT_DB_NAME']
        return all(var in os.environ for var in required_vars)
    
    def _get_connection(self) -> pymysql.Connection:
        """获取数据库连接"""
        return pymysql.Connection(**self.db_config)
    
    def _get_vhcid_connection(self) -> pymysql.Connection:
        """获取vhcid映射数据库连接"""
        return pymysql.Connection(**self.vhcid_db_config)
    
    def get_ticker_to_vhcid_mapping(self, tickers: List[str]) -> Tuple[Dict[str, int], List[str]]:
        """
        从security_master数据库获取股票代码到vhcid的映射（批量查询优化版）
        
        :param tickers: 股票代码列表
        :return: (映射字典, 未找到映射的股票列表)
        """
        if not tickers:
            logger.warning("⚠️ 未提供股票代码列表")
            return {}, []
        
        logger.info(f"🔄 从security_master数据库批量获取 {len(tickers)} 个股票的vhcid映射...")
        
        mapping = {}
        conn = self._get_vhcid_connection()
        
        try:
            # 使用批量查询提高效率
            batch_size = 1000  # 每批处理1000个股票
            
            # 导入tqdm进度条
            from tqdm import tqdm
            
            # 使用tqdm显示批次进度
            with tqdm(total=len(tickers), desc="🔄 获取vhcid映射", unit="股票") as pbar:
                for i in range(0, len(tickers), batch_size):
                    batch_tickers = tickers[i:i + batch_size]
                    
                    # 构建IN查询
                    placeholders = ','.join(['%s'] * len(batch_tickers))
                    sql = f"""
                    SELECT Ticker, vhcid FROM ticker_to_secid_lookup 
                    WHERE EndDate='29991231' AND Ticker IN ({placeholders})
                    """
                    
                    with conn.cursor() as cursor:
                        cursor.execute(sql, batch_tickers)
                        results = cursor.fetchall()
                        
                        # 处理结果
                        batch_mapping = {}
                        for row in results:
                            ticker, vhcid = row
                            if ticker and vhcid:
                                batch_mapping[ticker] = vhcid
                        
                        mapping.update(batch_mapping)
                        
                        # 更新进度条
                        progress = min(i + batch_size, len(tickers))
                        pbar.update(len(batch_tickers))
                        pbar.set_postfix({
                            '已映射': len(mapping),
                            '本批次': len(batch_mapping),
                            '总计': f"{progress}/{len(tickers)}"
                        })
            
            # 找出未映射的股票
            missing_tickers = [t for t in tickers if t not in mapping]
            
            logger.info(f"✅ 批量查询完成! 成功获取 {len(mapping)}/{len(tickers)} 个映射关系")
            
            if missing_tickers:
                logger.warning(f"⚠️ 未找到映射的股票数量: {len(missing_tickers)}")
                logger.debug(f"⚠️ 未找到映射的股票样例: {missing_tickers[:10]}{'...' if len(missing_tickers) > 10 else ''}")
            
            return mapping, missing_tickers
            
        except Exception as e:
            logger.error(f"❌ 批量查询vhcid映射时出错: {e}")
            return {}, tickers
        finally:
            conn.close()
    
    def get_single_ticker_vhcid(self, ticker: str) -> Optional[int]:
        """
        获取单个股票代码的vhcid
        
        :param ticker: 股票代码
        :return: vhcid或None
        """
        mapping = self.get_ticker_to_vhcid_mapping([ticker])
        return mapping.get(ticker)
    
    def get_all_ticker_vhcid_mappings(self) -> Dict[str, int]:
        """
        获取所有股票代码的vhcid映射
        
        :return: {ticker: vhcid} 字典
        """
        logger.info("🔄 获取所有有效的股票代码vhcid映射...")
        
        conn = self._get_vhcid_connection()
        mapping = {}
        
        try:
            with conn.cursor() as cursor:
                sql = """
                SELECT Ticker, vhcid FROM ticker_to_secid_lookup 
                WHERE EndDate='29991231'
                ORDER BY Ticker
                """
                
                cursor.execute(sql)
                results = cursor.fetchall()
                
                for row in results:
                    ticker, vhcid = row
                    if ticker and vhcid:
                        mapping[ticker] = vhcid
                
                logger.info(f"✅ 获取到 {len(mapping)} 个完整的股票代码vhcid映射")
                
                return mapping
                
        except Exception as e:
            logger.error(f"❌ 查询所有vhcid映射时出错: {e}")
            return {}
        finally:
            conn.close()
    
    def get_all_markets(self) -> List[str]:
        """
        从数据库动态获取所有市场分组
        
        :return: 市场分组列表
        """
        logger.info("📊 连接数据库获取市场分组...")
        conn = self._get_connection()
        
        try:
            # 从market_config_gics表查询所有market分组
            logger.info("🔍 查询market_config_gics表获取市场分组...")
            market_sql = """
            SELECT DISTINCT market FROM market_config_gics ORDER BY market
            """
            
            with conn.cursor() as cursor:
                cursor.execute(market_sql)
                market_result = cursor.fetchall()
                markets = [row[0] for row in market_result]
            
            logger.info(f"📈 获取到 {len(markets)} 个market分组")
            logger.debug(f"📋 市场列表: {markets}")
            
            return markets
            
        except Exception as e:
            logger.error(f"❌ 查询市场分组时出错: {e}")
            # 如果数据库查询失败，回退到默认分组
            default_markets = ['SP500', 'NASDAQ100', 'RUSSELL2000', 'NASDAQ', 'NYSE']
            logger.warning(f"⚠️ 使用默认分组: {default_markets}")
            return default_markets
        finally:
            conn.close()
    
    def get_companies_by_markets(self, markets: List[str]) -> List[str]:
        """
        根据市场分组获取股票列表
        
        :param markets: 市场分组列表
        :return: 股票代码列表
        """
        if not markets:
            logger.warning("⚠️ 未提供市场分组")
            return []
        
        conn = self._get_connection()
        
        try:
            placeholders = ','.join([f"'{m}'" for m in markets])
            sql = f"SELECT DISTINCT company FROM index_company_mapping_gics WHERE market IN ({placeholders})"
            
            with conn.cursor() as cursor:
                cursor.execute(sql)
                companies = cursor.fetchall()
                companies = [c[0] for c in companies]
            
            logger.info(f"📊 从 {len(markets)} 个市场获取到 {len(companies)} 只股票")
            return companies
            
        except Exception as e:
            logger.error(f"❌ 查询股票列表时出错: {e}")
            return []
        finally:
            conn.close()
    
    def get_companies_by_market(self, market: str) -> List[str]:
        """
        根据单个市场获取股票列表
        
        :param market: 市场名称
        :return: 股票代码列表
        """
        return self.get_companies_by_markets([market])
    
    def get_all_companies(self) -> List[str]:
        """
        获取所有市场的股票列表
        
        :return: 去重后的股票代码列表
        """
        markets = self.get_all_markets()
        companies = self.get_companies_by_markets(markets)
        
        # 去重并排序
        unique_companies = sorted(list(set(companies)))
        
        original_count = len(companies)
        unique_count = len(unique_companies)
        duplicate_count = original_count - unique_count
        
        logger.info(f"📊 原始查询结果: {original_count} 个")
        logger.info(f"📊 去重后结果: {unique_count} 个")
        if duplicate_count > 0:
            logger.info(f"📊 重复数量: {duplicate_count} 个")
        
        return unique_companies
    
    def get_market_companies_mapping(self) -> Dict[str, List[str]]:
        """
        获取市场到股票的映射关系
        
        :return: {market: [companies]} 字典
        """
        markets = self.get_all_markets()
        result = {}
        
        for market in markets:
            companies = self.get_companies_by_market(market)
            result[market] = companies
            logger.debug(f"📊 {market}: {len(companies)} 只股票")
        
        return result
    
    def get_vhcids_with_stats(self, expected_total: int = 7679) -> Tuple[List[str], Dict[str, int]]:
        """
        获取股票列表并提供详细统计信息（兼容旧版本接口）
        
        :param expected_total: 预期的股票总数，用于验证
        :return: (股票列表, 统计信息字典)
        """
        logger.info("📊 开始获取股票vhcid列表...")
        
        # 第一步：获取市场分组
        markets = self.get_all_markets()
        
        if not markets:
            logger.warning("⚠️ 未找到任何market分组")
            return [], {'total': 0, 'markets': 0}
        
        # 第二步：逐个市场统计
        logger.info("🔍 统计各市场股票数量...")
        for market in markets:
            companies = self.get_companies_by_market(market)
            logger.info(f"  📊 {market}: {len(companies)} 个company")
        
        # 第三步：获取所有股票
        all_companies = self.get_all_companies()
        
        # 统计信息
        stats = {
            'total': len(all_companies),
            'markets': len(markets),
            'expected': expected_total
        }
        
        # 数据质量检查
        if len(all_companies) < expected_total * 0.5:
            logger.warning(f"⚠️ 获取的股票数量({len(all_companies)})远少于预期({expected_total})")
            logger.warning("   可能原因：")
            logger.warning("   1. 数据库中只包含特定的股票池（如指数成分股）")
            logger.warning("   2. market_config_gics表中的分组不是交易所维度")
            logger.warning("   3. index_company_mapping_gics表数据不完整")
        else:
            logger.info(f"✅ 获取的股票数量({len(all_companies)})符合预期")
        
        # 显示样例
        sample_size = min(10, len(all_companies))
        if sample_size > 0:
            logger.debug(f"📊 vhcid样例: {all_companies[:sample_size]}{'...' if len(all_companies) > sample_size else ''}")
        
        return all_companies, stats
    
    def get_high_frequency_markets(self, priority_list: Optional[List[str]] = None) -> List[str]:
        """
        获取高频市场分组（用于频繁更新）
        
        :param priority_list: 高频市场优先级列表
        :return: 高频市场列表
        """
        if priority_list is None:
            priority_list = ['SP500', 'NASDAQ100', 'QQQ', 'SPY']
        
        all_markets = self.get_all_markets()
        
        # 从所有市场中筛选出高频市场
        high_freq_markets = []
        for market in priority_list:
            if market in all_markets:
                high_freq_markets.append(market)
        
        # 如果没有找到预定义的高频市场，取前2个
        if not high_freq_markets:
            high_freq_markets = all_markets[:2]
        
        logger.debug(f"📈 高频市场: {high_freq_markets}")
        return high_freq_markets
    
    def get_low_frequency_markets(self, priority_list: Optional[List[str]] = None) -> List[str]:
        """
        获取低频市场分组（排除高频市场）
        
        :param priority_list: 高频市场优先级列表
        :return: 低频市场列表
        """
        all_markets = self.get_all_markets()
        high_freq_markets = self.get_high_frequency_markets(priority_list)
        
        # 排除高频市场，其余为低频市场
        low_freq_markets = [market for market in all_markets if market not in high_freq_markets]
        
        logger.debug(f"🐌 低频市场: {low_freq_markets}")
        return low_freq_markets


# 全局实例，提供简单的函数接口（向后兼容）
_default_manager = None

def get_default_manager() -> MarketDataManager:
    """获取默认的市场数据管理器实例"""
    global _default_manager
    if _default_manager is None:
        _default_manager = MarketDataManager()
    return _default_manager

# 简化的函数接口，保持向后兼容
def get_all_markets() -> List[str]:
    """获取所有市场分组（简化接口）"""
    return get_default_manager().get_all_markets()

def get_companies_by_markets(markets: List[str]) -> List[str]:
    """根据市场分组获取股票列表（简化接口）"""
    return get_default_manager().get_companies_by_markets(markets)

def get_all_companies() -> List[str]:
    """获取所有股票列表（简化接口）"""
    return get_default_manager().get_all_companies()

def get_vhcids() -> List[str]:
    """获取股票vhcid列表（向后兼容接口）"""
    companies, _ = get_default_manager().get_vhcids_with_stats()
    return companies

def get_high_frequency_markets() -> List[str]:
    """获取高频市场分组（简化接口）"""
    return get_default_manager().get_high_frequency_markets()

def get_low_frequency_markets() -> List[str]:
    """获取低频市场分组（简化接口）"""
    return get_default_manager().get_low_frequency_markets()


if __name__ == "__main__":
    # 测试代码
    import logging
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 测试市场数据管理器...")
    
    manager = MarketDataManager()
    
    # 测试获取市场分组
    markets = manager.get_all_markets()
    print(f"✅ 获取到 {len(markets)} 个市场分组")
    
    # 测试获取股票列表
    companies, stats = manager.get_vhcids_with_stats()
    print(f"✅ 获取到 {len(companies)} 只股票")
    
    # 测试高低频分组
    high_freq = manager.get_high_frequency_markets()
    low_freq = manager.get_low_frequency_markets()
    print(f"✅ 高频市场: {high_freq}")
    print(f"✅ 低频市场: {low_freq}")
    
    print("🎉 测试完成！")