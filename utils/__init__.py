#!/usr/bin/env python3
"""
工具模块初始化文件
"""

from .market_data_manager import MarketDataManager

# 导入 download_hist_price 函数
import sys
import os

# 添加 core 目录到路径
core_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'core')
if core_path not in sys.path:
    sys.path.insert(0, core_path)

try:
    from core.utils import download_hist_price
    __all__ = ['MarketDataManager', 'download_hist_price']
except ImportError:
    # 如果导入失败，尝试直接从 utils 导入
    try:
        from utils import download_hist_price as _download_hist_price
        download_hist_price = _download_hist_price
        __all__ = ['MarketDataManager', 'download_hist_price']
    except ImportError:
        print("Warning: download_hist_price function not found")
        __all__ = ['MarketDataManager']
