# 板块轮动指标系统使用指南

## 🎯 系统概述

本系统完全符合v41文档标准，实现了完整的多时间框架板块轮动指标分析，包括RII指数、轮动阶段、价格离散度等关键指标的计算和可视化展示。

### ✅ 已实现的核心功能

#### 🔄 板块轮动指标
- **统一轮动强度指数 (RII)**: 衡量板块间轮动的剧烈程度
- **价格离散度**: 板块间价格差异程度
- **排名变化速度**: 板块排名变化频率
- **成交量集中度**: 资金流向集中程度
- **轮动阶段识别**: 稳定期、收敛期、启动期、加速期、混乱期

#### 🎯 多时间框架支持
- **6个时间框架**: 5分钟、15分钟、1小时、日线、周线、月线
- **动态权重调整**: 根据市场状态自动调整各时间框架权重
- **信号一致性分析**: 检测各时间框架信号的一致程度
- **冲突信号处理**: 智能处理不同时间框架间的信号冲突

#### 📊 可视化展示
- **轮动指标仪表盘**: RII指数的半圆形仪表盘显示
- **阶段状态卡片**: 不同颜色标识的轮动阶段
- **多时间框架标签页**: 分时间框架展示详细数据
- **风险评估面板**: 整体风险等级和建议

## 🚀 快速开始

### 1. 启动Web界面
```bash
python web_interface.py
```

### 2. 选择分析参数
- **板块选择**: 从38个板块中选择要分析的板块
- **时间框架组合**: 选择适合的多时间框架组合
  - 短期组合: 5分钟+15分钟+1小时
  - 标准组合: 15分钟+1小时+日线
  - 长期组合: 1小时+日线+周线
  - 完整组合: 全部6个时间框架
- **输出格式**: 完整报告、摘要格式、信号格式

### 3. 查看轮动指标
分析完成后，系统将显示：
- 🔄 **板块轮动指标分析**面板
- 🎯 **多时间框架统一决策**
- ⚠️ **风险评估**信息

## 📊 轮动指标详解

### 统一轮动强度指数 (RII)
- **范围**: 0.0 - 1.0
- **解释**:
  - 0.0-0.2: 轮动极弱，市场缺乏活力
  - 0.2-0.4: 轮动较弱，趋势相对稳定
  - 0.4-0.6: 轮动适中，正常市场状态
  - 0.6-0.8: 轮动活跃，需要灵活应对
  - 0.8-1.0: 轮动剧烈，高风险高机会

### 轮动阶段识别
| 阶段 | 特征 | 投资策略 |
|------|------|----------|
| **稳定期** | 板块表现相对稳定 | 适合长期持有 |
| **收敛期** | 板块开始收敛 | 关注突破方向 |
| **启动期** | 轮动刚开始 | 寻找领涨板块 |
| **加速期** | 轮动加速 | 快速调仓换股 |
| **混乱期** | 市场混乱 | 控制风险为主 |

### 价格离散度
- **含义**: 衡量不同时间框架下价格指标的差异程度
- **应用**: 高离散度表示市场分歧较大，需谨慎操作

### 排名变化速度
- **含义**: 板块相对排名的变化频率
- **应用**: 高变化速度表示轮动活跃，适合短线操作

## 🎨 Web界面功能

### 轮动指标可视化
1. **RII仪表盘**: 半圆形进度条显示轮动强度
2. **阶段标识**: 彩色标签显示当前轮动阶段
3. **指标卡片**: 网格布局展示各项轮动指标
4. **趋势图表**: 动态展示指标变化趋势

### 多时间框架展示
- **标签页切换**: 不同时间框架的独立展示
- **一致性评分**: 各时间框架信号的一致程度
- **权重分配**: 动态调整的时间框架权重

### 决策支持
- **仓位建议**: 基于轮动分析的具体仓位建议
- **操作指导**: 入场时机、退出策略、执行建议
- **风险提示**: 主要风险因素和缓解策略

## 🔧 技术架构

### 后端实现
- **`core/v41_report_generator.py`**: v41标准报告生成器
- **`core/multi_timeframe_analyzer.py`**: 多时间框架分析器
- **`core/mtf_analyzer_service.py`**: MTF分析服务
- **轮动指标计算**: 完整的RII、离散度、速度等指标算法

### 前端展示
- **响应式设计**: 适配不同屏幕尺寸
- **实时更新**: 动态数据刷新和图表更新
- **交互式界面**: 支持参数调整和结果筛选

### 数据流程
```
数据获取 → 多时间框架分析 → 轮动指标计算 → 统一决策 → 可视化展示
```

## 📈 使用场景

### 1. 日内交易
- 使用短期组合 (5分钟+15分钟+1小时)
- 关注RII指数和轮动阶段
- 快速响应轮动信号

### 2. 波段操作
- 使用标准组合 (15分钟+1小时+日线)
- 结合轮动阶段和价格离散度
- 中期持仓调整

### 3. 长期投资
- 使用长期组合 (1小时+日线+周线)
- 重点关注轮动趋势和风险评估
- 战略性资产配置

## 🚨 注意事项

### 数据要求
- 确保各时间框架数据完整性
- 定期运行市场广度计算更新数据
- 监控数据新鲜度和质量

### 指标解读
- RII指数需结合市场环境理解
- 轮动阶段判断需要历史对比
- 多时间框架信号可能存在冲突

### 风险控制
- 高RII时期控制仓位规模
- 混乱期优先保护本金
- 结合其他技术指标确认信号

## 🔄 系统维护

### 定期任务
```bash
# 每日运行市场广度计算
python scripts/run_all_timeframes.py

# 更新预计算数据
python scripts/setup_precomputed_system.py --update

# 系统状态检查
python scripts/monitor_precomputed.py
```

### 性能优化
- 使用预计算系统加速指标计算
- 定期清理历史数据
- 监控系统资源使用情况

## 📞 故障排除

### 常见问题
1. **轮动指标为0**: 检查数据完整性和时间框架配置
2. **Web界面显示异常**: 清除浏览器缓存，检查JavaScript错误
3. **分析速度慢**: 启用预计算系统，优化数据库查询

### 调试工具
```bash
# 测试轮动指标生成
python test_rotation_indicators.py

# 检查数据状态
python scripts/setup_precomputed_system.py --status

# 查看详细日志
tail -f logs/market_breadth_daily.log
```

## 🎉 成功指标

系统正常运行时，您应该看到：
- ✅ RII指数正常计算并显示
- ✅ 轮动阶段准确识别
- ✅ 多时间框架数据完整
- ✅ Web界面响应流畅
- ✅ 决策建议合理可行

---

**版本**: v4.1  
**最后更新**: 2025-07-31  
**技术支持**: 参考系统日志和测试脚本进行故障排除
