#!/usr/bin/env python3
"""
测试 OPT-T5.2 系统集成接口
验证集成市场分析系统的功能
"""

import sys
import os
import tempfile
import json
from datetime import datetime
import importlib.util

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task'))

# 导入集成系统
integration_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'market-breadth-task', 'integrated_market_analysis_system.py')
spec = importlib.util.spec_from_file_location("integrated_market_analysis_system", integration_path)
integration_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(integration_module)

IntegratedMarketAnalysisSystem = integration_module.IntegratedMarketAnalysisSystem
IntegratedAnalysisResult = integration_module.IntegratedAnalysisResult


def create_mock_market_data():
    """创建模拟市场数据"""
    return {
        'market_volatility': 0.25,
        'sectors': {
            'Technology': {
                'price_change': 0.02,
                'volume_ratio': 1.2,
                'breadth_score': 0.75
            },
            'Healthcare': {
                'price_change': 0.01,
                'volume_ratio': 1.1,
                'breadth_score': 0.68
            },
            'Finance': {
                'price_change': -0.01,
                'volume_ratio': 0.9,
                'breadth_score': 0.45
            },
            'Energy': {
                'price_change': -0.03,
                'volume_ratio': 0.8,
                'breadth_score': 0.32
            }
        },
        'market_indicators': {
            'advance_decline_ratio': 1.2,
            'new_highs_lows_ratio': 1.5,
            'volume_trend': 'increasing'
        }
    }


def test_system_initialization():
    """测试系统初始化"""
    print("=== 测试系统初始化 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建集成系统
        system = IntegratedMarketAnalysisSystem(
            config_dir=temp_dir,
            enable_performance_monitoring=True,
            enable_decision_tracking=True
        )
        
        # 获取系统状态
        status = system.get_system_status()
        
        print(f"📊 系统初始化状态:")
        print(f"已初始化组件: {status['system_info']['initialized_components']}")
        print(f"配置管理器: {status['system_info']['config_manager_status']}")
        print(f"决策追踪器: {status['system_info']['decision_tracker_status']}")
        print(f"性能监控: {status['system_info']['performance_monitoring']}")
        print(f"决策追踪: {status['system_info']['decision_tracking']}")
        print(f"系统健康: {status['system_health']}")
        
        # 验证关键组件
        required_components = ['config_manager']  # 至少配置管理器应该初始化
        initialized_components = status['system_info']['initialized_components']
        
        missing_critical = [comp for comp in required_components if comp not in initialized_components]
        
        if not missing_critical:
            print("✅ 关键组件初始化成功")
        else:
            print(f"⚠️  缺少关键组件: {missing_critical}")
        
        return len(missing_critical) == 0


def test_market_analysis():
    """测试市场分析功能"""
    print("\n=== 测试市场分析功能 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(config_dir=temp_dir)
        
        # 创建模拟市场数据
        market_data = create_mock_market_data()
        
        print(f"📊 输入市场数据:")
        print(f"市场波动率: {market_data['market_volatility']}")
        print(f"板块数量: {len(market_data['sectors'])}")
        print(f"市场指标数: {len(market_data['market_indicators'])}")
        
        # 执行市场分析
        try:
            result = system.analyze_market(market_data)
            
            print(f"\n🎯 分析结果:")
            print(f"分析时间: {result.analysis_timestamp}")
            print(f"市场状态: {result.market_regime}")
            print(f"分析置信度: {result.analysis_confidence}")
            
            print(f"\n📈 统一决策:")
            decision = result.unified_decision
            if 'error' not in decision:
                print(f"建议仓位: {decision.get('suggested_position', 'N/A')}%")
                print(f"操作建议: {decision.get('action', 'N/A')}")
                print(f"推荐板块: {decision.get('top_sectors', [])}")
                print(f"风险等级: {decision.get('risk_level', 'N/A')}")
                print(f"决策置信度: {decision.get('confidence', 'N/A')}")
            else:
                print(f"决策生成错误: {decision['error']}")
            
            print(f"\n⚡ 性能指标:")
            perf = result.performance_metrics
            print(f"分析耗时: {perf['analysis_time_seconds']}s")
            print(f"性能等级: {perf['performance_grade']}")
            print(f"成功率: {perf['success_rate']:.1%}")
            
            # 验证结果完整性
            required_fields = ['analysis_timestamp', 'market_regime', 'analysis_confidence', 'unified_decision']
            missing_fields = [field for field in required_fields if not hasattr(result, field)]
            
            if not missing_fields:
                print("✅ 分析结果完整")
                return True
            else:
                print(f"❌ 缺少字段: {missing_fields}")
                return False
                
        except Exception as e:
            print(f"❌ 市场分析失败: {e}")
            return False


def test_market_regime_detection():
    """测试市场状态检测"""
    print("\n=== 测试市场状态检测 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(config_dir=temp_dir)
        
        # 测试不同波动率的市场状态检测
        test_scenarios = [
            {'volatility': 0.1, 'expected': 'trending_stable'},
            {'volatility': 0.25, 'expected': 'normal_market'},
            {'volatility': 0.35, 'expected': 'high_rotation'},
            {'volatility': 0.45, 'expected': 'short_term_stress'}
        ]
        
        print(f"市场状态检测测试:")
        print(f"{'波动率':<10} {'检测状态':<18} {'预期状态':<18} {'匹配'}")
        print("-" * 55)
        
        correct_detections = 0
        
        for scenario in test_scenarios:
            market_data = {'market_volatility': scenario['volatility']}
            detected_regime = system._detect_market_regime(market_data)
            
            match = detected_regime == scenario['expected']
            status = "✅" if match else "❌"
            
            print(f"{scenario['volatility']:<10.2f} {detected_regime:<18} {scenario['expected']:<18} {status}")
            
            if match:
                correct_detections += 1
        
        print(f"\n📊 检测准确率: {correct_detections}/{len(test_scenarios)} ({correct_detections/len(test_scenarios):.1%})")
        
        return correct_detections == len(test_scenarios)


def test_config_integration():
    """测试配置集成"""
    print("\n=== 测试配置集成 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(config_dir=temp_dir)
        
        # 测试配置更新
        print(f"📝 测试配置更新:")
        
        config_tests = [
            ('algorithm.breadth_calculation.min_stocks_threshold', 15),
            ('risk_management.position_limits.max_position', 85),
            ('custom.test_parameter', 'test_value')
        ]
        
        update_results = []
        for path, value in config_tests:
            success = system.update_config(path, value, f"测试更新 {path}")
            status = "✅" if success else "❌"
            print(f"  {path}: {status}")
            update_results.append(success)
        
        # 测试市场状态切换
        print(f"\n🔄 测试市场状态切换:")
        
        regime_tests = ['trending_stable', 'high_rotation', 'normal_market']
        switch_results = []
        
        for regime in regime_tests:
            success = system.switch_market_regime(regime)
            status = "✅" if success else "❌"
            print(f"  切换到 {regime}: {status}")
            switch_results.append(success)
        
        # 获取配置摘要
        config_summary = system._get_config_summary()
        print(f"\n📊 配置摘要:")
        if 'error' not in config_summary:
            print(f"  配置组数: {config_summary.get('total_config_groups', 'N/A')}")
            print(f"  当前市场状态: {config_summary.get('current_market_regime', 'N/A')}")
            print(f"  验证规则数: {config_summary.get('validation_rules_count', 'N/A')}")
        else:
            print(f"  配置摘要错误: {config_summary['error']}")
        
        # 计算成功率
        total_updates = len(update_results)
        successful_updates = sum(update_results)
        total_switches = len(switch_results)
        successful_switches = sum(switch_results)
        
        print(f"\n📈 配置集成结果:")
        print(f"配置更新成功率: {successful_updates}/{total_updates}")
        print(f"状态切换成功率: {successful_switches}/{total_switches}")
        
        return (successful_updates == total_updates and 
                successful_switches == total_switches)


def test_system_validation():
    """测试系统验证"""
    print("\n=== 测试系统验证 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(config_dir=temp_dir)
        
        # 执行系统完整性验证
        validation_result = system.validate_system_integrity()
        
        print(f"📊 系统验证结果:")
        print(f"总体状态: {validation_result['overall_status']}")
        
        print(f"\n🔧 组件状态:")
        for component, status in validation_result['component_status'].items():
            status_icon = "✅" if status == 'active' else "❌"
            print(f"  {component}: {status} {status_icon}")
        
        print(f"\n📋 配置验证:")
        config_val = validation_result.get('config_validation', {})
        if 'error' not in config_val:
            print(f"  错误数: {config_val.get('errors', 'N/A')}")
            print(f"  警告数: {config_val.get('warnings', 'N/A')}")
        else:
            print(f"  配置验证失败")
        
        print(f"\n⚡ 性能检查:")
        perf_check = validation_result.get('performance_check', {})
        print(f"  错误率: {perf_check.get('error_rate', 'N/A')}")
        print(f"  平均分析时间: {perf_check.get('average_analysis_time', 'N/A')}s")
        print(f"  性能等级: {perf_check.get('performance_grade', 'N/A')}")
        
        print(f"\n💡 建议:")
        recommendations = validation_result.get('recommendations', [])
        if recommendations:
            for rec in recommendations:
                print(f"  - {rec}")
        else:
            print(f"  无特殊建议")
        
        # 验证系统状态
        overall_status = validation_result['overall_status']
        acceptable_statuses = ['healthy', 'warning']
        
        return overall_status in acceptable_statuses


def test_result_export():
    """测试结果导出"""
    print("\n=== 测试结果导出 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(config_dir=temp_dir)
        
        # 执行分析获取结果
        market_data = create_mock_market_data()
        result = system.analyze_market(market_data)
        
        # 测试导出功能
        export_file = os.path.join(temp_dir, 'analysis_result.json')
        export_success = system.export_analysis_result(result, export_file, 'json')
        
        print(f"📤 导出测试:")
        print(f"导出成功: {'✅' if export_success else '❌'}")
        
        if export_success:
            # 验证导出文件
            if os.path.exists(export_file):
                print(f"文件存在: ✅")
                
                # 验证文件内容
                try:
                    with open(export_file, 'r', encoding='utf-8') as f:
                        exported_data = json.load(f)
                    
                    # 检查关键字段
                    required_fields = ['analysis_timestamp', 'market_regime', 'unified_decision']
                    missing_fields = [field for field in required_fields if field not in exported_data]
                    
                    if not missing_fields:
                        print(f"内容完整: ✅")
                        print(f"文件大小: {os.path.getsize(export_file)} bytes")
                        return True
                    else:
                        print(f"内容不完整: ❌ (缺少: {missing_fields})")
                        return False
                        
                except Exception as e:
                    print(f"文件读取失败: ❌ ({e})")
                    return False
            else:
                print(f"文件不存在: ❌")
                return False
        else:
            return False


def test_performance_monitoring():
    """测试性能监控"""
    print("\n=== 测试性能监控 ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        system = IntegratedMarketAnalysisSystem(
            config_dir=temp_dir,
            enable_performance_monitoring=True
        )
        
        # 执行多次分析以测试性能监控
        market_data = create_mock_market_data()
        
        print(f"📊 执行多次分析测试性能监控:")
        
        analysis_count = 3
        for i in range(analysis_count):
            print(f"  执行第 {i+1} 次分析...")
            result = system.analyze_market(market_data)
            
            # 检查性能指标
            perf = result.performance_metrics
            print(f"    耗时: {perf['analysis_time_seconds']}s, 等级: {perf['performance_grade']}")
        
        # 获取最终性能统计
        final_status = system.get_system_status()
        perf_stats = final_status['performance_stats']
        
        print(f"\n📈 性能统计总结:")
        print(f"总分析次数: {perf_stats['total_analyses']}")
        print(f"平均分析时间: {perf_stats['average_analysis_time']:.3f}s")
        print(f"错误次数: {perf_stats['error_count']}")
        print(f"成功率: {((perf_stats['total_analyses'] - perf_stats['error_count']) / perf_stats['total_analyses']):.1%}")
        
        # 验证性能监控是否正常工作
        expected_analyses = analysis_count
        actual_analyses = perf_stats['total_analyses']
        
        return actual_analyses >= expected_analyses


def main():
    """主测试函数"""
    print("🚀 开始测试 OPT-T5.2 系统集成接口\n")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 测试系统初始化
        test_results.append(("系统初始化", test_system_initialization()))
        
        print("=" * 60)
        
        # 2. 测试市场分析
        test_results.append(("市场分析功能", test_market_analysis()))
        
        print("=" * 60)
        
        # 3. 测试市场状态检测
        test_results.append(("市场状态检测", test_market_regime_detection()))
        
        print("=" * 60)
        
        # 4. 测试配置集成
        test_results.append(("配置集成", test_config_integration()))
        
        print("=" * 60)
        
        # 5. 测试系统验证
        test_results.append(("系统验证", test_system_validation()))
        
        print("=" * 60)
        
        # 6. 测试结果导出
        test_results.append(("结果导出", test_result_export()))
        
        print("=" * 60)
        
        # 7. 测试性能监控
        test_results.append(("性能监控", test_performance_monitoring()))
        
        print("=" * 60)
        
        # 总结
        print("🎯 === 测试总结 ===")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 OPT-T5.2 系统集成接口完成！")
            print("🚀 主要功能:")
            print("  - 统一系统接口")
            print("  - 组件集成管理")
            print("  - 市场状态检测")
            print("  - 配置动态调整")
            print("  - 性能监控")
            print("  - 结果导出")
            print("  - 系统验证")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
