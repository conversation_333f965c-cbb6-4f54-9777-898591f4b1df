#!/usr/bin/env python3
"""
简化的板块轮动历史数据回填
使用现有的sector_rotation_scheduler逐日计算
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'config'))

def get_missing_rotation_dates(days_back=30):
    """获取缺少轮动数据的日期"""
    print(f"📅 检查最近{days_back}天缺少轮动数据的日期...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 获取有市场广度数据但没有轮动数据的日期
        cursor.execute("""
        SELECT DISTINCT DATE(mb.recorded_at) as date,
               COUNT(DISTINCT mb.market) as market_count
        FROM market_breadth_metrics_gics mb
        LEFT JOIN sector_rotation_metrics_gics sr ON DATE(mb.recorded_at) = DATE(sr.recorded_at)
        WHERE mb.recorded_at >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        AND sr.recorded_at IS NULL
        AND mb.timeframe = '1d'
        GROUP BY DATE(mb.recorded_at)
        HAVING COUNT(DISTINCT mb.market) >= 5
        ORDER BY date ASC
        """, (days_back,))
        
        missing_dates = cursor.fetchall()
        
        # 统计已有数据
        cursor.execute("""
        SELECT COUNT(DISTINCT DATE(recorded_at)) as existing_days
        FROM sector_rotation_metrics_gics
        WHERE recorded_at >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
        """, (days_back,))
        
        existing_days = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 统计结果:")
        print(f"   已有轮动数据: {existing_days} 天")
        print(f"   需要回填: {len(missing_dates)} 天")
        
        if missing_dates:
            print("   缺失日期:")
            for date, market_count in missing_dates:
                print(f"     {date} ({market_count} 个市场有广度数据)")
        
        return [date for date, market_count in missing_dates]
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def run_rotation_calculation_for_date(target_date):
    """为指定日期运行轮动计算"""
    print(f"🔄 计算 {target_date} 的轮动数据...")
    
    try:
        # 临时修改系统日期的方法不可行，我们用另一种方法
        # 直接调用sector_rotation_scheduler的计算方法
        from sector_rotation_scheduler import SectorRotationScheduler
        
        scheduler = SectorRotationScheduler()
        
        # 这里需要修改scheduler来支持指定日期计算
        # 由于现有的scheduler可能不支持历史日期，我们采用简化方法
        
        # 检查该日期是否有市场广度数据
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT COUNT(DISTINCT market) as market_count
        FROM market_breadth_metrics_gics
        WHERE DATE(recorded_at) = %s AND timeframe = '1d'
        """, (target_date,))
        
        market_count = cursor.fetchone()[0]
        
        if market_count < 5:
            print(f"⚠️  {target_date} 市场广度数据不足 ({market_count} 个市场)")
            conn.close()
            return False
        
        # 由于现有scheduler可能不支持历史日期，我们先跳过实际计算
        # 只是标记这个日期需要处理
        print(f"📊 {target_date} 有 {market_count} 个市场的广度数据")
        print("⚠️  当前scheduler不支持历史日期计算，建议手动运行当前日期的计算")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_current_rotation_calculation():
    """运行当前日期的轮动计算"""
    print("🔄 运行当前日期的轮动计算...")
    
    try:
        from sector_rotation_scheduler import SectorRotationScheduler
        
        scheduler = SectorRotationScheduler()
        success = scheduler.calculate_rotation_metrics()
        
        if success:
            print("✅ 当前日期轮动计算成功")
            return True
        else:
            print("❌ 当前日期轮动计算失败")
            return False
            
    except Exception as e:
        print(f"❌ 轮动计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_rotation_data_quality():
    """检查轮动数据质量"""
    print("\n🔍 检查轮动数据质量...")
    
    try:
        import pymysql
        from db_settings import get_default_db_config
        
        config = get_default_db_config()
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 检查最新的轮动数据
        cursor.execute("""
        SELECT sector, rotation_intensity_index, price_dispersion, 
               rank_velocity, volume_concentration, rotation_stage, risk_level
        FROM sector_rotation_metrics_gics
        WHERE DATE(recorded_at) = CURDATE()
        ORDER BY composite_score DESC
        LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        if results:
            print("📊 今日轮动数据（前5名）:")
            print("-" * 80)
            print(f"{'板块':<15} {'RII':<8} {'价格离散':<8} {'排名速度':<8} {'轮动阶段':<10} {'风险':<8}")
            print("-" * 80)
            
            for row in results:
                sector, rii, price_disp, rank_vel, vol_conc, stage, risk = row
                print(f"{sector:<15} {rii:<8.4f} {price_disp:<8.4f} {rank_vel:<8.4f} {stage:<10} {risk:<8}")
            
            # 检查是否有非零值
            non_zero_rii = sum(1 for row in results if row[1] > 0)
            non_zero_disp = sum(1 for row in results if row[2] > 0)
            
            print(f"\n📈 数据质量:")
            print(f"   非零RII指数: {non_zero_rii}/{len(results)}")
            print(f"   非零价格离散度: {non_zero_disp}/{len(results)}")
            
            if non_zero_rii > 0 and non_zero_disp > 0:
                print("✅ 轮动数据质量良好")
                return True
            else:
                print("⚠️  轮动数据大多为0，可能需要更多历史数据")
                return False
        else:
            print("❌ 今日无轮动数据")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据质量失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简化板块轮动数据回填工具")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查缺失的日期
    missing_dates = get_missing_rotation_dates(30)
    
    if not missing_dates:
        print("✅ 最近30天的轮动数据都已存在")
        
        # 检查数据质量
        quality_ok = check_rotation_data_quality()
        
        if not quality_ok:
            print("\n💡 数据质量不佳，建议重新运行当前计算:")
            confirm = input("是否重新运行当前日期的轮动计算? (y/N): ").strip().lower()
            if confirm == 'y':
                run_current_rotation_calculation()
                check_rotation_data_quality()
        
        return True
    
    print(f"\n🎯 发现 {len(missing_dates)} 个日期缺少轮动数据")
    print("\n💡 解决方案:")
    print("1. 当前的sector_rotation_scheduler主要计算当前日期")
    print("2. 历史数据回填需要特殊处理")
    print("3. 建议先确保当前日期的计算正常")
    
    # 询问是否运行当前日期计算
    confirm = input("\n是否运行当前日期的轮动计算? (y/N): ").strip().lower()
    if confirm == 'y':
        success = run_current_rotation_calculation()
        
        if success:
            check_rotation_data_quality()
            
            print("\n💡 建议:")
            print("1. 每日运行轮动计算积累数据")
            print("2. 配置自动化任务确保数据连续性")
            print("3. 等待7-30天积累足够历史数据")
            print("4. 历史数据回填需要修改scheduler支持指定日期")
        
        return success
    else:
        print("\n💡 手动运行建议:")
        print("   python run_sector_rotation_calculation.py")
        return False

if __name__ == "__main__":
    success = main()
    
    # 等待用户确认
    input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
