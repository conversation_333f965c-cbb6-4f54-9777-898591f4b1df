#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Breadth Pulse - 市场广度脉搏系统
主程序入口，负责启动和管理核心组件
"""

import os
import sys
import signal
import time
import logging
import argparse
import multiprocessing
from typing import List, Optional
from concurrent.futures import ProcessPoolExecutor
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 确保logs目录存在
logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 配置主程序日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(logs_dir, 'main.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 全局进程管理
running_processes = []
shutdown_flag = False


def signal_handler(signum, frame):
    """信号处理器，优雅关闭所有进程"""
    global shutdown_flag
    logger.info(f"接收到信号 {signum}，开始优雅关闭...")
    shutdown_flag = True
    
    for process in running_processes:
        if process.is_alive():
            logger.info(f"正在终止进程 {process.name} (PID: {process.pid})")
            process.terminate()
    
    # 等待进程结束
    for process in running_processes:
        process.join(timeout=10)
        if process.is_alive():
            logger.warning(f"强制杀死进程 {process.name} (PID: {process.pid})")
            process.kill()
    
    logger.info("所有进程已关闭")
    sys.exit(0)


def run_price_data_collector():
    """运行价格数据收集器"""
    try:
        logger.info("启动价格数据收集器...")
        from data_source_tool.price_data_collector import main as collector_main
        collector_main()
    except Exception as e:
        logger.error(f"价格数据收集器运行异常: {e}")
        raise


def run_market_breadth_integrator():
    """运行市场广度整合器"""
    try:
        logger.info("启动市场广度整合器...")
        from core.market_breadth_integrator import main as integrator_main
        integrator_main()
    except Exception as e:
        logger.error(f"市场广度整合器运行异常: {e}")
        raise


def run_sector_rotation_scheduler():
    """运行板块轮动调度器"""
    try:
        logger.info("启动板块轮动调度器...")
        from core.sector_rotation_scheduler import main as scheduler_main
        scheduler_main()
    except Exception as e:
        logger.error(f"板块轮动调度器运行异常: {e}")
        raise


def start_component(component_func, component_name: str) -> multiprocessing.Process:
    """启动单个组件"""
    process = multiprocessing.Process(
        target=component_func,
        name=component_name
    )
    process.start()
    running_processes.append(process)
    logger.info(f"组件 {component_name} 已启动 (PID: {process.pid})")
    return process


def monitor_processes():
    """监控进程状态"""
    global running_processes, shutdown_flag
    while not shutdown_flag:
        for i, process in enumerate(running_processes[:]):
            if not process.is_alive():
                logger.warning(f"检测到进程 {process.name} 已退出 (退出码: {process.exitcode})")
                running_processes.remove(process)
                
                if not shutdown_flag:
                    logger.info(f"重启进程 {process.name}...")
                    if process.name == "PriceDataCollector":
                        new_process = start_component(run_price_data_collector, "PriceDataCollector")
                    elif process.name == "MarketBreadthIntegrator":
                        new_process = start_component(run_market_breadth_integrator, "MarketBreadthIntegrator")
                    elif process.name == "SectorRotationScheduler":
                        new_process = start_component(run_sector_rotation_scheduler, "SectorRotationScheduler")
        
        time.sleep(5)  # 每5秒检查一次


def main():
    """主函数"""
    global running_processes, shutdown_flag
    parser = argparse.ArgumentParser(description='Breadth Pulse - 市场广度脉搏系统')
    parser.add_argument('--collector-only', action='store_true', help='仅启动价格数据收集器')
    parser.add_argument('--integrator-only', action='store_true', help='仅启动市场广度整合器')
    parser.add_argument('--scheduler-only', action='store_true', help='仅启动板块轮动调度器')
    parser.add_argument('--no-monitor', action='store_true', help='禁用进程监控')
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("="*60)
    logger.info("Breadth Pulse 市场广度脉搏系统启动")
    logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"进程ID: {os.getpid()}")
    logger.info("="*60)
    
    try:
        # 根据参数启动相应组件
        if args.collector_only:
            logger.info("启动模式: 仅价格数据收集器")
            start_component(run_price_data_collector, "PriceDataCollector")
        elif args.integrator_only:
            logger.info("启动模式: 仅市场广度整合器")
            start_component(run_market_breadth_integrator, "MarketBreadthIntegrator")
        elif args.scheduler_only:
            logger.info("启动模式: 仅板块轮动调度器")
            start_component(run_sector_rotation_scheduler, "SectorRotationScheduler")
        else:
            logger.info("启动模式: 完整系统")
            start_component(run_price_data_collector, "PriceDataCollector")
            time.sleep(2)  # 等待数据收集器初始化
            start_component(run_market_breadth_integrator, "MarketBreadthIntegrator")
            time.sleep(3)  # 等待市场广度整合器初始化
            start_component(run_sector_rotation_scheduler, "SectorRotationScheduler")
        
        logger.info(f"已启动 {len(running_processes)} 个组件")
        
        # 启动进程监控
        if not args.no_monitor and running_processes:
            logger.info("启动进程监控...")
            monitor_processes()
        else:
            # 简单等待
            while running_processes and not shutdown_flag:
                time.sleep(1)
                running_processes = [p for p in running_processes if p.is_alive()]
    
    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
        signal_handler(signal.SIGTERM, None)
    
    logger.info("Breadth Pulse 系统已退出")


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()